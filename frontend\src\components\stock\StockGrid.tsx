import React, { useState } from 'react';
import StockCard from './StockCard';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select-radix';
import { 
  RefreshCw, 
  Package, 
  ChevronLeft, 
  ChevronRight,
  Grid3X3,
  List,
  MoreHorizontal,
  AlertTriangle
} from 'lucide-react';
import type { StockItem } from '../../services/stockService';
import type { StockPagination } from '../../store/stockStore';

interface StockGridProps {
  stockItems: StockItem[];
  isLoading?: boolean;
  pagination?: StockPagination;
  onPageChange?: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  onUpdateStock?: (itemId: string, quantity: number) => Promise<void>;
  onViewDetails?: (item: StockItem) => void;
  onDeleteItem?: (itemId: string) => Promise<void>;
  onRefreshItem?: (itemId: string) => Promise<void>;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
  onRefresh?: () => void;
}

export default function StockGrid({
  stockItems,
  isLoading = false,
  pagination,
  onPageChange,
  onLimitChange,
  onUpdateStock,
  onViewDetails,
  onDeleteItem,
  onRefreshItem,
  viewMode = 'grid',
  onViewModeChange,
  onRefresh
}: StockGridProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId) 
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const handleSelectAll = () => {
    if (selectedItems.length === stockItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(stockItems.map(item => item.id));
    }
  };

  const renderPagination = () => {
    if (!pagination || pagination.totalPages <= 1) return null;

    const { page, totalPages, total, limit } = pagination;
    const startItem = (page - 1) * limit + 1;
    const endItem = Math.min(page * limit, total);

    return (
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>
            Mostrando {startItem}-{endItem} de {total} itens
          </span>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Itens por página:</span>
            <Select
              value={limit.toString()}
              onValueChange={(value) => onLimitChange?.(parseInt(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5</SelectItem>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(page - 1)}
              disabled={page <= 1 || isLoading}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (page <= 3) {
                  pageNum = i + 1;
                } else if (page >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = page - 2 + i;
                }
                
                return (
                  <Button
                    key={pageNum}
                    variant={page === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange?.(pageNum)}
                    disabled={isLoading}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(page + 1)}
              disabled={page >= totalPages || isLoading}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const renderToolbar = () => (
    <div className="flex items-center justify-between mb-4">
      <div className="flex items-center gap-2">
        <h3 className="text-lg font-semibold">
          {stockItems.length} {stockItems.length === 1 ? 'Item' : 'Itens'}
        </h3>
        {selectedItems.length > 0 && (
          <span className="text-sm text-muted-foreground">
            ({selectedItems.length} selecionados)
          </span>
        )}
      </div>
      
      <div className="flex items-center gap-2">
        {onRefresh && (
          <Button
            variant="outline"
            size="sm"
            onClick={onRefresh}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-1 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        )}
        
        {onViewModeChange && (
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onViewModeChange('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );

  const renderEmptyState = () => (
    <Card>
      <CardContent className="flex flex-col items-center justify-center py-12">
        <Package className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold mb-2">Nenhum item de estoque encontrado</h3>
        <p className="text-muted-foreground text-center mb-4">
          Não há itens de estoque que correspondam aos filtros aplicados.
        </p>
        <Button variant="outline" onClick={onRefresh} disabled={isLoading}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Recarregar
        </Button>
      </CardContent>
    </Card>
  );

  const renderLoadingState = () => (
    <div className={`grid gap-4 ${
      viewMode === 'grid' 
        ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
        : 'grid-cols-1'
    }`}>
      {Array.from({ length: pagination?.limit || 10 }, (_, i) => (
        <Card key={i} className="animate-pulse">
          <CardContent className="p-4">
            <div className="space-y-3">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
              <div className="h-8 bg-muted rounded"></div>
              <div className="flex gap-2">
                <div className="h-6 bg-muted rounded w-16"></div>
                <div className="h-6 bg-muted rounded w-16"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  const renderStockItems = () => {
    if (viewMode === 'list') {
      return (
        <div className="space-y-2">
          {stockItems.map((item) => (
            <StockCard
              key={item.id}
              stockItem={item}
              onUpdateStock={onUpdateStock}
              onViewDetails={onViewDetails}
              onDelete={onDeleteItem}
              onRefresh={onRefreshItem}
              isSelected={selectedItems.includes(item.id)}
              onSelect={() => handleSelectItem(item.id)}
              viewMode="list"
            />
          ))}
        </div>
      );
    }

    return (
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {stockItems.map((item) => (
          <StockCard
            key={item.id}
            stockItem={item}
            onUpdateStock={onUpdateStock}
            onViewDetails={onViewDetails}
            onDelete={onDeleteItem}
            onRefresh={onRefreshItem}
            isSelected={selectedItems.includes(item.id)}
            onSelect={() => handleSelectItem(item.id)}
            viewMode="card"
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-4">
      {renderToolbar()}
      
      {isLoading ? (
        renderLoadingState()
      ) : stockItems.length === 0 ? (
        renderEmptyState()
      ) : (
        <>
          {renderStockItems()}
          {renderPagination()}
        </>
      )}
    </div>
  );
}
