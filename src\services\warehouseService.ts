import {
  Warehouse,
  AutoDistributionConfig,
  DistributionRule,
  ShippingProduct,
  SpreadsheetPreview
} from '../types/stock';

/**
 * Serviço para gerenciamento de múltiplos armazéns
 * do Mercado Envios Full
 */
class WarehouseService {
  /**
   * Lista todos os armazéns de um tenant
   */
  async getWarehouses(tenantId: string): Promise<Warehouse[]> {
    try {
      // Mock - substituir por busca real no banco de dados
      const warehouses: Warehouse[] = [
        {
          id: 'warehouse-sp-001',
          name: 'Centro de Distribuição São Paulo',
          code: 'SP001',
          address: {
            street: 'Av. Paulista',
            number: '1000',
            complement: 'Galpão A',
            neighborhood: 'Bela Vista',
            city: 'São Paulo',
            state: 'SP',
            zipCode: '01310-100',
            country: 'BR'
          },
          isActive: true,
          priority: 1,
          maxCapacity: 10000,
          currentOccupancy: 2500,
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date()
        },
        {
          id: 'warehouse-rj-001',
          name: 'Centro de Distribuição Rio de Janeiro',
          code: 'RJ001',
          address: {
            street: 'Av. Brasil',
            number: '2000',
            neighborhood: 'Benfica',
            city: 'Rio de Janeiro',
            state: 'RJ',
            zipCode: '20911-130',
            country: 'BR'
          },
          isActive: true,
          priority: 2,
          maxCapacity: 8000,
          currentOccupancy: 1800,
          createdAt: new Date('2024-02-01'),
          updatedAt: new Date()
        },
        {
          id: 'warehouse-mg-001',
          name: 'Centro de Distribuição Belo Horizonte',
          code: 'MG001',
          address: {
            street: 'Rod. BR-040',
            number: 'Km 15',
            neighborhood: 'Distrito Industrial',
            city: 'Contagem',
            state: 'MG',
            zipCode: '32110-900',
            country: 'BR'
          },
          isActive: true,
          priority: 3,
          maxCapacity: 6000,
          currentOccupancy: 1200,
          createdAt: new Date('2024-03-10'),
          updatedAt: new Date()
        }
      ];

      return warehouses.filter(w => w.isActive);
    } catch (error) {
      console.error('Erro ao buscar armazéns:', error);
      throw new Error('Falha ao buscar armazéns');
    }
  }

  /**
   * Busca armazém por ID
   */
  async getWarehouseById(warehouseId: string, tenantId: string): Promise<Warehouse | null> {
    try {
      const warehouses = await this.getWarehouses(tenantId);
      return warehouses.find(w => w.id === warehouseId) || null;
    } catch (error) {
      console.error('Erro ao buscar armazém:', error);
      return null;
    }
  }

  /**
   * Cria novo armazém
   */
  async createWarehouse(warehouseData: Omit<Warehouse, 'id' | 'createdAt' | 'updatedAt'>, tenantId: string): Promise<Warehouse> {
    try {
      const newWarehouse: Warehouse = {
        id: this.generateWarehouseId(),
        ...warehouseData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Mock - substituir por inserção real no banco de dados
      console.log('Criando armazém:', newWarehouse);

      return newWarehouse;
    } catch (error) {
      console.error('Erro ao criar armazém:', error);
      throw new Error('Falha ao criar armazém');
    }
  }

  /**
   * Atualiza armazém existente
   */
  async updateWarehouse(warehouseId: string, updates: Partial<Warehouse>, tenantId: string): Promise<Warehouse | null> {
    try {
      const warehouse = await this.getWarehouseById(warehouseId, tenantId);
      
      if (!warehouse) {
        return null;
      }

      const updatedWarehouse: Warehouse = {
        ...warehouse,
        ...updates,
        id: warehouseId, // Garantir que ID não mude
        updatedAt: new Date()
      };

      // Mock - substituir por atualização real no banco de dados
      console.log('Atualizando armazém:', updatedWarehouse);

      return updatedWarehouse;
    } catch (error) {
      console.error('Erro ao atualizar armazém:', error);
      throw new Error('Falha ao atualizar armazém');
    }
  }

  /**
   * Remove armazém (soft delete)
   */
  async deleteWarehouse(warehouseId: string, tenantId: string): Promise<boolean> {
    try {
      const warehouse = await this.getWarehouseById(warehouseId, tenantId);
      
      if (!warehouse) {
        return false;
      }

      // Soft delete - marcar como inativo
      await this.updateWarehouse(warehouseId, { isActive: false }, tenantId);

      return true;
    } catch (error) {
      console.error('Erro ao remover armazém:', error);
      return false;
    }
  }

  /**
   * Calcula capacidade disponível de um armazém
   */
  async getAvailableCapacity(warehouseId: string, tenantId: string): Promise<number> {
    try {
      const warehouse = await this.getWarehouseById(warehouseId, tenantId);
      
      if (!warehouse || !warehouse.maxCapacity) {
        return 0;
      }

      const currentOccupancy = warehouse.currentOccupancy || 0;
      return Math.max(0, warehouse.maxCapacity - currentOccupancy);
    } catch (error) {
      console.error('Erro ao calcular capacidade disponível:', error);
      return 0;
    }
  }

  /**
   * Lista armazéns com capacidade disponível
   */
  async getWarehousesWithCapacity(tenantId: string, minCapacity: number = 1): Promise<Warehouse[]> {
    try {
      const warehouses = await this.getWarehouses(tenantId);
      const warehousesWithCapacity: Warehouse[] = [];

      for (const warehouse of warehouses) {
        const availableCapacity = await this.getAvailableCapacity(warehouse.id, tenantId);
        if (availableCapacity >= minCapacity) {
          warehousesWithCapacity.push(warehouse);
        }
      }

      // Ordenar por prioridade e capacidade disponível
      return warehousesWithCapacity.sort((a, b) => {
        // Primeiro por prioridade (menor número = maior prioridade)
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        
        // Depois por capacidade disponível (maior capacidade primeiro)
        const capacityA = (a.maxCapacity || 0) - (a.currentOccupancy || 0);
        const capacityB = (b.maxCapacity || 0) - (b.currentOccupancy || 0);
        return capacityB - capacityA;
      });
    } catch (error) {
      console.error('Erro ao buscar armazéns com capacidade:', error);
      return [];
    }
  }

  /**
   * Distribui produtos entre armazéns baseado na estratégia configurada
   */
  async distributeProducts(
    products: ShippingProduct[],
    warehouseIds: string[],
    tenantId: string,
    strategy: 'balanced' | 'priority' | 'capacity' | 'proximity' = 'balanced'
  ): Promise<Map<string, ShippingProduct[]>> {
    try {
      const warehouses = await this.getWarehousesByIds(warehouseIds, tenantId);
      const distribution = new Map<string, ShippingProduct[]>();

      // Inicializar distribuição
      warehouses.forEach(w => {
        distribution.set(w.id, []);
      });

      switch (strategy) {
        case 'balanced':
          return this.distributeBalanced(products, warehouses, distribution);
        
        case 'priority':
          return this.distributePriority(products, warehouses, distribution);
        
        case 'capacity':
          return this.distributeCapacity(products, warehouses, distribution);
        
        case 'proximity':
          return this.distributeProximity(products, warehouses, distribution);
        
        default:
          return this.distributeBalanced(products, warehouses, distribution);
      }
    } catch (error) {
      console.error('Erro ao distribuir produtos:', error);
      throw new Error('Falha na distribuição de produtos');
    }
  }

  /**
   * Distribuição balanceada (round robin)
   */
  private distributeBalanced(
    products: ShippingProduct[],
    warehouses: Warehouse[],
    distribution: Map<string, ShippingProduct[]>
  ): Map<string, ShippingProduct[]> {
    if (warehouses.length === 0) {
      return distribution;
    }

    products.forEach((product, index) => {
      const warehouse = warehouses[index % warehouses.length];
      if (warehouse) {
        const productList = distribution.get(warehouse.id) || [];
        productList.push({
          ...product,
          warehouseId: warehouse.id,
          warehouseName: warehouse.name
        });
        distribution.set(warehouse.id, productList);
      }
    });

    return distribution;
  }

  /**
   * Distribuição por prioridade
   */
  private distributePriority(
    products: ShippingProduct[],
    warehouses: Warehouse[],
    distribution: Map<string, ShippingProduct[]>
  ): Map<string, ShippingProduct[]> {
    // Ordenar armazéns por prioridade
    const sortedWarehouses = [...warehouses].sort((a, b) => a.priority - b.priority);
    
    if (sortedWarehouses.length === 0) {
      return distribution;
    }

    // Distribuir todos os produtos para o armazém de maior prioridade primeiro
    const primaryWarehouse = sortedWarehouses[0];
    if (primaryWarehouse) {
      const productList = distribution.get(primaryWarehouse.id) || [];
      
      products.forEach(product => {
        productList.push({
          ...product,
          warehouseId: primaryWarehouse.id,
          warehouseName: primaryWarehouse.name
        });
      });
      
      distribution.set(primaryWarehouse.id, productList);
    }
    return distribution;
  }

  /**
   * Distribuição por capacidade
   */
  private distributeCapacity(
    products: ShippingProduct[],
    warehouses: Warehouse[],
    distribution: Map<string, ShippingProduct[]>
  ): Map<string, ShippingProduct[]> {
    // Ordenar por capacidade disponível
    const sortedWarehouses = [...warehouses].sort((a, b) => {
      const capacityA = (a.maxCapacity || 0) - (a.currentOccupancy || 0);
      const capacityB = (b.maxCapacity || 0) - (b.currentOccupancy || 0);
      return capacityB - capacityA;
    });

    // Distribuir baseado na capacidade
    const warehouseLoads = new Map<string, number>();
    sortedWarehouses.forEach(w => {
      warehouseLoads.set(w.id, w.currentOccupancy || 0);
    });

    for (const product of products) {
      // Encontrar armazém com mais capacidade disponível
      let bestWarehouse = sortedWarehouses[0];
      let bestAvailableCapacity = 0;

      for (const warehouse of sortedWarehouses) {
        const currentLoad = warehouseLoads.get(warehouse.id) || 0;
        const availableCapacity = (warehouse.maxCapacity || 0) - currentLoad;
        
        if (availableCapacity > bestAvailableCapacity) {
          bestWarehouse = warehouse;
          bestAvailableCapacity = availableCapacity;
        }
      }

      if (bestWarehouse && bestAvailableCapacity >= product.quantityToSend) {
        const productList = distribution.get(bestWarehouse.id) || [];
        productList.push({
          ...product,
          warehouseId: bestWarehouse.id,
          warehouseName: bestWarehouse.name
        });
        distribution.set(bestWarehouse.id, productList);

        // Atualizar carga do armazém
        const currentLoad = warehouseLoads.get(bestWarehouse.id) || 0;
        warehouseLoads.set(bestWarehouse.id, currentLoad + product.quantityToSend);
      }
    }

    return distribution;
  }

  /**
   * Distribuição por proximidade (mock - implementar lógica real)
   */
  private distributeProximity(
    products: ShippingProduct[],
    warehouses: Warehouse[],
    distribution: Map<string, ShippingProduct[]>
  ): Map<string, ShippingProduct[]> {
    // Por enquanto, usar distribuição balanceada
    // Em implementação real, calcular proximidade baseada em CEP/região
    return this.distributeBalanced(products, warehouses, distribution);
  }

  /**
   * Busca armazéns por IDs
   */
  private async getWarehousesByIds(warehouseIds: string[], tenantId: string): Promise<Warehouse[]> {
    const allWarehouses = await this.getWarehouses(tenantId);
    return allWarehouses.filter(w => warehouseIds.includes(w.id));
  }

  /**
   * Gera preview de distribuição
   */
  async generateDistributionPreview(
    products: ShippingProduct[],
    warehouseIds: string[],
    tenantId: string,
    strategy: 'balanced' | 'priority' | 'capacity' | 'proximity' = 'balanced'
  ): Promise<SpreadsheetPreview> {
    try {
      const distribution = await this.distributeProducts(products, warehouseIds, tenantId, strategy);
      const warehouses = await this.getWarehousesByIds(warehouseIds, tenantId);

      // Calcular estatísticas por armazém
      const warehouseBreakdown = Array.from(distribution.entries()).map(([warehouseId, warehouseProducts]) => {
        const warehouse = warehouses.find(w => w.id === warehouseId);
        const productCount = warehouseProducts.length;
        const totalQuantity = warehouseProducts.reduce((sum, p) => sum + p.quantityToSend, 0);
        const totalValue = warehouseProducts.reduce((sum, p) => sum + (p.quantityToSend * p.unitPrice), 0);

        return {
          warehouseId,
          warehouseName: warehouse?.name || 'Armazém Desconhecido',
          productCount,
          totalQuantity,
          totalValue
        };
      });

      // Estatísticas gerais
      const totalProducts = products.length;
      const totalQuantity = products.reduce((sum, p) => sum + p.quantityToSend, 0);
      const totalValue = products.reduce((sum, p) => sum + (p.quantityToSend * p.unitPrice), 0);

      // Amostra de produtos (primeiros 5 de cada armazém)
      const sampleProducts: ShippingProduct[] = [];
      distribution.forEach(warehouseProducts => {
        sampleProducts.push(...warehouseProducts.slice(0, 5));
      });

      // Identificar possíveis problemas
      const potentialIssues = {
        missingDimensions: products.filter(p => !p.height || !p.width || !p.depth).length,
        missingWeight: products.filter(p => !p.weight).length,
        missingBarcode: products.filter(p => !p.barcode).length,
        zeroStock: products.filter(p => p.currentStock <= 0).length,
        negativeGap: products.filter(p => p.quantityToSend <= 0).length
      };

      // Gerar recomendações
      const recommendations: string[] = [];
      
      if (potentialIssues.missingDimensions > 0) {
        recommendations.push(`${potentialIssues.missingDimensions} produtos sem dimensões completas`);
      }
      
      if (potentialIssues.missingWeight > 0) {
        recommendations.push(`${potentialIssues.missingWeight} produtos sem peso informado`);
      }

      if (warehouseBreakdown.length > 1) {
        const maxProducts = Math.max(...warehouseBreakdown.map(w => w.productCount));
        const minProducts = Math.min(...warehouseBreakdown.map(w => w.productCount));
        const difference = maxProducts - minProducts;
        
        if (difference > totalProducts * 0.3) {
          recommendations.push('Distribuição desbalanceada entre armazéns');
        }
      }

      return {
        tenantId,
        generatedAt: new Date(),
        totalProducts,
        totalQuantity,
        totalValue,
        warehouseBreakdown,
        sampleProducts,
        potentialIssues,
        recommendations
      };
    } catch (error) {
      console.error('Erro ao gerar preview de distribuição:', error);
      throw new Error('Falha ao gerar preview de distribuição');
    }
  }

  /**
   * Valida configuração de armazém
   */
  validateWarehouse(warehouse: Partial<Warehouse>): string[] {
    const errors: string[] = [];

    if (!warehouse.name || warehouse.name.trim() === '') {
      errors.push('Nome do armazém é obrigatório');
    }

    if (!warehouse.code || warehouse.code.trim() === '') {
      errors.push('Código do armazém é obrigatório');
    }

    if (!warehouse.address) {
      errors.push('Endereço é obrigatório');
    } else {
      if (!warehouse.address.street) {
        errors.push('Rua é obrigatória');
      }
      if (!warehouse.address.city) {
        errors.push('Cidade é obrigatória');
      }
      if (!warehouse.address.state) {
        errors.push('Estado é obrigatório');
      }
      if (!warehouse.address.zipCode) {
        errors.push('CEP é obrigatório');
      }
    }

    if (warehouse.priority !== undefined && warehouse.priority < 1) {
      errors.push('Prioridade deve ser maior que zero');
    }

    if (warehouse.maxCapacity !== undefined && warehouse.maxCapacity < 0) {
      errors.push('Capacidade máxima não pode ser negativa');
    }

    return errors;
  }

  /**
   * Gera ID único para armazém
   */
  private generateWarehouseId(): string {
    return `warehouse-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Atualiza ocupação atual do armazém
   */
  async updateWarehouseOccupancy(warehouseId: string, tenantId: string, newOccupancy: number): Promise<boolean> {
    try {
      const updated = await this.updateWarehouse(warehouseId, { currentOccupancy: newOccupancy }, tenantId);
      return updated !== null;
    } catch (error) {
      console.error('Erro ao atualizar ocupação do armazém:', error);
      return false;
    }
  }

  /**
   * Calcula estatísticas de todos os armazéns
   */
  async getWarehouseStatistics(tenantId: string): Promise<{
    totalWarehouses: number;
    activeWarehouses: number;
    totalCapacity: number;
    totalOccupancy: number;
    utilizationRate: number;
    warehousesByState: Record<string, number>;
  }> {
    try {
      const warehouses = await this.getWarehouses(tenantId);
      const totalWarehouses = warehouses.length;
      const activeWarehouses = warehouses.filter(w => w.isActive).length;
      const totalCapacity = warehouses.reduce((sum, w) => sum + (w.maxCapacity || 0), 0);
      const totalOccupancy = warehouses.reduce((sum, w) => sum + (w.currentOccupancy || 0), 0);
      const utilizationRate = totalCapacity > 0 ? (totalOccupancy / totalCapacity) * 100 : 0;

      // Agrupar por estado
      const warehousesByState: Record<string, number> = {};
      warehouses.forEach(w => {
        const state = w.address.state;
        warehousesByState[state] = (warehousesByState[state] || 0) + 1;
      });

      return {
        totalWarehouses,
        activeWarehouses,
        totalCapacity,
        totalOccupancy,
        utilizationRate,
        warehousesByState
      };
    } catch (error) {
      console.error('Erro ao calcular estatísticas de armazéns:', error);
      throw new Error('Falha ao calcular estatísticas');
    }
  }
}

export const warehouseService = new WarehouseService();
