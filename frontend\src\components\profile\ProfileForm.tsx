import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select-radix';
import { useConfigStore } from '../../store/configStore';
import { useNotificationStore } from '../../store/notificationStore';
import { useAuthStore } from '../../store/authStore';
import FileUploadService from '../../services/fileUploadService';
import {
  personalInfoSchema,
  preferencesSchema,
  notificationsSchema,
  formatPhone,
  type PersonalInfoFormData,
  type PreferencesFormData,
  type NotificationsFormData,
} from '../../schemas/profileSchemas';
import { mockUploadAvatar, getInitials, getAvatarColor } from '../../mocks/profileMock';
import { UserCircleIcon, CameraIcon } from '@heroicons/react/24/outline';
import { FadeIn } from '../ui/Animations';

interface ProfileFormProps {
  showTitle?: boolean;
  onSaveSuccess?: () => void;
  onSaveError?: (error: string) => void;
  className?: string;
}

interface CombinedFormData {
  personalInfo: PersonalInfoFormData;
  preferences: PreferencesFormData;
  notifications: NotificationsFormData;
}

const ProfileForm: React.FC<ProfileFormProps> = ({
  showTitle = true,
  onSaveSuccess,
  onSaveError,
  className = '',
}) => {
  const {
    userProfile,
    userProfileLoading,
    userProfileError,
    loadUserProfile,
    updateUserProfile,
  } = useConfigStore();
  const { addNotification } = useNotificationStore();
  const { user, setUser } = useAuthStore();

  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);

  // Form setup with validation
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<CombinedFormData>({
    resolver: zodResolver(
      z.object({
        personalInfo: personalInfoSchema,
        preferences: preferencesSchema,
        notifications: notificationsSchema,
      })
    ),
    defaultValues: {
      personalInfo: {
        name: '',
        email: '',
        phone: '',
        department: '',
        position: '',
      },
      preferences: {
        language: 'pt-BR',
        dateFormat: 'DD/MM/YYYY',
        defaultView: 'grid',
        timezone: 'America/Sao_Paulo',
      },
      notifications: {
        email: true,
        stockAlerts: true,
        salesReports: false,
      },
    },
  });

  // Load profile data on mount
  useEffect(() => {
    loadUserProfile();
  }, [loadUserProfile]);

  // Update form when profile loads
  useEffect(() => {
    if (userProfile) {
      reset({
        personalInfo: {
          name: userProfile.name,
          email: userProfile.email,
          phone: userProfile.phone || '',
          department: user?.department || '',
          position: user?.position || '',
        },
        preferences: {
          language: userProfile.language,
          dateFormat: userProfile.preferences.dateFormat,
          defaultView: userProfile.preferences.defaultView,
          timezone: userProfile.timezone,
        },
        notifications: {
          email: userProfile.notifications.email,
          stockAlerts: userProfile.notifications.stockAlerts,
          salesReports: userProfile.notifications.salesReports,
        },
      });
    }
  }, [userProfile, user, reset]);

  // Handle avatar upload
  const handleAvatarChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file using the service
    const validation = FileUploadService.validateFile(file, 'avatar');
    if (!validation.valid) {
      addNotification({
        title: 'Arquivo inválido',
        message: validation.error || 'Erro na validação do arquivo',
        type: 'error',
        severity: 'error',
        duration: 5000,
      });
      return;
    }

    setAvatarFile(file);

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setAvatarPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  // Handle form submission
  const onSubmit = async (data: CombinedFormData) => {
    try {
      let avatarUrl = userProfile?.avatar;

      // Upload avatar if changed
      if (avatarFile) {
        setIsUploadingAvatar(true);
        try {
          const uploadResult = await FileUploadService.uploadAvatar(avatarFile);
          if (uploadResult.success && uploadResult.data) {
            avatarUrl = uploadResult.data.url;
            addNotification({
              title: 'Avatar atualizado',
              message: 'Sua foto de perfil foi atualizada com sucesso.',
              type: 'success',
              severity: 'success',
              duration: 3000,
            });
          } else {
            throw new Error(uploadResult.error || 'Erro no upload do avatar');
          }
        } catch (error) {
          console.error('Erro ao fazer upload do avatar:', error);
          addNotification({
            title: 'Erro no upload',
            message: error instanceof Error ? error.message : 'Erro desconhecido no upload do avatar',
            type: 'error',
            severity: 'error',
            duration: 5000,
          });
          // Não falhar o salvamento do perfil por causa do avatar
          avatarUrl = userProfile?.avatar;
        } finally {
          setIsUploadingAvatar(false);
        }
      }

      // Update profile
      await updateUserProfile({
        name: data.personalInfo.name,
        email: data.personalInfo.email,
        phone: data.personalInfo.phone || undefined,
        avatar: avatarUrl,
        language: data.preferences.language,
        timezone: data.preferences.timezone,
        preferences: {
          theme: userProfile?.preferences?.theme || 'system',
          currency: userProfile?.preferences?.currency || 'BRL',
          dateFormat: data.preferences.dateFormat,
          defaultView: data.preferences.defaultView,
        },
        notifications: {
          email: data.notifications.email,
          push: userProfile?.notifications?.push || true,
          stockAlerts: data.notifications.stockAlerts,
          salesReports: data.notifications.salesReports,
          systemUpdates: userProfile?.notifications?.systemUpdates || true,
        },
      });

      // Update auth store with new user data
      if (user) {
        setUser({
          ...user,
          name: data.personalInfo.name,
          email: data.personalInfo.email,
          phone: data.personalInfo.phone || undefined,
          avatar: avatarUrl,
          department: data.personalInfo.department,
          position: data.personalInfo.position,
        });
      }

      addNotification({
        title: 'Perfil Atualizado',
        message: 'Suas informações foram salvas com sucesso.',
        type: 'system',
        severity: 'success',
        duration: 5000,
      });

      // Reset avatar state
      setAvatarFile(null);
      setAvatarPreview(null);

      onSaveSuccess?.();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      addNotification({
        title: 'Erro ao Salvar',
        message: 'Não foi possível salvar as alterações do perfil.',
        type: 'error',
        severity: 'error',
        duration: 8000,
      });

      onSaveError?.(errorMessage);
    }
  };

  // Handle phone formatting
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value);
    setValue('personalInfo.phone', formatted);
  };

  if (userProfileError) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader>
            <CardTitle>Perfil do Usuário</CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="text-red-600 text-sm">
            Erro ao carregar perfil: {userProfileError}
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentAvatar = avatarPreview || userProfile?.avatar;
  const userName = watch('personalInfo.name') || user?.name || '';

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle>Perfil do Usuário</CardTitle>
          <p className="text-sm text-muted-foreground">
            Gerencie suas informações pessoais e preferências.
          </p>
        </CardHeader>
      )}
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Avatar Section */}
          <FadeIn>
            <div className="flex items-center space-x-4">
              <div className="relative">
                {currentAvatar ? (
                  <img
                    src={currentAvatar}
                    alt="Avatar"
                    className="w-20 h-20 rounded-full object-cover border-2 border-border"
                  />
                ) : (
                  <div className={`w-20 h-20 rounded-full flex items-center justify-center text-white text-xl font-semibold ${getAvatarColor(userName)}`}>
                    {userName ? getInitials(userName) : <UserCircleIcon className="w-12 h-12" />}
                  </div>
                )}
                
                <label
                  htmlFor="avatar-upload"
                  className="absolute -bottom-1 -right-1 bg-primary text-primary-foreground p-2 rounded-full cursor-pointer hover:bg-primary/90 transition-colors"
                >
                  <CameraIcon className="w-4 h-4" />
                </label>
                
                <input
                  id="avatar-upload"
                  type="file"
                  accept="image/jpeg,image/png,image/webp"
                  onChange={handleAvatarChange}
                  className="hidden"
                  disabled={userProfileLoading || isUploadingAvatar}
                />
              </div>
              
              <div>
                <h3 className="font-medium">Foto do Perfil</h3>
                <p className="text-sm text-muted-foreground">
                  Clique no ícone da câmera para alterar sua foto
                </p>
                <p className="text-xs text-muted-foreground">
                  JPEG, PNG ou WebP. Máximo 5MB.
                </p>
              </div>
            </div>
          </FadeIn>

          {/* Personal Information */}
          <FadeIn delay={100}>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Informações Pessoais</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome *</Label>
                  <Input
                    id="name"
                    {...register('personalInfo.name')}
                    disabled={userProfileLoading || isSubmitting}
                    className={errors.personalInfo?.name ? 'border-red-500' : ''}
                  />
                  {errors.personalInfo?.name && (
                    <p className="text-sm text-red-600">{errors.personalInfo.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...register('personalInfo.email')}
                    disabled={userProfileLoading || isSubmitting}
                    className={errors.personalInfo?.email ? 'border-red-500' : ''}
                  />
                  {errors.personalInfo?.email && (
                    <p className="text-sm text-red-600">{errors.personalInfo.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  <Input
                    id="phone"
                    type="tel"
                    {...register('personalInfo.phone')}
                    onChange={handlePhoneChange}
                    placeholder="(11) 99999-9999"
                    disabled={userProfileLoading || isSubmitting}
                    className={errors.personalInfo?.phone ? 'border-red-500' : ''}
                  />
                  {errors.personalInfo?.phone && (
                    <p className="text-sm text-red-600">{errors.personalInfo.phone.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="department">Departamento</Label>
                  <Input
                    id="department"
                    {...register('personalInfo.department')}
                    disabled={userProfileLoading || isSubmitting}
                    className={errors.personalInfo?.department ? 'border-red-500' : ''}
                  />
                  {errors.personalInfo?.department && (
                    <p className="text-sm text-red-600">{errors.personalInfo.department.message}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="position">Cargo</Label>
                <Input
                  id="position"
                  {...register('personalInfo.position')}
                  disabled={userProfileLoading || isSubmitting}
                  className={errors.personalInfo?.position ? 'border-red-500' : ''}
                />
                {errors.personalInfo?.position && (
                  <p className="text-sm text-red-600">{errors.personalInfo.position.message}</p>
                )}
              </div>
            </div>
          </FadeIn>

          {/* Preferences */}
          <FadeIn delay={150}>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Preferências</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="language">Idioma</Label>
                  <Select
                    value={watch('preferences.language')}
                    onValueChange={(value: 'pt-BR' | 'en-US' | 'es-ES') =>
                      setValue('preferences.language', value)
                    }
                    disabled={userProfileLoading || isSubmitting}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o idioma" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pt-BR">Português (Brasil)</SelectItem>
                      <SelectItem value="en-US">English (US)</SelectItem>
                      <SelectItem value="es-ES">Español</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.preferences?.language && (
                    <p className="text-sm text-red-600">{errors.preferences.language.message}</p>
                  )}
                </div>



                <div className="space-y-2">
                  <Label htmlFor="dateFormat">Formato de Data</Label>
                  <Select
                    value={watch('preferences.dateFormat')}
                    onValueChange={(value: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD') =>
                      setValue('preferences.dateFormat', value)
                    }
                    disabled={userProfileLoading || isSubmitting}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o formato" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                      <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                      <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.preferences?.dateFormat && (
                    <p className="text-sm text-red-600">{errors.preferences.dateFormat.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="defaultView">Visualização Padrão</Label>
                  <Select
                    value={watch('preferences.defaultView')}
                    onValueChange={(value: 'grid' | 'list') =>
                      setValue('preferences.defaultView', value)
                    }
                    disabled={userProfileLoading || isSubmitting}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a visualização" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="grid">Grade</SelectItem>
                      <SelectItem value="list">Lista</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.preferences?.defaultView && (
                    <p className="text-sm text-red-600">{errors.preferences.defaultView.message}</p>
                  )}
                </div>
              </div>
            </div>
          </FadeIn>

          {/* Notifications */}
          <FadeIn delay={200}>
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Notificações</h3>

              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="emailNotifications"
                    name="emailNotifications"
                    checked={watch('notifications.email')}
                    onChange={(e) =>
                      setValue('notifications.email', e.target.checked)
                    }
                    disabled={userProfileLoading || isSubmitting}
                  />
                  <Label htmlFor="emailNotifications" className="text-sm">
                    Receber notificações por email
                  </Label>
                </div>



                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="stockAlerts"
                    name="stockAlerts"
                    checked={watch('notifications.stockAlerts')}
                    onChange={(e) =>
                      setValue('notifications.stockAlerts', e.target.checked)
                    }
                    disabled={userProfileLoading || isSubmitting}
                  />
                  <Label htmlFor="stockAlerts" className="text-sm">
                    Alertas de estoque baixo
                  </Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="salesReports"
                    name="salesReports"
                    checked={watch('notifications.salesReports')}
                    onChange={(e) =>
                      setValue('notifications.salesReports', e.target.checked)
                    }
                    disabled={userProfileLoading || isSubmitting}
                  />
                  <Label htmlFor="salesReports" className="text-sm">
                    Relatórios de vendas
                  </Label>
                </div>


              </div>
            </div>
          </FadeIn>

          {/* Save Button */}
          <FadeIn delay={250}>
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={userProfileLoading || isSubmitting || isUploadingAvatar}
                className="min-w-[120px]"
              >
                {isSubmitting || isUploadingAvatar ? 'Salvando...' : 'Salvar Perfil'}
              </Button>
            </div>
          </FadeIn>
        </form>
      </CardContent>
    </Card>
  );
};

export default ProfileForm;
