import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Button } from '../ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Package,
  Calculator,
  RefreshCw
} from 'lucide-react';
import type { StockCalculation, ProductWithStock } from '../../types/api';

interface StockCalculationCardProps {
  product: ProductWithStock;
  calculation?: StockCalculation;
  onRecalculate?: (productId: string) => void;
  onUpdateStock?: (productId: string, quantity: number) => void;
  isCalculating?: boolean;
  className?: string;
}

export const StockCalculationCard: React.FC<StockCalculationCardProps> = ({
  product,
  calculation,
  onRecalculate,
  onUpdateStock,
  isCalculating = false,
  className
}) => {
  if (!calculation) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Calculator className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Cálculo não disponível</p>
            {onRecalculate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRecalculate(product.id)}
                disabled={isCalculating}
                className="mt-2"
              >
                <Calculator className="h-4 w-4 mr-2" />
                Calcular
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />;
      case 'high':
        return <TrendingUp className="h-4 w-4" />;
      case 'medium':
        return <Clock className="h-4 w-4" />;
      case 'low':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  const getGapStatus = () => {
    if (calculation.gap > 0) {
      return {
        label: 'Déficit',
        value: calculation.gap,
        color: 'text-red-600',
        icon: <TrendingDown className="h-4 w-4 text-red-600" />
      };
    } else if (calculation.gap < 0) {
      return {
        label: 'Excesso',
        value: Math.abs(calculation.gap),
        color: 'text-blue-600',
        icon: <TrendingUp className="h-4 w-4 text-blue-600" />
      };
    } else {
      return {
        label: 'Equilibrado',
        value: 0,
        color: 'text-green-600',
        icon: <CheckCircle className="h-4 w-4 text-green-600" />
      };
    }
  };

  const gapStatus = getGapStatus();
  const stockCoverage = calculation.coverageDays;
  const coveragePercentage = Math.min((stockCoverage / 30) * 100, 100); // Assuming 30 days is ideal

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <Calculator className="h-5 w-5" />
            Cálculo de Estoque
          </span>
          <Badge className={getPriorityColor(calculation.priority)}>
            {getPriorityIcon(calculation.priority)}
            <span className="ml-1 capitalize">{calculation.priority}</span>
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Stock Metrics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Estoque Atual</div>
            <div className="text-2xl font-bold">{calculation.currentStock}</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Estoque Ideal</div>
            <div className="text-2xl font-bold">{calculation.idealStock}</div>
          </div>
        </div>

        {/* Gap Analysis */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Gap de Estoque</span>
            <div className="flex items-center gap-1">
              {gapStatus.icon}
              <span className={`font-bold ${gapStatus.color}`}>
                {gapStatus.value} unidades
              </span>
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            {gapStatus.label}: {gapStatus.value > 0 ? 
              `Necessário repor ${gapStatus.value} unidades` : 
              gapStatus.value < 0 ? 
                `Excesso de ${gapStatus.value} unidades` : 
                'Estoque em equilíbrio'
            }
          </div>
        </div>

        {/* Coverage Analysis */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Cobertura de Estoque</span>
            <span className="font-bold">{stockCoverage} dias</span>
          </div>
          <Progress value={coveragePercentage} className="h-2" />
          <div className="text-xs text-muted-foreground">
            {stockCoverage < 7 ? 'Crítico - Menos de 1 semana' :
             stockCoverage < 15 ? 'Baixo - Menos de 2 semanas' :
             stockCoverage < 30 ? 'Adequado - Menos de 1 mês' :
             'Excelente - Mais de 1 mês'}
          </div>
        </div>

        {/* Sales Metrics */}
        <div className="grid grid-cols-2 gap-4 pt-2 border-t">
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Vendas Médias</div>
            <div className="font-semibold">{calculation.averageSales}/dia</div>
          </div>
          <div className="space-y-1">
            <div className="text-sm text-muted-foreground">Estoque Segurança</div>
            <div className="font-semibold">{calculation.safetyStock}</div>
          </div>
        </div>

        {/* Transit Stock */}
        {calculation.unitsInTransit > 0 && (
          <div className="flex items-center justify-between p-2 bg-blue-50 rounded-md">
            <span className="text-sm text-blue-800">Em Trânsito</span>
            <span className="font-semibold text-blue-800">
              {calculation.unitsInTransit} unidades
            </span>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          {onRecalculate && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onRecalculate(product.id)}
              disabled={isCalculating}
              className="flex-1"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isCalculating ? 'animate-spin' : ''}`} />
              Recalcular
            </Button>
          )}
          
          {onUpdateStock && calculation.gap > 0 && (
            <Button
              size="sm"
              onClick={() => onUpdateStock(product.id, calculation.idealStock)}
              className="flex-1"
            >
              <Package className="h-4 w-4 mr-2" />
              Ajustar para {calculation.idealStock}
            </Button>
          )}
        </div>

        {/* Last Calculated */}
        <div className="text-xs text-muted-foreground text-center pt-2 border-t">
          Calculado em: {formatDate(calculation.lastCalculated)}
        </div>
      </CardContent>
    </Card>
  );
};

export default StockCalculationCard;
