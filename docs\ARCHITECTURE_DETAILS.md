# Documentação Detalhada da Arquitetura do Magnow

## Introdução

Este documento detalha a arquitetura técnica do **Magnow**, uma plataforma SaaS para gestão inteligente de estoque no Mercado Livre Full. Para uma compreensão completa do produto, recomenda-se a leitura dos seguintes documentos relacionados:

- **[BUSINESS_REQUIREMENTS.md](./BUSINESS_REQUIREMENTS.md)**: Especificação completa do produto, requisitos de negócio e visão estratégica
- **[USER_GUIDE.md](./USER_GUIDE.md)**: Manual do usuário com instruções de uso da plataforma
- **[FRONTEND_ARCHITECTURE_DETAILS.md](./FRONTEND_ARCHITECTURE_DETAILS.md)**: Detalhes da arquitetura do frontend React/TypeScript

## Visão Geral do Sistema

O Magnow é um sistema SaaS de controle de estoque com as seguintes características principais:
- Arquitetura baseada em microsserviços
- Autenticação JWT com multi-tenancy
- Integração com Mercado Livre via OAuth 2.0
- Banco de dados PostgreSQL com Redis para cache
- Documentação Swagger/OpenAPI

## Diagrama de Componentes

```mermaid
graph TD
    A[Frontend] -->|HTTP| B[API Gateway]
    B --> C[Auth Service]
    B --> D[Stock Service]
    B --> E[MercadoLivre Service]
    B --> F[Reports Service]
    C --> G[(PostgreSQL)]
    D --> G
    D --> H[(Redis)]
    E --> G
    E --> H
    F --> G
```

## Serviços Principais

### 1. Auth Service
- Responsável por autenticação e autorização
- Implementa JWT com refresh tokens
- Gerencia tenants e usuários

### 2. Stock Service
- Gerencia produtos e estoque
- Calcula gaps de estoque automaticamente
- Gera alertas baseados em regras configuráveis

### 3. MercadoLivre Service
- Integração com API do Mercado Livre
- Sincronização bidirecional de produtos
- Tratamento de webhooks e notificações

### 4. Reports Service
- Geração de relatórios e dashboards
- Exportação em múltiplos formatos (PDF, Excel, CSV)
- Agendamento de relatórios periódicos

## Fluxos de Dados

### Fluxo de Autenticação
1. Cliente envia credenciais
2. Servidor valida e retorna JWT
3. Token é usado em requisições subsequentes
4. Refresh token mantém sessão ativa

### Fluxo de Sincronização com Mercado Livre
1. Usuário autoriza conexão via OAuth
2. Sistema agenda sincronização inicial
3. Webhooks atualizam dados em tempo real
4. Jobs periódicos garantem consistência

## Próximos Passos para Melhorias
1. Implementar filas para processamento assíncrono
2. Adicionar suporte a mais marketplaces
3. Melhorar sistema de alertas com machine learning
4. Otimizar queries complexas com materialized views
## Estrutura de Pastas e Arquivos

```
estoque-full/
├── src/                  # Código fonte da aplicação
│   ├── config/           # Configurações de banco de dados, Redis, Swagger
│   ├── controllers/      # Lógica de manipulação de requisições HTTP
│   ├── middleware/       # Middlewares Express para autenticação, logs, erros, etc.
│   ├── models/           # Definições de modelos de dados (Prisma)
│   ├── routes/           # Definição das rotas da API
│   ├── services/         # Lógica de negócios e serviços da aplicação
│   ├── types/            # Definições de tipos TypeScript
│   └── utils/            # Funções utilitárias e helpers
├── prisma/               # Esquemas Prisma e migrações de banco de dados
├── docs/                 # Documentação do projeto
├── frontend/             # Código fonte do frontend (se aplicável)
├── tests/                # Testes unitários e de integração
├── .env.example          # Exemplo de variáveis de ambiente
├── package.json          # Dependências e scripts do projeto
├── tsconfig.json         # Configurações do TypeScript
└── Dockerfile            # Configuração Docker
```

### Descrição dos Diretórios Principais:

- **`src/config`**: Contém arquivos de configuração para serviços externos e internos, como conexão com o banco de dados (`database.ts`), Redis (`redis.ts`) e a configuração do Swagger para documentação da API (`swagger.ts`).
- **`src/controllers`**: Responsável por receber as requisições HTTP, processá-las e enviar as respostas. Cada arquivo aqui geralmente corresponde a um recurso da API (ex: `authController.ts`, `stockController.ts`).
- **`src/middleware`**: Contém funções que são executadas antes ou depois das rotas. Inclui middlewares para autenticação (`auth.ts`), tratamento de erros (`errorHandler.ts`), logging (`logger.ts`), cache (`cacheMiddleware.ts`) e validação de tenant (`validateTenant.ts`).
- **`src/models`**: Define os modelos de dados da aplicação, geralmente gerados pelo Prisma a partir do `schema.prisma`. Estes modelos interagem diretamente com o banco de dados.
- **`src/routes`**: Define os endpoints da API e mapeia-os para os controladores correspondentes. Cada arquivo de rota agrupa endpoints relacionados (ex: `auth.ts`, `stock.ts`, `mercadolivre.ts`).
- **`src/services`**: Contém a lógica de negócios principal da aplicação. Cada serviço encapsula funcionalidades específicas e pode interagir com o banco de dados, APIs externas ou outros serviços. Exemplos incluem `authService.ts`, `stockCalculationService.ts`, `mercadoLivreApiService.ts`.
- **`src/types`**: Armazena definições de tipos TypeScript para garantir a segurança e clareza do código, especialmente para estruturas de dados complexas e interfaces de API.
- **`src/utils`**: Contém funções utilitárias e helpers que são usadas em várias partes da aplicação, como funções de logging (`logger.ts`), formatação de dados, etc.
- **`prisma`**: Contém o esquema do banco de dados (`schema.prisma`) e as migrações geradas pelo Prisma. É a fonte da verdade para a estrutura do banco de dados.
- **`docs`**: Este diretório é dedicado à documentação do projeto, incluindo guias de instalação, arquitetura, e outros documentos relevantes.
- **`frontend`**: Se houver um frontend separado no mesmo repositório, seu código fonte estará aqui. No caso do Magnow, o foco principal é o backend, mas pode haver um frontend associado.
- **`tests`**: Contém todos os testes automatizados do projeto, divididos em testes unitários e de integração, garantindo a qualidade e a funcionalidade do código.

## Próximos Passos para Melhorias
1. Implementar filas para processamento assíncrono
2. Adicionar suporte a mais marketplaces
3. Melhorar sistema de alertas com machine learning
4. Otimizar queries complexas com materialized views
## Tecnologias Utilizadas

O Magnow é construído com um conjunto robusto de tecnologias para garantir escalabilidade, performance e segurança:

### Backend
- **Node.js**: Runtime JavaScript para o lado do servidor.
- **TypeScript**: Linguagem de programação que adiciona tipagem estática ao JavaScript, melhorando a manutenibilidade e a detecção de erros.
- **Express.js**: Framework web minimalista e flexível para construir APIs RESTful.
- **Prisma**: ORM (Object-Relational Mapper) moderno para interagir com o banco de dados de forma segura e eficiente, facilitando migrações e consultas.

### Banco de Dados
- **PostgreSQL**: Banco de dados relacional robusto e de código aberto, utilizado como o banco de dados principal para persistência de dados.
- **Redis**: Armazenamento de dados em memória, usado para cache, sessões e rate limiting, melhorando a performance e a responsividade da aplicação.

### Autenticação & Segurança
- **JWT (JSON Web Tokens)**: Padrão para criação de tokens de acesso seguros, utilizados para autenticação e autorização de usuários.
- **Helmet**: Coleção de middlewares para Express que ajudam a proteger a aplicação contra vulnerabilidades web comuns, configurando cabeçalhos HTTP de segurança.
- **bcryptjs**: Biblioteca para hash de senhas, garantindo que as senhas dos usuários sejam armazenadas de forma segura.
- **express-rate-limit**: Middleware para limitar o número de requisições de um IP em um determinado período, protegendo contra ataques de força bruta e DoS.

### Monitoramento & Logs
- **Winston**: Biblioteca de logging versátil para Node.js, permitindo a criação de logs estruturados e configuráveis para diferentes ambientes.
- **morgan**: Middleware de logging HTTP para Express, que registra detalhes de cada requisição recebida pelo servidor.

### Desenvolvimento & Testes
- **Jest**: Framework de testes JavaScript, utilizado para testes unitários e de integração, garantindo a qualidade e o comportamento esperado do código.
- **Supertest**: Biblioteca para testar APIs HTTP, facilitando a escrita de testes de integração para os endpoints da aplicação.
- **ESLint**: Ferramenta de linting para identificar e reportar padrões problemáticos no código JavaScript/TypeScript, promovendo a consistência e a qualidade do código.
- **Prettier**: Ferramenta de formatação de código que garante um estilo de código consistente em todo o projeto.
- **Husky**: Ferramenta que permite a configuração de git hooks, automatizando tarefas como linting e formatação antes de cada commit.

### Documentação
- **Swagger/OpenAPI**: Padrão e ferramentas para documentar APIs RESTful, gerando uma interface interativa para explorar e testar os endpoints da API.
- **JSDoc**: Sistema de documentação para código JavaScript, utilizado para gerar documentação a partir dos comentários no código-fonte.

Esta seção detalha as principais tecnologias que compõem o ecossistema do Magnow, fornecendo uma base sólida para futuras melhorias e manutenções.
## Implementação de Melhorias e Aplicação de Correções

Para implementar melhorias ou aplicar correções no sistema Magnow, siga as seguintes diretrizes:

### 1. Entendimento do Problema/Funcionalidade
- **Análise da Requisito**: Compreenda completamente a nova funcionalidade ou o bug a ser corrigido. Consulte a documentação existente (incluindo este documento, o `README.md` e a documentação do Swagger).
- **Identificação do Serviço/Módulo**: Determine qual serviço (`src/services`), controlador (`src/controllers`), rota (`src/routes`) ou middleware (`src/middleware`) é afetado ou precisa ser modificado.

### 2. Desenvolvimento
- **Criação de Branch**: Crie uma nova branch a partir da `main` (ou da branch de desenvolvimento principal) com um nome descritivo (ex: `feature/nova-funcionalidade` ou `bugfix/correcao-erro-x`).
- **Implementação da Lógica**: Desenvolva a lógica necessária no(s) arquivo(s) apropriado(s) dentro de `src/`.
  - **Serviços**: A lógica de negócios deve residir nos arquivos de serviço (`src/services`).
  - **Controladores**: Controladores devem ser leves, focando na manipulação de requisições e respostas, delegando a lógica complexa aos serviços.
  - **Validação**: Utilize `Joi` ou `Zod` para validação de entrada de dados.
  - **Tratamento de Erros**: Utilize o sistema de tratamento de erros global (`src/middleware/errorHandler.ts`) e o serviço de monitoramento de erros (`src/services/errorMonitoringService.ts`).
- **Testes**: Escreva ou atualize testes unitários e de integração (`tests/`) para cobrir as alterações. Garanta que os testes existentes continuem passando.
  - Execute `npm test` para todos os testes.
  - Execute `npm run test:unit` para testes unitários.
  - Execute `npm run test:integration` para testes de integração.
- **Documentação de Código**: Adicione comentários JSDoc relevantes para novas funções, classes e parâmetros, especialmente em arquivos de serviço e utilitários.
- **Variáveis de Ambiente**: Se novas variáveis de ambiente forem necessárias, adicione-as ao `.env.example` e documente seu propósito.

### 3. Revisão e Qualidade
- **Linting e Formatação**: Execute `npm run lint:fix` para garantir que o código siga os padrões de linting e formatação do projeto.
- **Revisão de Código (Code Review)**: Submeta seu código para revisão por outro membro da equipe. Explique as alterações, o problema que resolvem e como foram testadas.

### 4. Deploy
- **Migrações de Banco de Dados**: Se houver alterações no esquema do banco de dados (`prisma/schema.prisma`), execute `npm run db:migrate` para gerar e aplicar as migrações. Certifique-se de que as migrações são compatíveis com o ambiente de produção.
- **Build da Aplicação**: Crie a versão de produção da aplicação com `npm run build`.
- **Deploy**: Siga o processo de deploy definido para o ambiente (Docker, PM2, etc.).

### Dicas para Correções:
- **Reprodução do Bug**: Antes de tentar corrigir, certifique-se de que consegue reproduzir o bug de forma consistente.
- **Logs**: Utilize os logs (`Winston`) para obter informações detalhadas sobre o erro. Verifique os logs de desenvolvimento e produção.
- **Debugging**: Utilize as ferramentas de debugging do Node.js ou do seu IDE para inspecionar o fluxo de execução e os valores das variáveis.
- **Testes de Regressão**: Após a correção, adicione um teste que falharia antes da correção e passe após ela, para evitar que o mesmo bug ocorra novamente.

Seguindo estas diretrizes, garantimos um processo de desenvolvimento e manutenção consistente e de alta qualidade para o Magnow.