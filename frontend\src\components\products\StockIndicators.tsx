import React from 'react';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../ui/tooltip';
import { 
  AlertTriangle, 
  CheckCircle, 
  Package, 
  TrendingUp, 
  TrendingDown,
  Clock,
  Target,
  Zap
} from 'lucide-react';
import type { ProductWithStock } from '../../types/api';

interface StockIndicatorsProps {
  product: ProductWithStock;
  showDetailed?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export default function StockIndicators({ 
  product, 
  showDetailed = false,
  size = 'md' 
}: StockIndicatorsProps) {
  const getStockLevel = () => {
    const stock = product.availableQuantity;
    const reorderPoint = product.metrics?.reorderPoint || 10;
    
    if (stock === 0) return {
      level: 'critical',
      label: 'Sem estoque',
      color: 'destructive',
      bgColor: 'bg-red-100',
      textColor: 'text-red-700',
      icon: AlertTriangle,
      percentage: 0
    };
    
    if (stock <= reorderPoint * 0.5) return {
      level: 'critical',
      label: 'Crítico',
      color: 'destructive',
      bgColor: 'bg-red-100',
      textColor: 'text-red-700',
      icon: AlertTriangle,
      percentage: 25
    };
    
    if (stock <= reorderPoint) return {
      level: 'low',
      label: 'Baixo',
      color: 'warning',
      bgColor: 'bg-yellow-100',
      textColor: 'text-yellow-700',
      icon: AlertTriangle,
      percentage: 50
    };
    
    if (stock <= reorderPoint * 2) return {
      level: 'medium',
      label: 'Médio',
      color: 'secondary',
      bgColor: 'bg-blue-100',
      textColor: 'text-blue-700',
      icon: Package,
      percentage: 75
    };
    
    return {
      level: 'good',
      label: 'Bom',
      color: 'default',
      bgColor: 'bg-green-100',
      textColor: 'text-green-700',
      icon: CheckCircle,
      percentage: 100
    };
  };

  const getCoverageStatus = () => {
    const coverage = product.metrics?.stockCoverageDays;
    if (!coverage) return null;
    
    if (coverage <= 7) return {
      status: 'critical',
      label: `${coverage}d restantes`,
      color: 'text-red-600',
      icon: Clock
    };
    
    if (coverage <= 14) return {
      status: 'warning',
      label: `${coverage}d restantes`,
      color: 'text-yellow-600',
      icon: Clock
    };
    
    return {
      status: 'good',
      label: `${coverage}d restantes`,
      color: 'text-green-600',
      icon: Clock
    };
  };

  const getTurnoverStatus = () => {
    const turnover = product.metrics?.stockTurnover;
    if (!turnover) return null;
    
    if (turnover >= 4) return {
      status: 'excellent',
      label: `${turnover.toFixed(1)}x/ano`,
      color: 'text-green-600',
      icon: TrendingUp
    };
    
    if (turnover >= 2) return {
      status: 'good',
      label: `${turnover.toFixed(1)}x/ano`,
      color: 'text-blue-600',
      icon: TrendingUp
    };
    
    return {
      status: 'low',
      label: `${turnover.toFixed(1)}x/ano`,
      color: 'text-yellow-600',
      icon: TrendingDown
    };
  };

  const getGapStatus = () => {
    const gap = product.stockCalculation?.gap;
    if (!gap || gap <= 0) return null;
    
    return {
      status: 'gap',
      label: `Gap: ${gap}`,
      color: 'text-red-600',
      icon: Target
    };
  };

  const stockLevel = getStockLevel();
  const coverage = getCoverageStatus();
  const turnover = getTurnoverStatus();
  const gap = getGapStatus();
  const StockIcon = stockLevel.icon;

  const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4';
  const textSize = size === 'sm' ? 'text-xs' : size === 'lg' ? 'text-base' : 'text-sm';

  return (
    <TooltipProvider>
      <div className="space-y-2">
        {/* Badge principal de estoque */}
        <div className="flex items-center gap-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge 
                variant={stockLevel.color as any}
                className={`flex items-center gap-1 ${textSize}`}
              >
                <StockIcon className={iconSize} />
                {stockLevel.label}
                <span className="font-mono">({product.availableQuantity})</span>
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <p>Estoque atual: {product.availableQuantity} unidades</p>
                {product.metrics?.reorderPoint && (
                  <p>Ponto de reposição: {product.metrics.reorderPoint}</p>
                )}
                <p>Status: {stockLevel.label}</p>
              </div>
            </TooltipContent>
          </Tooltip>

          {/* Gap de estoque */}
          {gap && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Badge variant="destructive" className={`flex items-center gap-1 ${textSize}`}>
                  <Target className={iconSize} />
                  {gap.label}
                </Badge>
              </TooltipTrigger>
              <TooltipContent>
                <p>Diferença entre estoque necessário e atual</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>

        {/* Barra de progresso do estoque */}
        {showDetailed && (
          <div className="space-y-1">
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>Nível de estoque</span>
              <span>{stockLevel.percentage}%</span>
            </div>
            <Progress 
              value={stockLevel.percentage} 
              className="h-2"
            />
          </div>
        )}

        {/* Indicadores detalhados */}
        {showDetailed && (
          <div className="flex flex-wrap gap-2">
            {/* Cobertura */}
            {coverage && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className={`flex items-center gap-1 ${textSize} ${coverage.color}`}>
                    <Clock className={iconSize} />
                    <span>{coverage.label}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Tempo estimado até acabar o estoque</p>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Giro de estoque */}
            {turnover && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className={`flex items-center gap-1 ${textSize} ${turnover.color}`}>
                    <turnover.icon className={iconSize} />
                    <span>{turnover.label}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Velocidade de rotação do estoque</p>
                </TooltipContent>
              </Tooltip>
            )}

            {/* Vendas médias */}
            {product.metrics?.averageDailySales && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className={`flex items-center gap-1 ${textSize} text-blue-600`}>
                    <Zap className={iconSize} />
                    <span>{product.metrics.averageDailySales.toFixed(1)}/dia</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Média de vendas diárias</p>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        )}

        {/* Alertas de estoque */}
        {product.alerts && product.alerts.length > 0 && (
          <div className="space-y-1">
            {product.alerts.slice(0, showDetailed ? 3 : 1).map((alert, index) => (
              <Tooltip key={index}>
                <TooltipTrigger asChild>
                  <div className={`flex items-center gap-1 ${textSize} text-amber-600`}>
                    <AlertTriangle className={iconSize} />
                    <span className="truncate">{alert.message}</span>
                  </div>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    <p><strong>Alerta:</strong> {alert.message}</p>
                    <p><strong>Prioridade:</strong> {alert.priority}</p>
                    {alert.threshold && (
                      <p><strong>Limite:</strong> {alert.threshold}</p>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            ))}
            
            {product.alerts.length > (showDetailed ? 3 : 1) && (
              <div className={`${textSize} text-muted-foreground`}>
                +{product.alerts.length - (showDetailed ? 3 : 1)} mais alertas
              </div>
            )}
          </div>
        )}

        {/* Status de sincronização */}
        {product.syncStatus?.isOutOfSync && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Badge variant="outline" className={`flex items-center gap-1 ${textSize} text-orange-600`}>
                <TrendingDown className={iconSize} />
                Fora de sincronia
              </Badge>
            </TooltipTrigger>
            <TooltipContent>
              <div className="space-y-1">
                <p>Produto não sincronizado com o Mercado Livre</p>
                {product.syncStatus.pendingUpdates && (
                  <p>Atualizações pendentes: {product.syncStatus.pendingUpdates.join(', ')}</p>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
        )}
      </div>
    </TooltipProvider>
  );
}
