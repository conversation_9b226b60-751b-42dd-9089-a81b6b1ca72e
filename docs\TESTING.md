# Guia de Testes

Este documento descreve a estratégia de testes do Magnow e como executar as suítes de teste.

## Estratégia de Testes

Nossa estratégia de testes é baseada em uma pirâmide de testes, com foco em testes unitários, complementados por testes de integração e alguns testes E2E (End-to-End) para os fluxos mais críticos.

-   **Testes Unitários**: Testam as menores unidades de código (funções, métodos) de forma isolada. Eles são rápidos e constituem a base da nossa pirâmide. Usamos `jest` e mocks para isolar dependências.
-   **Testes de Integração**: Verificam a interação entre diferentes partes do sistema, como a comunicação entre controllers, services e o banco de dados. Eles garantem que os componentes funcionam juntos conforme o esperado. Usamos `supertest` para fazer requisições HTTP à nossa API e um banco de dados de teste.
-   **Testes E2E**: Simulam o comportamento real do usuário, testando fluxos completos da aplicação. São mais lentos e frágeis, por isso são usados com moderação para os cenários mais importantes.

## Cobertura de Código

Nosso objetivo é manter uma cobertura de código acima de **90%**. Isso nos ajuda a garantir que a maior parte do nosso código está sendo testada e reduz a probabilidade de bugs em produção.

Para verificar a cobertura, execute:

```bash
npm run test:coverage
```

Um relatório detalhado será gerado na pasta `coverage/`.

## Como Executar os Testes

### Executando Todos os Testes

Para rodar a suíte completa de testes (unitários e de integração), use o comando:

```bash
npm test
```

### Executando Testes em Modo Watch

Durante o desenvolvimento, é útil rodar os testes automaticamente a cada alteração de arquivo. Para isso, use:

```bash
npm run test:watch
```

### Executando Testes Específicos

Você pode executar um único arquivo de teste ou um conjunto de testes que correspondam a um padrão:

```bash
# Executa todos os testes no arquivo auth.test.ts
npm test -- src/__tests__/integration/auth.test.ts

# Executa testes cujo nome contenha "login"
npm test -- --testNamePattern="login"
```

## Estrutura dos Testes

Os arquivos de teste estão localizados na pasta `src/__tests__` e seguem a mesma estrutura de pastas do código-fonte, facilitando a localização dos testes para um determinado arquivo.

```
src/__tests__/
├── integration/    # Testes de integração para os endpoints da API
│   ├── auth.test.ts
│   └── stock.test.ts
├── unit/           # Testes unitários para services, utils, etc.
│   ├── services/
│   │   └── stockCalculator.service.test.ts
│   └── utils/
│       └── encryption.test.ts
└── setup.ts        # Arquivo de configuração para os testes (ex: setup do banco)
```