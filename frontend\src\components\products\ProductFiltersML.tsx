import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select-radix';
import { Checkbox } from '../ui/checkbox';
import { Slider } from '../ui/slider';
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown,
  ChevronUp,
  RotateCcw
} from 'lucide-react';
import type { MLProductFilters } from '../../types/api';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';

interface ProductFiltersMLProps {
  filters: MLProductFilters;
  onFiltersChange: (filters: MLProductFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
  productCount?: number;
}

export default function ProductFiltersML({
  filters,
  onFiltersChange,
  onClearFilters,
  isLoading = false,
  productCount = 0
}: ProductFiltersMLProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState<MLProductFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof MLProductFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleArrayFilterChange = (key: keyof MLProductFilters, value: string, checked: boolean) => {
    const currentArray = (localFilters[key] as string[]) || [];
    const newArray = checked 
      ? [...currentArray, value]
      : currentArray.filter(item => item !== value);
    
    handleFilterChange(key, newArray.length > 0 ? newArray : undefined);
  };

  const handlePriceRangeChange = (values: number[], type: 'price' | 'stock' | 'sales') => {
    if (type === 'price') {
      handleFilterChange('minPrice', values[0] > 0 ? values[0] : undefined);
      handleFilterChange('maxPrice', values[1] < 10000 ? values[1] : undefined);
    } else if (type === 'stock') {
      handleFilterChange('minStock', values[0] > 0 ? values[0] : undefined);
      handleFilterChange('maxStock', values[1] < 1000 ? values[1] : undefined);
    } else if (type === 'sales') {
      handleFilterChange('minSoldQuantity', values[0] > 0 ? values[0] : undefined);
      handleFilterChange('maxSoldQuantity', values[1] < 1000 ? values[1] : undefined);
    }
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.search) count++;
    if (localFilters.category) count++;
    if (localFilters.status?.length) count++;
    if (localFilters.stockPriority?.length) count++;
    if (localFilters.hasGap) count++;
    if (localFilters.hasStockAlert) count++;
    if (localFilters.isOutOfSync) count++;
    if (localFilters.minPrice || localFilters.maxPrice) count++;
    if (localFilters.minStock || localFilters.maxStock) count++;
    if (localFilters.minSoldQuantity || localFilters.maxSoldQuantity) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <CardTitle className="text-base">Filtros</CardTitle>
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFiltersCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="h-8 text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Limpar
              </Button>
            )}
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Busca sempre visível */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar por título, SKU ou ML ID..."
            value={localFilters.search || ''}
            onChange={(e) => handleFilterChange('search', e.target.value || undefined)}
            className="pl-10"
          />
        </div>

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-4">
            {/* Status do produto */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Status do Produto</Label>
              <div className="flex flex-wrap gap-2">
                {['active', 'paused', 'closed'].map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}`}
                      checked={localFilters.status?.includes(status as any) || false}
                      onCheckedChange={(checked) => 
                        handleArrayFilterChange('status', status, checked as boolean)
                      }
                    />
                    <Label htmlFor={`status-${status}`} className="text-sm capitalize">
                      {status}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Prioridade de estoque */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Prioridade de Estoque</Label>
              <div className="flex flex-wrap gap-2">
                {[
                  { value: 'critical', label: 'Crítico', color: 'destructive' },
                  { value: 'high', label: 'Alto', color: 'warning' },
                  { value: 'medium', label: 'Médio', color: 'secondary' },
                  { value: 'low', label: 'Baixo', color: 'default' }
                ].map((priority) => (
                  <div key={priority.value} className="flex items-center space-x-2">
                    <Checkbox
                      id={`priority-${priority.value}`}
                      checked={localFilters.stockPriority?.includes(priority.value as any) || false}
                      onCheckedChange={(checked) => 
                        handleArrayFilterChange('stockPriority', priority.value, checked as boolean)
                      }
                    />
                    <Label htmlFor={`priority-${priority.value}`} className="text-sm">
                      {priority.label}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Filtros booleanos */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasGap"
                  checked={localFilters.hasGap || false}
                  onCheckedChange={(checked) => handleFilterChange('hasGap', checked || undefined)}
                />
                <Label htmlFor="hasGap" className="text-sm">Com gap de estoque</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasStockAlert"
                  checked={localFilters.hasStockAlert || false}
                  onCheckedChange={(checked) => handleFilterChange('hasStockAlert', checked || undefined)}
                />
                <Label htmlFor="hasStockAlert" className="text-sm">Com alertas</Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isOutOfSync"
                  checked={localFilters.isOutOfSync || false}
                  onCheckedChange={(checked) => handleFilterChange('isOutOfSync', checked || undefined)}
                />
                <Label htmlFor="isOutOfSync" className="text-sm">Fora de sincronia</Label>
              </div>
            </div>

            {/* Faixa de preço */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Faixa de Preço (R$)</Label>
              <div className="px-2">
                <Slider
                  value={[localFilters.minPrice || 0, localFilters.maxPrice || 10000]}
                  onValueChange={(values) => handlePriceRangeChange(values, 'price')}
                  max={10000}
                  step={10}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>R$ {localFilters.minPrice || 0}</span>
                  <span>R$ {localFilters.maxPrice || 10000}</span>
                </div>
              </div>
            </div>

            {/* Faixa de estoque */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Faixa de Estoque</Label>
              <div className="px-2">
                <Slider
                  value={[localFilters.minStock || 0, localFilters.maxStock || 1000]}
                  onValueChange={(values) => handlePriceRangeChange(values, 'stock')}
                  max={1000}
                  step={1}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{localFilters.minStock || 0}</span>
                  <span>{localFilters.maxStock || 1000}</span>
                </div>
              </div>
            </div>

            {/* Ordenação */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Ordenar por</Label>
                <Select
                  value={localFilters.sortBy || 'title'}
                  onValueChange={(value) => handleFilterChange('sortBy', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="title">Título</SelectItem>
                    <SelectItem value="price">Preço</SelectItem>
                    <SelectItem value="stock">Estoque</SelectItem>
                    <SelectItem value="sales">Vendas</SelectItem>
                    <SelectItem value="gap">Gap</SelectItem>
                    <SelectItem value="coverage">Cobertura</SelectItem>
                    <SelectItem value="lastSync">Última Sync</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">Ordem</Label>
                <Select
                  value={localFilters.sortOrder || 'asc'}
                  onValueChange={(value) => handleFilterChange('sortOrder', value as 'asc' | 'desc')}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Crescente</SelectItem>
                    <SelectItem value="desc">Decrescente</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Resultado */}
        <div className="flex items-center justify-between mt-4 pt-4 border-t text-sm text-muted-foreground">
          <span>
            {isLoading ? 'Carregando...' : `${productCount} produto(s) encontrado(s)`}
          </span>
          {activeFiltersCount > 0 && (
            <span>{activeFiltersCount} filtro(s) ativo(s)</span>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
