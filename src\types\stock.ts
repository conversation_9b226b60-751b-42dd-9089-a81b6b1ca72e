/**
 * Tipos TypeScript para Sistema de Cálculo de Estoque Ideal
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

/**
 * Configurações de estoque por produto
 */
export interface StockConfiguration {
  id: string;
  tenantId: string;
  mlItemId: string;
  sku: string;
  
  // Configurações de cálculo
  coverageDays: number; // Dias de cobertura desejados
  safetyStockDays: number; // Dias de estoque de segurança
  minStockLevel: number; // Nível mínimo de estoque
  maxStockLevel?: number; // Nível máximo de estoque (opcional)
  
  // Configurações de análise
  analysisWindowDays: number; // Janela de análise de vendas (ex: 30, 60, 90 dias)
  seasonalityFactor: number; // Fator de sazonalidade (1.0 = sem sazonalidade)
  trendWeight: number; // Peso para tendência de vendas (0-1)
  
  // Configurações de alertas
  criticalGapThreshold: number; // Threshold para gap crítico (em unidades)
  warningGapThreshold: number; // Threshold para gap de alerta (em unidades)
  
  // Metadados
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Dados de vendas históricas
 */
export interface SalesData {
  mlItemId: string;
  sku: string;
  date: Date;
  quantity: number;
  price: number;
  revenue: number;
}

/**
 * Análise de vendas por período
 */
export interface SalesAnalysis {
  mlItemId: string;
  sku: string;
  
  // Período analisado
  startDate: Date;
  endDate: Date;
  totalDays: number;
  
  // Métricas de vendas
  totalQuantitySold: number;
  totalRevenue: number;
  averageDailySales: number;
  medianDailySales: number;
  
  // Análise estatística
  salesVariance: number;
  salesStandardDeviation: number;
  maxDailySales: number;
  minDailySales: number;
  
  // Tendência
  trendDirection: 'increasing' | 'decreasing' | 'stable';
  trendStrength: number; // 0-1 (força da tendência)
  
  // Sazonalidade
  seasonalityDetected: boolean;
  seasonalityPattern?: SeasonalityPattern;
}

/**
 * Padrão de sazonalidade
 */
export interface SeasonalityPattern {
  type: 'weekly' | 'monthly' | 'quarterly';
  peakPeriods: string[]; // Ex: ['monday', 'friday'] ou ['january', 'december']
  lowPeriods: string[];
  seasonalityFactor: number; // Multiplicador para ajuste sazonal
}

/**
 * Dados de estoque atual
 */
export interface CurrentStock {
  mlItemId: string;
  sku: string;
  currentQuantity: number;
  availableQuantity: number; // Quantidade disponível para venda
  reservedQuantity: number; // Quantidade reservada
  inTransitQuantity: number; // Quantidade em trânsito
  lastUpdated: Date;
}

/**
 * Parâmetros para análise de gap de estoque
 */
export interface StockGapAnalysisParams {
  tenantId: string;
  mlItemIds?: string[];
  warehouseIds?: string[];
  categories?: string[];
  minGapThreshold?: number;
  maxGapThreshold?: number;
  includeZeroStock?: boolean;
  sortBy?: 'gap' | 'priority' | 'value' | 'sku';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Resultado do cálculo de estoque ideal
 */
export interface StockCalculationResult {
  mlItemId: string;
  sku: string;
  calculatedAt: Date;
  
  // Dados base
  currentStock: number;
  inTransitStock: number;
  safetyStock: number;
  
  // Análise de vendas
  averageDailySales: number;
  adjustedDailySales: number; // Com fatores de sazonalidade e tendência
  
  // Cálculos
  idealStock: number; // Estoque ideal baseado na cobertura de dias
  stockGap: number; // Gap de estoque (positivo = falta, negativo = sobra)
  coverageDaysAvailable: number; // Dias de cobertura com estoque atual
  
  // Status
  status: StockStatus;
  priority: StockPriority;
  
  // Recomendações
  recommendedAction: StockAction;
  recommendedQuantity: number;
  
  // Próxima revisão
  nextReviewDate: Date;
}

/**
 * Status do estoque
 */
export type StockStatus = 
  | 'critical' // Gap crítico - ação imediata necessária
  | 'warning'  // Gap de alerta - atenção necessária
  | 'optimal'  // Estoque em nível ideal
  | 'excess';  // Excesso de estoque

/**
 * Prioridade do estoque
 */
export type StockPriority = 'high' | 'medium' | 'low';

/**
 * Ação recomendada
 */
export type StockAction = 
  | 'urgent_restock'   // Reposição urgente
  | 'plan_restock'     // Planejar reposição
  | 'monitor'          // Monitorar
  | 'reduce_stock'     // Reduzir estoque
  | 'no_action';       // Nenhuma ação necessária

/**
 * Alerta de estoque
 */
export interface StockAlert {
  id: string;
  tenantId: string;
  mlItemId: string;
  sku: string;
  
  // Tipo e severidade
  type: StockAlertType;
  severity: 'critical' | 'warning' | 'info';
  
  // Detalhes
  message: string;
  description: string;
  currentStock: number;
  recommendedStock: number;
  gap: number;
  
  // Status
  status: 'active' | 'acknowledged' | 'resolved';
  createdAt: Date;
  acknowledgedAt?: Date;
  acknowledgedBy?: string;
  resolvedAt?: Date;
  
  // Ações
  recommendedAction: StockAction;
  actionTaken?: string;
  actionTakenAt?: Date;
  actionTakenBy?: string;
}

/**
 * Tipos de alerta de estoque
 */
export type StockAlertType = 
  | 'low_stock'        // Estoque baixo
  | 'critical_stock'   // Estoque crítico
  | 'out_of_stock'     // Sem estoque
  | 'excess_stock'     // Excesso de estoque
  | 'trend_change'     // Mudança de tendência
  | 'calculation_error'; // Erro no cálculo

/**
 * Configurações globais do sistema de estoque
 */
export interface StockSystemConfiguration {
  tenantId: string;
  
  // Configurações padrão
  defaultCoverageDays: number;
  defaultSafetyStockDays: number;
  defaultAnalysisWindowDays: number;
  
  // Thresholds globais
  globalCriticalThreshold: number;
  globalWarningThreshold: number;
  
  // Configurações de cálculo
  calculationFrequencyHours: number; // Frequência de recálculo automático
  enableAutomaticCalculation: boolean;
  enableSeasonalityAdjustment: boolean;
  enableTrendAdjustment: boolean;
  
  // Configurações de alertas
  enableAlerts: boolean;
  alertRetentionDays: number;
  notificationChannels: NotificationChannel[];
  
  // Metadados
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Canais de notificação
 */
export interface NotificationChannel {
  type: 'email' | 'webhook' | 'dashboard';
  isActive: boolean;
  configuration: Record<string, any>;
}

/**
 * Parâmetros para cálculo de estoque
 */
export interface StockCalculationParams {
  mlItemIds?: string[]; // IDs específicos para calcular (opcional)
  forceRecalculation?: boolean; // Forçar recálculo mesmo se recente
  includeInactive?: boolean; // Incluir produtos inativos
  analysisStartDate?: Date; // Data de início da análise
  analysisEndDate?: Date; // Data de fim da análise
}

/**
 * Resultado em lote de cálculos
 */
export interface BatchStockCalculationResult {
  calculatedAt: Date;
  totalItems: number;
  successfulCalculations: number;
  failedCalculations: number;
  results: StockCalculationResult[];
  errors: StockCalculationError[];
  executionTimeMs: number;
}

/**
 * Erro de cálculo de estoque
 */
export interface StockCalculationError {
  mlItemId: string;
  sku: string;
  error: string;
  details?: string;
  timestamp: Date;
}

/**
 * Métricas de performance do sistema
 */
export interface StockSystemMetrics {
  tenantId: string;
  calculatedAt: Date;
  
  // Métricas gerais
  totalProducts: number;
  productsWithCriticalStock: number;
  productsWithWarningStock: number;
  productsWithOptimalStock: number;
  productsWithExcessStock: number;
  
  // Métricas de alertas
  activeAlerts: number;
  criticalAlerts: number;
  warningAlerts: number;
  
  // Métricas de performance
  averageCalculationTimeMs: number;
  lastCalculationAt: Date;
  calculationSuccessRate: number;
  
  // Métricas de negócio
  totalStockValue: number;
  potentialLostSales: number; // Vendas perdidas por falta de estoque
  excessStockValue: number; // Valor em estoque excessivo
}

/**
 * Histórico de cálculos
 */
export interface StockCalculationHistory {
  id: string;
  tenantId: string;
  mlItemId: string;
  sku: string;
  
  // Dados do cálculo
  calculatedAt: Date;
  configuration: StockConfiguration;
  result: StockCalculationResult;
  
  // Contexto
  triggeredBy: 'automatic' | 'manual' | 'api';
  triggeredByUser?: string;
  
  // Performance
  calculationTimeMs: number;
  dataQuality: 'excellent' | 'good' | 'fair' | 'poor';
}

/**
 * Configuração de alertas de estoque
 */
export interface StockAlertConfiguration {
  id: string;
  tenantId: string;
  isEnabled: boolean;
  criticalThreshold: number;
  warningThreshold: number;
  checkFrequencyMinutes: number;
  maxAlertsPerProduct: number;
  alertRetentionDays: number;
  notificationChannels: AlertChannel[];
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Severidade do alerta
 */
export type AlertSeverity = 'critical' | 'warning' | 'info';

/**
 * Canais de alerta
 */
export type AlertChannel = 'email' | 'webhook' | 'slack' | 'discord' | 'telegram' | 'dashboard';

/**
 * Regra de alerta
 */
export interface AlertRule {
  id: string;
  tenantId: string;
  name: string;
  description: string;
  isActive: boolean;
  
  // Condições
  conditions: AlertCondition[];
  
  // Ações
  actions: AlertAction[];
  
  // Configurações
  cooldownMinutes: number; // Tempo mínimo entre alertas
  maxAlertsPerDay: number;
  
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Condição de alerta
 */
export interface AlertCondition {
  field: 'stockGap' | 'coverageDays' | 'status' | 'priority';
  operator: 'equals' | 'greaterThan' | 'lessThan' | 'greaterThanOrEqual' | 'lessThanOrEqual';
  value: string | number;
}

/**
 * Ação de alerta
 */
export interface AlertAction {
  type: 'notification' | 'webhook' | 'email' | 'autoRestock';
  configuration: Record<string, any>;
}

/**
 * Configuração de job de recálculo automático
 */
export interface JobConfig {
  name: string;
  tenantId: string;
  cronExpression: string; // Expressão cron para agendamento
  timezone?: string;
  enabled: boolean;
  
  // Configurações de execução
  forceRecalculation?: boolean;
  includeSpecificItems?: boolean;
  specificItemIds?: string[];
  includeInactiveItems?: boolean;
  processAlerts?: boolean;
  
  // Metadados
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

/**
 * Execução de job
 */
export interface JobExecution {
  id: string;
  tenantId: string;
  jobName: string;
  startedAt: Date;
  finishedAt?: Date;
  status: 'running' | 'completed' | 'failed';
  triggeredBy: 'scheduler' | 'manual';
  triggeredByUserId?: string;
  error?: string;
  result?: {
    calculation?: BatchStockCalculationResult;
    alerts?: any;
    executionTimeMs: number;
  };
}

/**
 * Status de job ativo
 */
export interface JobStatus {
  tenantId: string;
  isActive: boolean;
  isRunning: boolean;
  lastExecution?: JobExecution;
  recentExecutions: JobExecution[];
  nextExecution: Date | null;
}

/**
 * ============================================================================
 * TIPOS PARA SISTEMA DE PLANILHAS MERCADO ENVIOS FULL
 * ============================================================================
 */

/**
 * Dados de produto para planilha do Mercado Envios Full
 */
export interface ShippingProduct {
  // Identificação
  sku: string;
  mlItemId: string;
  title: string;
  description?: string;
  
  // Códigos e identificação
  barcode?: string; // EAN/UPC
  internalCode?: string;
  
  // Dimensões e peso
  height?: number; // cm
  width?: number; // cm
  depth?: number; // cm
  weight?: number; // gramas
  
  // Informações comerciais
  unitPrice: number;
  category?: string;
  brand?: string;
  
  // Estoque
  currentStock: number;
  quantityToSend: number;
  
  // Armazém
  warehouseId: string;
  warehouseName: string;
}

/**
 * Configuração de armazém do Mercado Envios Full
 */
export interface Warehouse {
  id: string;
  name: string;
  code: string; // Código do ML para o armazém
  address: {
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  isActive: boolean;
  priority: number; // Prioridade para distribuição automática
  maxCapacity?: number; // Capacidade máxima em unidades
  currentOccupancy?: number; // Ocupação atual
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Linha da planilha do Mercado Envios Full
 */
export interface ShippingSpreadsheetRow {
  sku: string;
  title: string;
  quantity: number;
  barcode?: string;
  height?: number;
  width?: number;
  depth?: number;
  weight?: number;
  unitPrice: number;
  category?: string;
  brand?: string;
  warehouseCode: string;
}

/**
 * Template de planilha
 */
export interface SpreadsheetTemplate {
  id: string;
  name: string;
  description: string;
  columns: TemplateColumn[];
  validations: ValidationRule[];
  formatting: FormattingRule[];
  conditionalLogic: ConditionalRule[];
  isDefault: boolean;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Coluna do template
 */
export interface TemplateColumn {
  id: string;
  name: string;
  displayName: string;
  dataType: 'string' | 'number' | 'date' | 'boolean';
  isRequired: boolean;
  defaultValue?: any;
  validation?: ColumnValidation;
  formatting?: ColumnFormatting;
  order: number;
}

/**
 * Validação de coluna
 */
export interface ColumnValidation {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
  allowedValues?: any[];
  customValidator?: string;
}

/**
 * Formatação de coluna
 */
export interface ColumnFormatting {
  numberFormat?: string;
  dateFormat?: string;
  alignment?: 'left' | 'center' | 'right';
  backgroundColor?: string;
  fontColor?: string;
  fontWeight?: 'normal' | 'bold';
}

/**
 * Regra de validação
 */
export interface ValidationRule {
  id: string;
  name: string;
  type: 'required' | 'format' | 'range' | 'custom';
  field: string;
  condition: any;
  message: string;
  severity: 'error' | 'warning';
}

/**
 * Regra de formatação
 */
export interface FormattingRule {
  id: string;
  name: string;
  condition: any;
  formatting: ColumnFormatting;
}

/**
 * Regra condicional
 */
export interface ConditionalRule {
  id: string;
  name: string;
  condition: any;
  action: 'show' | 'hide' | 'require' | 'calculate';
  target: string;
  value?: any;
}

/**
 * Configuração da planilha (expandida)
 */
export interface SpreadsheetConfig {
  tenantId: string;
  
  // Template
  template: SpreadsheetTemplate;
  
  // Formato
  format: 'csv' | 'xlsx';
  encoding: 'utf8' | 'latin1';
  delimiter?: string; // Para CSV
  
  // Colunas obrigatórias
  requiredColumns: string[];
  
  // Mapeamento de colunas
  columnMapping: Record<string, string>;
  
  // Validações
  validateBarcode: boolean;
  validateDimensions: boolean;
  validateWeight: boolean;
  
  // Configurações de geração
  includeHeader: boolean;
  maxRowsPerFile: number;
  
  // Automação
  automationRules: AutomationRule[];
  
  // Configurações de marketplace
  marketplaceSettings: MarketplaceConfig[];
  
  // Analytics
  analyticsEnabled: boolean;
  
  // Notificações
  notificationSettings: NotificationConfig;
  
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Regra de automação
 */
export interface AutomationRule {
  id: string;
  name: string;
  trigger: {
    type: 'schedule' | 'stock_level' | 'sales_threshold';
    conditions: any;
  };
  actions: {
    generateSpreadsheet: boolean;
    notifyUsers: string[];
    updateStock: boolean;
  };
  isActive: boolean;
}

/**
 * Configuração de marketplace
 */
export interface MarketplaceConfig {
  id: string;
  name: string;
  adapter: string;
  settings: Record<string, any>;
  isActive: boolean;
}

/**
 * Configuração de notificações
 */
export interface NotificationConfig {
  email: boolean;
  push: boolean;
  slack: boolean;
  discord: boolean;
  webhooks: string[];
}

/**
 * Resultado da geração de planilha
 */
export interface SpreadsheetGenerationResult {
  id: string;
  tenantId: string;
  
  // Metadados da geração
  generatedAt: Date;
  generatedBy: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  format: 'csv' | 'xlsx';
  
  // Estatísticas
  totalProducts: number;
  totalQuantity: number;
  totalValue: number;
  warehousesCount: number;
  
  // Produtos incluídos
  products: ShippingProduct[];
  
  // Validações
  hasErrors: boolean;
  errors: SpreadsheetError[];
  warnings: SpreadsheetWarning[];
  
  // Status
  status: 'generated' | 'downloaded' | 'sent' | 'processed';
  downloadedAt?: Date;
  downloadedBy?: string;
  
  // Configurações usadas
  config: SpreadsheetConfig;
}

/**
 * Erro na geração de planilha
 */
export interface SpreadsheetError {
  type: 'validation' | 'data' | 'format' | 'system';
  severity: 'error' | 'warning';
  code: string;
  message: string;
  details?: string;
  affectedSku?: string;
  affectedField?: string;
}

/**
 * Aviso na geração de planilha
 */
export interface SpreadsheetWarning {
  type: 'missing_data' | 'estimated_value' | 'dimension_assumed';
  message: string;
  affectedSku: string;
  affectedField: string;
  assumedValue?: any;
}

/**
 * Histórico de planilhas geradas
 */
export interface SpreadsheetHistory {
  id: string;
  tenantId: string;
  
  // Dados da geração
  generationResult: SpreadsheetGenerationResult;
  
  // Tracking
  createdAt: Date;
  lastAccessedAt?: Date;
  accessCount: number;
  
  // Status no ML
  mlStatus?: 'pending' | 'processing' | 'completed' | 'error';
  mlProcessedAt?: Date;
  mlError?: string;
  
  // Metadados adicionais
  notes?: string;
  tags?: string[];
}

/**
 * Parâmetros para geração de planilha
 */
export interface GenerateSpreadsheetParams {
  tenantId: string;
  
  // Filtros de produtos
  warehouseIds?: string[];
  productIds?: string[]; // SKUs ou MLItemIds
  categories?: string[];
  brands?: string[];
  
  // Critérios de seleção automática
  useStockCalculation?: boolean; // Usar cálculo de gap de estoque
  minGapThreshold?: number; // Gap mínimo para incluir produto
  maxProducts?: number; // Limite máximo de produtos
  
  // Configurações de geração
  format: 'csv' | 'xlsx';
  splitByWarehouse?: boolean; // Gerar arquivo separado por armazém
  
  // Ajustes manuais
  manualAdjustments?: ProductAdjustment[];
  
  // Metadados
  generatedBy: string;
  notes?: string;
  tags?: string[];
}

/**
 * Ajuste manual de produto
 */
export interface ProductAdjustment {
  sku: string;
  action: 'include' | 'exclude' | 'modify';
  newQuantity?: number;
  newWarehouse?: string;
  reason?: string;
}

/**
 * Preview da planilha antes da geração
 */
export interface SpreadsheetPreview {
  tenantId: string;
  generatedAt: Date;
  
  // Estatísticas
  totalProducts: number;
  totalQuantity: number;
  totalValue: number;
  
  // Produtos por armazém
  warehouseBreakdown: {
    warehouseId: string;
    warehouseName: string;
    productCount: number;
    totalQuantity: number;
    totalValue: number;
  }[];
  
  // Amostra de produtos
  sampleProducts: ShippingProduct[];
  
  // Possíveis problemas
  potentialIssues: {
    missingDimensions: number;
    missingWeight: number;
    missingBarcode: number;
    zeroStock: number;
    negativeGap: number;
  };
  
  // Recomendações
  recommendations: string[];
}

/**
 * Configurações de distribuição automática
 */
export interface AutoDistributionConfig {
  tenantId: string;
  
  // Estratégia de distribuição
  strategy: 'balanced' | 'priority' | 'capacity' | 'proximity';
  
  // Regras de distribuição
  rules: DistributionRule[];
  
  // Limites
  maxProductsPerWarehouse?: number;
  maxValuePerWarehouse?: number;
  
  // Configurações
  respectWarehousePriority: boolean;
  considerCapacity: boolean;
  balanceLoad: boolean;
  
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Regra de distribuição
 */
export interface DistributionRule {
  id: string;
  name: string;
  priority: number;
  
  // Condições
  conditions: {
    categories?: string[];
    brands?: string[];
    minValue?: number;
    maxValue?: number;
    minWeight?: number;
    maxWeight?: number;
  };
  
  // Ação
  action: {
    preferredWarehouses?: string[];
    excludedWarehouses?: string[];
    maxQuantityPerWarehouse?: number;
  };
  
  isActive: boolean;
}

/**
 * Status de processamento no Mercado Livre
 */
export interface MLProcessingStatus {
  spreadsheetId: string;
  mlReferenceId?: string;
  status: 'uploaded' | 'validating' | 'processing' | 'completed' | 'error';
  progress?: number; // 0-100
  message?: string;
  error?: string;
  processedProducts?: number;
  totalProducts?: number;
  lastUpdated: Date;
}