 
import { render, screen, fireEvent } from '@testing-library/react';
import { Switch } from './Switch';
import { describe, it, expect, vi } from 'vitest';

describe('Switch', () => {
  it('should render a switch component', () => {
    render(<Switch />);
    expect(screen.getByRole('switch')).toBeInTheDocument();
  });

  it('should render with a label', () => {
    render(<Switch label="Enable Notifications" />);
    expect(screen.getByLabelText('Enable Notifications')).toBeInTheDocument();
  });

  it('should be unchecked by default', () => {
    render(<Switch />);
    expect(screen.getByRole('switch')).not.toBeChecked();
  });

  it('should toggle checked state on click', () => {
    render(<Switch />);
    const switchElement = screen.getByRole('switch');
    fireEvent.click(switchElement);
    expect(switchElement).toBeChecked();
    fireEvent.click(switchElement);
    expect(switchElement).not.toBeChecked();
  });

  it('should call onCheckedChange handler when toggled', () => {
    const handleChange = vi.fn();
    render(<Switch onCheckedChange={handleChange} />);
    const switchElement = screen.getByRole('switch');
    fireEvent.click(switchElement);
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith(true);
  });

  it('should be disabled when disabled prop is true', () => {
    render(<Switch disabled />);
    expect(screen.getByRole('switch')).toBeDisabled();
  });

  it('should apply custom variant classes', () => {
    render(<Switch variant="primary" data-testid="switch-primary" />);
    const switchElement = screen.getByTestId('switch-primary');
    expect(switchElement).toHaveClass('data-[state=checked]:bg-design-primary-600');
  });

  it('should apply custom size classes', () => {
    render(<Switch size="lg" data-testid="switch-lg" />);
    const switchElement = screen.getByTestId('switch-lg');
    expect(switchElement).toHaveClass('w-14');
    expect(switchElement).toHaveClass('h-8');
  });
}); 
