/**
 * Serviço de Templates de Planilhas
 * Sistema Magnow - Gerenciamento de Templates
 */

import {
  SpreadsheetTemplate,
  TemplateColumn,
  ValidationRule,
  FormattingRule,
  ConditionalRule,
  ColumnValidation,
  ColumnFormatting
} from '../types/stock';
import { logger } from '../utils/logger';

class TemplateService {
  private templates: SpreadsheetTemplate[] = [];

  constructor() {
    this.initializeDefaultTemplates();
  }

  /**
   * Inicializa templates padrão
   */
  private initializeDefaultTemplates(): void {
    const defaultTemplate = this.createDefaultMercadoLivreTemplate();
    this.templates.push(defaultTemplate);
  }

  /**
   * Cria template padrão do Mercado Livre
   */
  private createDefaultMercadoLivreTemplate(): SpreadsheetTemplate {
    const columns: TemplateColumn[] = [
      {
        id: 'sku',
        name: 'sku',
        displayName: 'SKU',
        dataType: 'string',
        isRequired: true,
        validation: {
          minLength: 1,
          maxLength: 50,
          pattern: '^[A-Za-z0-9-_]+$'
        },
        formatting: {
          alignment: 'left',
          fontWeight: 'normal'
        },
        order: 1
      },
      {
        id: 'title',
        name: 'title',
        displayName: 'Título',
        dataType: 'string',
        isRequired: true,
        validation: {
          minLength: 5,
          maxLength: 200
        },
        formatting: {
          alignment: 'left'
        },
        order: 2
      },
      {
        id: 'quantity',
        name: 'quantity',
        displayName: 'Quantidade',
        dataType: 'number',
        isRequired: true,
        validation: {
          min: 1,
          max: 99999
        },
        formatting: {
          numberFormat: '#,##0',
          alignment: 'right'
        },
        order: 3
      },
      {
        id: 'barcode',
        name: 'barcode',
        displayName: 'Código de Barras',
        dataType: 'string',
        isRequired: false,
        validation: {
          pattern: '^[0-9]{8,14}$'
        },
        formatting: {
          alignment: 'center'
        },
        order: 4
      },
      {
        id: 'height',
        name: 'height',
        displayName: 'Altura (cm)',
        dataType: 'number',
        isRequired: true,
        validation: {
          min: 0.1,
          max: 200
        },
        formatting: {
          numberFormat: '0.0',
          alignment: 'right'
        },
        order: 5
      },
      {
        id: 'width',
        name: 'width',
        displayName: 'Largura (cm)',
        dataType: 'number',
        isRequired: true,
        validation: {
          min: 0.1,
          max: 200
        },
        formatting: {
          numberFormat: '0.0',
          alignment: 'right'
        },
        order: 6
      },
      {
        id: 'depth',
        name: 'depth',
        displayName: 'Profundidade (cm)',
        dataType: 'number',
        isRequired: true,
        validation: {
          min: 0.1,
          max: 200
        },
        formatting: {
          numberFormat: '0.0',
          alignment: 'right'
        },
        order: 7
      },
      {
        id: 'weight',
        name: 'weight',
        displayName: 'Peso (g)',
        dataType: 'number',
        isRequired: true,
        validation: {
          min: 1,
          max: 30000
        },
        formatting: {
          numberFormat: '#,##0',
          alignment: 'right'
        },
        order: 8
      },
      {
        id: 'unitPrice',
        name: 'unitPrice',
        displayName: 'Preço Unitário',
        dataType: 'number',
        isRequired: true,
        validation: {
          min: 0.01,
          max: 999999.99
        },
        formatting: {
          numberFormat: 'R$ #,##0.00',
          alignment: 'right'
        },
        order: 9
      },
      {
        id: 'category',
        name: 'category',
        displayName: 'Categoria',
        dataType: 'string',
        isRequired: false,
        formatting: {
          alignment: 'left'
        },
        order: 10
      },
      {
        id: 'brand',
        name: 'brand',
        displayName: 'Marca',
        dataType: 'string',
        isRequired: false,
        formatting: {
          alignment: 'left'
        },
        order: 11
      },
      {
        id: 'warehouseCode',
        name: 'warehouseCode',
        displayName: 'Armazém',
        dataType: 'string',
        isRequired: true,
        formatting: {
          alignment: 'center',
          fontWeight: 'bold'
        },
        order: 12
      }
    ];

    const validations: ValidationRule[] = [
      {
        id: 'required-sku',
        name: 'SKU Obrigatório',
        type: 'required',
        field: 'sku',
        condition: { notEmpty: true },
        message: 'SKU é obrigatório',
        severity: 'error'
      },
      {
        id: 'dimensions-check',
        name: 'Verificação de Dimensões',
        type: 'custom',
        field: 'dimensions',
        condition: { 
          formula: 'height * width * depth <= 1000000' // 1m³ em cm³
        },
        message: 'Volume total não pode exceder 1m³',
        severity: 'warning'
      },
      {
        id: 'weight-dimension-ratio',
        name: 'Proporção Peso/Dimensão',
        type: 'custom',
        field: 'weight',
        condition: {
          formula: 'weight / (height * width * depth) < 1' // densidade < 1g/cm³
        },
        message: 'Densidade do produto parece muito alta',
        severity: 'warning'
      }
    ];

    const formatting: FormattingRule[] = [
      {
        id: 'highlight-high-value',
        name: 'Destacar Alto Valor',
        condition: { field: 'unitPrice', operator: '>', value: 1000 },
        formatting: {
          backgroundColor: '#fff3cd',
          fontColor: '#856404'
        }
      },
      {
        id: 'highlight-low-stock',
        name: 'Destacar Baixo Estoque',
        condition: { field: 'quantity', operator: '<', value: 10 },
        formatting: {
          backgroundColor: '#f8d7da',
          fontColor: '#721c24'
        }
      }
    ];

    const conditionalLogic: ConditionalRule[] = [
      {
        id: 'calculate-volume',
        name: 'Calcular Volume',
        condition: { always: true },
        action: 'calculate',
        target: 'volume',
        value: 'height * width * depth'
      },
      {
        id: 'calculate-total-value',
        name: 'Calcular Valor Total',
        condition: { always: true },
        action: 'calculate',
        target: 'totalValue',
        value: 'quantity * unitPrice'
      }
    ];

    return {
      id: 'default-mercadolivre',
      name: 'Mercado Livre - Padrão',
      description: 'Template padrão para planilhas do Mercado Envios Full',
      columns,
      validations,
      formatting,
      conditionalLogic,
      isDefault: true,
      tenantId: 'system',
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Obtém todos os templates de um tenant
   */
  async getTemplatesByTenant(tenantId: string): Promise<SpreadsheetTemplate[]> {
    try {
      return this.templates.filter(t => t.tenantId === tenantId || t.tenantId === 'system');
    } catch (error) {
      logger.error('Erro ao buscar templates:', error);
      throw new Error('Falha ao buscar templates');
    }
  }

  /**
   * Obtém template por ID
   */
  async getTemplateById(id: string, tenantId: string): Promise<SpreadsheetTemplate | null> {
    try {
      const template = this.templates.find(t => 
        t.id === id && (t.tenantId === tenantId || t.tenantId === 'system')
      );
      return template || null;
    } catch (error) {
      logger.error('Erro ao buscar template:', error);
      return null;
    }
  }

  /**
   * Cria novo template
   */
  async createTemplate(template: Omit<SpreadsheetTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<SpreadsheetTemplate> {
    try {
      const newTemplate: SpreadsheetTemplate = {
        ...template,
        id: this.generateTemplateId(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      this.templates.push(newTemplate);
      logger.info(`Template criado: ${newTemplate.id}`);
      
      return newTemplate;
    } catch (error) {
      logger.error('Erro ao criar template:', error);
      throw new Error('Falha ao criar template');
    }
  }

  /**
   * Atualiza template existente
   */
  async updateTemplate(id: string, tenantId: string, updates: Partial<SpreadsheetTemplate>): Promise<SpreadsheetTemplate | null> {
    try {
      const index = this.templates.findIndex(t => 
        t.id === id && t.tenantId === tenantId
      );

      if (index === -1) {
        return null;
      }

      const currentTemplate = this.templates[index];
      if (!currentTemplate) {
        return null;
      }

      const updatedTemplate: SpreadsheetTemplate = {
        ...currentTemplate,
        ...updates,
        id: currentTemplate.id, // Garantir que o id não seja alterado
        tenantId: currentTemplate.tenantId, // Garantir que o tenantId não seja alterado
        name: updates.name || currentTemplate.name, // Garantir que name seja string
        updatedAt: new Date()
      };

      this.templates[index] = updatedTemplate;

      logger.info(`Template atualizado: ${id}`);
      return this.templates[index];
    } catch (error) {
      logger.error('Erro ao atualizar template:', error);
      throw new Error('Falha ao atualizar template');
    }
  }

  /**
   * Remove template
   */
  async deleteTemplate(id: string, tenantId: string): Promise<boolean> {
    try {
      const index = this.templates.findIndex(t => 
        t.id === id && t.tenantId === tenantId && !t.isDefault
      );

      if (index === -1) {
        return false;
      }

      this.templates.splice(index, 1);
      logger.info(`Template removido: ${id}`);
      
      return true;
    } catch (error) {
      logger.error('Erro ao remover template:', error);
      return false;
    }
  }

  /**
   * Valida dados usando template
   */
  async validateData(data: any[], template: SpreadsheetTemplate): Promise<{
    isValid: boolean;
    errors: Array<{ row: number; field: string; message: string; severity: 'error' | 'warning' }>;
  }> {
    const errors: Array<{ row: number; field: string; message: string; severity: 'error' | 'warning' }> = [];

    for (let rowIndex = 0; rowIndex < data.length; rowIndex++) {
      const row = data[rowIndex];

      // Validar colunas obrigatórias
      for (const column of template.columns) {
        if (column.isRequired && (!row[column.name] || row[column.name] === '')) {
          errors.push({
            row: rowIndex + 1,
            field: column.name,
            message: `${column.displayName} é obrigatório`,
            severity: 'error'
          });
        }

        // Validar tipo de dados
        if (row[column.name] !== undefined && row[column.name] !== '') {
          const validationError = this.validateFieldType(row[column.name], column);
          if (validationError) {
            errors.push({
              row: rowIndex + 1,
              field: column.name,
              message: validationError,
              severity: 'error'
            });
          }
        }

        // Validar regras específicas
        if (column.validation && row[column.name] !== undefined) {
          const validationErrors = this.validateFieldRules(row[column.name], column.validation, column.displayName);
          validationErrors.forEach(error => {
            errors.push({
              row: rowIndex + 1,
              field: column.name,
              message: error,
              severity: 'warning'
            });
          });
        }
      }

      // Validar regras customizadas
      for (const rule of template.validations) {
        if (rule.type === 'custom') {
          const ruleError = this.validateCustomRule(row, rule);
          if (ruleError) {
            errors.push({
              row: rowIndex + 1,
              field: rule.field,
              message: ruleError,
              severity: rule.severity
            });
          }
        }
      }
    }

    return {
      isValid: errors.filter(e => e.severity === 'error').length === 0,
      errors
    };
  }

  /**
   * Valida tipo de campo
   */
  private validateFieldType(value: any, column: TemplateColumn): string | null {
    switch (column.dataType) {
      case 'number':
        if (isNaN(Number(value))) {
          return `${column.displayName} deve ser um número`;
        }
        break;
      case 'date':
        if (isNaN(Date.parse(value))) {
          return `${column.displayName} deve ser uma data válida`;
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean' && !['true', 'false', '1', '0'].includes(String(value).toLowerCase())) {
          return `${column.displayName} deve ser verdadeiro ou falso`;
        }
        break;
    }
    return null;
  }

  /**
   * Valida regras específicas do campo
   */
  private validateFieldRules(value: any, validation: ColumnValidation, fieldName: string): string[] {
    const errors: string[] = [];

    if (validation.minLength && String(value).length < validation.minLength) {
      errors.push(`${fieldName} deve ter pelo menos ${validation.minLength} caracteres`);
    }

    if (validation.maxLength && String(value).length > validation.maxLength) {
      errors.push(`${fieldName} deve ter no máximo ${validation.maxLength} caracteres`);
    }

    if (validation.pattern && !new RegExp(validation.pattern).test(String(value))) {
      errors.push(`${fieldName} não atende ao formato esperado`);
    }

    if (validation.min && Number(value) < validation.min) {
      errors.push(`${fieldName} deve ser pelo menos ${validation.min}`);
    }

    if (validation.max && Number(value) > validation.max) {
      errors.push(`${fieldName} deve ser no máximo ${validation.max}`);
    }

    if (validation.allowedValues && !validation.allowedValues.includes(value)) {
      errors.push(`${fieldName} deve ser um dos valores: ${validation.allowedValues.join(', ')}`);
    }

    return errors;
  }

  /**
   * Valida regra customizada
   */
  private validateCustomRule(row: any, rule: ValidationRule): string | null {
    try {
      // Implementação simplificada - em produção usaria um parser seguro
      if (rule.condition.formula) {
        const formula = rule.condition.formula;
        // Substituir variáveis pelos valores
        let expression = formula;
        Object.keys(row).forEach(key => {
          expression = expression.replace(new RegExp(key, 'g'), row[key] || 0);
        });
        
        // Avaliar expressão (CUIDADO: em produção usar parser seguro)
        const result = eval(expression);
        if (!result) {
          return rule.message;
        }
      }
    } catch (error) {
      logger.error('Erro ao validar regra customizada:', error);
    }
    return null;
  }

  /**
   * Gera ID único para template
   */
  private generateTemplateId(): string {
    return `template_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const templateService = new TemplateService();
export default templateService;