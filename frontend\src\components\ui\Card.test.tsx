 
import { render, screen } from '@testing-library/react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card';
import { describe, it, expect } from 'vitest';

describe('Card', () => {
  it('should render the Card component with default variant', () => {
    render(<Card>Conteúdo do Card</Card>);
    const card = screen.getByText('Conteúdo do Card').closest('div');
    expect(card).toBeInTheDocument();
    expect(card).toHaveClass('bg-design-neutral-50'); // Assuming default background
    expect(card).toHaveClass('rounded-lg');
    expect(card).toHaveClass('shadow-sm');
  });

  it('should render CardHeader, CardTitle, and CardDescription', () => {
    render(
      <Card>
        <CardHeader>
          <CardTitle>Test Title</CardTitle>
          <CardDescription>Test Description</CardDescription>
        </CardHeader>
      </Card>
    );
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
  });

  it('should render CardContent', () => {
    render(
      <Card>
        <CardContent>Main Content</CardContent>
      </Card>
    );
    expect(screen.getByText('Main Content')).toBeInTheDocument();
  });

  it('should render CardFooter', () => {
    render(
      <Card>
        <CardFooter>Card Footer</CardFooter>
      </Card>
    );
    expect(screen.getByText('Card Footer')).toBeInTheDocument();
  });

  it('should apply custom variant classes', () => {
    render(<Card variant="elevated">Elevated Card</Card>);
    const card = screen.getByText('Elevated Card').closest('div');
    expect(card).toHaveClass('shadow-md'); // Assuming elevated adds shadow-md
  });

  it('should apply custom size classes', () => {
    render(<Card size="lg">Large Card</Card>);
    const card = screen.getByText('Large Card').closest('div');
    expect(card).toHaveClass('p-8'); // Assuming lg size adds p-8
  });

  it('should apply hover effect classes when hoverEffect is true', () => {
    render(<Card hoverEffect>Hoverable Card</Card>);
    const card = screen.getByText('Hoverable Card').closest('div');
    expect(card).toHaveClass('hover:shadow-lg'); // Assuming hoverEffect adds hover:shadow-lg
  });
}); 
