# Documentação da API Magnow

Bem-vindo à documentação da API do Magnow. Nossa API RESTful permite que você integre seus sistemas e automatize processos de gestão de estoque de forma programática.

## Acesso à API e Autenticação

O acesso à API é protegido e requer autenticação via **JWT (JSON Web Tokens)**. Todas as requisições devem incluir um token de acesso no cabeçalho `Authorization`.

```
Authorization: Bearer <seu_token_jwt>
```

Alé<PERSON> disso, por ser uma plataforma multi-tenant, a maioria dos endpoints requer a identificação do tenant através do cabeçalho `X-Tenant-ID`.

```
X-Tenant-ID: <id_do_seu_tenant>
```

### Geração de Token

Para obter um token, utilize o endpoint de autenticação:

-   `POST /api/auth/login`: Autentica um usuário e retorna um `accessToken` e um `refreshToken`.

## Documentação Interativa (Swagger)

Para uma exploração detalhada e interativa de todos os endpoints, recomendamos o uso da nossa documentação gerada pelo Swagger.

Quando a aplicação está rodando localmente, você pode acessá-la em:

**[http://localhost:3000/api/docs](http://localhost:3000/api/docs)**

O Swagger UI permite que você visualize todos os endpoints, seus parâmetros, corpos de requisição e respostas esperadas, além de permitir a execução de chamadas de API diretamente do navegador.

## Endpoints Principais

Abaixo estão alguns dos endpoints mais importantes da nossa API.

### Autenticação (`/api/auth`)

-   `POST /login`: Login de usuário.
-   `POST /register`: Registro de um novo usuário e tenant.
-   `POST /refresh-token`: Obtenção de um novo `accessToken` usando um `refreshToken`.
-   `GET /ml/url`: Obter a URL de autorização do Mercado Livre para iniciar o processo de OAuth 2.0.
-   `GET /ml/callback`: Endpoint de callback para o Mercado Livre após a autorização.

### Gestão de Estoque (`/api/stock`)

-   `GET /`: Listar todos os produtos em estoque para o tenant.
-   `GET /sync`: Forçar a sincronização do estoque com o Mercado Livre.
-   `GET /calculate-target`: Calcular o estoque alvo para os produtos.
-   `POST /shipment`: Gerar uma nova planilha de remessa de entrada.

### Relatórios (`/api/reports`)

-   `GET /inventory`: Gerar um relatório completo do inventário atual.
-   `GET /performance`: Gerar um relatório de performance de vendas dos produtos.
-   `GET /gaps`: Identificar e relatar gaps de estoque (produtos que deveriam estar no Full e não estão).

## Rate Limiting

Para garantir a estabilidade da plataforma, nossa API implementa um sistema de `rate limiting`. Por padrão, são permitidas **100 requisições a cada 15 minutos** por IP. Se você exceder esse limite, receberá uma resposta com o status `429 Too Many Requests`.

## Códigos de Status

Nossa API utiliza os códigos de status HTTP padrão para indicar o sucesso ou falha de uma requisição:

-   `200 OK`: A requisição foi bem-sucedida.
-   `201 Created`: O recurso foi criado com sucesso.
-   `204 No Content`: A requisição foi bem-sucedida, mas não há conteúdo para retornar.
-   `400 Bad Request`: A requisição é inválida (ex: parâmetros faltando).
-   `401 Unauthorized`: O token de autenticação é inválido ou está faltando.
-   `403 Forbidden`: O usuário não tem permissão para acessar o recurso.
-   `404 Not Found`: O recurso solicitado não foi encontrado.
-   `429 Too Many Requests`: O limite de requisições foi excedido.
-   `500 Internal Server Error`: Ocorreu um erro inesperado no servidor.