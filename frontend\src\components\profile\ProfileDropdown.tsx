import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  UserIcon,
  Cog6ToothIcon,
  ClockIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
} from '@heroicons/react/24/outline';
import { useAuthStore } from '../../store/authStore';
import { formatLastLogin, getInitials, getAvatarColor } from '../../mocks/profileMock';
import ProfileModal from './ProfileModal';
import { FadeIn, SlideIn } from '../ui/Animations';

interface ProfileDropdownProps {
  className?: string;
}

const ProfileDropdown: React.FC<ProfileDropdownProps> = ({ className = '' }) => {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();
  
  const [isOpen, setIsOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isOpen]);

  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      setIsOpen(false);
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleEditProfile = () => {
    setIsOpen(false);
    setIsModalOpen(true);
  };

  const handleSettings = () => {
    setIsOpen(false);
    navigate('/settings');
  };

  const handleActivityHistory = () => {
    setIsOpen(false);
    setIsModalOpen(true);
  };

  if (!user) {
    return null;
  }

  const userInitials = getInitials(user.name);
  const avatarColor = getAvatarColor(user.name);

  return (
    <>
      <div className={`relative ${className}`} ref={dropdownRef}>
        {/* Trigger Button */}
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-3 p-2 rounded-lg hover:bg-accent transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          aria-expanded={isOpen}
          aria-haspopup="true"
          disabled={isLoggingOut}
        >
          {/* User Info (Desktop) */}
          <div className="hidden lg:flex items-center space-x-3">
            <div className="text-right">
              <div className="text-sm font-medium text-foreground">{user.name}</div>
              <div className="text-xs text-muted-foreground">{user.email}</div>
            </div>
            
            {/* Avatar */}
            {user.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="w-8 h-8 rounded-full object-cover border-2 border-border"
              />
            ) : (
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold ${avatarColor}`}>
                {userInitials}
              </div>
            )}
            
            <ChevronDownIcon className={`w-4 h-4 text-muted-foreground transition-transform ${isOpen ? 'rotate-180' : ''}`} />
          </div>

          {/* Avatar Only (Mobile/Tablet) */}
          <div className="lg:hidden">
            {user.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="w-8 h-8 rounded-full object-cover border-2 border-border"
              />
            ) : (
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-semibold ${avatarColor}`}>
                {userInitials}
              </div>
            )}
          </div>
        </button>

        {/* Dropdown Menu */}
        {isOpen && (
          <FadeIn>
            <div className="absolute right-0 mt-2 w-80 bg-card border border-border rounded-lg shadow-lg z-50">
              {/* User Header */}
              <div className="p-4 border-b border-border">
                <div className="flex items-center space-x-3">
                  {user.avatar ? (
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-12 h-12 rounded-full object-cover border-2 border-border"
                    />
                  ) : (
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white text-lg font-semibold ${avatarColor}`}>
                      {userInitials}
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-foreground truncate">{user.name}</h3>
                    <p className="text-sm text-muted-foreground truncate">{user.email}</p>
                    
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                        {user.role === 'admin' ? 'Admin' : 
                         user.role === 'manager' ? 'Gerente' : 
                         user.role === 'user' ? 'Usuário' : 'Visualizador'}
                      </span>
                      
                      {user.department && (
                        <span className="text-xs text-muted-foreground">
                          {user.department}
                        </span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Last Login */}
                {user.lastLoginAt && (
                  <div className="mt-3 pt-3 border-t border-border">
                    <div className="flex items-center space-x-2">
                      <ClockIcon className="w-4 h-4 text-muted-foreground" />
                      <span className="text-xs text-muted-foreground">
                        Último login: {formatLastLogin(user.lastLoginAt)}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              {/* Menu Items */}
              <div className="py-2">
                <SlideIn direction="left" delay={50}>
                  <button
                    onClick={handleEditProfile}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-accent transition-colors"
                  >
                    <UserIcon className="w-4 h-4 text-muted-foreground" />
                    <span>Editar Perfil</span>
                  </button>
                </SlideIn>

                <SlideIn direction="left" delay={100}>
                  <button
                    onClick={handleSettings}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-accent transition-colors"
                  >
                    <Cog6ToothIcon className="w-4 h-4 text-muted-foreground" />
                    <span>Configurações</span>
                  </button>
                </SlideIn>

                <SlideIn direction="left" delay={150}>
                  <button
                    onClick={handleActivityHistory}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-foreground hover:bg-accent transition-colors"
                  >
                    <ClockIcon className="w-4 h-4 text-muted-foreground" />
                    <span>Histórico de Atividades</span>
                  </button>
                </SlideIn>

                <div className="my-2 border-t border-border" />

                <SlideIn direction="left" delay={200}>
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-destructive hover:bg-destructive/10 transition-colors disabled:opacity-50"
                  >
                    <ArrowRightOnRectangleIcon className="w-4 h-4" />
                    <span>{isLoggingOut ? 'Saindo...' : 'Sair'}</span>
                  </button>
                </SlideIn>
              </div>
            </div>
          </FadeIn>
        )}
      </div>

      {/* Profile Modal */}
      <ProfileModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        defaultTab="profile"
      />
    </>
  );
};

export default ProfileDropdown;
