/**
 * Gerenciador de Templates de Planilhas
 * Siste<PERSON> Magnow - Interface Aprimorada
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select-radix';
import { Checkbox } from '../ui/checkbox';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Alert, AlertDescription } from '../ui/alert';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Copy, 
  Save, 
  X, 
  FileSpreadsheet, 
  Settings, 
  Eye,
  Star,
  StarOff,
  AlertCircle,
  CheckCircle,
  Info,
  ArrowUp,
  ArrowDown,
  GripVertical
} from 'lucide-react';

interface SpreadsheetTemplate {
  id: string;
  name: string;
  description: string;
  columns: TemplateColumn[];
  isDefault: boolean;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
  usageCount: number;
  marketplace?: string;
}

interface TemplateColumn {
  id: string;
  name: string;
  displayName: string;
  dataType: 'string' | 'number' | 'boolean' | 'date' | 'currency' | 'url';
  isRequired: boolean;
  order: number;
  validation?: ColumnValidation;
  formatting?: ColumnFormatting;
  description?: string;
}

interface ColumnValidation {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  allowedValues?: string[];
  forbiddenValues?: string[];
  min?: number;
  max?: number;
}

interface ColumnFormatting {
  prefix?: string;
  suffix?: string;
  decimalPlaces?: number;
  thousandsSeparator?: boolean;
  dateFormat?: string;
  alignment?: 'left' | 'center' | 'right';
  backgroundColor?: string;
  textColor?: string;
}

interface TemplateManagerProps {
  templates: SpreadsheetTemplate[];
  onCreateTemplate: (template: Omit<SpreadsheetTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>) => Promise<void>;
  onUpdateTemplate: (id: string, template: Partial<SpreadsheetTemplate>) => Promise<void>;
  onDeleteTemplate: (id: string) => Promise<void>;
  onDuplicateTemplate: (id: string) => Promise<void>;
  onSetDefault: (id: string) => Promise<void>;
  isLoading?: boolean;
}

const DATA_TYPES = [
  { value: 'string', label: 'Texto', icon: '📝' },
  { value: 'number', label: 'Número', icon: '🔢' },
  { value: 'currency', label: 'Moeda', icon: '💰' },
  { value: 'date', label: 'Data', icon: '📅' },
  { value: 'boolean', label: 'Sim/Não', icon: '✅' },
  { value: 'url', label: 'URL', icon: '🔗' }
];

const MARKETPLACE_TEMPLATES = [
  { value: 'mercadolivre', label: 'Mercado Livre' },
  { value: 'amazon', label: 'Amazon' },
  { value: 'shopee', label: 'Shopee' },
  { value: 'magalu', label: 'Magazine Luiza' },
  { value: 'custom', label: 'Personalizado' }
];

const TemplateManager: React.FC<TemplateManagerProps> = ({
  templates,
  onCreateTemplate,
  onUpdateTemplate,
  onDeleteTemplate,
  onDuplicateTemplate,
  onSetDefault,
  isLoading = false
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<SpreadsheetTemplate | null>(null);
  const [editingTemplate, setEditingTemplate] = useState<SpreadsheetTemplate | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterMarketplace, setFilterMarketplace] = useState<string>('all');
  const [draggedColumn, setDraggedColumn] = useState<string | null>(null);

  // Novo template em criação
  const [newTemplate, setNewTemplate] = useState<Partial<SpreadsheetTemplate>>({
    name: '',
    description: '',
    columns: [],
    isDefault: false,
    isSystem: false,
    marketplace: 'custom'
  });

  // Nova coluna em criação
  const [newColumn, setNewColumn] = useState<Partial<TemplateColumn>>({
    name: '',
    displayName: '',
    dataType: 'string',
    isRequired: false,
    order: 0
  });

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesMarketplace = filterMarketplace === 'all' || template.marketplace === filterMarketplace;
    return matchesSearch && matchesMarketplace;
  });

  const resetNewTemplate = () => {
    setNewTemplate({
      name: '',
      description: '',
      columns: [],
      isDefault: false,
      isSystem: false,
      marketplace: 'custom'
    });
  };

  const resetNewColumn = () => {
    setNewColumn({
      name: '',
      displayName: '',
      dataType: 'string',
      isRequired: false,
      order: (newTemplate.columns?.length || 0)
    });
  };

  const handleCreateTemplate = async () => {
    if (!newTemplate.name || !newTemplate.columns?.length) return;
    
    try {
      await onCreateTemplate(newTemplate as Omit<SpreadsheetTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>);
      setIsCreateDialogOpen(false);
      resetNewTemplate();
    } catch (error) {
      console.error('Erro ao criar template:', error);
    }
  };

  const handleUpdateTemplate = async () => {
    if (!editingTemplate) return;
    
    try {
      await onUpdateTemplate(editingTemplate.id, editingTemplate);
      setIsEditDialogOpen(false);
      setEditingTemplate(null);
    } catch (error) {
      console.error('Erro ao atualizar template:', error);
    }
  };

  const handleAddColumn = () => {
    if (!newColumn.name || !newColumn.displayName) return;
    
    const column: TemplateColumn = {
      id: `col_${Date.now()}`,
      name: newColumn.name!,
      displayName: newColumn.displayName!,
      dataType: newColumn.dataType!,
      isRequired: newColumn.isRequired!,
      order: newTemplate.columns?.length || 0,
      validation: newColumn.validation,
      formatting: newColumn.formatting,
      description: newColumn.description
    };
    
    setNewTemplate(prev => ({
      ...prev,
      columns: [...(prev.columns || []), column]
    }));
    
    resetNewColumn();
  };

  const handleRemoveColumn = (columnId: string) => {
    setNewTemplate(prev => ({
      ...prev,
      columns: prev.columns?.filter(col => col.id !== columnId) || []
    }));
  };

  const handleMoveColumn = (columnId: string, direction: 'up' | 'down') => {
    if (!newTemplate.columns) return;
    
    const columns = [...newTemplate.columns];
    const index = columns.findIndex(col => col.id === columnId);
    
    if (direction === 'up' && index > 0) {
      [columns[index], columns[index - 1]] = [columns[index - 1], columns[index]];
    } else if (direction === 'down' && index < columns.length - 1) {
      [columns[index], columns[index + 1]] = [columns[index + 1], columns[index]];
    }
    
    // Atualizar ordem
    columns.forEach((col, idx) => {
      col.order = idx;
    });
    
    setNewTemplate(prev => ({ ...prev, columns }));
  };

  const renderTemplateCard = (template: SpreadsheetTemplate) => (
    <Card 
      key={template.id} 
      className={`cursor-pointer transition-all hover:shadow-md ${
        selectedTemplate?.id === template.id ? 'ring-2 ring-blue-500' : ''
      }`}
      onClick={() => setSelectedTemplate(template)}
    >
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="text-base">{template.name}</CardTitle>
            {template.isDefault && (
              <Star className="w-4 h-4 text-yellow-500 fill-current" />
            )}
            {template.isSystem && (
              <Badge variant="secondary" className="text-xs">Sistema</Badge>
            )}
          </div>
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setEditingTemplate(template);
                setIsEditDialogOpen(true);
              }}
              disabled={template.isSystem}
            >
              <Edit3 className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onDuplicateTemplate(template.id);
              }}
            >
              <Copy className="w-4 h-4" />
            </Button>
            {!template.isSystem && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onDeleteTemplate(template.id);
                }}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>
        <CardDescription className="text-sm">
          {template.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-600">{template.columns.length} colunas</span>
            <span className="text-gray-600">{template.usageCount} usos</span>
          </div>
          {template.marketplace && (
            <Badge variant="outline" className="text-xs">
              {MARKETPLACE_TEMPLATES.find(m => m.value === template.marketplace)?.label}
            </Badge>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderColumnForm = () => (
    <div className="space-y-4 border rounded-lg p-4 bg-gray-50">
      <h4 className="font-medium">Adicionar Nova Coluna</h4>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="columnName">Nome da Coluna</Label>
          <Input
            id="columnName"
            value={newColumn.name || ''}
            onChange={(e) => setNewColumn(prev => ({ ...prev, name: e.target.value }))}
            placeholder="ex: sku, title, price"
          />
        </div>
        <div>
          <Label htmlFor="columnDisplayName">Nome de Exibição</Label>
          <Input
            id="columnDisplayName"
            value={newColumn.displayName || ''}
            onChange={(e) => setNewColumn(prev => ({ ...prev, displayName: e.target.value }))}
            placeholder="ex: SKU, Título, Preço"
          />
        </div>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="columnType">Tipo de Dados</Label>
          <Select 
            value={newColumn.dataType} 
            onValueChange={(value) => setNewColumn(prev => ({ ...prev, dataType: value as any }))}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {DATA_TYPES.map(type => (
                <SelectItem key={type.value} value={type.value}>
                  <span className="flex items-center">
                    <span className="mr-2">{type.icon}</span>
                    {type.label}
                  </span>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center space-x-2 pt-6">
          <Checkbox
            id="columnRequired"
            name="columnRequired"
            checked={newColumn.isRequired}
            onCheckedChange={(checked) => setNewColumn(prev => ({ ...prev, isRequired: !!checked }))}
          />
          <Label htmlFor="columnRequired">Campo obrigatório</Label>
        </div>
      </div>
      
      <div>
        <Label htmlFor="columnDescription">Descrição (opcional)</Label>
        <Textarea
          id="columnDescription"
          value={newColumn.description || ''}
          onChange={(e) => setNewColumn(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Descrição da coluna..."
          rows={2}
        />
      </div>
      
      <Button onClick={handleAddColumn} disabled={!newColumn.name || !newColumn.displayName}>
        <Plus className="w-4 h-4 mr-2" />
        Adicionar Coluna
      </Button>
    </div>
  );

  const renderColumnsList = () => (
    <div className="space-y-2">
      <h4 className="font-medium">Colunas do Template ({newTemplate.columns?.length || 0})</h4>
      {newTemplate.columns?.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <FileSpreadsheet className="w-12 h-12 mx-auto mb-2 opacity-50" />
          <p>Nenhuma coluna adicionada ainda</p>
        </div>
      ) : (
        <ScrollArea className="h-64 border rounded-md p-3">
          <div className="space-y-2">
            {newTemplate.columns?.map((column, index) => (
              <div key={column.id} className="flex items-center justify-between p-3 bg-white border rounded-md">
                <div className="flex items-center space-x-3">
                  <GripVertical className="w-4 h-4 text-gray-400" />
                  <div>
                    <div className="font-medium">{column.displayName}</div>
                    <div className="text-sm text-gray-600">
                      {column.name} • {DATA_TYPES.find(t => t.value === column.dataType)?.label}
                      {column.isRequired && <Badge variant="destructive" className="ml-2 text-xs">Obrigatório</Badge>}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleMoveColumn(column.id, 'up')}
                    disabled={index === 0}
                  >
                    <ArrowUp className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleMoveColumn(column.id, 'down')}
                    disabled={index === (newTemplate.columns?.length || 0) - 1}
                  >
                    <ArrowDown className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveColumn(column.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Gerenciar Templates</h2>
          <p className="text-gray-600">Crie e gerencie templates personalizados para suas planilhas</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => resetNewTemplate()}>
              <Plus className="w-4 h-4 mr-2" />
              Novo Template
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Criar Novo Template</DialogTitle>
              <DialogDescription>
                Configure as colunas e propriedades do seu template personalizado
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-6">
              {/* Informações Básicas */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="templateName">Nome do Template</Label>
                  <Input
                    id="templateName"
                    value={newTemplate.name || ''}
                    onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="ex: Mercado Livre - Eletrônicos"
                  />
                </div>
                <div>
                  <Label htmlFor="templateMarketplace">Marketplace</Label>
                  <Select 
                    value={newTemplate.marketplace} 
                    onValueChange={(value) => setNewTemplate(prev => ({ ...prev, marketplace: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {MARKETPLACE_TEMPLATES.map(marketplace => (
                        <SelectItem key={marketplace.value} value={marketplace.value}>
                          {marketplace.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <Label htmlFor="templateDescription">Descrição</Label>
                <Textarea
                  id="templateDescription"
                  value={newTemplate.description || ''}
                  onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Descreva o propósito e uso deste template..."
                  rows={3}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="templateDefault"
                  name="templateDefault"
                  checked={newTemplate.isDefault}
                  onCheckedChange={(checked) => setNewTemplate(prev => ({ ...prev, isDefault: !!checked }))}
                />
                <Label htmlFor="templateDefault">Definir como template padrão</Label>
              </div>
              
              <Separator />
              
              {/* Formulário de Colunas */}
              {renderColumnForm()}
              
              <Separator />
              
              {/* Lista de Colunas */}
              {renderColumnsList()}
              
              {/* Botões de Ação */}
              <div className="flex items-center justify-end space-x-3 pt-4 border-t">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button 
                  onClick={handleCreateTemplate}
                  disabled={!newTemplate.name || !newTemplate.columns?.length}
                >
                  <Save className="w-4 h-4 mr-2" />
                  Criar Template
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Filtros */}
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <Input
            placeholder="Buscar templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Select value={filterMarketplace} onValueChange={setFilterMarketplace}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos os Marketplaces</SelectItem>
            {MARKETPLACE_TEMPLATES.map(marketplace => (
              <SelectItem key={marketplace.value} value={marketplace.value}>
                {marketplace.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Lista de Templates */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.map(renderTemplateCard)}
      </div>

      {filteredTemplates.length === 0 && (
        <div className="text-center py-12">
          <FileSpreadsheet className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhum template encontrado</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || filterMarketplace !== 'all' 
              ? 'Tente ajustar os filtros de busca'
              : 'Crie seu primeiro template personalizado'
            }
          </p>
          {!searchTerm && filterMarketplace === 'all' && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Criar Primeiro Template
            </Button>
          )}
        </div>
      )}

      {/* Detalhes do Template Selecionado */}
      {selectedTemplate && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  {selectedTemplate.name}
                  {selectedTemplate.isDefault && (
                    <Star className="w-5 h-5 text-yellow-500 fill-current ml-2" />
                  )}
                </CardTitle>
                <CardDescription>{selectedTemplate.description}</CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                {!selectedTemplate.isDefault && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onSetDefault(selectedTemplate.id)}
                  >
                    <Star className="w-4 h-4 mr-2" />
                    Definir como Padrão
                  </Button>
                )}
                <Button variant="outline" size="sm" onClick={() => setSelectedTemplate(null)}>
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Colunas:</span>
                  <div className="font-semibold">{selectedTemplate.columns.length}</div>
                </div>
                <div>
                  <span className="text-gray-600">Usos:</span>
                  <div className="font-semibold">{selectedTemplate.usageCount}</div>
                </div>
                <div>
                  <span className="text-gray-600">Criado:</span>
                  <div className="font-semibold">{new Date(selectedTemplate.createdAt).toLocaleDateString()}</div>
                </div>
                <div>
                  <span className="text-gray-600">Atualizado:</span>
                  <div className="font-semibold">{new Date(selectedTemplate.updatedAt).toLocaleDateString()}</div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h4 className="font-medium mb-3">Colunas Configuradas</h4>
                <div className="grid gap-2">
                  {selectedTemplate.columns
                    .sort((a, b) => a.order - b.order)
                    .map(column => (
                      <div key={column.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                        <div className="flex items-center space-x-3">
                          <Badge variant="outline" className="text-xs">
                            {DATA_TYPES.find(t => t.value === column.dataType)?.icon}
                          </Badge>
                          <div>
                            <div className="font-medium">{column.displayName}</div>
                            <div className="text-sm text-gray-600">{column.name}</div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {column.isRequired && (
                            <Badge variant="destructive" className="text-xs">Obrigatório</Badge>
                          )}
                          <Badge variant="secondary" className="text-xs">
                            {DATA_TYPES.find(t => t.value === column.dataType)?.label}
                          </Badge>
                        </div>
                      </div>
                    ))
                  }
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TemplateManager;