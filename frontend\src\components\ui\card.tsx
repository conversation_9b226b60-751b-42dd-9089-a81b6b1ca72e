import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const cardVariants = cva(
  'rounded-lg border transition-all duration-200 ease-in-out',
  {
    variants: {
      variant: {
        default: 'bg-surface border-border shadow-sm hover:shadow-md',
        elevated: 'bg-surface border-border shadow-md hover:shadow-lg',
        filled: 'bg-surface-secondary border-border-secondary shadow-sm hover:shadow-md',
        outline: 'bg-transparent border-border hover:border-border-hover shadow-none hover:shadow-sm',
        ghost: 'bg-transparent border-transparent shadow-none hover:bg-surface-secondary hover:shadow-sm',
        primary: 'bg-primary-50 border-primary-200 shadow-sm hover:shadow-md',
        success: 'bg-success-50 border-success-200 shadow-sm hover:shadow-md',
        warning: 'bg-warning-50 border-warning-200 shadow-sm hover:shadow-md',
        danger: 'bg-danger-50 border-danger-200 shadow-sm hover:shadow-md',
        info: 'bg-info-50 border-info-200 shadow-sm hover:shadow-md',
        interactive: 'bg-surface border-border shadow-sm hover:shadow-md hover:border-border-hover cursor-pointer focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
      },
      size: {
        sm: 'text-sm',
        default: 'text-base',
        lg: 'text-lg',
      },
      padding: {
        none: 'p-0',
        sm: 'p-3',
        default: 'p-4',
        lg: 'p-6',
        xl: 'p-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      padding: 'default',
    },
  }
);

const cardHeaderVariants = cva(
  'flex flex-col space-y-1.5',
  {
    variants: {
      padding: {
        none: 'p-0',
        sm: 'p-3',
        default: 'p-4 pb-3',
        lg: 'p-6 pb-4',
        xl: 'p-8 pb-6',
      },
    },
    defaultVariants: {
      padding: 'default',
    },
  }
);

const cardContentVariants = cva(
  '',
  {
    variants: {
      padding: {
        none: 'p-0',
        sm: 'p-3 pt-0',
        default: 'p-4 pt-0',
        lg: 'p-6 pt-0',
        xl: 'p-8 pt-0',
      },
    },
    defaultVariants: {
      padding: 'default',
    },
  }
);

const cardFooterVariants = cva(
  'flex items-center border-t border-border',
  {
    variants: {
      padding: {
        none: 'p-0',
        sm: 'p-3',
        default: 'p-4',
        lg: 'p-6',
        xl: 'p-8',
      },
      alignment: {
        start: 'justify-start',
        center: 'justify-center',
        end: 'justify-end',
        between: 'justify-between',
        around: 'justify-around',
      },
    },
    defaultVariants: {
      padding: 'default',
      alignment: 'end',
    },
  }
);

export interface CardProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  asChild?: boolean;
}

const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({ className, variant, size, padding, asChild = false, ...props }, ref) => {
    const Comp = asChild ? React.Fragment : 'div';
    
    if (asChild) {
      return <>{props.children}</>;
    }

    return (
      <Comp
        className={cn(cardVariants({ variant, size, padding, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Card.displayName = 'Card';

export interface CardHeaderProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardHeaderVariants> {}

const CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(
  ({ className, padding, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardHeaderVariants({ padding, className }))}
      {...props}
    />
  )
);
CardHeader.displayName = 'CardHeader';

export interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

const CardTitle = React.forwardRef<HTMLHeadingElement, CardTitleProps>(
  ({ className, as: Comp = 'h3', ...props }, ref) => (
    <Comp
      ref={ref}
      className={cn(
        'text-xl font-semibold leading-none tracking-tight text-foreground',
        className
      )}
      {...props}
    />
  )
);
CardTitle.displayName = 'CardTitle';

export interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

const CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-sm text-muted-foreground leading-relaxed', className)}
      {...props}
    />
  )
);
CardDescription.displayName = 'CardDescription';

export interface CardContentProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardContentVariants> {}

const CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(
  ({ className, padding, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardContentVariants({ padding, className }))}
      {...props}
    />
  )
);
CardContent.displayName = 'CardContent';

export interface CardFooterProps 
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardFooterVariants> {}

const CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(
  ({ className, padding, alignment, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(cardFooterVariants({ padding, alignment, className }))}
      {...props}
    />
  )
);
CardFooter.displayName = 'CardFooter';

export { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter,
  cardVariants,
  cardHeaderVariants,
  cardContentVariants,
  cardFooterVariants,
}; 
