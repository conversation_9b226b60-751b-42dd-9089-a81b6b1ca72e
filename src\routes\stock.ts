/**
 * Rotas de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Router } from 'express';
import { StockController } from '../controllers/stockController';
import { requireAuth } from '../middleware/auth';
import { validateTenant } from '../middleware/validateTenant';
import { rateLimiter } from '../middleware/rateLimiter';
import { 
  userActivityLogger, 
  stockOperationLogger, 
  sensitiveOperationLogger 
} from '@/middleware/logger';
import CacheMiddleware from '../middleware/cacheMiddleware';

const router: Router = Router();
const stockController = new StockController();

// Instanciar middlewares de cache
const stockCache = CacheMiddleware.forStock();

// Aplicar middlewares base em todas as rotas
router.use(requireAuth);
router.use(validateTenant);
// router.use(CacheMiddleware.metrics()); // Métricas de cache - temporariamente desabilitado

/**
 * ROTAS DE ESTOQUE COM AUDITORIA
 * Sistema Magnow - Logs completos de todas as operações
 */

/**
 * @swagger
 * /api/stock/calculations:
 *   get:
 *     summary: Listar cálculos de estoque
 *     description: Retorna a lista de cálculos de estoque realizados
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Página da paginação
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 50
 *         description: Itens por página
 *     responses:
 *       200:
 *         description: Lista de cálculos obtida com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 calculations:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/StockItem'
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page: 
 *                       type: integer
 *                     limit:
 *                       type: integer
 *                     total:
 *                       type: integer
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 */
router.get('/calculations',
  rateLimiter({ windowMs: 60 * 1000, max: 60 }), // 60 requests por minuto
  stockCache.read, // Cache de leitura
  userActivityLogger('VIEW_STOCK_CALCULATIONS', 'stock'),
  stockController.getCalculations
);

/**
 * @swagger
 * /api/stock/calculate:
 *   post:
 *     summary: Calcular estoque em lote
 *     description: Executa cálculo de estoque para múltiplos produtos
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               productIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: IDs dos produtos para calcular (opcional - se vazio, calcula todos)
 *               forceRecalculation:
 *                 type: boolean
 *                 default: false
 *                 description: Forçar recálculo mesmo se recente
 *     responses:
 *       200:
 *         description: Cálculo executado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 calculatedProducts:
 *                   type: integer
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 */
router.post('/calculate',
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests por minuto (operação pesada)
  stockCache.invalidate, // Invalidar cache após cálculo
  stockOperationLogger('BULK_UPDATE'),
  sensitiveOperationLogger('STOCK_CALCULATION', 'warn'),
  stockController.calculateStock
);

/**
 * @swagger
 * /api/stock/calculate/{mlItemId}:
 *   post:
 *     summary: Calcular estoque de produto específico
 *     description: Executa cálculo de estoque para um produto específico do Mercado Livre
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: mlItemId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do item no Mercado Livre
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *     responses:
 *       200:
 *         description: Cálculo executado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 productId:
 *                   type: string
 *                 stockCalculation:
 *                   $ref: '#/components/schemas/StockItem'
 *       404:
 *         description: Produto não encontrado
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.post('/calculate/:mlItemId',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  stockCache.invalidate, // Invalidar cache após cálculo
  userActivityLogger('CALCULATE_SINGLE_PRODUCT', 'stock'),
  stockController.calculateSingleProduct
);

/**
 * @swagger
 * /api/stock/gap/report:
 *   get:
 *     summary: Relatório de gap de estoque
 *     description: Retorna relatório detalhado dos gaps de estoque
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *       - in: query
 *         name: severity
 *         schema:
 *           type: string
 *           enum: [low, medium, high, critical]
 *         description: Filtrar por severidade do gap
 *     responses:
 *       200:
 *         description: Relatório gerado com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 summary:
 *                   type: object
 *                   properties:
 *                     totalProducts:
 *                       type: integer
 *                     productsWithGap:
 *                       type: integer
 *                     criticalGaps:
 *                       type: integer
 *                 gaps:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       productId:
 *                         type: string
 *                       gapValue:
 *                         type: integer
 *                       severity:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.get('/gap/report',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  stockCache.read, // Cache de leitura
  userActivityLogger('VIEW_GAP_REPORT', 'stock'),
  stockController.getGapReport
);

/**
 * @swagger
 * /api/stock/gap/critical:
 *   get:
 *     summary: Produtos com gap crítico
 *     description: Retorna a lista de produtos com gap crítico
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *     responses:
 *       200:
 *         description: Lista de produtos com gap crítico obtida com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   name:
 *                     type: string
 *                   gapValue:
 *                     type: integer
 *                   severity:
 *                     type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.get('/gap/critical',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  stockCache.read, // Cache de leitura
  userActivityLogger('VIEW_CRITICAL_GAP', 'stock'),
  stockController.getCriticalGapProducts
);

/**
 * @swagger
 * /api/stock/gap/analyze:
 *   post:
 *     summary: Análise de gap de produtos
 *     description: Executa análise de gap de produtos
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               productIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: IDs dos produtos para analisar
 *     responses:
 *       200:
 *         description: Análise executada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 analysisResults:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       productId:
 *                         type: string
 *                       gapValue:
 *                         type: integer
 *                       severity:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.post('/gap/analyze',
  rateLimiter({ windowMs: 60 * 1000, max: 15 }), // 15 requests por minuto
  stockCache.read, // Cache de leitura (análise não modifica dados)
  userActivityLogger('ANALYZE_PRODUCTS_GAP', 'stock'),
  stockController.analyzeProductsGap
);

/**
 * @swagger
 * /api/stock/alerts:
 *   get:
 *     summary: Listar alertas de estoque
 *     description: Retorna a lista de alertas de estoque ativos
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, resolved, dismissed]
 *         description: Filtrar por status do alerta
 *     responses:
 *       200:
 *         description: Lista de alertas obtida com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                   type:
 *                     type: string
 *                   message:
 *                     type: string
 *                   severity:
 *                     type: string
 *                   productId:
 *                     type: string
 *                   createdAt:
 *                     type: string
 *                     format: date-time
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *   post:
 *     summary: Criar alerta personalizado
 *     description: Cria um novo alerta de estoque personalizado
 *     tags: [Estoque]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: header
 *         name: X-Tenant-ID
 *         required: true
 *         schema:
 *           type: string
 *         description: ID do tenant
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - type
 *               - message
 *               - productId
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [LOW_STOCK, OUT_OF_STOCK, GAP_CRITICAL, CUSTOM]
 *               message:
 *                 type: string
 *                 description: Mensagem do alerta
 *               productId:
 *                 type: string
 *                 description: ID do produto relacionado
 *               threshold:
 *                 type: integer
 *                 description: Limite que disparou o alerta
 *     responses:
 *       201:
 *         description: Alerta criado com sucesso
 *       400:
 *         description: Dados inválidos
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 */
router.get('/alerts',
  rateLimiter({ windowMs: 60 * 1000, max: 60 }), // 60 requests por minuto
  stockCache.read, // Cache de leitura
  userActivityLogger('VIEW_STOCK_ALERTS', 'stock'),
  stockController.getAlerts
);

router.post('/alerts',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  stockCache.invalidate, // Invalidar cache após criação
  userActivityLogger('CREATE_STOCK_ALERT', 'stock'),
  stockController.createAlert
);

// PUT /api/stock/alerts/:alertId/resolve - Resolve alerta
router.put('/alerts/:alertId/resolve',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  stockCache.invalidate, // Invalidar cache após resolução
  userActivityLogger('RESOLVE_STOCK_ALERT', 'stock'),
  stockController.resolveAlert
);

// POST /api/stock/alerts/process - Processa alertas manualmente
router.post('/alerts/process',
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests por minuto
  stockCache.invalidate, // Invalidar cache após processamento
  userActivityLogger('PROCESS_STOCK_ALERTS', 'stock'),
  stockController.processAlerts
);

// GET /api/stock/config - Busca configuração de estoque
router.get('/config',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  stockCache.read, // Cache de leitura
  userActivityLogger('VIEW_STOCK_CONFIG', 'stock'),
  stockController.getStockConfig
);

// PUT /api/stock/config - Atualiza configuração de estoque
router.put('/config',
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests por minuto
  stockCache.invalidate, // Invalidar cache após atualização
  sensitiveOperationLogger('UPDATE_STOCK_CONFIG', 'warn'),
  stockController.updateStockConfig
);

// GET /api/stock/metrics - Busca métricas do sistema
router.get('/metrics',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  stockCache.read, // Cache de leitura
  userActivityLogger('VIEW_STOCK_METRICS', 'stock'),
  stockController.getStockMetrics
);

// POST /api/stock/job/setup - Configura job de recálculo automático
router.post('/job/setup',
  rateLimiter({ windowMs: 60 * 1000, max: 5 }), // 5 requests por minuto
  stockCache.invalidate, // Invalidar cache após configuração
  sensitiveOperationLogger('SETUP_AUTOMATIC_JOB', 'warn'),
  stockController.setupAutomaticJob
);

// POST /api/stock/job/execute - Executa job manualmente
router.post('/job/execute',
  rateLimiter({ windowMs: 60 * 1000, max: 3 }), // 3 requests por minuto (operação muito pesada)
  stockCache.invalidate, // Invalidar cache após execução
  sensitiveOperationLogger('EXECUTE_MANUAL_JOB', 'error'),
  stockController.executeManualJob
);

export default router;