import { User, Tenant, UserRole } from '@prisma/client';

// Tipos para autenticação
export interface LoginRequest {
  email: string;
  password: string;
  tenantDomain?: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  tenantName: string;
  tenantDomain: string;
}

export interface LoginResponse {
  user: UserSafe;
  tenant: TenantSafe;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface JwtPayload {
  userId: string;
  tenantId: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

export interface UserSafe {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt: Date | null;
  emailVerified: boolean;
  tenantId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TenantSafe {
  id: string;
  name: string;
  domain: string;
  subdomain: string | null;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthenticatedRequest extends Request {
  user?: UserSafe;
  tenant?: TenantSafe;
}

// Tipos para middleware de autenticação
export interface AuthMiddlewareOptions {
  required?: boolean;
  roles?: UserRole[];
}

// Tipos para criptografia
export interface EncryptionResult {
  encrypted: string;
  iv: string;
}

export interface DecryptionParams {
  encrypted: string;
  iv: string;
}

// Tipos para validação
export interface PasswordValidation {
  isValid: boolean;
  errors: string[];
}

export interface EmailValidation {
  isValid: boolean;
  error?: string;
}

// Tipos para auditoria
export interface AuditLogData {
  action: string;
  entity: string;
  entityId?: string;
  oldValues?: any;
  newValues?: any;
  ipAddress?: string;
  userAgent?: string;
  userId?: string;
  tenantId: string;
}

export type UserWithTenant = User & {
  tenant: Tenant;
};

export type TenantWithUsers = Tenant & {
  users: User[];
}; 