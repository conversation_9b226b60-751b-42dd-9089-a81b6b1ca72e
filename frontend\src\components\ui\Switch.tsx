import React, { InputHTMLAttributes } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { useFormContext } from 'react-hook-form';

const switchThumbVariants = cva(
  'pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0',
  {
    variants: {
      size: {
        sm: 'h-3 w-3 data-[state=checked]:translate-x-3',
        default: 'h-4 w-4 data-[state=checked]:translate-x-4',
        lg: 'h-5 w-5 data-[state=checked]:translate-x-5',
      },
    },
    defaultVariants: {
      size: 'default',
    },
  }
);

const switchRootVariants = cva(
  'peer inline-flex h-6 w-10 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input',
  {
    variants: {
      state: {
        default: 'data-[state=checked]:bg-primary data-[state=unchecked]:bg-input',
        error: 'data-[state=checked]:bg-danger-500 data-[state=unchecked]:bg-danger-200',
        success: 'data-[state=checked]:bg-success-500 data-[state=unchecked]:bg-success-200',
        warning: 'data-[state=checked]:bg-warning-500 data-[state=unchecked]:bg-warning-200',
      },
      size: {
        sm: 'h-5 w-8',
        default: 'h-6 w-10',
        lg: 'h-7 w-12',
      },
    },
    defaultVariants: {
      state: 'default',
      size: 'default',
    },
  }
);

export interface SwitchProps
  extends Omit<InputHTMLAttributes<HTMLInputElement>, 'size'>,
    VariantProps<typeof switchRootVariants> {
  name: string;
  label?: string;
  description?: string;
  // For React Hook Form integration
  control?: any; // Use `control` from useForm() in RHF
  register?: any; // Use `register` from useForm() in RHF
  rules?: Record<string, any>;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
}

const Switch = React.forwardRef<HTMLInputElement, SwitchProps>(
  (
    {
      className,
      id,
      name,
      label,
      description,
      state: propState,
      size,
      disabled,
      checked: controlledChecked,
      onCheckedChange,
      control, // for RHF
      register: registerProp, // for RHF
      rules,
      ...props
    },
    ref
  ) => {
    // Try to get form context, but make it optional
    let register, errors, setValue, getValues;
    try {
      const formContext = useFormContext();
      register = formContext?.register;
      errors = formContext?.formState?.errors || {};
      setValue = formContext?.setValue;
      getValues = formContext?.getValues;
    } catch {
      // No form context available, use props only
      register = null;
      errors = {};
      setValue = null;
      getValues = null;
    }

    const [localChecked, setLocalChecked] = React.useState(controlledChecked || false);

    const uniqueId = id || `switch-${name}`;
    const validationError = errors[name]?.message as string | undefined;
    const hasError = !!validationError;
    const effectiveState = hasError ? 'error' : propState;

    const isControlled = controlledChecked !== undefined;
    const currentChecked = isControlled ? controlledChecked : localChecked;

    // Register with React Hook Form (if available)
    React.useEffect(() => {
      if (registerProp) {
        registerProp(name, rules);
      } else if (register) {
        register(name, rules);
      }
      // Set initial value for RHF if not controlled by RHF already
      if (!isControlled && setValue && getValues && !getValues(name)) {
        setValue(name, localChecked);
      }
    }, [name, register, registerProp, rules, isControlled, localChecked, getValues, setValue]);

    const handleToggle = React.useCallback(() => {
      const newChecked = !currentChecked;
      if (!isControlled) {
        setLocalChecked(newChecked);
      }
      if (onCheckedChange) {
        onCheckedChange(newChecked);
      }
      // Update RHF value
      if (setValue) {
        setValue(name, newChecked, { shouldValidate: true });
      }
    }, [currentChecked, isControlled, onCheckedChange, name, setValue]);

    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <button
          type="button"
          role="switch"
          aria-checked={currentChecked}
          aria-labelledby={label ? uniqueId + '-label' : undefined}
          aria-describedby={description ? uniqueId + '-description' : undefined}
          data-state={currentChecked ? 'checked' : 'unchecked'}
          disabled={disabled}
          onClick={handleToggle}
          className={cn(
            switchRootVariants({ state: effectiveState, size }),
            'flex-shrink-0' // Ensure button doesn't grow
          )}
        >
          <span
            data-state={currentChecked ? 'checked' : 'unchecked'}
            className={cn(switchThumbVariants({ size }))}
          />
          <input
            type="checkbox"
            id={uniqueId}
            name={name}
            checked={currentChecked}
            onChange={() => {}} // Handle change via button for accessibility
            className="sr-only" // Visually hidden input for form submission
            disabled={disabled}
            ref={ref} // Pass ref to hidden input
            {...props}
          />
        </button>
        <div className="grid gap-1.5 leading-none">
          {label && (
            <label
              htmlFor={uniqueId}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {label}
            </label>
          )}
          {description && (
            <p id={uniqueId + '-description'} className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
          {hasError && (
            <p className="text-sm text-danger-600" role="alert">
              {validationError}
            </p>
          )}
        </div>
      </div>
    );
  }
);
Switch.displayName = 'Switch';

export { Switch }; 
