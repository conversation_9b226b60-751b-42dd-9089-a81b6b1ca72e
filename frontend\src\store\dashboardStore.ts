import { create } from 'zustand';
import type {
  DashboardStats,
  StockCalculation,
  StockAlert,
  Product
} from '../types/api';
import apiService from '../services/api';
import { useNotificationStore } from './notificationStore';
// MOCK DATA IMPORT
import { mockMLProducts } from '../mocks/mlProductsMock';

// MOCK DATA FOR DEVELOPMENT - Aligned with DashboardStats interface
const mockDashboardStats: DashboardStats = {
  totalProducts: 126,
  activeProducts: 118,
  gapProducts: 23,
  criticalProducts: 8,
  totalSales: 1247,
  totalRevenue: 156420.50,
  avgStock: 45.8,
  alertsCount: 15,
  trendsData: {
    sales: [
      { date: '2024-01-11', sales: 8, revenue: 890.50, products: 3 },
      { date: '2024-01-12', sales: 12, revenue: 1245.80, products: 5 },
      { date: '2024-01-13', sales: 6, revenue: 678.90, products: 2 },
      { date: '2024-01-14', sales: 15, revenue: 1567.20, products: 7 },
      { date: '2024-01-15', sales: 9, revenue: 945.60, products: 4 },
      { date: '2024-01-16', sales: 11, revenue: 1123.40, products: 5 },
      { date: '2024-01-17', sales: 13, revenue: 1389.70, products: 6 },
      { date: '2024-01-18', sales: 15, revenue: 1579.40, products: 8 },
    ],
    stock: [
      { date: '2024-01-11', totalStock: 5420, totalProducts: 126, gapProducts: 25, criticalProducts: 10 },
      { date: '2024-01-12', totalStock: 5380, totalProducts: 126, gapProducts: 24, criticalProducts: 9 },
      { date: '2024-01-13', totalStock: 5340, totalProducts: 126, gapProducts: 23, criticalProducts: 8 },
      { date: '2024-01-14', totalStock: 5290, totalProducts: 126, gapProducts: 22, criticalProducts: 8 },
      { date: '2024-01-15', totalStock: 5250, totalProducts: 126, gapProducts: 23, criticalProducts: 8 },
      { date: '2024-01-16', totalStock: 5210, totalProducts: 126, gapProducts: 23, criticalProducts: 8 },
      { date: '2024-01-17', totalStock: 5170, totalProducts: 126, gapProducts: 23, criticalProducts: 8 },
      { date: '2024-01-18', totalStock: 5130, totalProducts: 126, gapProducts: 23, criticalProducts: 8 },
    ],
    categories: [
      { category: 'Eletrônicos', products: 45, sales: 234, revenue: 67500.00, avgStock: 12.5, gapProducts: 8 },
      { category: 'Casa e Jardim', products: 32, sales: 156, revenue: 34200.00, avgStock: 18.2, gapProducts: 6 },
      { category: 'Moda', products: 28, sales: 89, revenue: 28900.00, avgStock: 22.1, gapProducts: 5 },
      { category: 'Esportes', products: 21, sales: 67, revenue: 25820.50, avgStock: 15.8, gapProducts: 4 },
    ]
  }
};

interface DashboardState {
  // Estatísticas gerais
  stats: DashboardStats | null;
  statsLoading: boolean;
  statsError: string | null;

  // Produtos com gap
  gapProducts: StockCalculation[];
  gapProductsLoading: boolean;
  gapProductsError: string | null;

  // Alertas
  alerts: StockAlert[];
  alertsLoading: boolean;
  alertsError: string | null;
  unreadAlertsCount: number;

  // Produtos
  products: Product[];
  productsLoading: boolean;
  productsError: string | null;
  productsTotal: number;

  // Período selecionado
  selectedPeriod: '7d' | '30d' | '90d' | '1y';
}

interface DashboardActions {
  // Estatísticas
  loadDashboardStats: (period?: string) => Promise<void>;
  
  // Produtos com gap
  loadGapProducts: () => Promise<void>;

  // Alertas
  loadAlerts: () => Promise<void>;
  markAlertAsRead: (alertId: string) => Promise<void>;

  // Produtos
  loadProducts: () => Promise<void>;
  
  // Período
  setPeriod: (period: '7d' | '30d' | '90d' | '1y') => void;
  
  // Ações de recálculo
  recalculateStock: (productIds?: string[]) => Promise<void>;
  syncProducts: () => Promise<void>;
  
  // Limpeza de erros
  clearErrors: () => void;
}

type DashboardStore = DashboardState & DashboardActions;

export const useDashboardStore = create<DashboardStore>((set, get) => ({
  // Estado inicial
  stats: null,
  statsLoading: false,
  statsError: null,

  gapProducts: [],
  gapProductsLoading: false,
  gapProductsError: null,

  alerts: [],
  alertsLoading: false,
  alertsError: null,
  unreadAlertsCount: 0,

  products: [],
  productsLoading: false,
  productsError: null,
  productsTotal: 0,

  selectedPeriod: '30d',

  // Ações
  loadDashboardStats: async (period) => {
    const { selectedPeriod } = get();
    const actualPeriod = period || selectedPeriod;

    set({ statsLoading: true, statsError: null });

    try {
      // MOCK DATA FOR DEVELOPMENT - Replace with real API call
      // const response = await apiService.getDashboardStats(actualPeriod);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));

      set({
        stats: mockDashboardStats,
        statsLoading: false
      });
    } catch (error: any) {
      set({
        statsLoading: false,
        statsError: error.message || 'Erro ao carregar estatísticas'
      });
    }
  },

  loadGapProducts: async () => {
    set({ gapProductsLoading: true, gapProductsError: null });

    try {
      // MOCK DATA FOR DEVELOPMENT - Replace with real API call
      // const response = await apiService.getStockCalculations({ hasGap: true }, 1, 50);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 600));

      // Filter products with gap from mock data and create proper StockCalculation objects
      const gapProducts = mockMLProducts
        .filter(product => product.stockCalculation?.gap && product.stockCalculation.gap > 0)
        .map(product => ({
          productId: product.id,
          product: product,
          currentStock: product.availableQuantity,
          idealStock: product.stockCalculation?.recommendedStock || 50,
          gap: product.stockCalculation?.gap || 0,
          averageSales: product.stockCalculation?.averageSales || 0,
          coverageDays: product.stockCalculation?.coverageDays || 0,
          safetyStock: Math.floor((product.stockCalculation?.recommendedStock || 50) * 0.2),
          unitsInTransit: 0,
          priority: product.stockCalculation?.gap > 20 ? 'critical' :
                   product.stockCalculation?.gap > 10 ? 'high' :
                   product.stockCalculation?.gap > 5 ? 'medium' : 'low',
          lastCalculated: new Date().toISOString()
        }))
        .filter(Boolean);

      set({
        gapProducts,
        gapProductsLoading: false
      });
    } catch (error: any) {
      set({
        gapProductsLoading: false,
        gapProductsError: error.message || 'Erro ao carregar produtos com gap'
      });
    }
  },

  loadAlerts: async () => {
    set({ alertsLoading: true, alertsError: null });

    try {
      // MOCK DATA FOR DEVELOPMENT - Replace with real API call
      // const response = await apiService.getStockAlerts(1, 20);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));

      // Generate mock alerts from products with stock issues
      const mockAlerts: StockAlert[] = mockMLProducts
        .filter(product => product.availableQuantity <= 5 || (product.stockCalculation?.gap && product.stockCalculation.gap > 0))
        .map((product, index) => ({
          id: `alert-${product.id}`,
          productId: product.id,
          product: product, // Include full product object as required by interface
          type: product.availableQuantity === 0 ? 'stock_low' :
                product.availableQuantity <= 5 ? 'stock_low' : 'gap_critical',
          severity: product.availableQuantity === 0 ? 'critical' :
                   product.availableQuantity <= 2 ? 'high' : 'medium',
          message: product.availableQuantity === 0
            ? `Produto ${product.title} está sem estoque`
            : product.availableQuantity <= 5
            ? `Produto ${product.title} com estoque baixo (${product.availableQuantity} unidades)`
            : `Produto ${product.title} com gap de estoque (${product.stockCalculation?.gap} unidades)`,
          threshold: product.availableQuantity <= 5 ? 5 : (product.stockCalculation?.idealStock || 10),
          currentValue: product.availableQuantity,
          isRead: index % 3 === 0, // Some alerts read, some unread
          createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
        }));

      const unreadCount = mockAlerts.filter(alert => !alert.isRead).length;

      set({
        alerts: mockAlerts,
        unreadAlertsCount: unreadCount,
        alertsLoading: false
      });

      // INTEGRATION: Process alerts through notification system
      try {
        const notificationStore = useNotificationStore.getState();
        notificationStore.processStockAlerts(mockAlerts);
      } catch (error) {
        console.warn('Failed to process alerts through notification system:', error);
      }
    } catch (error: any) {
      set({
        alertsLoading: false,
        alertsError: error.message || 'Erro ao carregar alertas'
      });
    }
  },

  markAlertAsRead: async (alertId: string) => {
    try {
      // MOCK DATA FOR DEVELOPMENT - Replace with real API call
      // await apiService.markAlertAsRead(alertId);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      const { alerts } = get();
      const updatedAlerts = alerts.map(alert =>
        alert.id === alertId ? { ...alert, isRead: true } : alert
      );

      const unreadCount = updatedAlerts.filter(alert => !alert.isRead).length;

      set({
        alerts: updatedAlerts,
        unreadAlertsCount: unreadCount
      });
    } catch (error: any) {
      console.error('Erro ao marcar alerta como lido:', error);
    }
  },

  loadProducts: async () => {
    set({ productsLoading: true, productsError: null });

    try {
      // MOCK DATA FOR DEVELOPMENT - Replace with real API call
      // const response = await apiService.getProducts({}, 1, 20);

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 700));

      set({
        products: mockMLProducts,
        productsTotal: mockMLProducts.length,
        productsLoading: false
      });
    } catch (error: any) {
      set({
        productsLoading: false,
        productsError: error.message || 'Erro ao carregar produtos'
      });
    }
  },

  setPeriod: (period) => {
    set({ selectedPeriod: period });
    // Recarregar estatísticas com novo período
    get().loadDashboardStats(period);
  },

  recalculateStock: async (productIds) => {
    try {
      await apiService.calculateStock(productIds);
      // Recarregar dados após recálculo
      await Promise.all([
        get().loadDashboardStats(),
        get().loadGapProducts(),
        get().loadAlerts()
      ]);
    } catch (error: any) {
      console.error('Erro ao recalcular estoque:', error);
      throw error;
    }
  },

  syncProducts: async () => {
    try {
      await apiService.syncProducts();
      // Recarregar dados após sincronização
      await Promise.all([
        get().loadProducts(),
        get().loadDashboardStats(),
        get().loadGapProducts()
      ]);
    } catch (error: any) {
      console.error('Erro ao sincronizar produtos:', error);
      throw error;
    }
  },

  clearErrors: () => {
    set({
      statsError: null,
      gapProductsError: null,
      alertsError: null,
      productsError: null
    });
  },
})); 