# Sistema de Arquivos - API Documentation

## Visão Geral

O Sistema de Arquivos do Magnow fornece funcionalidades completas para upload, download, gerenciamento e organização de arquivos com suporte a multi-tenancy, controle de acesso e limpeza automática.

## Estrutura de Diretórios

```
uploads/
├── {tenantId}/
│   ├── avatars/          # Avatars de usuários
│   ├── documents/        # PDFs e documentos
│   └── spreadsheets/     # Planilhas Excel/CSV
├── temp/                 # Arquivos temporários
└── public/
    └── system/           # Arquivos públicos do sistema
```

## Configurações

| Variável | Valor Padrão | Descrição |
|----------|--------------|-----------|
| `UPLOADS_DIR` | `uploads` | Diretório base para uploads |
| `UPLOAD_MAX_SIZE` | `10485760` | Tamanho máximo (10MB) |
| `API_BASE_URL` | `http://localhost:3000` | URL base para downloads |

## Tipos de Arquivo Suportados

### Avatar
- **Formatos**: JPEG, PNG, WebP
- **Tamanho máximo**: 5MB
- **Extensões**: `.jpg`, `.jpeg`, `.png`, `.webp`

### Document
- **Formatos**: PDF
- **Tamanho máximo**: 10MB
- **Extensões**: `.pdf`

### Spreadsheet
- **Formatos**: Excel, CSV
- **Tamanho máximo**: 15MB
- **Extensões**: `.xlsx`, `.xls`, `.csv`

## Endpoints da API

### Upload de Arquivos

#### POST /api/files/upload/avatar
Upload de avatar de usuário.

**Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data
```

**Body:**
```
file: [arquivo de imagem]
```

**Response:**
```json
{
  "success": true,
  "message": "Avatar enviado com sucesso",
  "data": {
    "file": {
      "id": "uuid",
      "originalName": "avatar.jpg",
      "fileSize": 1024,
      "fileType": "avatar",
      "mimeType": "image/jpeg",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "url": "/api/files/download/uuid"
  }
}
```

#### POST /api/files/upload/document
Upload de documentos (PDFs).

**Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data
```

**Body:**
```
file: [arquivo PDF]
isPublic: false (opcional)
expiresIn: 86400 (opcional, em segundos)
```

**Response:**
```json
{
  "success": true,
  "message": "Documento enviado com sucesso",
  "data": {
    "file": {
      "id": "uuid",
      "originalName": "comprovante.pdf",
      "fileSize": 2048,
      "fileType": "document",
      "mimeType": "application/pdf",
      "isPublic": false,
      "expiresAt": "2024-01-02T00:00:00Z",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "url": "/api/files/download/uuid"
  }
}
```

#### POST /api/files/upload/spreadsheet
Upload de planilhas.

**Headers:**
```
Authorization: Bearer {jwt_token}
Content-Type: multipart/form-data
```

**Body:**
```
file: [arquivo Excel/CSV]
isPublic: false (opcional)
```

### Download e Acesso

#### GET /api/files/download/:fileId
Download de arquivo por ID.

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
- Arquivo binário com headers apropriados
- `Content-Type`: MIME type do arquivo
- `Content-Disposition`: attachment; filename="nome_original.ext"
- `Content-Length`: tamanho do arquivo

#### GET /api/files/stream/:fileId
Stream de arquivo (para arquivos grandes).

**Headers:**
```
Authorization: Bearer {jwt_token}
Range: bytes=0-1023 (opcional)
```

**Response:**
- Stream do arquivo com suporte a range requests
- Status 206 para partial content

#### GET /api/files/preview/:fileId
Preview de arquivo (apenas imagens).

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
- Imagem com cache headers
- `Cache-Control: public, max-age=3600`

### Gerenciamento

#### GET /api/files
Listar arquivos do tenant.

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Query Parameters:**
- `fileType`: Filtrar por tipo (avatar, document, spreadsheet)
- `uploadedBy`: Filtrar por usuário
- `limit`: Limite de resultados (padrão: 50)
- `offset`: Offset para paginação (padrão: 0)

**Response:**
```json
{
  "success": true,
  "data": {
    "files": [
      {
        "id": "uuid",
        "originalName": "arquivo.pdf",
        "fileSize": 1024,
        "fileType": "document",
        "mimeType": "application/pdf",
        "isPublic": false,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "limit": 50,
      "offset": 0,
      "total": 1
    }
  }
}
```

#### GET /api/files/:fileId
Obter informações de um arquivo específico.

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "file": {
      "id": "uuid",
      "originalName": "arquivo.pdf",
      "fileSize": 1024,
      "fileType": "document",
      "mimeType": "application/pdf",
      "isPublic": false,
      "downloadCount": 5,
      "lastAccessedAt": "2024-01-01T12:00:00Z",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "downloadUrl": "/api/files/download/uuid"
  }
}
```

#### DELETE /api/files/:fileId
Deletar arquivo.

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "message": "Arquivo deletado com sucesso"
}
```

### Estatísticas

#### GET /api/files/stats/overview
Estatísticas gerais de arquivos do tenant.

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalFiles": 150,
    "totalSize": 52428800,
    "filesByType": {
      "avatar": 50,
      "document": 75,
      "spreadsheet": 25
    },
    "sizeByType": {
      "avatar": 10485760,
      "document": 31457280,
      "spreadsheet": 10485760
    }
  }
}
```

#### GET /api/files/stats/usage
Estatísticas de uso de storage.

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalSize": 1073741824,
    "usedSize": 52428800,
    "availableSize": 1021313024,
    "usagePercentage": 4.88,
    "fileCount": 150
  }
}
```

### Operações Administrativas

#### POST /api/files/cleanup/expired
Limpar arquivos expirados (apenas admins).

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "message": "5 arquivos expirados foram removidos",
  "data": {
    "deletedCount": 5
  }
}
```

#### POST /api/files/cleanup/temp
Limpar arquivos temporários (apenas admins).

**Headers:**
```
Authorization: Bearer {jwt_token}
```

**Response:**
```json
{
  "success": true,
  "message": "12 arquivos temporários foram removidos",
  "data": {
    "deletedCount": 12
  }
}
```

## Rate Limiting

| Endpoint | Limite | Janela |
|----------|--------|--------|
| Upload Avatar | 10 requests | 15 minutos |
| Upload Document | 20 requests | 15 minutos |
| Upload Spreadsheet | 5 requests | 15 minutos |
| Download | 100 requests | 1 minuto |
| Stream | 50 requests | 1 minuto |
| Preview | 200 requests | 1 minuto |
| List Files | 60 requests | 1 minuto |
| Delete | 30 requests | 1 minuto |
| Admin Operations | 5 requests | 1 minuto |

## Códigos de Erro

| Código | Descrição |
|--------|-----------|
| 400 | Arquivo inválido ou dados malformados |
| 401 | Token de autenticação inválido |
| 403 | Acesso negado ao arquivo |
| 404 | Arquivo não encontrado |
| 413 | Arquivo muito grande |
| 415 | Tipo de arquivo não suportado |
| 429 | Rate limit excedido |
| 500 | Erro interno do servidor |

## Segurança

### Validações Implementadas
- Verificação de MIME type
- Validação de extensão de arquivo
- Verificação de tamanho
- Sanitização de nomes de arquivo
- Controle de acesso baseado em tenant
- Verificação de permissões de usuário

### Controle de Acesso
- Arquivos privados: apenas o uploader tem acesso
- Arquivos públicos: qualquer usuário do mesmo tenant
- Arquivos de sistema: acesso público controlado
- Verificação de JWT em todas as operações

## Limpeza Automática

### Agendamento
- **Limpeza completa**: Diariamente às 2:00 AM
- **Arquivos temporários**: A cada 4 horas

### Critérios de Limpeza
- Arquivos temporários: > 4 horas
- Arquivos expirados: baseado em `expiresAt`
- Arquivos órfãos: > 72 horas sem registro no banco

## Monitoramento

### Logs Gerados
- Upload de arquivos (INFO)
- Download de arquivos (INFO)
- Erros de validação (WARN)
- Falhas de upload/download (ERROR)
- Operações de limpeza (INFO)

### Métricas Disponíveis
- Total de arquivos por tenant
- Uso de storage por tipo
- Contadores de download
- Estatísticas de limpeza

## Desenvolvimento

### Página de Teste
Em ambiente de desenvolvimento, acesse:
```
GET /api/files/dev/test-upload
```

### Armazenamento Temporário
Durante o desenvolvimento, os metadados são armazenados em memória. Em produção, será utilizado o banco PostgreSQL.

### Estrutura de Dados
```typescript
interface FileMetadata {
  id: string;
  tenantId: string;
  uploadedBy: string;
  originalName: string;
  storedName: string;
  filePath: string;
  mimeType: string;
  fileSize: number;
  fileType: 'avatar' | 'document' | 'spreadsheet';
  checksum?: string;
  isPublic: boolean;
  expiresAt?: Date;
  createdAt: Date;
}
```
