/**
 * Serviço de Análise de Vendas Históricas
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { 
  SalesData, 
  SalesAnalysis, 
  SeasonalityPattern,
  StockCalculationError 
} from '../types/stock';
import { ValidationError, DatabaseError } from '../middleware/errorHandler';
import CacheService, { CacheNamespaces } from './cacheService';

export class SalesAnalysisService {
  private prisma: PrismaClient;
  private cache: CacheService;

  constructor(prisma?: PrismaClient, cache?: CacheService) {
    this.prisma = prisma || new PrismaClient();
    this.cache = cache || new CacheService();
  }

  /**
   * Analisa vendas históricas para um produto específico
   */
  public async analyzeSalesHistory(
    tenantId: string,
    mlItemId: string,
    analysisWindowDays: number = 60,
    endDate: Date = new Date()
  ): Promise<SalesAnalysis> {
    try {
      const startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - analysisWindowDays);

      logger.info('Iniciando análise de vendas', { 
        tenantId, 
        mlItemId, 
        analysisWindowDays,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });

      // Busca dados de vendas do período
      const salesData = await this.getSalesData(tenantId, mlItemId, startDate, endDate);
      
      if (salesData.length === 0) {
        logger.warn('Nenhum dado de vendas encontrado', { tenantId, mlItemId });
        return this.createEmptyAnalysis(mlItemId, startDate, endDate, analysisWindowDays);
      }

      // Calcula métricas básicas
      const basicMetrics = this.calculateBasicMetrics(salesData, analysisWindowDays);

      // Analisa tendência
      const trendAnalysis = this.analyzeTrend(salesData);

      // Detecta sazonalidade
      const seasonalityAnalysis = this.detectSeasonality(salesData);

      const analysis: SalesAnalysis = {
        mlItemId,
        sku: salesData[0]?.sku || '',
        startDate,
        endDate,
        totalDays: analysisWindowDays,
        ...basicMetrics,
        ...trendAnalysis,
        seasonalityDetected: seasonalityAnalysis.seasonalityDetected || false,
        seasonalityPattern: seasonalityAnalysis.seasonalityPattern || undefined
      };

      logger.info('Análise de vendas concluída', { 
        mlItemId, 
        averageDailySales: analysis.averageDailySales,
        trendDirection: analysis.trendDirection,
        seasonalityDetected: analysis.seasonalityDetected
      });

      return analysis;
    } catch (error) {
      logger.error('Erro na análise de vendas', { error, tenantId, mlItemId });
      throw new DatabaseError('Erro ao analisar vendas históricas');
    }
  }

  /**
   * Analisa vendas para múltiplos produtos
   */
  public async analyzeBatchSalesHistory(
    tenantId: string,
    mlItemIds: string[],
    analysisWindowDays: number = 60
  ): Promise<{ analyses: SalesAnalysis[]; errors: StockCalculationError[] }> {
    const analyses: SalesAnalysis[] = [];
    const errors: StockCalculationError[] = [];

    logger.info('Iniciando análise em lote', { tenantId, itemCount: mlItemIds.length });

    for (const mlItemId of mlItemIds) {
      try {
        const analysis = await this.analyzeSalesHistory(tenantId, mlItemId, analysisWindowDays);
        analyses.push(analysis);
      } catch (error: any) {
        errors.push({
          mlItemId,
          sku: '',
          error: 'Erro na análise de vendas',
          details: error.message,
          timestamp: new Date()
        });
      }
    }

    logger.info('Análise em lote concluída', { 
      totalItems: mlItemIds.length,
      successful: analyses.length,
      errors: errors.length
    });

    return { analyses, errors };
  }

  /**
   * Busca dados de vendas do banco de dados
   */
  private async getSalesData(
    tenantId: string,
    mlItemId: string,
    startDate: Date,
    endDate: Date
  ): Promise<SalesData[]> {
    try {
      // Busca vendas da tabela Sale
      const sales = await this.prisma.sale.findMany({
        where: {
          product: {
            tenantId,
            mlId: mlItemId
          },
          saleDate: {
            gte: startDate,
            lte: endDate
          },
          status: {
            in: ['paid', 'shipped', 'delivered'] // Apenas vendas confirmadas
          }
        },
        include: {
          product: true
        },
        orderBy: {
          saleDate: 'asc'
        }
      });

      // Converte para formato SalesData
      const salesData: SalesData[] = sales.map(sale => ({
        mlItemId: sale.product.mlId,
        sku: sale.product.sku || '',
        date: sale.saleDate,
        quantity: sale.quantity,
        price: Number(sale.unitPrice),
        revenue: Number(sale.totalPrice)
      }));

      return salesData;
    } catch (error) {
      logger.error('Erro ao buscar dados de vendas', { error, tenantId, mlItemId });
      throw error;
    }
  }

  /**
   * Calcula métricas básicas de vendas
   */
  private calculateBasicMetrics(salesData: SalesData[], totalDays: number) {
    const quantities = salesData.map(sale => sale.quantity);
    const revenues = salesData.map(sale => sale.revenue);

    const totalQuantitySold = quantities.reduce((sum, qty) => sum + qty, 0);
    const totalRevenue = revenues.reduce((sum, rev) => sum + rev, 0);

    // Agrupa vendas por dia para calcular média diária
    const dailySales = this.groupSalesByDay(salesData);
    const dailyQuantities = Object.values(dailySales);
    
    const averageDailySales = totalQuantitySold / totalDays;
    const medianDailySales = this.calculateMedian(dailyQuantities);
    
    // Calcula variância e desvio padrão
    const mean = averageDailySales;
    const variance = dailyQuantities.reduce((sum, qty) => sum + Math.pow(qty - mean, 2), 0) / totalDays;
    const standardDeviation = Math.sqrt(variance);

    const maxDailySales = Math.max(...dailyQuantities, 0);
    const minDailySales = Math.min(...dailyQuantities, 0);

    return {
      totalQuantitySold,
      totalRevenue,
      averageDailySales,
      medianDailySales,
      salesVariance: variance,
      salesStandardDeviation: standardDeviation,
      maxDailySales,
      minDailySales
    };
  }

  /**
   * Analisa tendência de vendas
   */
  private analyzeTrend(salesData: SalesData[]) {
    if (salesData.length < 2) {
      return {
        trendDirection: 'stable' as const,
        trendStrength: 0
      };
    }

    // Agrupa vendas por semana para análise de tendência
    const weeklySales = this.groupSalesByWeek(salesData);
    const weeks = Object.keys(weeklySales).sort();
    
    if (weeks.length < 2) {
      return {
        trendDirection: 'stable' as const,
        trendStrength: 0
      };
    }

    // Calcula regressão linear simples
    const regression = this.calculateLinearRegression(
      weeks.map((_, index) => index),
      weeks.map(week => weeklySales[week] || 0)
    );

    // Determina direção da tendência
    let trendDirection: 'increasing' | 'decreasing' | 'stable';
    if (regression.slope > 0.1) {
      trendDirection = 'increasing';
    } else if (regression.slope < -0.1) {
      trendDirection = 'decreasing';
    } else {
      trendDirection = 'stable';
    }

    // Força da tendência baseada no R²
    const trendStrength = Math.abs(regression.rSquared);

    return {
      trendDirection,
      trendStrength
    };
  }

  /**
   * Detecta padrões de sazonalidade
   */
  private detectSeasonality(salesData: SalesData[]) {
    if (salesData.length < 14) { // Mínimo 2 semanas
      return {
        seasonalityDetected: false
      };
    }

    // Analisa padrão semanal
    const weeklyPattern = this.analyzeWeeklyPattern(salesData);
    
    // Analisa padrão mensal (se há dados suficientes)
    const monthlyPattern = salesData.length >= 60 ? this.analyzeMonthlyPattern(salesData) : null;

    // Determina se há sazonalidade significativa
    const weeklySeasonality = weeklyPattern && weeklyPattern.seasonalityFactor > 1.2;
    const monthlySeasonality = monthlyPattern && monthlyPattern.seasonalityFactor > 1.3;

    if (weeklySeasonality || monthlySeasonality) {
      return {
        seasonalityDetected: true,
        seasonalityPattern: weeklySeasonality ? weeklyPattern : monthlyPattern
      };
    }

    return {
      seasonalityDetected: false
    };
  }

  /**
   * Analisa padrão semanal de vendas
   */
  private analyzeWeeklyPattern(salesData: SalesData[]): SeasonalityPattern | null {
    const dayOfWeekSales: { [key: string]: number[] } = {};
    
    // Agrupa vendas por dia da semana
    for (const sale of salesData) {
      const dayOfWeek = sale.date.toLocaleDateString('pt-BR', { weekday: 'long' });
      if (!dayOfWeekSales[dayOfWeek]) {
        dayOfWeekSales[dayOfWeek] = [];
      }
      dayOfWeekSales[dayOfWeek].push(sale.quantity);
    }

    // Calcula média por dia da semana
    const dayAverages: { [key: string]: number } = {};
    for (const [day, quantities] of Object.entries(dayOfWeekSales)) {
      dayAverages[day] = quantities.reduce((sum, qty) => sum + qty, 0) / quantities.length;
    }

    const averages = Object.values(dayAverages);
    const overallAverage = averages.reduce((sum, avg) => sum + avg, 0) / averages.length;
    
    // Calcula fator de sazonalidade
    const maxAverage = Math.max(...averages);
    const minAverage = Math.min(...averages);
    const seasonalityFactor = maxAverage / (minAverage || 1);

    if (seasonalityFactor < 1.2) return null;

    // Identifica picos e vales
    const threshold = overallAverage * 1.1;
    const peakPeriods = Object.entries(dayAverages)
      .filter(([_, avg]) => avg > threshold)
      .map(([day, _]) => day);

    const lowThreshold = overallAverage * 0.9;
    const lowPeriods = Object.entries(dayAverages)
      .filter(([_, avg]) => avg < lowThreshold)
      .map(([day, _]) => day);

    return {
      type: 'weekly',
      peakPeriods,
      lowPeriods,
      seasonalityFactor
    };
  }

  /**
   * Analisa padrão mensal de vendas
   */
  private analyzeMonthlyPattern(salesData: SalesData[]): SeasonalityPattern | null {
    const monthSales: { [key: string]: number[] } = {};
    
    // Agrupa vendas por mês
    for (const sale of salesData) {
      const month = sale.date.toLocaleDateString('pt-BR', { month: 'long' });
      if (!monthSales[month]) {
        monthSales[month] = [];
      }
      monthSales[month].push(sale.quantity);
    }

    if (Object.keys(monthSales).length < 3) return null;

    // Calcula média por mês
    const monthAverages: { [key: string]: number } = {};
    for (const [month, quantities] of Object.entries(monthSales)) {
      monthAverages[month] = quantities.reduce((sum, qty) => sum + qty, 0) / quantities.length;
    }

    const averages = Object.values(monthAverages);
    const overallAverage = averages.reduce((sum, avg) => sum + avg, 0) / averages.length;
    
    const maxAverage = Math.max(...averages);
    const minAverage = Math.min(...averages);
    const seasonalityFactor = maxAverage / (minAverage || 1);

    if (seasonalityFactor < 1.3) return null;

    const threshold = overallAverage * 1.15;
    const peakPeriods = Object.entries(monthAverages)
      .filter(([_, avg]) => avg > threshold)
      .map(([month, _]) => month);

    const lowThreshold = overallAverage * 0.85;
    const lowPeriods = Object.entries(monthAverages)
      .filter(([_, avg]) => avg < lowThreshold)
      .map(([month, _]) => month);

    return {
      type: 'monthly',
      peakPeriods,
      lowPeriods,
      seasonalityFactor
    };
  }

  /**
   * Agrupa vendas por dia
   */
  private groupSalesByDay(salesData: SalesData[]): { [key: string]: number } {
    const dailySales: { [key: string]: number } = {};
    
    for (const sale of salesData) {
      const dateKey = sale.date.toISOString().split('T')[0];
      dailySales[dateKey] = (dailySales[dateKey] || 0) + sale.quantity;
    }
    
    return dailySales;
  }

  /**
   * Agrupa vendas por semana
   */
  private groupSalesByWeek(salesData: SalesData[]): { [key: string]: number } {
    const weeklySales: { [key: string]: number } = {};
    
    for (const sale of salesData) {
      // Calcula início da semana (domingo)
      const date = new Date(sale.date);
      const dayOfWeek = date.getDay();
      const startOfWeek = new Date(date);
      startOfWeek.setDate(date.getDate() - dayOfWeek);
      
      const weekKey = startOfWeek.toISOString().split('T')[0];
      weeklySales[weekKey] = (weeklySales[weekKey] || 0) + sale.quantity;
    }
    
    return weeklySales;
  }

  /**
   * Calcula mediana de um array
   */
  private calculateMedian(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    
    const sorted = [...numbers].sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);
    
    if (sorted.length % 2 === 0) {
      return (sorted[middle - 1] + sorted[middle]) / 2;
    } else {
      return sorted[middle];
    }
  }

  /**
   * Calcula regressão linear simples
   */
  private calculateLinearRegression(x: number[], y: number[]) {
    const n = x.length;
    if (n === 0 || y.length === 0 || n !== y.length) return { slope: 0, intercept: 0, rSquared: 0 };

    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * (y[i] || 0), 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    const sumYY = y.reduce((sum, val) => sum + val * val, 0);

    const denominator = n * sumXX - sumX * sumX;
    if (denominator === 0) return { slope: 0, intercept: 0, rSquared: 0 };

    const slope = (n * sumXY - sumX * sumY) / denominator;
    const intercept = (sumY - slope * sumX) / n;

    // Calcula R²
    const meanY = sumY / n;
    const totalSumSquares = y.reduce((sum, val) => sum + Math.pow(val - meanY, 2), 0);
    const residualSumSquares = y.reduce((sum, val, i) => {
      const predicted = slope * (x[i] || 0) + intercept;
      return sum + Math.pow(val - predicted, 2);
    }, 0);

    const rSquared = totalSumSquares > 0 ? 1 - (residualSumSquares / totalSumSquares) : 0;

    return { slope, intercept, rSquared };
  }

  /**
   * Cria análise vazia quando não há dados
   */
  private createEmptyAnalysis(
    mlItemId: string,
    startDate: Date,
    endDate: Date,
    totalDays: number
  ): SalesAnalysis {
    return {
      mlItemId,
      sku: '',
      startDate,
      endDate,
      totalDays,
      totalQuantitySold: 0,
      totalRevenue: 0,
      averageDailySales: 0,
      medianDailySales: 0,
      salesVariance: 0,
      salesStandardDeviation: 0,
      maxDailySales: 0,
      minDailySales: 0,
      trendDirection: 'stable',
      trendStrength: 0,
      seasonalityDetected: false
    };
  }

  /**
   * Obtém análise mais recente de um produto
   */
  public async getLatestAnalysis(
    tenantId: string,
    mlItemId: string
  ): Promise<SalesAnalysis | null> {
    try {
      const cacheKey = `analysis:${mlItemId}`;
      const cached = await this.cache.get(
        CacheNamespaces.ANALYTICS,
        cacheKey,
        tenantId
      );

      if (cached) {
        logger.debug('Cache hit para análise de vendas', { tenantId, mlItemId });
        return cached;
      }

      logger.debug('Cache miss para análise de vendas', { tenantId, mlItemId });
      return null;
    } catch (error) {
      logger.error('Erro ao buscar análise mais recente', { error, tenantId, mlItemId });
      return null;
    }
  }

  /**
   * Salva análise no cache
   */
  public async cacheAnalysis(
    tenantId: string,
    analysis: SalesAnalysis,
    ttlHours: number = 24
  ): Promise<void> {
    try {
      const cacheKey = `analysis:${analysis.mlItemId}`;
      const ttlSeconds = ttlHours * 3600;
      
      await this.cache.set(
        CacheNamespaces.ANALYTICS,
        cacheKey,
        analysis,
        { ttl: ttlSeconds },
        tenantId
      );

      logger.debug('Análise salva no cache', { 
        tenantId, 
        mlItemId: analysis.mlItemId, 
        ttlHours 
      });
    } catch (error) {
      logger.error('Erro ao salvar análise no cache', { 
        error, 
        tenantId, 
        mlItemId: analysis.mlItemId 
      });
      // Não propaga o erro para não interromper o fluxo principal
    }
  }
}