import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { LogEntry, LogFilters, LogsResponse, LogAction, LogSeverity, LogCategory } from '../types/api';
import apiService from '../services/api';

interface LogsState {
  // Logs data
  logs: LogEntry[];
  total: number;
  hasMore: boolean;
  nextOffset: number;
  
  // Loading states
  logsLoading: boolean;
  logsError: string | null;
  
  // Filters
  filters: LogFilters;
  
  // Real-time updates
  isRealTimeEnabled: boolean;
  lastUpdate: string | null;
}

interface LogsActions {
  // Data actions
  loadLogs: (filters?: LogFilters) => Promise<void>;
  loadMoreLogs: () => Promise<void>;
  refreshLogs: () => Promise<void>;
  
  // Filter actions
  setFilters: (filters: Partial<LogFilters>) => void;
  clearFilters: () => void;
  
  // Real-time actions
  enableRealTime: () => void;
  disableRealTime: () => void;
  
  // Utility actions
  clearError: () => void;
  exportLogs: (filters?: LogFilters) => Promise<void>;
}

type LogsStore = LogsState & LogsActions;

// MOCK DATA FOR DEVELOPMENT - Remove when API is ready
const mockUsers = [
  { id: 'user-1', name: 'João Silva', email: '<EMAIL>' },
  { id: 'user-2', name: 'Maria Santos', email: '<EMAIL>' },
  { id: 'user-3', name: 'Pedro Costa', email: '<EMAIL>' },
  { id: 'admin-1', name: 'Admin Sistema', email: '<EMAIL>' },
];

const mockResources = [
  'products', 'stock', 'orders', 'users', 'settings', 'integrations', 'reports'
];

const mockActions: LogAction[] = [
  'LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'EXPORT', 
  'IMPORT', 'SYNC', 'CONFIG_CHANGE', 'PASSWORD_CHANGE'
];

const mockSeverities: LogSeverity[] = ['info', 'warning', 'error', 'critical'];
const mockCategories: LogCategory[] = [
  'authentication', 'authorization', 'data', 'system', 'security', 'integration', 'configuration'
];

const generateMockLog = (): LogEntry => {
  const user = mockUsers[Math.floor(Math.random() * mockUsers.length)];
  const action = mockActions[Math.floor(Math.random() * mockActions.length)];
  const resource = mockResources[Math.floor(Math.random() * mockResources.length)];
  const severity = mockSeverities[Math.floor(Math.random() * mockSeverities.length)];
  const category = mockCategories[Math.floor(Math.random() * mockCategories.length)];
  
  return {
    id: `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    userId: user.id,
    userName: user.name,
    userEmail: user.email,
    action,
    resource,
    resourceId: `${resource}-${Math.floor(Math.random() * 1000)}`,
    details: {
      description: `${action} operation on ${resource}`,
      changes: action === 'UPDATE' ? { field: 'value' } : undefined,
      metadata: { source: 'web', version: '1.0.0' }
    },
    ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    sessionId: `session-${Math.random().toString(36).substr(2, 16)}`,
    severity,
    category,
  };
};

const generateMockLogs = (count: number): LogEntry[] => {
  return Array.from({ length: count }, () => generateMockLog())
    .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
};

export const useLogsStore = create<LogsStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      logs: [],
      total: 0,
      hasMore: true,
      nextOffset: 0,
      
      logsLoading: false,
      logsError: null,
      
      filters: {
        limit: 50,
        offset: 0,
      },
      
      isRealTimeEnabled: false,
      lastUpdate: null,

      // Data actions
      loadLogs: async (filters?: LogFilters) => {
        set({ logsLoading: true, logsError: null });
        
        try {
          const currentFilters = { ...get().filters, ...filters };
          
          // MOCK DATA FOR DEVELOPMENT - Replace with API call
          await new Promise(resolve => setTimeout(resolve, 800));
          
          const allMockLogs = generateMockLogs(500);
          
          // Apply filters
          let filteredLogs = allMockLogs;
          
          if (currentFilters.search) {
            const searchLower = currentFilters.search.toLowerCase();
            filteredLogs = filteredLogs.filter(log => 
              log.userName.toLowerCase().includes(searchLower) ||
              log.action.toLowerCase().includes(searchLower) ||
              log.resource.toLowerCase().includes(searchLower)
            );
          }
          
          if (currentFilters.action) {
            filteredLogs = filteredLogs.filter(log => log.action === currentFilters.action);
          }
          
          if (currentFilters.severity) {
            filteredLogs = filteredLogs.filter(log => log.severity === currentFilters.severity);
          }
          
          if (currentFilters.category) {
            filteredLogs = filteredLogs.filter(log => log.category === currentFilters.category);
          }
          
          if (currentFilters.dateFrom) {
            const fromDate = new Date(currentFilters.dateFrom).getTime();
            filteredLogs = filteredLogs.filter(log => 
              new Date(log.timestamp).getTime() >= fromDate
            );
          }
          
          if (currentFilters.dateTo) {
            const toDate = new Date(currentFilters.dateTo).getTime();
            filteredLogs = filteredLogs.filter(log => 
              new Date(log.timestamp).getTime() <= toDate
            );
          }
          
          // Pagination
          const offset = currentFilters.offset || 0;
          const limit = currentFilters.limit || 50;
          const paginatedLogs = filteredLogs.slice(offset, offset + limit);
          const hasMore = offset + limit < filteredLogs.length;
          
          set({
            logs: offset === 0 ? paginatedLogs : [...get().logs, ...paginatedLogs],
            total: filteredLogs.length,
            hasMore,
            nextOffset: offset + limit,
            filters: currentFilters,
            logsLoading: false,
            lastUpdate: new Date().toISOString(),
          });
          
          // TODO: Replace with actual API call
          // const response = await apiService.getLogs(currentFilters);
          // if (response.success) {
          //   set({
          //     logs: offset === 0 ? response.data.logs : [...get().logs, ...response.data.logs],
          //     total: response.data.total,
          //     hasMore: response.data.hasMore,
          //     nextOffset: response.data.nextOffset || offset + limit,
          //     filters: currentFilters,
          //     logsLoading: false,
          //     lastUpdate: new Date().toISOString(),
          //   });
          // }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar logs';
          set({ 
            logsLoading: false, 
            logsError: errorMessage 
          });
        }
      },

      loadMoreLogs: async () => {
        const { hasMore, nextOffset, filters } = get();
        if (!hasMore) return;
        
        await get().loadLogs({ ...filters, offset: nextOffset });
      },

      refreshLogs: async () => {
        const { filters } = get();
        await get().loadLogs({ ...filters, offset: 0 });
      },

      // Filter actions
      setFilters: (newFilters: Partial<LogFilters>) => {
        const currentFilters = get().filters;
        const updatedFilters = { ...currentFilters, ...newFilters, offset: 0 };
        set({ filters: updatedFilters });
        
        // Auto-load with new filters
        get().loadLogs(updatedFilters);
      },

      clearFilters: () => {
        const defaultFilters: LogFilters = {
          limit: 50,
          offset: 0,
        };
        set({ filters: defaultFilters });
        get().loadLogs(defaultFilters);
      },

      // Real-time actions
      enableRealTime: () => {
        set({ isRealTimeEnabled: true });
        
        // TODO: Implement WebSocket or polling for real-time updates
        // const interval = setInterval(() => {
        //   if (get().isRealTimeEnabled) {
        //     get().refreshLogs();
        //   }
        // }, 30000); // Refresh every 30 seconds
      },

      disableRealTime: () => {
        set({ isRealTimeEnabled: false });
      },

      // Utility actions
      clearError: () => {
        set({ logsError: null });
      },

      exportLogs: async (filters?: LogFilters) => {
        try {
          const exportFilters = { ...get().filters, ...filters, limit: undefined, offset: undefined };
          
          // MOCK DATA FOR DEVELOPMENT
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // TODO: Replace with actual API call
          // const response = await apiService.exportLogs(exportFilters);
          // if (response.success) {
          //   // Download file
          //   const blob = new Blob([response.data], { type: 'text/csv' });
          //   const url = window.URL.createObjectURL(blob);
          //   const a = document.createElement('a');
          //   a.href = url;
          //   a.download = `logs-${new Date().toISOString().split('T')[0]}.csv`;
          //   a.click();
          //   window.URL.revokeObjectURL(url);
          // }
          
          console.log('Export logs with filters:', exportFilters);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao exportar logs';
          set({ logsError: errorMessage });
        }
      },
    }),
    {
      name: 'logs-store',
    }
  )
);

export default useLogsStore;
