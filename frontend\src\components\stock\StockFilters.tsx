import React, { useState, useEffect } from 'react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select-radix';

interface StockFiltersProps {
  onFilterChange: (filters: { search?: string; location?: string; status?: string }) => void;
}

export default function StockFilters({ onFilterChange }: StockFiltersProps) {
  const [search, setSearch] = useState('');
  const [location, setLocation] = useState('all');
  const [status, setStatus] = useState('all');

  useEffect(() => {
    const handler = setTimeout(() => {
      onFilterChange({
        search,
        location: location === 'all' ? '' : location,
        status: status === 'all' ? '' : status
      });
    }, 300);

    return () => clearTimeout(handler);
  }, [search, location, status, onFilterChange]);

  const locationOptions = [
    { label: 'Todas as Localizações', value: 'all' },
    { label: 'Armazém 1 - Prateleira A1', value: 'Armazém 1 - Prateleira A1' },
    { label: 'Armazém 2 - Prateleira B2', value: 'Armazém 2 - Prateleira B2' },
    { label: 'Loja - Mostruário', value: 'Loja - Mostruário' },
    { label: 'Loja - Depósito', value: 'Loja - Depósito' },
  ];

  const statusOptions = [
    { label: 'Todos os Status', value: 'all' },
    { label: 'Em Estoque', value: 'Em Estoque' },
    { label: 'Baixo Estoque', value: 'Baixo Estoque' },
    { label: 'Fora de Estoque', value: 'Fora de Estoque' },
  ];

  const handleClearFilters = () => {
    setSearch('');
    setLocation('all');
    setStatus('all');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Filtros de Estoque</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-4 items-end">
          <div className="space-y-2">
            <Label htmlFor="search">Buscar</Label>
            <Input
              id="search"
              type="text"
              placeholder="Pesquisar por Produto ou SKU"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="location">Localização</Label>
            <Select value={location} onValueChange={setLocation}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma localização" />
              </SelectTrigger>
              <SelectContent>
                {locationOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button variant="secondary" onClick={handleClearFilters}>Limpar Filtros</Button>
        </div>
      </CardContent>
    </Card>
  );
} 