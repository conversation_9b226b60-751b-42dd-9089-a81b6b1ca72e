﻿import { createClient, RedisClientType } from 'redis';
import { logger } from '../utils/logger';

// Cache em memória como fallback
const memoryCache = new Map<string, { value: string; expiry?: number }>();

// Verificar se Redis está habilitado
const REDIS_ENABLED = process.env.REDIS_ENABLED !== 'false';

// Cliente Redis
let redisClient: RedisClientType | null = null;
let redisAvailable = false;

export const connectRedis = async (): Promise<RedisClientType | null> => {
  // Se Redis está desabilitado, retornar null imediatamente
  if (!REDIS_ENABLED) {
    logger.info('  Redis desabilitado, usando apenas cache em memória');
    return null;
  }

  try {
    const clientOptions: any = {
      socket: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        connectTimeout: 3000,
      },
      database: parseInt(process.env.REDIS_DB || '0'),
    };

    if (process.env.REDIS_PASSWORD) {
      clientOptions.password = process.env.REDIS_PASSWORD;
    }

    redisClient = createClient(clientOptions);

    // Event listeners minimalistas - SEM logs para evitar loops
    redisClient.on('error', () => {
      redisAvailable = false;
    });

    redisClient.on('ready', () => {
      redisAvailable = true;
    });

    // Conectar com timeout
    await Promise.race([
      redisClient.connect(),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('Redis timeout')), 3000)
      )
    ]);
    
    await redisClient.ping();
    logger.info(' Redis conectado');
    redisAvailable = true;
    return redisClient;
  } catch (error) {
    logger.warn('  Redis não disponível, usando cache em memória');
    redisAvailable = false;
    redisClient = null;
    return null;
  }
};

export const getRedisClient = (): RedisClientType | null => {
  return redisClient;
};

export const cacheGet = async (key: string): Promise<string | null> => {
  try {
    if (REDIS_ENABLED && redisAvailable && redisClient) {
      return await redisClient.get(key);
    }
  } catch (error) {}
  
  const cached = memoryCache.get(key);
  if (cached) {
    if (cached.expiry && Date.now() > cached.expiry) {
      memoryCache.delete(key);
      return null;
    }
    return cached.value;
  }
  return null;
};

export const cacheSet = async (key: string, value: string, ttl?: number): Promise<boolean> => {
  try {
    if (REDIS_ENABLED && redisAvailable && redisClient) {
      if (ttl) {
        await redisClient.setEx(key, ttl, value);
      } else {
        await redisClient.set(key, value);
      }
      return true;
    }
  } catch (error) {}
  
  const defaultTTL = parseInt(process.env.CACHE_TTL || '3600');
  const expiry = ttl || defaultTTL;
  
  memoryCache.set(key, {
    value,
    expiry: expiry ? Date.now() + (expiry * 1000) : undefined
  });
  
  return true;
};

export const getRedisStatus = () => {
  return {
    enabled: REDIS_ENABLED,
    available: redisAvailable,
    fallbackMode: !REDIS_ENABLED || !redisAvailable
  };
};

export { redisClient };
