import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/Form';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select-radix';
import { Modal } from '../ui/Modal';

const productFormSchema = z.object({
  id: z.string().optional(), // Para edição
  title: z.string().min(1, { message: "O título é obrigatório." }).max(255, { message: "O título é muito longo." }),
  sku: z.string().min(1, { message: "O SKU é obrigatório." }).max(100, { message: "O SKU é muito longo." }),
  price: z.preprocess(
    (val) => parseFloat(String(val)),
    z.number().min(0.01, { message: "O preço deve ser maior que zero." })
  ),
  availableQuantity: z.preprocess(
    (val) => parseInt(String(val), 10),
    z.number().int().min(0, { message: "A quantidade deve ser um número inteiro positivo." })
  ),
  category: z.string().min(1, { message: "A categoria é obrigatória." }),
  status: z.string().min(1, { message: "O status é obrigatório." }),
  description: z.string().optional(),
});

type ProductFormData = z.infer<typeof productFormSchema>;

interface ProductFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ProductFormData) => void;
  initialData?: ProductFormData; // Para edição
}

export default function ProductForm({ isOpen, onClose, onSubmit, initialData }: ProductFormProps) {
  const formMethods = useForm<ProductFormData>({
    resolver: zodResolver(productFormSchema),
    defaultValues: initialData || {
      title: '',
      sku: '',
      price: 0,
      availableQuantity: 0,
      category: '',
      status: '',
      description: '',
    },
  });

  useEffect(() => {
    if (initialData) {
      formMethods.reset(initialData);
    } else {
      formMethods.reset();
    }
  }, [initialData, formMethods]);

  const categoryOptions = [
    { label: 'Selecione a Categoria', value: '', disabled: true },
    { label: 'Eletrônicos', value: 'Eletrônicos' },
    { label: 'Roupas', value: 'Roupas' },
    { label: 'Casa', value: 'Casa' },
    { label: 'Esportes', value: 'Esportes' },
    { label: 'Outros', value: 'Outros' },
  ];

  const statusOptions = [
    { label: 'Selecione o Status', value: '', disabled: true },
    { label: 'Ativo', value: 'Ativo' },
    { label: 'Inativo', value: 'Inativo' },
    { label: 'Rascunho', value: 'Rascunho' },
  ];

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={initialData ? "Editar Produto" : "Adicionar Produto"}>
      <Form {...formMethods}>
        <form onSubmit={formMethods.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={formMethods.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Título</FormLabel>
                <FormControl>
                  <Input placeholder="Nome do produto" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={formMethods.control}
            name="sku"
            render={({ field }) => (
              <FormItem>
                <FormLabel>SKU</FormLabel>
                <FormControl>
                  <Input placeholder="Identificador único" {...field} />
                </FormControl>
                <FormDescription>Stock Keeping Unit</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={formMethods.control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preço</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" placeholder="0.00" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={formMethods.control}
              name="availableQuantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantidade Disponível</FormLabel>
                  <FormControl>
                    <Input type="number" placeholder="0" {...field} onChange={e => field.onChange(parseInt(e.target.value))} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <FormField
            control={formMethods.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Categoria</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione uma categoria" />
                    </SelectTrigger>
                    <SelectContent>
                      {categoryOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value} disabled={option.disabled}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={formMethods.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <FormControl>
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione um status" />
                    </SelectTrigger>
                    <SelectContent>
                      {statusOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value} disabled={option.disabled}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={formMethods.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Descrição</FormLabel>
                <FormControl>
                  <Input placeholder="Descrição detalhada do produto (opcional)" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>Cancelar</Button>
            <Button type="submit">Salvar Produto</Button>
          </div>
        </form>
      </Form>
    </Modal>
  );
}