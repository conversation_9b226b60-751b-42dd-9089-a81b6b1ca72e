import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';

interface FilterState {
  search: string;
  filters: Record<string, any>;
}

export const useUrlFilters = (defaultFilters: FilterState = { search: '', filters: {} }) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [filters, setFilters] = useState<FilterState>(defaultFilters);

  // Carregar filtros da URL ao montar o componente
  useEffect(() => {
    const urlSearch = searchParams.get('search') || '';
    const urlFilters = searchParams.get('filters');
    
    let parsedFilters = {};
    if (urlFilters) {
      try {
        parsedFilters = JSON.parse(decodeURIComponent(urlFilters));
      } catch (error) {
        console.warn('Erro ao parsear filtros da URL:', error);
      }
    }

    setFilters({
      search: urlSearch,
      filters: parsedFilters
    });
  }, [searchParams]);

  // Atualizar URL quando filtros mudarem
  const updateFilters = useCallback((newFilters: FilterState) => {
    setFilters(newFilters);
    
    const newSearchParams = new URLSearchParams();
    
    // Adicionar search se não estiver vazio
    if (newFilters.search) {
      newSearchParams.set('search', newFilters.search);
    }
    
    // Adicionar filtros se não estiver vazio
    if (Object.keys(newFilters.filters).length > 0) {
      newSearchParams.set('filters', encodeURIComponent(JSON.stringify(newFilters.filters)));
    }
    
    setSearchParams(newSearchParams);
  }, [setSearchParams]);

  // Limpar todos os filtros
  const clearFilters = useCallback(() => {
    const emptyFilters = { search: '', filters: {} };
    setFilters(emptyFilters);
    setSearchParams(new URLSearchParams());
  }, [setSearchParams]);

  // Verificar se há filtros ativos
  const hasActiveFilters = filters.search || Object.keys(filters.filters).length > 0;

  return {
    filters,
    updateFilters,
    clearFilters,
    hasActiveFilters
  };
}; 