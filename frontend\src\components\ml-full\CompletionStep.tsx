import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { useMLFullWizardStore } from '../../store/mlFullWizardStore';
import {
  CheckCircle,
  Upload,
  FileText,
  Download,
  ExternalLink,
  AlertTriangle,
  Info,
  Calendar,
  Package,
  Loader2,
  X,
  Eye
} from 'lucide-react';

interface CompletionStepProps {
  onNext: () => void;
  onBack: () => void;
}

export default function CompletionStep({ onNext, onBack }: CompletionStepProps) {
  const {
    selectedProducts,
    generatedFile,
    uploadedPDF,
    isUploading,
    shipmentData,
    uploadPDF,
    saveShipment,
    resetWizard,
    getTotalQuantity,
    getTotalValue,
  } = useMLFullWizardStore();

  const [dragActive, setDragActive] = useState(false);
  const [notes, setNotes] = useState('');
  const [expectedDelivery, setExpectedDelivery] = useState('');
  const [isCompleting, setIsCompleting] = useState(false);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (file.type === 'application/pdf') {
        uploadPDF(file);
      } else {
        alert('Por favor, selecione apenas arquivos PDF.');
      }
    }
  }, [uploadPDF]);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (file.type === 'application/pdf') {
        uploadPDF(file);
      } else {
        alert('Por favor, selecione apenas arquivos PDF.');
      }
    }
  };

  const handleComplete = async () => {
    setIsCompleting(true);
    try {
      await saveShipment({
        notes,
        expectedDelivery,
        generatedFile,
        uploadedPDF,
        products: selectedProducts,
        totalQuantity: getTotalQuantity(),
        totalValue: getTotalValue(),
      });

      // Show success and reset after delay
      setTimeout(() => {
        resetWizard();
        setIsCompleting(false);
      }, 2000);
    } catch (error) {
      console.error('Erro ao finalizar envio:', error);
      setIsCompleting(false);
    }
  };

  const renderUploadArea = () => (
    <div
      className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
        dragActive
          ? 'border-blue-500 bg-blue-50'
          : 'border-gray-300 hover:border-gray-400'
      }`}
      onDragEnter={handleDrag}
      onDragLeave={handleDrag}
      onDragOver={handleDrag}
      onDrop={handleDrop}
    >
      {isUploading ? (
        <div className="flex flex-col items-center gap-3">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <p className="text-sm text-muted-foreground">Enviando PDF...</p>
        </div>
      ) : uploadedPDF ? (
        <div className="space-y-4">
          {/* PDF Upload Success */}
          <div className="flex flex-col items-center gap-3 p-4 bg-green-50 border border-green-200 rounded-lg">
            <CheckCircle className="h-8 w-8 text-green-500" />
            <div className="text-center">
              <p className="font-medium text-green-700">{uploadedPDF.filename}</p>
              <p className="text-sm text-green-600">
                {(uploadedPDF.size / 1024 / 1024).toFixed(2)} MB • Processado com sucesso
              </p>
            </div>
          </div>

          {/* Extracted Information */}
          {uploadedPDF.extractedData && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-3 flex items-center gap-2">
                <Info className="h-4 w-4" />
                Informações Extraídas do PDF
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {uploadedPDF.extractedData.inboundId && (
                  <div>
                    <span className="font-medium text-blue-700">Inbound ID:</span>
                    <span className="ml-2 text-blue-600">#{uploadedPDF.extractedData.inboundId}</span>
                  </div>
                )}
                {uploadedPDF.extractedData.productCount && (
                  <div>
                    <span className="font-medium text-blue-700">Produtos Confirmados:</span>
                    <span className="ml-2 text-blue-600">{uploadedPDF.extractedData.productCount}</span>
                  </div>
                )}
                {uploadedPDF.extractedData.totalUnits && (
                  <div>
                    <span className="font-medium text-blue-700">Total de Unidades:</span>
                    <span className="ml-2 text-blue-600">{uploadedPDF.extractedData.totalUnits}</span>
                  </div>
                )}
                {uploadedPDF.extractedData.confirmationStatus && (
                  <div>
                    <span className="font-medium text-blue-700">Status:</span>
                    <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                      {uploadedPDF.extractedData.confirmationStatus === 'confirmed' ? 'Confirmado' : 'Pendente'}
                    </Badge>
                  </div>
                )}
              </div>

              {uploadedPDF.extractedData.preparationInstructions && (
                <div className="mt-4">
                  <span className="font-medium text-blue-700 block mb-2">Instruções de Preparação:</span>
                  <ul className="list-disc list-inside space-y-1 text-blue-600 text-sm">
                    {uploadedPDF.extractedData.preparationInstructions.slice(0, 2).map((instruction, index) => (
                      <li key={index}>{instruction}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 justify-center">
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(uploadedPDF.url, '_blank')}
            >
              <Eye className="h-4 w-4 mr-1" />
              Visualizar PDF
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Remove uploaded PDF - call store function
                const { clearUploadedPDF } = useMLFullWizardStore.getState();
                clearUploadedPDF();
              }}
            >
              <X className="h-4 w-4 mr-1" />
              Remover
            </Button>
          </div>
        </div>
      ) : (
        <div className="flex flex-col items-center gap-3">
          <Upload className="h-8 w-8 text-muted-foreground" />
          <div>
            <p className="font-medium">Arraste o PDF de confirmação aqui ou clique para selecionar</p>
            <p className="text-sm text-muted-foreground">
              Arquivo "Inbound-XXXXXXXX-preparation-instructions.pdf" retornado pelo ML Full
            </p>
          </div>
          <input
            type="file"
            accept=".pdf"
            onChange={handleFileInput}
            className="hidden"
            id="pdf-upload"
          />
          <Button asChild variant="outline">
            <label htmlFor="pdf-upload" className="cursor-pointer">
              Selecionar PDF
            </label>
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold mb-2">Finalização do Envio</h2>
        <p className="text-muted-foreground">
          Faça o upload do PDF de confirmação retornado pelo ML Full para vincular as instruções de preparação ao envio.
        </p>
      </div>

      {/* Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Resumo do Envio
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold">{selectedProducts.length}</p>
              <p className="text-sm text-muted-foreground">Produtos</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-2xl font-bold">{getTotalQuantity()}</p>
              <p className="text-sm text-muted-foreground">Unidades</p>
            </div>
            <div className="text-center p-4 bg-muted/30 rounded-lg">
              <p className="text-xl font-bold">
                R$ {getTotalValue().toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </p>
              <p className="text-sm text-muted-foreground">Valor Total</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generated File */}
      {generatedFile && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Planilha Gerada
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium text-green-800">{generatedFile.filename}</p>
                  <p className="text-sm text-green-600">
                    {generatedFile.productCount} produtos • {generatedFile.totalQuantity} unidades
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                onClick={() => {
                  // Download the generated file
                  if (generatedFile.url) {
                    window.open(generatedFile.url, '_blank');
                  }
                }}
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Próximos Passos
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">Processo ML Full:</h4>
            <ol className="list-decimal list-inside space-y-2 text-sm text-blue-700">
              <li>
                <strong>Download da planilha:</strong> Baixe a planilha Excel gerada acima
              </li>
              <li>
                <strong>Upload no ML Full:</strong> Acesse o portal e envie a planilha
              </li>
              <li>
                <strong>Confirmação do ML:</strong> O sistema processará e gerará um PDF de confirmação
              </li>
              <li>
                <strong>PDF de Preparação:</strong> Você receberá um arquivo como "Inbound-XXXXXXXX-preparation-instructions.pdf"
              </li>
              <li>
                <strong>Finalização:</strong> Faça o upload do PDF abaixo para completar o registro
              </li>
            </ol>
          </div>

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-amber-800 mb-1">Importante</h4>
                <p className="text-sm text-amber-700">
                  O PDF de preparação contém informações essenciais como o Inbound ID,
                  lista de produtos confirmada e instruções específicas para o envio.
                </p>
              </div>
            </div>
          </div>

          <Button
            variant="outline"
            className="w-full"
            onClick={() => window.open('https://vendedores.mercadolivre.com.br/full', '_blank')}
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Acessar Portal ML Full
          </Button>
        </CardContent>
      </Card>

      {/* PDF Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            PDF de Confirmação ML Full
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-2">
            Faça o upload do PDF de preparação retornado pelo Mercado Livre Full após o registro do envio
          </p>
        </CardHeader>
        <CardContent>
          {renderUploadArea()}
        </CardContent>
      </Card>

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle>Informações Adicionais</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="expected-delivery">Data Prevista de Entrega</Label>
            <Input
              id="expected-delivery"
              type="date"
              value={expectedDelivery}
              onChange={(e) => setExpectedDelivery(e.target.value)}
              className="mt-1"
            />
          </div>

          <div>
            <Label htmlFor="notes">Observações</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Adicione observações sobre este envio..."
              className="mt-1"
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button onClick={onBack} variant="outline" disabled={isCompleting}>
          Voltar
        </Button>
        <Button
          onClick={handleComplete}
          disabled={!uploadedPDF || isCompleting}
          className="flex items-center gap-2"
        >
          {isCompleting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              Finalizando...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4" />
              Finalizar Envio
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
