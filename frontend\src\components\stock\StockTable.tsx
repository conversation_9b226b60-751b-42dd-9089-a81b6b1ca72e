import React from 'react';
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from '../ui/table';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '../ui/card';
import { ChevronLeftIcon, ChevronRightIcon, ArrowUpIcon, ArrowDownIcon } from '@radix-ui/react-icons';
import type { StockItem } from '../../services/stockService';

interface SortConfig {
  column: keyof StockItem;
  direction: 'asc' | 'desc';
}

interface StockTableProps {
  stockItems: StockItem[];
  onAdjust: (stockItem: StockItem) => void; // Para ajustar quantidade
  onDelete: (stockItemId: string) => void;
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  sortConfig?: SortConfig;
  onSort: (column: keyof StockItem) => void;
}

export default function StockTable({ stockItems, onAdjust, onDelete, loading, currentPage, totalPages, onPageChange, sortConfig, onSort }: StockTableProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader><CardTitle>Carregando Itens de Estoque...</CardTitle></CardHeader>
        <CardContent>
          <div className="animate-pulse flex space-x-4">
            <div className="flex-1 space-y-4 py-1">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getSortIcon = (column: keyof StockItem) => {
    if (!sortConfig || sortConfig.column !== column) {
      return null;
    }
    if (sortConfig.direction === 'asc') {
      return <ArrowUpIcon className="ml-1 h-3 w-3 inline" />;
    } else {
      return <ArrowDownIcon className="ml-1 h-3 w-3 inline" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lista de Itens de Estoque</CardTitle>
      </CardHeader>
      <CardContent>
        {stockItems.length === 0 ? (
          <p className="text-muted-foreground">Nenhum item de estoque encontrado.</p>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('productTitle')}>Produto{getSortIcon('productTitle')}</TableHead>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('productSku')}>SKU{getSortIcon('productSku')}</TableHead>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('currentQuantity')}>Qtd. Atual{getSortIcon('currentQuantity')}</TableHead>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('location')}>Localização{getSortIcon('location')}</TableHead>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('lastUpdated')}>Última Atualização{getSortIcon('lastUpdated')}</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {stockItems.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>{item.productTitle}</TableCell>
                    <TableCell>{item.productSku}</TableCell>
                    <TableCell>{item.currentQuantity}</TableCell>
                    <TableCell>{item.location}</TableCell>
                    <TableCell>{new Date(item.lastUpdated).toLocaleString()}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm" onClick={() => onAdjust(item)}>Ajustar</Button>
                      <Button variant="destructive" size="sm" className="ml-2" onClick={() => onDelete(item.id)}>Excluir</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      {totalPages > 1 && (
        <CardFooter className="flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeftIcon className="mr-2 h-4 w-4" /> Anterior
          </Button>
          <span className="text-sm text-muted-foreground">
            Página {currentPage} de {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Próximo <ChevronRightIcon className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      )}
    </Card>
  );
} 