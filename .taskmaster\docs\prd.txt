# PRD - Magnow: Sistema de Controle Inteligente de Estoque para Mercado Livre

## 1. Visão Geral do Produto

### 1.1 Objetivo
Desenvolver um sistema web SaaS para controle inteligente de estoque integrado à API oficial do Mercado Livre, focado na otimização de envios para o Mercado Envios Full através de cálculos automáticos de gap de estoque e geração de planilhas de envio.

### 1.2 Problema a ser Resolvido
- Dificuldade em calcular manualmente o estoque ideal baseado em vendas históricas
- Controle manual ineficiente de produtos em trânsito para o Full
- Processo manual de geração de planilhas para envio ao Mercado Envios Full
- Necessidade de gerenciar múltiplas contas do Mercado Livre de forma centralizada

### 1.3 Público-Alvo
- Vendedores do Mercado Livre que utilizam o serviço Mercado Envios Full
- Empresas com múltiplas contas no Mercado Livre
- Gestores de estoque que precisam de automação e inteligência nos envios

## 2. Funcionalidades Principais (MVP - Fase 1)

### 2.1 Integração com API do Mercado Livre
**Descrição**: Sistema completo de integração com a API oficial do Mercado Livre
**Requisitos**:
- Implementar autenticação OAuth 2.0 multi-contas
- Sistema de gestão de tokens com refresh automático
- Endpoint para leitura de anúncios ativos e pausados
- Endpoint para consulta de estoque atual por SKU
- Endpoint para leitura de vendas (período configurável)
- Tratamento de rate limits e erros da API
- Cache inteligente com TTL configurável

**Critérios de Aceite**:
- Usuário pode conectar múltiplas contas do ML simultaneamente
- Sistema renova tokens automaticamente antes da expiração
- Dados são sincronizados em tempo real com cache otimizado
- Logs detalhados de todas as requisições à API

### 2.2 Cálculo de Estoque Ideal e Gap de Envio
**Descrição**: Motor de cálculo inteligente para determinar necessidades de envio
**Requisitos**:
- Cálculo de média de vendas por SKU (período configurável - 7, 15, 30 dias)
- Definição de estoque ideal baseado em cobertura de dias (configurável)
- Cálculo do gap considerando:
  - Estoque atual no ML
  - Unidades em trânsito (enviadas mas não conferidas pelo Full)
  - Estoque de segurança (configurável)
- Dashboard com visualização clara das diferenças
- Alertas para produtos com gap crítico

**Critérios de Aceite**:
- Usuário pode configurar período de análise de vendas
- Usuário pode definir dias de cobertura desejada por produto/categoria
- Sistema exibe gap de forma clara com indicadores visuais
- Cálculos são atualizados automaticamente a cada sincronização

### 2.3 Geração de Planilha para Envio ao Full
**Descrição**: Geração automática de planilhas no formato aceito pelo Mercado Livre
**Requisitos**:
- Identificação automática de produtos que precisam ser enviados
- Geração de planilha Excel/CSV no formato oficial do ML
- Organização por múltiplos armazéns (se aplicável)
- Opção de ajuste manual antes da geração final
- Histórico de planilhas geradas
- Download direto da planilha formatada

**Critérios de Aceite**:
- Planilha gerada está no formato exato aceito pelo ML
- Usuário pode revisar e ajustar quantidades antes da geração
- Sistema mantém histórico de todas as planilhas geradas
- Planilha pode ser baixada em formato Excel e CSV

### 2.4 Arquitetura SaaS Multi-tenant
**Descrição**: Arquitetura preparada para múltiplos clientes com isolamento completo
**Requisitos**:
- Isolamento completo de dados entre clientes (multi-tenant)
- Sistema de usuários com roles e permissões
- Criptografia de dados sensíveis (AES-256)
- Hash seguro de senhas (SHA-256 com salt)
- Gestão segura de tokens OAuth
- API RESTful com autenticação JWT
- Middleware de autorização por tenant

**Critérios de Aceite**:
- Dados de um cliente nunca são visíveis para outro
- Sistema de permissões funciona corretamente (admin, user, viewer)
- Todos os dados sensíveis são criptografados
- API possui autenticação e autorização robustas

### 2.5 Sistema de Logs e Monitoramento
**Descrição**: Sistema completo de auditoria e monitoramento
**Requisitos**:
- Logs de alterações de estoque
- Logs de geração de planilhas
- Logs de acessos de usuários
- Logs de requisições à API do ML
- Logs de erros e exceções
- Dashboard de monitoramento básico
- Exportação de logs para análise

**Critérios de Aceite**:
- Todos os eventos críticos são logados
- Logs incluem timestamp, usuário, ação e detalhes
- Logs são estruturados e pesquisáveis
- Dashboard mostra métricas básicas de uso

## 3. Arquitetura Técnica

### 3.1 Stack Tecnológica Sugerida
**Backend**:
- Node.js com Express.js ou Python com FastAPI
- PostgreSQL para dados relacionais
- Redis para cache e sessões
- JWT para autenticação
- Swagger para documentação da API

**Frontend**:
- React.js com TypeScript
- Tailwind CSS ou Material-UI
- Recharts para gráficos
- Axios para requisições HTTP

**Infraestrutura**:
- Docker para containerização
- AWS/Azure para hospedagem
- CI/CD com GitHub Actions
- Monitoramento com logs estruturados

### 3.2 Integrações Externas
- API oficial do Mercado Livre (OAuth 2.0)
- Serviços de email para notificações
- Sistema de backup automatizado

## 4. Requisitos Não Funcionais

### 4.1 Performance
- Tempo de resposta da API < 500ms
- Cache com TTL otimizado para reduzir chamadas à API do ML
- Suporte a pelo menos 100 usuários simultâneos no MVP

### 4.2 Segurança
- Criptografia de dados em trânsito (HTTPS)
- Criptografia de dados em repouso (AES-256)
- Autenticação multi-fator (preparado para implementação futura)
- Logs de auditoria completos

### 4.3 Escalabilidade
- Arquitetura preparada para crescimento horizontal
- Banco de dados otimizado para consultas complexas
- Sistema de cache distribuído
- API rate limiting

### 4.4 Usabilidade
- Interface intuitiva e responsiva
- Feedback visual claro sobre status de operações
- Mensagens de erro compreensíveis
- Documentação de usuário integrada

## 5. Estrutura Preparada para Fase 2 (Não Implementada no MVP)

### 5.1 Funcionalidades Futuras Estruturadas
- Suporte completo a múltiplos armazéns
- Análise de sazonalidade
- Previsão de demanda com Machine Learning
- Integração com outros marketplaces
- Sistema de alertas avançado
- API pública para integrações

### 5.2 Arquitetura Extensível
- Database schema preparado para múltiplos armazéns
- API endpoints versionados
- Sistema de plugins/módulos
- Webhooks para integrações externas

## 6. Critérios de Sucesso do MVP

### 6.1 Métricas Técnicas
- 99% de uptime
- 100% das integrações com ML funcionando
- 0 vazamentos de dados entre tenants
- Tempo de sincronização < 2 minutos

### 6.2 Métricas de Negócio
- Redução de 80% no tempo de geração de planilhas
- Precisão de 95% nos cálculos de gap de estoque
- Capacidade de gerenciar pelo menos 10 contas ML simultaneamente
- Interface com tempo de aprendizado < 30 minutos

## 7. Entregáveis do MVP

1. Sistema web completo (frontend + backend)
2. Integração funcional com API do Mercado Livre
3. Dashboard de controle de estoque
4. Gerador de planilhas de envio
5. Sistema de usuários multi-tenant
6. Documentação técnica e de usuário
7. Testes automatizados
8. Deploy em ambiente de produção

## 8. Cronograma Estimado

**Semana 1-2**: Setup inicial e arquitetura
**Semana 3-4**: Integração com API do Mercado Livre
**Semana 5-6**: Sistema de cálculo de estoque
**Semana 7-8**: Geração de planilhas
**Semana 9-10**: Sistema multi-tenant e segurança
**Semana 11-12**: Frontend e UX
**Semana 13-14**: Testes e refinamentos
**Semana 15-16**: Deploy e documentação

## 9. Riscos e Mitigações

### 9.1 Riscos Técnicos
- **Rate limits da API do ML**: Implementar cache inteligente e retry logic
- **Mudanças na API do ML**: Versioning e testes automatizados
- **Escalabilidade**: Arquitetura preparada desde o início

### 9.2 Riscos de Negócio
- **Mudanças nos requisitos**: Arquitetura flexível e iterações curtas
- **Concorrência**: Foco na diferenciação através da usabilidade
- **Adoção**: Interface intuitiva e onboarding eficiente 