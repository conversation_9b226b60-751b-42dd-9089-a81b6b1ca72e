import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Envios from '../../pages/Envios';

// Mock the ML Full Wizard store
jest.mock('../../store/mlFullWizardStore', () => ({
  useMLFullWizardStore: () => ({
    shipments: [],
    loadShipments: jest.fn(),
    updateShipmentStatus: jest.fn(),
    deleteShipment: jest.fn(),
  }),
}));

describe('Envios page', () => {
  it('renders headings and new shipment button', () => {
    render(
      <BrowserRouter>
        <Envios />
      </BrowserRouter>
    );
    expect(screen.getByText(/Envios/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Novo Envio/i })).toBeInTheDocument();
  });
});
