import { useEffect, useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Label } from '../components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select-radix';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import { RefreshCw, Download, Filter, Search, Calendar, User, Activity, AlertTriangle } from 'lucide-react';
import { useLogsStore } from '../store/logsStore';
import { useNotificationStore } from '../store/notificationStore';
import type { LogAction, LogSeverity, LogCategory } from '../types/api';

const logActions: LogAction[] = [
  'LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'EXPORT',
  'IMPORT', 'SYNC', 'CONFIG_CHANGE', 'PASSWORD_CHANGE', 'PERMISSION_CHANGE'
];

const logSeverities: LogSeverity[] = ['info', 'warning', 'error', 'critical'];

const logCategories: LogCategory[] = [
  'authentication', 'authorization', 'data', 'system', 'security', 'integration', 'configuration'
];

export default function LogsPage() {
  const {
    logs,
    total,
    hasMore,
    logsLoading,
    logsError,
    filters,
    isRealTimeEnabled,
    lastUpdate,
    loadLogs,
    loadMoreLogs,
    refreshLogs,
    setFilters,
    clearFilters,
    enableRealTime,
    disableRealTime,
    clearError,
    exportLogs,
  } = useLogsStore();

  const { addNotification } = useNotificationStore();

  // Local filter states
  const [search, setSearch] = useState('');
  const [actionFilter, setActionFilter] = useState<LogAction | 'all'>('all');
  const [severityFilter, setSeverityFilter] = useState<LogSeverity | 'all'>('all');
  const [categoryFilter, setCategoryFilter] = useState<LogCategory | 'all'>('all');
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');

  // Load logs on mount
  useEffect(() => {
    loadLogs();
  }, [loadLogs]);

  // Apply filters
  const handleApplyFilters = () => {
    setFilters({
      search: search || undefined,
      action: actionFilter === 'all' ? undefined : actionFilter,
      severity: severityFilter === 'all' ? undefined : severityFilter,
      category: categoryFilter === 'all' ? undefined : categoryFilter,
      dateFrom: dateFrom || undefined,
      dateTo: dateTo || undefined,
    });
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSearch('');
    setActionFilter('all');
    setSeverityFilter('all');
    setCategoryFilter('all');
    setDateFrom('');
    setDateTo('');
    clearFilters();
  };

  // Handle export
  const handleExport = async () => {
    try {
      await exportLogs();
      addNotification({
        title: 'Exportação Iniciada',
        message: 'Os logs estão sendo exportados. O download iniciará em breve.',
        type: 'system',
        severity: 'success',
        duration: 5000
      });
    } catch (error) {
      addNotification({
        title: 'Erro na Exportação',
        message: 'Não foi possível exportar os logs. Tente novamente.',
        type: 'error',
        severity: 'error',
        duration: 8000
      });
    }
  };

  // Handle real-time toggle
  const handleRealTimeToggle = () => {
    if (isRealTimeEnabled) {
      disableRealTime();
      addNotification({
        title: 'Tempo Real Desabilitado',
        message: 'As atualizações automáticas foram desabilitadas.',
        type: 'system',
        severity: 'info',
        duration: 3000
      });
    } else {
      enableRealTime();
      addNotification({
        title: 'Tempo Real Habilitado',
        message: 'Os logs serão atualizados automaticamente.',
        type: 'system',
        severity: 'success',
        duration: 3000
      });
    }
  };

  // Get severity badge color
  const getSeverityColor = (severity: LogSeverity): string => {
    switch (severity) {
      case 'critical': return 'bg-red-500 text-white';
      case 'error': return 'bg-red-100 text-red-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'info': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: LogCategory) => {
    switch (category) {
      case 'authentication': return <User className="h-3 w-3" />;
      case 'authorization': return <User className="h-3 w-3" />;
      case 'data': return <Activity className="h-3 w-3" />;
      case 'system': return <Activity className="h-3 w-3" />;
      case 'security': return <AlertTriangle className="h-3 w-3" />;
      case 'integration': return <Activity className="h-3 w-3" />;
      case 'configuration': return <Activity className="h-3 w-3" />;
      default: return <Activity className="h-3 w-3" />;
    }
  };

  if (logsError) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold text-foreground">Logs de Ações</h1>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
              <div>
                <h3 className="text-lg font-medium text-red-600">Erro ao Carregar Logs</h3>
                <p className="text-sm text-muted-foreground mt-1">{logsError}</p>
              </div>
              <Button onClick={() => { clearError(); loadLogs(); }}>
                Tentar Novamente
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">Logs de Ações</h1>
        <div className="flex items-center gap-2">
          {lastUpdate && (
            <span className="text-xs text-muted-foreground">
              Última atualização: {new Date(lastUpdate).toLocaleTimeString()}
            </span>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleRealTimeToggle}
            className={isRealTimeEnabled ? 'bg-green-50 border-green-200' : ''}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRealTimeEnabled ? 'animate-spin' : ''}`} />
            {isRealTimeEnabled ? 'Tempo Real Ativo' : 'Ativar Tempo Real'}
          </Button>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <Label htmlFor="search" className="flex items-center gap-2">
                <Search className="h-4 w-4" />
                Busca
              </Label>
              <Input
                id="search"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder="Usuário, ação ou recurso"
                disabled={logsLoading}
              />
            </div>

            {/* Action Filter */}
            <div className="space-y-2">
              <Label>Ação</Label>
              <Select value={actionFilter} onValueChange={(value: LogAction | 'all') => setActionFilter(value)}>
                <SelectTrigger disabled={logsLoading}>
                  <SelectValue placeholder="Todas as ações" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as ações</SelectItem>
                  {logActions.map((action) => (
                    <SelectItem key={action} value={action}>{action}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Severity Filter */}
            <div className="space-y-2">
              <Label>Severidade</Label>
              <Select value={severityFilter} onValueChange={(value: LogSeverity | 'all') => setSeverityFilter(value)}>
                <SelectTrigger disabled={logsLoading}>
                  <SelectValue placeholder="Todas as severidades" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as severidades</SelectItem>
                  {logSeverities.map((severity) => (
                    <SelectItem key={severity} value={severity}>
                      <span className="capitalize">{severity}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Category Filter */}
            <div className="space-y-2">
              <Label>Categoria</Label>
              <Select value={categoryFilter} onValueChange={(value: LogCategory | 'all') => setCategoryFilter(value)}>
                <SelectTrigger disabled={logsLoading}>
                  <SelectValue placeholder="Todas as categorias" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as categorias</SelectItem>
                  {logCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      <span className="capitalize">{category}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Date From */}
            <div className="space-y-2">
              <Label htmlFor="from-date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                De
              </Label>
              <Input
                id="from-date"
                type="date"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                disabled={logsLoading}
              />
            </div>

            {/* Date To */}
            <div className="space-y-2">
              <Label htmlFor="to-date" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Até
              </Label>
              <Input
                id="to-date"
                type="date"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                disabled={logsLoading}
              />
            </div>
          </div>

          <Separator className="my-4" />

          {/* Filter Actions */}
          <div className="flex items-center gap-2">
            <Button onClick={handleApplyFilters} disabled={logsLoading}>
              <Filter className="h-4 w-4 mr-2" />
              Aplicar Filtros
            </Button>
            <Button variant="outline" onClick={handleClearFilters} disabled={logsLoading}>
              Limpar Filtros
            </Button>
            <Button variant="outline" onClick={refreshLogs} disabled={logsLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${logsLoading ? 'animate-spin' : ''}`} />
              Atualizar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      <Card>
        <CardHeader>
          <CardTitle>
            Resultados ({total.toLocaleString('pt-BR')} {total === 1 ? 'log' : 'logs'})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {logsLoading && logs.length === 0 ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-muted-foreground" />
              <p className="text-sm text-muted-foreground mt-2">Carregando logs...</p>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto" />
              <h3 className="text-lg font-medium mt-4">Nenhum log encontrado</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Tente ajustar os filtros ou aguarde novas atividades.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Table */}
              <div className="overflow-x-auto max-h-[60vh] overflow-y-auto border rounded-lg">
                <table className="min-w-full divide-y divide-border">
                  <thead className="bg-muted sticky top-0">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Timestamp
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Usuário
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Ação
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Recurso
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Categoria
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        Severidade
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                        IP
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-background divide-y divide-border">
                    {logs.map((log) => (
                      <tr key={log.id} className="hover:bg-muted/50">
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          {new Date(log.timestamp).toLocaleString('pt-BR')}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <div>
                            <div className="font-medium">{log.userName}</div>
                            {log.userEmail && (
                              <div className="text-xs text-muted-foreground">{log.userEmail}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">
                          {log.action}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <div>
                            <div>{log.resource}</div>
                            {log.resourceId && (
                              <div className="text-xs text-muted-foreground">{log.resourceId}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(log.category)}
                            <span className="capitalize">{log.category}</span>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <Badge className={getSeverityColor(log.severity)}>
                            {log.severity}
                          </Badge>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-muted-foreground">
                          {log.ipAddress}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Load More */}
              {hasMore && (
                <div className="text-center">
                  <Button
                    variant="outline"
                    onClick={loadMoreLogs}
                    disabled={logsLoading}
                  >
                    {logsLoading ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Carregando...
                      </>
                    ) : (
                      'Carregar Mais'
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}