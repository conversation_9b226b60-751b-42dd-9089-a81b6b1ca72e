import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type { ProductWithStock } from '../types/api';
import { useStockStore } from './stockStore';
import { useMercadoLivreStore } from './mercadoLivreStore';
import { SpreadsheetGenerator } from '../utils/spreadsheetGenerator';
import { MLFullValidationSystem, type ValidationResult } from '../utils/validationSystem';
// Mock data for development
import {
  mockMLProducts,
  isProductEligibleForMLFull,
  getSuggestedQuantityForMLFull,
  getMaxQuantityForMLFull
} from '../mocks/mlProductsMock';

// Interfaces para o wizard
export interface MLFullProduct extends ProductWithStock {
  isEligible: boolean;
  suggestedQuantity: number;
  maxQuantity: number;
}

export interface ProductQuantity {
  productId: string;
  quantity: number;
  isValid: boolean;
  validationMessage?: string;
}

export interface WizardStep {
  id: number;
  name: string;
  title: string;
  description: string;
  isCompleted: boolean;
  isActive: boolean;
}

export interface GeneratedFile {
  filename: string;
  url: string;
  format: 'excel' | 'csv';
  generatedAt: string;
  productCount: number;
  totalQuantity: number;
}

export interface UploadedPDF {
  filename: string;
  url: string;
  size: number;
  uploadedAt: string;
  // Extracted information from ML Full PDF
  extractedData?: {
    inboundId?: string;
    productCount?: number;
    totalUnits?: number;
    preparationInstructions?: string[];
    trackingInfo?: string;
    confirmationStatus?: 'confirmed' | 'pending' | 'error';
  };
}

export interface ShipmentData {
  notes?: string;
  expectedDelivery?: string;
  generatedFile?: GeneratedFile;
  uploadedPDF?: UploadedPDF;
  products: string[];
  totalQuantity: number;
  totalValue: number;
}

export interface MLFullShipment {
  id: string;
  generatedFile: GeneratedFile;
  status: 'pending' | 'received' | 'processing' | 'rejected';
  createdAt: string;
  updatedAt: string;
  collectionDate?: string;
  receiptPdf?: {
    filename: string;
    url: string;
    uploadedAt: string;
  };
  products: ProductQuantity[];
  totalQuantity: number;
  totalValue: number;
  productCount: number;
  notes?: string;
  expectedDelivery?: string;
}

export interface WizardFilters {
  search: string;
  category: string;
  stockStatus: ('in_stock' | 'low_stock' | 'critical_gap')[];
  hasRecentShipments: boolean;
  minStock: number;
  maxStock: number;
}

// Interface principal do store
interface MLFullWizardState {
  // Wizard Navigation
  currentStep: number;
  steps: WizardStep[];
  canProceed: boolean;
  canGoBack: boolean;
  
  // Products Data
  availableProducts: MLFullProduct[];
  selectedProducts: string[];
  productQuantities: Record<string, ProductQuantity>;
  
  // Filters and Search
  filters: WizardFilters;
  filteredProducts: MLFullProduct[];
  
  // Generation
  isGenerating: boolean;
  generatedFile: GeneratedFile | null;
  generationError: string | null;

  // PDF Upload
  uploadedPDF: UploadedPDF | null;
  isUploading: boolean;
  uploadError: string | null;

  // Shipment Data
  shipmentData: ShipmentData | null;

  // Shipments Tracking
  shipments: MLFullShipment[];
  currentShipment: MLFullShipment | null;

  // Loading States
  isLoadingProducts: boolean;
  isUploadingReceipt: boolean;
  
  // Error Handling
  error: string | null;

  // Validation
  currentValidation: ValidationResult | null;
  stepValidations: Record<number, ValidationResult>;
  
  // Actions - Navigation
  nextStep: () => void;
  previousStep: () => void;
  goToStep: (step: number) => void;
  resetWizard: () => void;
  
  // Actions - Products
  loadProducts: () => Promise<void>;
  selectProduct: (productId: string) => void;
  unselectProduct: (productId: string) => void;
  selectAllProducts: () => void;
  unselectAllProducts: () => void;
  toggleProductSelection: (productId: string) => void;
  
  // Actions - Quantities
  updateQuantity: (productId: string, quantity: number) => void;
  validateQuantity: (productId: string, quantity: number) => boolean;
  validateAllQuantities: () => boolean;
  resetQuantities: () => void;
  applySuggestedQuantities: () => void;
  
  // Actions - Filters
  setFilters: (filters: Partial<WizardFilters>) => void;
  clearFilters: () => void;
  applyFilters: () => void;
  
  // Actions - Generation
  generateSpreadsheet: () => Promise<void>;
  downloadFile: () => void;

  // Actions - PDF Upload
  uploadPDF: (file: File) => Promise<void>;
  clearUploadedPDF: () => void;
  extractPDFData: (file: File) => UploadedPDF['extractedData'];

  // Actions - Shipments
  saveShipment: (data: ShipmentData) => Promise<void>;
  createShipment: () => Promise<void>;
  uploadReceipt: (shipmentId: string, file: File) => Promise<void>;
  updateShipmentStatus: (shipmentId: string, status: MLFullShipment['status']) => void;
  deleteShipment: (shipmentId: string) => void;
  loadShipments: () => Promise<void>;
  
  // Actions - Validation
  validateCurrentStep: () => ValidationResult;
  validateStep: (step: number) => ValidationResult;
  clearValidation: () => void;

  // Actions - Utility
  clearError: () => void;
  getSelectedProductsData: () => MLFullProduct[];
  getTotalQuantity: () => number;
  getTotalValue: () => number;
  getStepProgress: () => number;
}

const initialSteps: WizardStep[] = [
  {
    id: 1,
    name: 'selection',
    title: 'Seleção de Produtos',
    description: 'Escolha os produtos para envio ao ML Full',
    isCompleted: false,
    isActive: true,
  },
  {
    id: 2,
    name: 'configuration',
    title: 'Configuração de Quantidades',
    description: 'Defina as quantidades para cada produto',
    isCompleted: false,
    isActive: false,
  },
  {
    id: 3,
    name: 'review',
    title: 'Revisão e Geração',
    description: 'Revise os dados e gere a planilha',
    isCompleted: false,
    isActive: false,
  },
  {
    id: 4,
    name: 'completion',
    title: 'Finalização',
    description: 'Download e upload do comprovante',
    isCompleted: false,
    isActive: false,
  },
];

const initialFilters: WizardFilters = {
  search: '',
  category: '',
  stockStatus: [],
  hasRecentShipments: false,
  minStock: 0,
  maxStock: 1000,
};

export const useMLFullWizardStore = create<MLFullWizardState>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentStep: 1,
      steps: initialSteps,
      canProceed: false,
      canGoBack: false,
      
      availableProducts: [],
      selectedProducts: [],
      productQuantities: {},
      
      filters: initialFilters,
      filteredProducts: [],
      
      isGenerating: false,
      generatedFile: null,
      generationError: null,

      uploadedPDF: null,
      isUploading: false,
      uploadError: null,

      shipmentData: null,

      shipments: [],
      currentShipment: null,

      isLoadingProducts: false,
      isUploadingReceipt: false,

      error: null,

      currentValidation: null,
      stepValidations: {},
      
      // Navigation Actions
      nextStep: () => {
        const { currentStep, steps } = get();
        if (currentStep < steps.length) {
          const newStep = currentStep + 1;
          const updatedSteps = steps.map(step => ({
            ...step,
            isCompleted: step.id < newStep,
            isActive: step.id === newStep,
          }));
          
          set({
            currentStep: newStep,
            steps: updatedSteps,
            canGoBack: newStep > 1,
            canProceed: get().validateCurrentStep(newStep),
          });
        }
      },
      
      previousStep: () => {
        const { currentStep, steps } = get();
        if (currentStep > 1) {
          const newStep = currentStep - 1;
          const updatedSteps = steps.map(step => ({
            ...step,
            isCompleted: step.id < newStep,
            isActive: step.id === newStep,
          }));
          
          set({
            currentStep: newStep,
            steps: updatedSteps,
            canGoBack: newStep > 1,
            canProceed: get().validateCurrentStep(newStep),
          });
        }
      },
      
      goToStep: (step: number) => {
        const { steps } = get();
        if (step >= 1 && step <= steps.length) {
          const updatedSteps = steps.map(s => ({
            ...s,
            isCompleted: s.id < step,
            isActive: s.id === step,
          }));
          
          set({
            currentStep: step,
            steps: updatedSteps,
            canGoBack: step > 1,
            canProceed: get().validateCurrentStep(step),
          });
        }
      },
      
      resetWizard: () => {
        set({
          currentStep: 1,
          steps: initialSteps,
          canProceed: false,
          canGoBack: false,
          selectedProducts: [],
          productQuantities: {},
          filters: initialFilters,
          generatedFile: null,
          generationError: null,
          currentShipment: null,
          error: null,
        });
        get().applyFilters();
      },
      
      // Products Actions
      loadProducts: async () => {
        set({ isLoadingProducts: true, error: null });
        try {
          // MOCK DATA FOR DEVELOPMENT
          console.log('MOCK: Loading products for ML Full wizard...');

          // Simulate loading delay
          await new Promise(resolve => setTimeout(resolve, 1000));

          // Transform mock products to MLFullProduct format
          const mlFullProducts: MLFullProduct[] = mockMLProducts.map(product => {
            const isEligible = isProductEligibleForMLFull(product);
            const suggestedQuantity = getSuggestedQuantityForMLFull(product);
            const maxQuantity = getMaxQuantityForMLFull(product);

            return {
              ...product,
              isEligible,
              suggestedQuantity,
              maxQuantity,
            };
          });

          console.log(`MOCK: Loaded ${mlFullProducts.length} products, ${mlFullProducts.filter(p => p.isEligible).length} eligible for ML Full`);

          set({
            availableProducts: mlFullProducts,
            isLoadingProducts: false,
          });

          get().applyFilters();

          // COMMENTED FOR MOCK DEVELOPMENT - Real ML Store integration
          // const mlStore = useMercadoLivreStore.getState();
          // const stockStore = useStockStore.getState();
          //
          // // Ensure we have products loaded
          // if (mlStore.products.length === 0 && mlStore.selectedAccount) {
          //   await mlStore.loadProductsWithStock(mlStore.selectedAccount.id);
          // }
          //
          // // Transform products to MLFullProduct format
          // const mlFullProducts: MLFullProduct[] = mlStore.products.map(product => {
          //   // Determine eligibility for ML Full
          //   const isEligible = product.status === 'active' &&
          //                     product.availableQuantity > 0 &&
          //                     product.price > 0;
          //
          //   // Calculate suggested quantity based on gap or default logic
          //   const suggestedQuantity = Math.max(
          //     product.stockCalculation?.gap || 0,
          //     Math.min(10, Math.floor(product.availableQuantity * 0.3)) // 30% of available stock, max 10
          //   );
          //
          //   // Max quantity is available stock
          //   const maxQuantity = product.availableQuantity;
          //
          //   return {
          //     ...product,
          //     isEligible,
          //     suggestedQuantity,
          //     maxQuantity,
          //   };
          // });

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar produtos';
          set({
            error: errorMessage,
            isLoadingProducts: false,
          });
        }
      },
      
      selectProduct: (productId: string) => {
        const { selectedProducts } = get();
        if (!selectedProducts.includes(productId)) {
          set({
            selectedProducts: [...selectedProducts, productId],
          });
          get().updateNavigationState();
        }
      },
      
      unselectProduct: (productId: string) => {
        const { selectedProducts, productQuantities } = get();
        const newSelected = selectedProducts.filter(id => id !== productId);
        const newQuantities = { ...productQuantities };
        delete newQuantities[productId];
        
        set({
          selectedProducts: newSelected,
          productQuantities: newQuantities,
        });
        get().updateNavigationState();
      },
      
      selectAllProducts: () => {
        const { filteredProducts } = get();
        const eligibleProducts = filteredProducts.filter(p => p.isEligible);
        set({
          selectedProducts: eligibleProducts.map(p => p.id),
        });
        get().updateNavigationState();
      },
      
      unselectAllProducts: () => {
        set({
          selectedProducts: [],
          productQuantities: {},
        });
        get().updateNavigationState();
      },
      
      toggleProductSelection: (productId: string) => {
        const { selectedProducts } = get();
        if (selectedProducts.includes(productId)) {
          get().unselectProduct(productId);
        } else {
          get().selectProduct(productId);
        }
      },
      
      // Quantities Actions
      updateQuantity: (productId: string, quantity: number) => {
        const { productQuantities } = get();
        const isValid = get().validateQuantity(productId, quantity);
        const validationMessage = isValid ? undefined : 'Quantidade inválida';

        set({
          productQuantities: {
            ...productQuantities,
            [productId]: {
              productId,
              quantity,
              isValid,
              validationMessage,
            },
          },
        });
        get().updateNavigationState();
      },

      validateQuantity: (productId: string, quantity: number): boolean => {
        const { availableProducts } = get();
        const product = availableProducts.find(p => p.id === productId);
        if (!product) return false;

        return quantity >= 1 && quantity <= product.maxQuantity && Number.isInteger(quantity);
      },

      validateAllQuantities: (): boolean => {
        const { selectedProducts, productQuantities } = get();

        // Todos os produtos selecionados devem ter quantidade definida
        for (const productId of selectedProducts) {
          const quantity = productQuantities[productId];
          if (!quantity || !quantity.isValid) {
            return false;
          }
        }

        return selectedProducts.length > 0;
      },

      resetQuantities: () => {
        set({ productQuantities: {} });
        get().updateNavigationState();
      },

      applySuggestedQuantities: () => {
        const { selectedProducts, availableProducts } = get();
        const newQuantities: Record<string, ProductQuantity> = {};

        selectedProducts.forEach(productId => {
          const product = availableProducts.find(p => p.id === productId);
          if (product) {
            newQuantities[productId] = {
              productId,
              quantity: product.suggestedQuantity,
              isValid: true,
            };
          }
        });

        set({ productQuantities: newQuantities });
        get().updateNavigationState();
      },

      // Filters Actions
      setFilters: (newFilters: Partial<WizardFilters>) => {
        const { filters } = get();
        set({
          filters: { ...filters, ...newFilters },
        });
        get().applyFilters();
      },

      clearFilters: () => {
        set({ filters: initialFilters });
        get().applyFilters();
      },

      applyFilters: () => {
        const { availableProducts, filters } = get();
        let filtered = [...availableProducts];

        // Apply search filter
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filtered = filtered.filter(product =>
            product.title.toLowerCase().includes(searchLower) ||
            product.sku.toLowerCase().includes(searchLower)
          );
        }

        // Apply category filter
        if (filters.category) {
          filtered = filtered.filter(product => product.categoryId === filters.category);
        }

        // Apply stock status filter
        if (filters.stockStatus.length > 0) {
          filtered = filtered.filter(product => {
            const stockStatus = product.currentStock === 0 ? 'out_of_stock' :
                              product.currentStock <= 10 ? 'low_stock' :
                              product.stockCalculation?.gap > 0 ? 'critical_gap' : 'in_stock';
            return filters.stockStatus.includes(stockStatus as any);
          });
        }

        // Apply stock range filter
        filtered = filtered.filter(product =>
          product.currentStock >= filters.minStock &&
          product.currentStock <= filters.maxStock
        );

        set({ filteredProducts: filtered });
      },

      // Generation Actions
      generateSpreadsheet: async () => {
        set({ isGenerating: true, generationError: null });
        try {
          const selectedData = get().getSelectedProductsData();
          const { productQuantities } = get();

          // Gerar planilha usando o SpreadsheetGenerator (sempre Excel para ML Full)
          const result = await SpreadsheetGenerator.generateMLFullSpreadsheet({
            products: selectedData,
            quantities: productQuantities,
          });

          const generatedFile: GeneratedFile = {
            filename: result.filename,
            url: result.url,
            format: 'excel', // Always Excel for ML Full
            generatedAt: new Date().toISOString(),
            productCount: selectedData.length,
            totalQuantity: get().getTotalQuantity(),
          };

          set({
            generatedFile,
            isGenerating: false,
          });

          get().updateNavigationState();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao gerar planilha';
          set({
            generationError: errorMessage,
            isGenerating: false,
          });
        }
      },

      downloadFile: () => {
        const { generatedFile } = get();
        if (generatedFile && generatedFile.url) {
          // Criar link de download
          const link = document.createElement('a');
          link.href = generatedFile.url;
          link.download = generatedFile.filename;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        }
      },

      // PDF Upload Actions
      uploadPDF: async (file: File) => {
        set({ isUploading: true, uploadError: null });
        try {
          // Simulate PDF processing and data extraction
          await new Promise(resolve => setTimeout(resolve, 2000));

          // Extract information from filename (e.g., "Inbound-47818702-preparation-instructions.pdf")
          const extractedData = get().extractPDFData(file);

          const uploadedPDF: UploadedPDF = {
            filename: file.name,
            url: URL.createObjectURL(file), // URL temporária para preview
            size: file.size,
            uploadedAt: new Date().toISOString(),
            extractedData,
          };

          set({
            uploadedPDF,
            isUploading: false,
          });

          get().updateNavigationState();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao fazer upload do PDF';
          set({
            uploadError: errorMessage,
            isUploading: false,
          });
        }
      },

      clearUploadedPDF: () => {
        const { uploadedPDF } = get();
        if (uploadedPDF?.url.startsWith('blob:')) {
          URL.revokeObjectURL(uploadedPDF.url);
        }
        set({ uploadedPDF: null, uploadError: null });
        get().updateNavigationState();
      },

      extractPDFData: (file: File): UploadedPDF['extractedData'] => {
        // Extract Inbound ID from filename pattern: "Inbound-XXXXXXXX-preparation-instructions.pdf"
        const inboundIdMatch = file.name.match(/Inbound-(\d+)-/);
        const inboundId = inboundIdMatch ? inboundIdMatch[1] : undefined;

        // Get current wizard data for validation
        const { selectedProducts, getTotalQuantity } = get();
        const productCount = selectedProducts.length;
        const totalUnits = getTotalQuantity();

        // Mock preparation instructions based on the PDF content
        const preparationInstructions = [
          'Cada unidade deve ter uma embalagem que a cubra completamente para estar protegida no centro de distribuição.',
          'Se a unidade não tiver embalagem, adicione uma considerando que o estoque de consumo recorrente não costuma precisar de embalagem adicional.',
          'O estoque frágil requer uma embalagem reforçada.',
          'Revise a embalagem para todos os seus produtos.'
        ];

        return {
          inboundId,
          productCount,
          totalUnits,
          preparationInstructions,
          trackingInfo: inboundId ? `Frete #${inboundId}` : undefined,
          confirmationStatus: 'confirmed' as const,
        };
      },

      // Shipment Actions
      saveShipment: async (data: ShipmentData) => {
        try {
          // TODO: Implementar salvamento real do envio
          // Por enquanto, simular salvamento
          await new Promise(resolve => setTimeout(resolve, 1000));

          set({ shipmentData: data });

          // Create shipment record
          const shipment: MLFullShipment = {
            id: `shipment-${Date.now()}`,
            generatedFile: data.generatedFile!,
            status: 'pending',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            products: data.products,
            productCount: data.products.length,
            totalQuantity: data.totalQuantity,
            totalValue: data.totalValue,
            notes: data.notes,
            expectedDelivery: data.expectedDelivery,
          };

          const { shipments } = get();
          set({
            shipments: [shipment, ...shipments],
            currentShipment: shipment,
          });

          console.log('Shipment saved:', shipment);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao salvar envio';
          set({ error: errorMessage });
          throw error;
        }
      },

      // Shipments Actions
      createShipment: async () => {
        const { generatedFile, selectedProducts, productQuantities } = get();
        if (!generatedFile) return;

        try {
          const shipment: MLFullShipment = {
            id: `shipment-${Date.now()}`,
            generatedFile,
            status: 'pending',
            createdAt: new Date().toISOString(),
            products: selectedProducts.map(productId => productQuantities[productId]),
          };

          // TODO: Salvar no backend
          const { shipments } = get();
          set({
            shipments: [...shipments, shipment],
            currentShipment: shipment,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao criar envio';
          set({ error: errorMessage });
        }
      },

      uploadReceipt: async (shipmentId: string, file: File) => {
        set({ isUploadingReceipt: true, error: null });
        try {
          // TODO: Implementar upload real
          await new Promise(resolve => setTimeout(resolve, 1000));

          const { shipments } = get();
          const updatedShipments = shipments.map(shipment =>
            shipment.id === shipmentId
              ? {
                  ...shipment,
                  status: 'confirmed' as const,
                  confirmedAt: new Date().toISOString(),
                  receiptPdf: {
                    filename: file.name,
                    url: '#', // URL será gerada pelo backend
                    uploadedAt: new Date().toISOString(),
                  },
                }
              : shipment
          );

          set({
            shipments: updatedShipments,
            isUploadingReceipt: false,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao fazer upload';
          set({
            error: errorMessage,
            isUploadingReceipt: false,
          });
        }
      },

      updateShipmentStatus: (shipmentId: string, status: MLFullShipment['status']) => {
        const { shipments } = get();
        const updatedShipments = shipments.map(shipment =>
          shipment.id === shipmentId
            ? {
                ...shipment,
                status,
                updatedAt: new Date().toISOString(),
                collectionDate: status === 'received' ? new Date().toISOString() : shipment.collectionDate
              }
            : shipment
        );
        set({ shipments: updatedShipments });
      },

      deleteShipment: (shipmentId: string) => {
        const { shipments } = get();
        const updatedShipments = shipments.filter(shipment => shipment.id !== shipmentId);
        set({ shipments: updatedShipments });
      },

      loadShipments: async () => {
        try {
          // MOCK DATA FOR DEVELOPMENT - Add sample shipments if none exist
          const { shipments } = get();
          if (shipments.length === 0) {
            const mockShipments: MLFullShipment[] = [
              {
                id: 'shipment-001',
                generatedFile: {
                  filename: 'envio-janeiro-2025.xlsx',
                  url: '/mock/envio-janeiro-2025.xlsx',
                  format: 'excel',
                  size: 45678,
                  createdAt: '2025-01-15T10:30:00Z'
                },
                status: 'received',
                createdAt: '2025-01-15T10:30:00Z',
                updatedAt: '2025-01-16T14:20:00Z',
                collectionDate: '2025-01-16T14:20:00Z',
                products: [
                  { productId: 'MLB123456', quantity: 50, price: 29.90 },
                  { productId: 'MLB789012', quantity: 30, price: 45.50 }
                ],
                totalQuantity: 80,
                totalValue: 2862.00,
                productCount: 2,
                notes: 'Primeiro envio do ano - produtos de alta rotação',
                expectedDelivery: '2025-01-20T00:00:00Z'
              },
              {
                id: 'shipment-002',
                generatedFile: {
                  filename: 'envio-eletronicos-2025.xlsx',
                  url: '/mock/envio-eletronicos-2025.xlsx',
                  format: 'excel',
                  size: 67890,
                  createdAt: '2025-01-18T09:15:00Z'
                },
                status: 'pending',
                createdAt: '2025-01-18T09:15:00Z',
                updatedAt: '2025-01-18T09:15:00Z',
                products: [
                  { productId: 'MLB345678', quantity: 25, price: 89.90 },
                  { productId: 'MLB901234', quantity: 15, price: 129.90 },
                  { productId: 'MLB567890', quantity: 40, price: 19.90 }
                ],
                totalQuantity: 80,
                totalValue: 4045.00,
                productCount: 3,
                notes: 'Eletrônicos e acessórios - aguardando coleta',
                expectedDelivery: '2025-01-25T00:00:00Z'
              },
              {
                id: 'shipment-003',
                generatedFile: {
                  filename: 'envio-casa-jardim-2025.csv',
                  url: '/mock/envio-casa-jardim-2025.csv',
                  format: 'csv',
                  size: 23456,
                  createdAt: '2025-01-17T16:45:00Z'
                },
                status: 'processing',
                createdAt: '2025-01-17T16:45:00Z',
                updatedAt: '2025-01-18T08:30:00Z',
                collectionDate: '2025-01-18T08:30:00Z',
                products: [
                  { productId: 'MLB111222', quantity: 60, price: 15.90 },
                  { productId: 'MLB333444', quantity: 35, price: 32.50 }
                ],
                totalQuantity: 95,
                totalValue: 2091.50,
                productCount: 2,
                notes: 'Produtos para casa e jardim - em processamento no centro de distribuição'
              }
            ];

            set({ shipments: mockShipments });
            console.log('Mock shipments loaded:', mockShipments.length);
          } else {
            console.log('Existing shipments found:', shipments.length);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar envios';
          set({ error: errorMessage });
        }
      },

      // Utility Actions
      clearError: () => {
        set({ error: null, generationError: null });
      },

      getSelectedProductsData: (): MLFullProduct[] => {
        const { availableProducts, selectedProducts } = get();
        return availableProducts.filter(product => selectedProducts.includes(product.id));
      },

      getTotalQuantity: (): number => {
        const { productQuantities } = get();
        return Object.values(productQuantities).reduce((total, pq) => total + pq.quantity, 0);
      },

      getTotalValue: (): number => {
        const { availableProducts, productQuantities } = get();
        return Object.values(productQuantities).reduce((total, pq) => {
          const product = availableProducts.find(p => p.id === pq.productId);
          return total + (product ? product.price * pq.quantity : 0);
        }, 0);
      },

      getStepProgress: (): number => {
        const { currentStep, steps } = get();
        return (currentStep / steps.length) * 100;
      },

      // Validation methods
      validateCurrentStep: (): ValidationResult => {
        const { currentStep } = get();
        return get().validateStep(currentStep);
      },

      validateStep: (step: number): ValidationResult => {
        const state = get();

        const validationData = {
          products: state.getSelectedProductsData(),
          quantities: state.productQuantities,
          format: 'excel' as const,
          generatedFile: state.generatedFile !== null,
          uploadedPDF: state.uploadedPDF !== null,
          expectedDelivery: undefined,
          notes: undefined,
        };

        const result = MLFullValidationSystem.validateWizardStep(step, validationData);

        // Store validation result
        set(state => ({
          currentValidation: result,
          stepValidations: {
            ...state.stepValidations,
            [step]: result
          }
        }));

        return result;
      },

      clearValidation: () => {
        set({
          currentValidation: null,
          stepValidations: {}
        });
      },

      // Helper methods
      validateCurrentStepBoolean: (step: number): boolean => {
        const result = get().validateStep(step);
        return result.isValid;
      },

      updateNavigationState: () => {
        const { currentStep } = get();
        set({
          canProceed: get().validateCurrentStep(currentStep),
        });
      },
    }),
    {
      name: 'ml-full-wizard-store',
    }
  )
);

export default useMLFullWizardStore;
