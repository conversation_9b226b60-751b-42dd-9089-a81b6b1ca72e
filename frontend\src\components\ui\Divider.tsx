 
import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const dividerVariants = cva(
  'shrink-0 bg-design-neutral-200',
  {
    variants: {
      orientation: {
        horizontal: 'w-full h-px',
        vertical: 'h-full w-px',
      },
      spacing: {
        none: 'my-0 mx-0',
        xs: 'my-1 mx-1',
        sm: 'my-2 mx-2',
        md: 'my-4 mx-4',
        lg: 'my-8 mx-8',
        xl: 'my-12 mx-12',
      },
    },
    defaultVariants: {
      orientation: 'horizontal',
      spacing: 'md',
    },
  }
);

export interface DividerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof dividerVariants> {
  asChild?: boolean;
}

const Divider = React.forwardRef<HTMLDivElement, DividerProps>(
  ({ className, orientation, spacing, asChild = false, ...props }, ref) => {
    const Comp = asChild ? 'div' : 'div';
    return (
      <Comp
        className={cn(dividerVariants({ orientation, spacing, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Divider.displayName = 'Divider';

export { Divider, dividerVariants }; 
