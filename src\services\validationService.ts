/**
 * Serviço de Validação Aprimorado
 * Sistema Magnow - Validações Robustas para Planilhas
 */

import {
  SpreadsheetTemplate,
  TemplateColumn,
  ValidationRule,
  ShippingProduct,
  SpreadsheetError,
  SpreadsheetWarning
} from '../types/stock';
import { logger } from '../utils/logger';

interface ValidationContext {
  tenantId: string;
  templateId?: string;
  strictMode?: boolean;
  skipWarnings?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  errors: SpreadsheetError[];
  warnings: SpreadsheetWarning[];
  validatedData: any[];
  stats: {
    totalRecords: number;
    validRecords: number;
    errorRecords: number;
    warningRecords: number;
  };
}

interface FieldValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  normalizedValue?: any;
}

class ValidationService {
  private customValidators: Map<string, (value: any, context: any) => FieldValidationResult> = new Map();

  constructor() {
    this.initializeCustomValidators();
  }

  /**
   * Valida dados usando template e contexto
   */
  async validateData(
    data: any[],
    template: SpreadsheetTemplate,
    context: ValidationContext
  ): Promise<ValidationResult> {
    const startTime = Date.now();
    
    try {
      logger.info('Iniciando validação de dados', {
        recordCount: data.length,
        templateId: template.id,
        tenantId: context.tenantId
      });

      const errors: SpreadsheetError[] = [];
      const warnings: SpreadsheetWarning[] = [];
      const validatedData: any[] = [];
      
      let validRecords = 0;
      let errorRecords = 0;
      let warningRecords = 0;

      // Validar estrutura do template
      const templateValidation = this.validateTemplate(template);
      if (!templateValidation.isValid) {
        errors.push(...templateValidation.errors);
        warnings.push(...templateValidation.warnings);
      }

      // Validar cada registro
      for (let index = 0; index < data.length; index++) {
        const record = data[index];
        const recordValidation = await this.validateRecord(
          record,
          template,
          context,
          index + 1
        );

        if (recordValidation.errors.length > 0) {
          errors.push(...recordValidation.errors);
          errorRecords++;
        }

        if (recordValidation.warnings.length > 0) {
          warnings.push(...recordValidation.warnings);
          warningRecords++;
        }

        if (recordValidation.errors.length === 0) {
          validatedData.push(recordValidation.normalizedData);
          validRecords++;
        }
      }

      // Validações globais
      const globalValidation = await this.validateGlobalRules(
        validatedData,
        template,
        context
      );
      
      errors.push(...globalValidation.errors);
      warnings.push(...globalValidation.warnings);

      const result: ValidationResult = {
        isValid: errors.length === 0,
        errors,
        warnings: context.skipWarnings ? [] : warnings,
        validatedData,
        stats: {
          totalRecords: data.length,
          validRecords,
          errorRecords,
          warningRecords
        }
      };

      logger.info('Validação concluída', {
        processingTime: Date.now() - startTime,
        isValid: result.isValid,
        errorCount: errors.length,
        warningCount: warnings.length
      });

      return result;

    } catch (error) {
      logger.error('Erro durante validação:', error);
      
      return {
        isValid: false,
        errors: [{
          type: 'system',
          severity: 'error',
          code: 'VALIDATION_SYSTEM_ERROR',
          message: 'Erro interno do sistema de validação',
          details: error instanceof Error ? error.message : 'Erro desconhecido'
        }],
        warnings: [],
        validatedData: [],
        stats: {
          totalRecords: data.length,
          validRecords: 0,
          errorRecords: data.length,
          warningRecords: 0
        }
      };
    }
  }

  /**
   * Valida um registro individual
   */
  private async validateRecord(
    record: any,
    template: SpreadsheetTemplate,
    context: ValidationContext,
    rowNumber: number
  ): Promise<{
    errors: SpreadsheetError[];
    warnings: SpreadsheetWarning[];
    normalizedData: any;
  }> {
    const errors: SpreadsheetError[] = [];
    const warnings: SpreadsheetWarning[] = [];
    const normalizedData: any = { ...record };

    // Validar cada coluna
    for (const column of template.columns) {
      const fieldValidation = await this.validateField(
        record[column.name],
        column,
        record,
        context,
        rowNumber
      );

      if (!fieldValidation.isValid) {
        fieldValidation.errors.forEach(error => {
          errors.push({
            type: 'validation',
            severity: 'error',
            code: 'FIELD_VALIDATION_ERROR',
            message: error,
            details: `Linha ${rowNumber}, Campo: ${column.displayName}`
          });
        });
      }

      fieldValidation.warnings.forEach(warning => {
        warnings.push({
          type: 'missing_data',
          message: warning,
          affectedSku: '',
          affectedField: column.displayName
        });
      });

      // Aplicar valor normalizado
      if (fieldValidation.normalizedValue !== undefined) {
        normalizedData[column.name] = fieldValidation.normalizedValue;
      }
    }

    // Validar regras customizadas do template
    for (const rule of template.validations) {
      const ruleValidation = await this.validateCustomRule(
        record,
        rule,
        context,
        rowNumber
      );

      if (!ruleValidation.isValid) {
        if (rule.severity === 'error') {
          errors.push({
            type: 'validation',
            severity: 'error',
            code: rule.id,
            message: rule.message,
            details: `Linha ${rowNumber}, Regra: ${rule.name}`
          });
        } else {
          warnings.push({
            type: 'missing_data',
            message: rule.message,
            affectedSku: '',
            affectedField: rule.name
          });
        }
      }
    }

    return { errors, warnings, normalizedData };
  }

  /**
   * Valida um campo específico
   */
  private async validateField(
    value: any,
    column: TemplateColumn,
    record: any,
    context: ValidationContext,
    rowNumber: number
  ): Promise<FieldValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    let normalizedValue = value;

    try {
      // Validação de campo obrigatório
      if (column.isRequired && this.isEmpty(value)) {
        errors.push(`${column.displayName} é obrigatório`);
        return { isValid: false, errors, warnings };
      }

      // Se campo está vazio e não é obrigatório, pular validações
      if (this.isEmpty(value) && !column.isRequired) {
        return { isValid: true, errors, warnings, normalizedValue: null };
      }

      // Validação de tipo de dados
      const typeValidation = this.validateDataType(value, column.dataType);
      if (!typeValidation.isValid) {
        errors.push(...typeValidation.errors);
        return { isValid: false, errors, warnings };
      }
      normalizedValue = typeValidation.normalizedValue;

      // Validações específicas da coluna
      if (column.validation) {
        const columnValidation = this.validateColumnRules(
          normalizedValue,
          column.validation,
          column.displayName
        );
        errors.push(...columnValidation.errors);
        warnings.push(...columnValidation.warnings);
      }

      // Validações customizadas
      if (this.customValidators.has(column.name)) {
        const customValidator = this.customValidators.get(column.name)!;
        const customValidation = customValidator(normalizedValue, {
          record,
          column,
          context,
          rowNumber
        });
        
        errors.push(...customValidation.errors);
        warnings.push(...customValidation.warnings);
        
        if (customValidation.normalizedValue !== undefined) {
          normalizedValue = customValidation.normalizedValue;
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        normalizedValue
      };

    } catch (error) {
      logger.error(`Erro na validação do campo ${column.name}:`, error);
      errors.push(`Erro interno na validação de ${column.displayName}`);
      return { isValid: false, errors, warnings };
    }
  }

  /**
   * Valida tipo de dados
   */
  private validateDataType(value: any, dataType: string): {
    isValid: boolean;
    errors: string[];
    normalizedValue: any;
  } {
    const errors: string[] = [];
    let normalizedValue = value;

    switch (dataType) {
      case 'string':
        normalizedValue = String(value).trim();
        break;

      case 'number':
        if (typeof value === 'string') {
          // Tentar converter string para número
          const cleaned = value.replace(/[^0-9.,\-]/g, '').replace(',', '.');
          const parsed = parseFloat(cleaned);
          
          if (isNaN(parsed)) {
            errors.push('Deve ser um número válido');
            return { isValid: false, errors, normalizedValue: value };
          }
          
          normalizedValue = parsed;
        } else if (typeof value !== 'number' || isNaN(value)) {
          errors.push('Deve ser um número válido');
          return { isValid: false, errors, normalizedValue: value };
        }
        break;

      case 'date':
        const date = new Date(value);
        if (isNaN(date.getTime())) {
          errors.push('Deve ser uma data válida');
          return { isValid: false, errors, normalizedValue: value };
        }
        normalizedValue = date;
        break;

      case 'boolean':
        if (typeof value === 'string') {
          const lower = value.toLowerCase();
          if (['true', '1', 'sim', 'yes', 'verdadeiro'].includes(lower)) {
            normalizedValue = true;
          } else if (['false', '0', 'não', 'no', 'falso'].includes(lower)) {
            normalizedValue = false;
          } else {
            errors.push('Deve ser verdadeiro ou falso');
            return { isValid: false, errors, normalizedValue: value };
          }
        } else if (typeof value !== 'boolean') {
          errors.push('Deve ser verdadeiro ou falso');
          return { isValid: false, errors, normalizedValue: value };
        }
        break;

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(String(value))) {
          errors.push('Deve ser um email válido');
          return { isValid: false, errors, normalizedValue: value };
        }
        normalizedValue = String(value).toLowerCase().trim();
        break;

      default:
        // Tipo desconhecido, manter como string
        normalizedValue = String(value);
    }

    return { isValid: true, errors, normalizedValue };
  }

  /**
   * Valida regras específicas da coluna
   */
  private validateColumnRules(
    value: any,
    validation: any,
    fieldName: string
  ): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Validação de comprimento mínimo
    if (validation.minLength && String(value).length < validation.minLength) {
      errors.push(`${fieldName} deve ter pelo menos ${validation.minLength} caracteres`);
    }

    // Validação de comprimento máximo
    if (validation.maxLength && String(value).length > validation.maxLength) {
      errors.push(`${fieldName} deve ter no máximo ${validation.maxLength} caracteres`);
    }

    // Validação de padrão (regex)
    if (validation.pattern) {
      try {
        const regex = new RegExp(validation.pattern);
        if (!regex.test(String(value))) {
          errors.push(`${fieldName} não atende ao formato esperado`);
        }
      } catch (error) {
        warnings.push(`Padrão de validação inválido para ${fieldName}`);
      }
    }

    // Validação de valor mínimo
    if (validation.min !== undefined && Number(value) < validation.min) {
      errors.push(`${fieldName} deve ser pelo menos ${validation.min}`);
    }

    // Validação de valor máximo
    if (validation.max !== undefined && Number(value) > validation.max) {
      errors.push(`${fieldName} deve ser no máximo ${validation.max}`);
    }

    // Validação de valores permitidos
    if (validation.allowedValues && Array.isArray(validation.allowedValues)) {
      if (!validation.allowedValues.includes(value)) {
        errors.push(`${fieldName} deve ser um dos valores: ${validation.allowedValues.join(', ')}`);
      }
    }

    // Validação de valores proibidos
    if (validation.forbiddenValues && Array.isArray(validation.forbiddenValues)) {
      if (validation.forbiddenValues.includes(value)) {
        errors.push(`${fieldName} não pode ser: ${validation.forbiddenValues.join(', ')}`);
      }
    }

    return { errors, warnings };
  }

  /**
   * Valida regra customizada
   */
  private async validateCustomRule(
    record: any,
    rule: ValidationRule,
    context: ValidationContext,
    rowNumber: number
  ): Promise<{ isValid: boolean }> {
    try {
      switch (rule.type) {
        case 'required':
          const field = record[rule.field];
          return { isValid: !this.isEmpty(field) };

        case 'custom':
          if (rule.condition.formula) {
            return { isValid: this.evaluateFormula(rule.condition.formula, record) };
          }
          break;

        case 'custom':
          // Validação customizada adicional
          if (rule.condition.dependsOn) {
            const dependentField = record[rule.condition.dependsOn];
            const currentField = record[rule.field];
            
            if (!this.isEmpty(dependentField) && this.isEmpty(currentField)) {
              return { isValid: false };
            }
          }
          
          // Validação de unicidade (seria implementada com acesso ao banco)
          // Por enquanto, sempre válido
          return { isValid: true };

        default:
          logger.warn(`Tipo de regra desconhecido: ${rule.type}`);
          return { isValid: true };
      }

      return { isValid: true };
    } catch (error) {
      logger.error(`Erro ao validar regra customizada ${rule.id}:`, error);
      return { isValid: false };
    }
  }

  /**
   * Valida regras globais
   */
  private async validateGlobalRules(
    data: any[],
    template: SpreadsheetTemplate,
    context: ValidationContext
  ): Promise<{ errors: SpreadsheetError[]; warnings: SpreadsheetWarning[] }> {
    const errors: SpreadsheetError[] = [];
    const warnings: SpreadsheetWarning[] = [];

    try {
      // Validação de duplicatas
      const duplicates = this.findDuplicates(data, ['sku']);
      if (duplicates.length > 0) {
        duplicates.forEach(duplicate => {
          warnings.push({
            type: 'missing_data',
            message: `SKU duplicado encontrado: ${duplicate.value}`,
            affectedSku: duplicate.value,
            affectedField: 'sku'
          });
        });
      }

      // Validação de consistência de dados
      const consistencyErrors = this.validateDataConsistency(data);
      errors.push(...consistencyErrors);

      // Validações específicas do negócio
      const businessValidation = await this.validateBusinessRules(data, context);
      errors.push(...businessValidation.errors);
      warnings.push(...businessValidation.warnings);

    } catch (error) {
      logger.error('Erro nas validações globais:', error);
      errors.push({
        type: 'system',
        severity: 'error',
        code: 'GLOBAL_VALIDATION_ERROR',
        message: 'Erro nas validações globais',
        details: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }

    return { errors, warnings };
  }

  /**
   * Valida template
   */
  private validateTemplate(template: SpreadsheetTemplate): {
    isValid: boolean;
    errors: SpreadsheetError[];
    warnings: SpreadsheetWarning[];
  } {
    const errors: SpreadsheetError[] = [];
    const warnings: SpreadsheetWarning[] = [];

    // Verificar se tem colunas
    if (!template.columns || template.columns.length === 0) {
      errors.push({
        type: 'validation',
        severity: 'error',
        code: 'TEMPLATE_NO_COLUMNS',
        message: 'Template não possui colunas definidas',
        details: `Template: ${template.name}`
      });
    }

    // Verificar duplicatas de nomes de colunas
    const columnNames = template.columns.map(c => c.name);
    const duplicateNames = columnNames.filter((name, index) => columnNames.indexOf(name) !== index);
    
    if (duplicateNames.length > 0) {
      errors.push({
        type: 'validation',
        severity: 'error',
        code: 'TEMPLATE_DUPLICATE_COLUMNS',
        message: 'Template possui colunas com nomes duplicados',
        details: `Colunas: ${duplicateNames.join(', ')}`
      });
    }

    // Verificar se tem pelo menos uma coluna obrigatória
    const requiredColumns = template.columns.filter(c => c.isRequired);
    if (requiredColumns.length === 0) {
      warnings.push({
        type: 'missing_data',
        message: 'Template não possui colunas obrigatórias',
        affectedSku: template.name,
        affectedField: 'required_columns'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Encontra duplicatas nos dados
   */
  private findDuplicates(data: any[], fields: string[]): Array<{ value: string; rows: number[] }> {
    const seen = new Map<string, number[]>();
    const duplicates: Array<{ value: string; rows: number[] }> = [];

    data.forEach((record, index) => {
      const key = fields.map(field => record[field]).join('|');
      
      if (seen.has(key)) {
        seen.get(key)!.push(index + 1);
      } else {
        seen.set(key, [index + 1]);
      }
    });

    seen.forEach((rows, key) => {
      if (rows.length > 1) {
        duplicates.push({ value: key, rows });
      }
    });

    return duplicates;
  }

  /**
   * Valida consistência dos dados
   */
  private validateDataConsistency(data: any[]): SpreadsheetError[] {
    const errors: SpreadsheetError[] = [];

    data.forEach((record, index) => {
      // Validar que dimensões fazem sentido
      if (record.height && record.width && record.depth) {
        const volume = record.height * record.width * record.depth;
        if (volume > 1000000) { // 1m³ em cm³
          errors.push({
            type: 'validation',
            severity: 'error',
            code: 'INVALID_DIMENSIONS',
            message: 'Dimensões do produto resultam em volume muito grande',
            details: `Linha ${index + 1}, Volume: ${volume.toFixed(2)} cm³`
          });
        }
      }

      // Validar relação peso/dimensões
      if (record.weight && record.height && record.width && record.depth) {
        const volume = record.height * record.width * record.depth;
        const density = record.weight / volume;
        
        if (density > 10) { // Densidade muito alta (g/cm³)
          errors.push({
            type: 'validation',
            severity: 'error',
            code: 'INVALID_DENSITY',
            message: 'Densidade do produto parece incorreta',
            details: `Linha ${index + 1}, Densidade: ${density.toFixed(2)} g/cm³`
          });
        }
      }
    });

    return errors;
  }

  /**
   * Valida regras de negócio
   */
  private async validateBusinessRules(
    data: any[],
    context: ValidationContext
  ): Promise<{ errors: SpreadsheetError[]; warnings: SpreadsheetWarning[] }> {
    const errors: SpreadsheetError[] = [];
    const warnings: SpreadsheetWarning[] = [];

    // Implementar validações específicas do negócio aqui
    // Por exemplo: verificar se produtos existem no sistema, etc.

    return { errors, warnings };
  }

  /**
   * Avalia fórmula simples
   */
  private evaluateFormula(formula: string, record: any): boolean {
    try {
      // Implementação simplificada - em produção usar parser seguro
      let expression = formula;
      
      Object.keys(record).forEach(key => {
        const value = record[key] || 0;
        expression = expression.replace(new RegExp(`\\b${key}\\b`, 'g'), String(value));
      });
      
      // CUIDADO: eval é perigoso - usar parser seguro em produção
      return Boolean(eval(expression));
    } catch (error) {
      logger.warn('Erro ao avaliar fórmula:', error);
      return false;
    }
  }

  /**
   * Verifica se valor está vazio
   */
  private isEmpty(value: any): boolean {
    return value === null || 
           value === undefined || 
           (typeof value === 'string' && value.trim() === '') ||
           (Array.isArray(value) && value.length === 0);
  }

  /**
   * Inicializa validadores customizados
   */
  private initializeCustomValidators(): void {
    // Validador para SKU
    this.customValidators.set('sku', (value, context) => {
      const errors: string[] = [];
      const warnings: string[] = [];
      
      if (typeof value === 'string') {
        const normalized = value.toUpperCase().trim();
        
        // Verificar caracteres especiais
        if (!/^[A-Z0-9\-_]+$/.test(normalized)) {
          warnings.push('SKU contém caracteres especiais que podem causar problemas');
        }
        
        return {
          isValid: true,
          errors,
          warnings,
          normalizedValue: normalized
        };
      }
      
      return { isValid: true, errors, warnings };
    });

    // Validador para código de barras
    this.customValidators.set('barcode', (value, context) => {
      const errors: string[] = [];
      const warnings: string[] = [];
      
      if (value && typeof value === 'string') {
        const cleaned = value.replace(/\D/g, '');
        
        if (cleaned.length < 8 || cleaned.length > 14) {
          warnings.push('Código de barras deve ter entre 8 e 14 dígitos');
        }
        
        return {
          isValid: true,
          errors,
          warnings,
          normalizedValue: cleaned
        };
      }
      
      return { isValid: true, errors, warnings };
    });

    // Validador para preço
    this.customValidators.set('unitPrice', (value, context) => {
      const errors: string[] = [];
      const warnings: string[] = [];
      
      if (typeof value === 'number') {
        if (value <= 0) {
          errors.push('Preço deve ser maior que zero');
        }
        
        if (value > 100000) {
          warnings.push('Preço muito alto, verificar se está correto');
        }
      }
      
      return { isValid: errors.length === 0, errors, warnings };
    });
  }

  /**
   * Adiciona validador customizado
   */
  addCustomValidator(
    fieldName: string,
    validator: (value: any, context: any) => FieldValidationResult
  ): void {
    this.customValidators.set(fieldName, validator);
    logger.info(`Validador customizado adicionado para campo: ${fieldName}`);
  }

  /**
   * Remove validador customizado
   */
  removeCustomValidator(fieldName: string): boolean {
    const removed = this.customValidators.delete(fieldName);
    if (removed) {
      logger.info(`Validador customizado removido para campo: ${fieldName}`);
    }
    return removed;
  }
}

export const validationService = new ValidationService();
export default validationService;
export type { ValidationResult, ValidationContext, FieldValidationResult };