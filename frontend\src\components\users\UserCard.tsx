import React from 'react';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import {
  User as UserIcon,
  Mail,
  Phone,
  Building,
  Briefcase,
  Calendar,
  Clock,
  Edit,
  Trash2,
  UserCheck,
  UserX,
  Shield,
  Users,
  Eye,
  Settings
} from 'lucide-react';
import type { User, UserRole } from '../../types/api';

interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  onDelete?: (user: User) => void;
  onToggleStatus?: (user: User) => void;
  onViewDetails?: (user: User) => void;
  showActions?: boolean;
  compact?: boolean;
}

const getRoleIcon = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return Shield;
    case 'manager':
      return Users;
    case 'user':
      return UserIcon;
    case 'viewer':
      return Eye;
    default:
      return UserIcon;
  }
};

const getRoleLabel = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return 'Administrador';
    case 'manager':
      return 'Gerente';
    case 'user':
      return 'Usuário';
    case 'viewer':
      return 'Visualizador';
    default:
      return role;
  }
};

const getRoleBadgeVariant = (role: UserRole) => {
  switch (role) {
    case 'admin':
      return 'destructive';
    case 'manager':
      return 'default';
    case 'user':
      return 'secondary';
    case 'viewer':
      return 'outline';
    default:
      return 'secondary';
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

const getStatusColor = (isActive?: boolean) => {
  return isActive ? 'text-green-600' : 'text-red-600';
};

const getStatusBgColor = (isActive?: boolean) => {
  return isActive ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
};

export default function UserCard({
  user,
  onEdit,
  onDelete,
  onToggleStatus,
  onViewDetails,
  showActions = true,
  compact = false,
}: UserCardProps) {
  const RoleIcon = getRoleIcon(user.role);
  const statusColor = getStatusColor(user.isActive);
  const statusBgColor = getStatusBgColor(user.isActive);

  if (compact) {
    return (
      <Card className={`hover:shadow-md transition-all duration-200 ${statusBgColor}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-3">
            {/* Avatar */}
            <div className="flex-shrink-0">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-12 h-12 rounded-full object-cover border-2 border-white shadow-sm"
                />
              ) : (
                <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                  <UserIcon className="w-6 h-6 text-gray-500" />
                </div>
              )}
            </div>

            {/* Info */}
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-medium text-sm truncate">{user.name}</h3>
                <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs">
                  {getRoleLabel(user.role)}
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground truncate">{user.email}</p>
              {user.department && (
                <p className="text-xs text-muted-foreground truncate">{user.department}</p>
              )}
            </div>

            {/* Status */}
            <div className="flex-shrink-0">
              {user.isActive ? (
                <UserCheck className={`w-4 h-4 ${statusColor}`} />
              ) : (
                <UserX className={`w-4 h-4 ${statusColor}`} />
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`hover:shadow-md transition-all duration-200 h-full ${statusBgColor}`}>
      <CardContent className="p-6 h-full flex flex-col">
        {/* Header: Avatar + Status */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            {user.avatar ? (
              <img
                src={user.avatar}
                alt={user.name}
                className="w-16 h-16 rounded-full object-cover border-2 border-white shadow-sm"
              />
            ) : (
              <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                <UserIcon className="w-8 h-8 text-gray-500" />
              </div>
            )}
            <div>
              <h3 className="font-semibold text-lg">{user.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={getRoleBadgeVariant(user.role)} className="text-xs">
                  <RoleIcon className="w-3 h-3 mr-1" />
                  {getRoleLabel(user.role)}
                </Badge>
                {user.isActive ? (
                  <Badge variant="outline" className="text-xs text-green-700 border-green-300">
                    <UserCheck className="w-3 h-3 mr-1" />
                    Ativo
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-xs text-red-700 border-red-300">
                    <UserX className="w-3 h-3 mr-1" />
                    Inativo
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Contact Info */}
        <div className="space-y-2 mb-4 flex-1">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Mail className="w-4 h-4" />
            <span className="truncate">{user.email}</span>
          </div>
          
          {user.phone && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Phone className="w-4 h-4" />
              <span>{user.phone}</span>
            </div>
          )}
          
          {user.department && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Building className="w-4 h-4" />
              <span>{user.department}</span>
            </div>
          )}
          
          {user.position && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Briefcase className="w-4 h-4" />
              <span>{user.position}</span>
            </div>
          )}
        </div>

        {/* Dates */}
        <div className="space-y-1 mb-4 text-xs text-muted-foreground">
          <div className="flex items-center gap-2">
            <Calendar className="w-3 h-3" />
            <span>Criado em {formatDate(user.createdAt)}</span>
          </div>
          {user.lastLoginAt && (
            <div className="flex items-center gap-2">
              <Clock className="w-3 h-3" />
              <span>Último acesso: {formatDateTime(user.lastLoginAt)}</span>
            </div>
          )}
        </div>

        {/* Actions */}
        {showActions && (
          <div className="flex gap-2 pt-2 border-t">
            {onViewDetails && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(user)}
                className="flex-1"
              >
                <Eye className="w-4 h-4 mr-1" />
                Ver
              </Button>
            )}
            {onEdit && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onEdit(user)}
                className="flex-1"
              >
                <Edit className="w-4 h-4 mr-1" />
                Editar
              </Button>
            )}
            {onToggleStatus && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onToggleStatus(user)}
                className={`flex-1 ${user.isActive ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}`}
              >
                {user.isActive ? (
                  <>
                    <UserX className="w-4 h-4 mr-1" />
                    Desativar
                  </>
                ) : (
                  <>
                    <UserCheck className="w-4 h-4 mr-1" />
                    Ativar
                  </>
                )}
              </Button>
            )}
            {onDelete && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(user)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
