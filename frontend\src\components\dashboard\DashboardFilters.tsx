import React, { useState, useMemo } from 'react';
import { SearchBar, FilterPanel, ActiveFilters } from '../ui/Filters';
import type { FilterGroup, ActiveFilter } from '../ui/Filters';

interface DashboardFiltersProps {
  onFiltersChange: (filters: any) => void;
  className?: string;
}

// Filtros rápidos predefinidos
const quickFilters = [
  {
    id: 'critical-gaps',
    label: 'Gaps Críticos',
    description: 'Produtos com prioridade crítica',
    filters: { priority: 'critical', hasGap: true }
  },
  {
    id: 'low-stock',
    label: 'Estoque Baixo',
    description: 'Produtos com estoque baixo',
    filters: { stockStatus: 'low_stock' }
  },
  {
    id: 'electronics',
    label: 'Eletrônicos',
    description: 'Categoria eletrônicos',
    filters: { category: ['electronics'] }
  },
  {
    id: 'high-value',
    label: 'Alto Valor',
    description: 'Produtos acima de R$ 1.000',
    filters: { priceRange: { min: 1000, max: null } }
  },
  {
    id: 'recent-sales',
    label: 'Vend<PERSON>',
    description: 'Últimos 7 dias',
    filters: { dateRange: { start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], end: new Date().toISOString().split('T')[0] } }
  }
];

export const DashboardFilters: React.FC<DashboardFiltersProps> = ({
  onFiltersChange,
  className = ""
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
  const [filterValues, setFilterValues] = useState<Record<string, any>>({});

  // Definir grupos de filtros para o dashboard
  const filterGroups: FilterGroup[] = [
    {
      id: 'category',
      label: 'Categoria',
      type: 'multiselect',
      placeholder: 'Todas as categorias',
      options: [
        { value: 'electronics', label: 'Eletrônicos', count: 45 },
        { value: 'home', label: 'Casa e Jardim', count: 32 },
        { value: 'fashion', label: 'Moda', count: 28 },
        { value: 'sports', label: 'Esportes', count: 21 },
        { value: 'books', label: 'Livros', count: 15 },
        { value: 'toys', label: 'Brinquedos', count: 12 }
      ],
      value: filterValues.category
    },
    {
      id: 'stockStatus',
      label: 'Status do Estoque',
      type: 'select',
      placeholder: 'Todos os status',
      options: [
        { value: 'in_stock', label: 'Em Estoque', count: 106 },
        { value: 'low_stock', label: 'Estoque Baixo', count: 14 },
        { value: 'out_of_stock', label: 'Sem Estoque', count: 6 },
        { value: 'overstocked', label: 'Excesso de Estoque', count: 3 }
      ],
      value: filterValues.stockStatus
    },
    {
      id: 'priceRange',
      label: 'Faixa de Preço',
      type: 'range',
      placeholder: 'Qualquer preço',
      min: 0,
      max: 10000,
      value: filterValues.priceRange
    },
    {
      id: 'priority',
      label: 'Prioridade de Gap',
      type: 'select',
      placeholder: 'Todas as prioridades',
      options: [
        { value: 'critical', label: 'Crítica', count: 8 },
        { value: 'high', label: 'Alta', count: 15 },
        { value: 'medium', label: 'Média', count: 22 },
        { value: 'low', label: 'Baixa', count: 35 }
      ],
      value: filterValues.priority
    },
    {
      id: 'dateRange',
      label: 'Período de Análise',
      type: 'date',
      placeholder: 'Selecionar período',
      value: filterValues.dateRange
    },
    {
      id: 'hasGap',
      label: 'Apenas com Gap',
      type: 'toggle',
      placeholder: 'Mostrar apenas produtos com gap de estoque',
      value: filterValues.hasGap
    },
    {
      id: 'hasAlerts',
      label: 'Com Alertas',
      type: 'toggle',
      placeholder: 'Mostrar apenas produtos com alertas ativos',
      value: filterValues.hasAlerts
    }
  ];

  // Calcular filtros ativos
  const activeFilters: ActiveFilter[] = useMemo(() => {
    const filters: ActiveFilter[] = [];

    Object.entries(filterValues).forEach(([groupId, value]) => {
      const group = filterGroups.find(g => g.id === groupId);
      if (!group || !value) return;

      if (group.type === 'multiselect' && Array.isArray(value) && value.length > 0) {
        value.forEach(v => {
          const option = group.options?.find(o => o.value === v);
          if (option) {
            filters.push({
              groupId,
              groupLabel: group.label,
              value: v,
              label: option.label
            });
          }
        });
      } else if (group.type === 'select' && value) {
        const option = group.options?.find(o => o.value === value);
        if (option) {
          filters.push({
            groupId,
            groupLabel: group.label,
            value,
            label: option.label
          });
        }
      } else if (group.type === 'range' && (value.min || value.max)) {
        const label = `R$ ${value.min || 0} - R$ ${value.max || '∞'}`;
        filters.push({
          groupId,
          groupLabel: group.label,
          value,
          label
        });
      } else if (group.type === 'date' && (value.start || value.end)) {
        const start = value.start ? new Date(value.start).toLocaleDateString('pt-BR') : '';
        const end = value.end ? new Date(value.end).toLocaleDateString('pt-BR') : '';
        const label = start && end ? `${start} - ${end}` : start || end;
        filters.push({
          groupId,
          groupLabel: group.label,
          value,
          label
        });
      } else if (group.type === 'toggle' && value) {
        filters.push({
          groupId,
          groupLabel: group.label,
          value,
          label: 'Ativo'
        });
      }
    });

    return filters;
  }, [filterValues, filterGroups]);

  // Manipular mudanças nos filtros
  const handleFilterChange = (groupId: string, value: any) => {
    const newFilterValues = {
      ...filterValues,
      [groupId]: value
    };
    setFilterValues(newFilterValues);
    
    // Notificar componente pai sobre mudanças
    onFiltersChange({
      search: searchTerm,
      filters: newFilterValues
    });
  };

  // Manipular mudanças na pesquisa
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    onFiltersChange({
      search: value,
      filters: filterValues
    });
  };

  // Remover filtro específico
  const handleRemoveFilter = (groupId: string) => {
    const newFilterValues = { ...filterValues };
    delete newFilterValues[groupId];
    setFilterValues(newFilterValues);
    
    onFiltersChange({
      search: searchTerm,
      filters: newFilterValues
    });
  };

  // Limpar todos os filtros
  const handleClearAllFilters = () => {
    setFilterValues({});
    setSearchTerm('');
    onFiltersChange({
      search: '',
      filters: {}
    });
  };

  // Aplicar filtro rápido
  const handleQuickFilter = (quickFilter: typeof quickFilters[0]) => {
    const newFilterValues = { ...filterValues, ...quickFilter.filters };
    setFilterValues(newFilterValues);
    
    onFiltersChange({
      search: searchTerm,
      filters: newFilterValues
    });
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Filtros rápidos */}
      <div className="flex flex-wrap gap-2">
        <span className="text-sm font-medium text-foreground self-center">Filtros rápidos:</span>
        {quickFilters.map((filter) => (
          <button
            key={filter.id}
            onClick={() => handleQuickFilter(filter)}
            className="px-3 py-1 text-sm bg-blue-100 dark:bg-blue-950/20 text-blue-700 dark:text-blue-400 rounded-full hover:bg-blue-200 dark:hover:bg-blue-950/30 transition-colors"
            title={filter.description}
          >
            {filter.label}
          </button>
        ))}
      </div>

      {/* Barra de pesquisa e botão de filtros */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <SearchBar
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Pesquisar produtos, SKUs, categorias..."
            onClear={() => handleSearchChange('')}
          />
        </div>
        
        <div className="relative">
          <FilterPanel
            groups={filterGroups}
            onChange={handleFilterChange}
            isOpen={isFilterPanelOpen}
            onToggle={() => setIsFilterPanelOpen(!isFilterPanelOpen)}
          />
        </div>
      </div>

      {/* Filtros ativos */}
      <ActiveFilters
        filters={activeFilters}
        onRemove={handleRemoveFilter}
        onClearAll={handleClearAllFilters}
      />

      {/* Resumo dos resultados */}
      {(searchTerm || activeFilters.length > 0) && (
        <div className="text-sm text-muted-foreground">
          {searchTerm && (
            <span>
              Pesquisando por: <strong className="text-foreground">"{searchTerm}"</strong>
              {activeFilters.length > 0 && ' • '}
            </span>
          )}
          {activeFilters.length > 0 && (
            <span>
              {activeFilters.length} filtro{activeFilters.length !== 1 ? 's' : ''} aplicado{activeFilters.length !== 1 ? 's' : ''}
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default DashboardFilters;