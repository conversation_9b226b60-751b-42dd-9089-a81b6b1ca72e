import { Request, Response, NextFunction } from 'express';
import { authService } from '@/services/authService';
import { getDatabase } from '@/config/database';
import { logger } from '@/utils/logger';
import { AuthenticationError, AuthorizationError } from '@/middleware/errorHandler';
import { AuthenticatedRequest, AuthMiddlewareOptions } from '@/types/auth';

// Estender interface Request para incluir user e tenant
declare global {
  namespace Express {
    interface Request {
      user?: any;
      tenant?: any;
    }
  }
}

// Middleware de autenticação
export const authenticate = (options: AuthMiddlewareOptions = { required: true }) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        if (options.required) {
          throw new AuthenticationError('Token de acesso não fornecido');
        }
        return next();
      }

      const token = authHeader.substring(7); // Remove 'Bearer '

      try {
        // Verificar token
        const payload = await authService.verifyAccessToken(token);
        
        const prisma = getDatabase();
        
        // Buscar usuário e tenant atualizados
        const user = await prisma.user.findUnique({
          where: { id: payload.userId },
          include: { tenant: true }
        });

        if (!user) {
          throw new AuthenticationError('Usuário não encontrado');
        }

        if (!user.isActive) {
          throw new AuthenticationError('Usuário inativo');
        }

        if (!user.tenant.isActive) {
          throw new AuthenticationError('Tenant inativo');
        }

        // Adicionar dados do usuário e tenant à requisição
        req.user = {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          isActive: user.isActive,
          lastLoginAt: user.lastLoginAt,
          emailVerified: user.emailVerified,
          tenantId: user.tenantId,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        };

        req.tenant = {
          id: user.tenant.id,
          name: user.tenant.name,
          domain: user.tenant.domain,
          subdomain: user.tenant.subdomain,
          isActive: user.tenant.isActive,
          createdAt: user.tenant.createdAt,
          updatedAt: user.tenant.updatedAt,
        };

        next();

      } catch (tokenError) {
        if (options.required) {
          throw new AuthenticationError('Token inválido');
        }
        next();
      }

    } catch (error) {
      logger.error('Erro na autenticação:', error);
      next(error);
    }
  };
};

// Middleware para verificar roles específicas
export const authorize = (roles: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('Usuário não autenticado');
      }

      if (roles.length > 0 && !roles.includes(req.user.role)) {
        throw new AuthorizationError('Acesso negado: permissão insuficiente');
      }

      next();
    } catch (error) {
      logger.error('Erro na autorização:', error);
      next(error);
    }
  };
};

// Middleware para verificar se é admin
export const requireAdmin = authorize(['ADMIN']);

// Middleware para verificar se é admin ou manager
export const requireAdminOrManager = authorize(['ADMIN', 'MANAGER']);

// Middleware para verificar se o usuário pertence ao tenant
export const requireTenant = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (!req.user || !req.tenant) {
      throw new AuthenticationError('Usuário ou tenant não encontrado');
    }

    if (req.user.tenantId !== req.tenant.id) {
      throw new AuthorizationError('Acesso negado: usuário não pertence a este tenant');
    }

    next();
  } catch (error) {
    logger.error('Erro na verificação de tenant:', error);
    next(error);
  }
};

// Middleware para verificar se o email foi verificado
export const requireEmailVerified = (req: Request, res: Response, next: NextFunction): void => {
  try {
    if (!req.user) {
      throw new AuthenticationError('Usuário não autenticado');
    }

    if (!req.user.emailVerified) {
      throw new AuthorizationError('Email não verificado. Verifique seu email antes de continuar.');
    }

    next();
  } catch (error) {
    logger.error('Erro na verificação de email:', error);
    next(error);
  }
};

// Middleware opcional de autenticação (não obrigatório)
export const optionalAuth = authenticate({ required: false });

// Middleware obrigatório de autenticação
export const requireAuth = authenticate({ required: true });

export default {
  authenticate,
  authorize,
  requireAdmin,
  requireAdminOrManager,
  requireTenant,
  requireEmailVerified,
  optionalAuth,
  requireAuth,
}; 