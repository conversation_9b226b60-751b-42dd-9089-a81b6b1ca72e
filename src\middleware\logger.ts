import { Request, Response, NextFunction } from 'express';
import { 
  logger, 
  logRequest, 
  logUserActivity, 
  logSecurityEvent,
  logPerformance 
} from '@/utils/logger';

// Interface para requisição com dados adicionais
interface RequestWithTiming extends Request {
  startTime?: number;
  requestId?: string;
}

// Gerar ID único para requisição
const generateRequestId = (): string => {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
};

// Middleware para logging de requisições
export const requestLogger = (
  req: RequestWithTiming,
  res: Response,
  next: NextFunction
): void => {
  // Marcar tempo de início e gerar ID da requisição
  req.startTime = Date.now();
  req.requestId = generateRequestId();

  // Capturar dados da requisição
  const { method, originalUrl, ip } = req;
  const userAgent = req.get('User-Agent') || 'Unknown';
  const contentLength = req.get('Content-Length') || '0';

  // Log de início da requisição (apenas em desenvolvimento)
  if (process.env.NODE_ENV === 'development') {
    logger.debug(`[${req.requestId}] Request started: ${method} ${originalUrl}`, {
      requestId: req.requestId,
      method,
      url: originalUrl,
      ip,
      userAgent,
      contentLength,
      timestamp: new Date().toISOString(),
    });
  }

  // Interceptar o final da resposta
  const originalSend = res.send;
  res.send = function(data) {
    const responseTime = Date.now() - (req.startTime || Date.now());
    const { statusCode } = res;
    const responseSize = Buffer.byteLength(data || '', 'utf8');

    // Usar a nova função estruturada de logging
    logRequest({
      method,
      url: originalUrl,
      statusCode,
      responseTime,
      ip,
      userAgent,
      userId: (req as any).user?.id,
      tenantId: (req as any).tenant?.id,
    });

    // Log de performance se requisição demorou muito
    if (responseTime > 1000) {
      logPerformance(
        `${method} ${originalUrl}`,
        responseTime,
        { 
          statusCode, 
          responseSize,
          requestId: req.requestId 
        },
        (req as any).user?.id,
        (req as any).tenant?.id
      );
    }

    // Chamar o método original
    return originalSend.call(this, data);
  };

  next();
};

// Middleware para logging de atividades do usuário (auditoria)
export const userActivityLogger = (action: string, resource?: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = (req as any).user;
    const tenant = (req as any).tenant;

    if (user) {
      logUserActivity({
        userId: user.id,
        action,
        resource: resource || req.originalUrl,
        details: {
          method: req.method,
          userEmail: user.email,
          body: req.method !== 'GET' ? req.body : undefined,
        },
        tenantId: tenant?.id,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
      });
    }

    next();
  };
};

// Middleware para logging de operações sensíveis (segurança)
export const sensitiveOperationLogger = (operation: string, level: 'info' | 'warn' | 'error' = 'warn') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = (req as any).user;
    const tenant = (req as any).tenant;

    logSecurityEvent(
      operation,
      level,
      {
        operation,
        url: req.originalUrl,
        method: req.method,
        userEmail: user?.email,
        body: req.body ? JSON.stringify(req.body) : undefined,
        headers: {
          authorization: req.get('Authorization') ? '[PRESENTE]' : '[AUSENTE]',
          userAgent: req.get('User-Agent'),
        },
      },
      user?.id,
      tenant?.id,
      req.ip
    );

    next();
  };
};

// Middleware para logging de tentativas de autenticação
export const authAttemptLogger = (req: Request, res: Response, next: NextFunction): void => {
  const originalSend = res.send;
  
  res.send = function(data) {
    const { statusCode } = res;
    const email = req.body?.email || req.body?.username;
    
    if (statusCode === 401 || statusCode === 403) {
      // Tentativa de login falhada
      logSecurityEvent(
        'FAILED_LOGIN_ATTEMPT',
        'warn',
        {
          email,
          statusCode,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString(),
        },
        undefined,
        undefined,
        req.ip
      );
    } else if (statusCode === 200 && email) {
      // Login bem-sucedido
      logSecurityEvent(
        'SUCCESSFUL_LOGIN',
        'info',
        {
          email,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          timestamp: new Date().toISOString(),
        },
        undefined,
        undefined,
        req.ip
      );
    }

    return originalSend.call(this, data);
  };

  next();
};

// Middleware para logging de operações críticas de estoque
export const stockOperationLogger = (operation: 'CREATE' | 'UPDATE' | 'DELETE' | 'BULK_UPDATE') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const user = (req as any).user;
    const tenant = (req as any).tenant;

    // Log antes da operação
    logUserActivity({
      userId: user?.id || 'SYSTEM',
      action: `STOCK_${operation}`,
      resource: 'stock',
      details: {
        operation,
        affectedItems: Array.isArray(req.body) ? req.body.length : 1,
        method: req.method,
        url: req.originalUrl,
      },
      tenantId: tenant?.id,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    next();
  };
};

// Middleware para logging de geração de planilhas
export const spreadsheetOperationLogger = (req: Request, res: Response, next: NextFunction): void => {
  const user = (req as any).user;
  const tenant = (req as any).tenant;

  logUserActivity({
    userId: user?.id || 'SYSTEM',
    action: 'GENERATE_SPREADSHEET',
    resource: 'spreadsheet',
    details: {
      type: req.body?.type || 'unknown',
      filters: req.body?.filters,
      format: req.body?.format || 'xlsx',
      url: req.originalUrl,
    },
    tenantId: tenant?.id,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  next();
};

// Middleware para logging de acesso a recursos administrativos
export const adminAccessLogger = (req: Request, res: Response, next: NextFunction): void => {
  const user = (req as any).user;
  const tenant = (req as any).tenant;

  logSecurityEvent(
    'ADMIN_ACCESS',
    'info',
    {
      resource: req.originalUrl,
      method: req.method,
      userEmail: user?.email,
      userRole: user?.role,
    },
    user?.id,
    tenant?.id,
    req.ip
  );

  next();
};

export default {
  requestLogger,
  userActivityLogger,
  sensitiveOperationLogger,
  authAttemptLogger,
  stockOperationLogger,
  spreadsheetOperationLogger,
  adminAccessLogger,
}; 