/**
 * Serviço de Agendamento de Alertas de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { StockAlertService } from './stockAlertService';
import { StockAlertConfiguration } from '../types/stock';

interface AlertJobConfig {
  tenantId: string;
  enabled: boolean;
  frequencyMinutes: number;
}

interface AlertJobExecution {
  id: string;
  tenantId: string;
  startedAt: Date;
  finishedAt?: Date;
  status: 'running' | 'completed' | 'failed';
  alertsGenerated: number;
  alertsResolved: number;
  notificationsSent: number;
  error?: string;
}

export class StockAlertSchedulerService {
  private prisma: PrismaClient;
  private stockAlertService: StockAlertService;
  private activeJobs: Map<string, NodeJS.Timeout> = new Map();
  private jobExecutions: Map<string, AlertJobExecution[]> = new Map();

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    this.stockAlertService = new StockAlertService(this.prisma);
  }

  /**
   * Configura job de alertas automático para um tenant
   * Padrão: 4 verificações por dia (a cada 6 horas)
   */
  public async setupTenantAlertJob(tenantId: string, config?: Partial<AlertJobConfig>): Promise<void> {
    try {
      // Configuração padrão: 4 verificações por dia (360 minutos = 6 horas)
      const jobConfig: AlertJobConfig = {
        tenantId,
        enabled: true,
        frequencyMinutes: 360, // 6 horas
        ...config
      };

      logger.info('Configurando job de alertas automático', { tenantId, config: jobConfig });

      // Para job existente se houver
      await this.stopTenantAlertJob(tenantId);

      if (jobConfig.enabled) {
        // Atualiza configuração no banco
        await this.stockAlertService.configureAlerts(tenantId, {
          checkFrequencyMinutes: jobConfig.frequencyMinutes
        });

        // Cria job com setInterval
        const intervalMs = jobConfig.frequencyMinutes * 60 * 1000;
        const interval = setInterval(async () => {
          await this.executeAlertJob(tenantId);
        }, intervalMs);

        this.activeJobs.set(tenantId, interval);
        
        logger.info('Job de alertas iniciado', { 
          tenantId, 
          frequencyMinutes: jobConfig.frequencyMinutes,
          intervalMs,
          verificationsPerDay: Math.round(1440 / jobConfig.frequencyMinutes)
        });

        // Executa primeira verificação imediatamente
        setTimeout(() => {
          this.executeAlertJob(tenantId);
        }, 5000); // 5 segundos após configuração
      }

    } catch (error: any) {
      logger.error('Erro ao configurar job de alertas', { tenantId, error: error.message });
      throw error;
    }
  }

  /**
   * Para job de alertas de um tenant específico
   */
  public async stopTenantAlertJob(tenantId: string): Promise<void> {
    const existingJob = this.activeJobs.get(tenantId);
    if (existingJob) {
      clearInterval(existingJob);
      this.activeJobs.delete(tenantId);
      logger.info('Job de alertas parado', { tenantId });
    }
  }

  /**
   * Executa job de alertas para um tenant
   */
  private async executeAlertJob(tenantId: string): Promise<void> {
    const executionId = `${tenantId}_alert_${Date.now()}`;
    const startTime = new Date();

    logger.info('Iniciando execução de job de alertas', { 
      tenantId, 
      executionId
    });

    const execution: AlertJobExecution = {
      id: executionId,
      tenantId,
      startedAt: startTime,
      status: 'running',
      alertsGenerated: 0,
      alertsResolved: 0,
      notificationsSent: 0
    };

    try {
      // Registra início da execução
      this.addJobExecution(tenantId, execution);

      // Executa processamento de alertas
      const result = await this.stockAlertService.processAlerts(tenantId);

      // Atualiza execução com sucesso
      execution.finishedAt = new Date();
      execution.status = 'completed';
      execution.alertsGenerated = result.alertsGenerated;
      execution.alertsResolved = result.alertsResolved;
      execution.notificationsSent = result.notificationsSent;

      logger.info('Job de alertas concluído com sucesso', {
        tenantId,
        executionId,
        alertsGenerated: result.alertsGenerated,
        alertsResolved: result.alertsResolved,
        notificationsSent: result.notificationsSent,
        duration: execution.finishedAt.getTime() - execution.startedAt.getTime()
      });

    } catch (error: any) {
      // Atualiza execução com erro
      execution.finishedAt = new Date();
      execution.status = 'failed';
      execution.error = error.message;

      logger.error('Erro na execução do job de alertas', {
        tenantId,
        executionId,
        error: error.message
      });

    } finally {
      // Atualiza registro da execução
      this.updateJobExecution(tenantId, execution);
    }
  }

  /**
   * Executa job de alertas manualmente
   */
  public async executeManualAlertJob(tenantId: string, userId: string): Promise<AlertJobExecution> {
    const executionId = `${tenantId}_manual_alert_${Date.now()}`;
    const startTime = new Date();

    logger.info('Iniciando execução manual de job de alertas', { 
      tenantId, 
      userId,
      executionId 
    });

    const execution: AlertJobExecution = {
      id: executionId,
      tenantId,
      startedAt: startTime,
      status: 'running',
      alertsGenerated: 0,
      alertsResolved: 0,
      notificationsSent: 0
    };

    try {
      // Registra início da execução
      this.addJobExecution(tenantId, execution);

      // Executa processamento
      const result = await this.stockAlertService.processAlerts(tenantId);

      // Atualiza execução com sucesso
      execution.finishedAt = new Date();
      execution.status = 'completed';
      execution.alertsGenerated = result.alertsGenerated;
      execution.alertsResolved = result.alertsResolved;
      execution.notificationsSent = result.notificationsSent;

      logger.info('Execução manual de job de alertas concluída', {
        tenantId,
        userId,
        executionId,
        alertsGenerated: result.alertsGenerated,
        alertsResolved: result.alertsResolved,
        notificationsSent: result.notificationsSent
      });

      return execution;

    } catch (error: any) {
      execution.finishedAt = new Date();
      execution.status = 'failed';
      execution.error = error.message;

      logger.error('Erro na execução manual do job de alertas', {
        tenantId,
        userId,
        executionId,
        error: error.message
      });

      throw error;
    } finally {
      this.updateJobExecution(tenantId, execution);
    }
  }

  /**
   * Lista execuções de jobs de alertas para um tenant
   */
  public getAlertJobExecutions(tenantId: string, limit: number = 50): AlertJobExecution[] {
    const executions = this.jobExecutions.get(tenantId) || [];
    return executions
      .sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime())
      .slice(0, limit);
  }

  /**
   * Verifica se job de alertas está ativo
   */
  public isAlertJobActive(tenantId: string): boolean {
    return this.activeJobs.has(tenantId);
  }

  /**
   * Para todos os jobs de alertas ativos
   */
  public async stopAllAlertJobs(): Promise<void> {
    logger.info('Parando todos os jobs de alertas');
    
    for (const tenantId of this.activeJobs.keys()) {
      try {
        await this.stopTenantAlertJob(tenantId);
      } catch (error: any) {
        logger.error('Erro ao parar job de alertas', { tenantId, error: error.message });
      }
    }

    logger.info('Todos os jobs de alertas foram parados');
  }

  /**
   * Configura frequência personalizada para um tenant
   */
  public async updateAlertFrequency(
    tenantId: string, 
    verificationsPerDay: number
  ): Promise<void> {
    if (verificationsPerDay < 1 || verificationsPerDay > 24) {
      throw new Error('Número de verificações por dia deve estar entre 1 e 24');
    }

    const frequencyMinutes = Math.round(1440 / verificationsPerDay); // 1440 minutos em um dia
    
    logger.info('Atualizando frequência de alertas', {
      tenantId,
      verificationsPerDay,
      frequencyMinutes
    });

    await this.setupTenantAlertJob(tenantId, {
      enabled: true,
      frequencyMinutes
    });
  }

  /**
   * Obtém configuração atual de alertas
   */
  public async getCurrentAlertConfig(tenantId: string): Promise<{
    isActive: boolean;
    frequencyMinutes: number;
    verificationsPerDay: number;
    nextExecution?: Date;
  }> {
    const isActive = this.isAlertJobActive(tenantId);
    
    // Busca configuração do banco
    const config = await this.prisma.stockAlertConfiguration.findFirst({
      where: { tenantId }
    });

    const frequencyMinutes = config?.checkFrequencyMinutes || 360;
    const verificationsPerDay = Math.round(1440 / frequencyMinutes);

    // Calcula próxima execução baseada na última execução
    let nextExecution: Date | undefined;
    if (isActive) {
      const executions = this.getAlertJobExecutions(tenantId, 1);
      if (executions.length > 0) {
        const lastExecution = executions[0];
        nextExecution = new Date(lastExecution.startedAt.getTime() + (frequencyMinutes * 60 * 1000));
      }
    }

    return {
      isActive,
      frequencyMinutes,
      verificationsPerDay,
      nextExecution
    };
  }

  /**
   * Adiciona execução ao histórico
   */
  private addJobExecution(tenantId: string, execution: AlertJobExecution): void {
    if (!this.jobExecutions.has(tenantId)) {
      this.jobExecutions.set(tenantId, []);
    }
    
    const executions = this.jobExecutions.get(tenantId)!;
    executions.unshift(execution);
    
    // Mantém apenas as últimas 100 execuções
    if (executions.length > 100) {
      executions.splice(100);
    }
  }

  /**
   * Atualiza execução no histórico
   */
  private updateJobExecution(tenantId: string, execution: AlertJobExecution): void {
    const executions = this.jobExecutions.get(tenantId);
    if (executions) {
      const index = executions.findIndex(e => e.id === execution.id);
      if (index !== -1) {
        executions[index] = execution;
      }
    }
  }

  /**
   * Obtém métricas dos jobs de alertas
   */
  public getAlertJobMetrics(tenantId: string, days: number = 7): any {
    const executions = this.getAlertJobExecutions(tenantId, 1000);
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    const recentExecutions = executions.filter(e => e.startedAt >= since);

    const successful = recentExecutions.filter(e => e.status === 'completed');
    const failed = recentExecutions.filter(e => e.status === 'failed');

    const totalAlertsGenerated = successful.reduce((sum, e) => sum + e.alertsGenerated, 0);
    const totalAlertsResolved = successful.reduce((sum, e) => sum + e.alertsResolved, 0);
    const totalNotificationsSent = successful.reduce((sum, e) => sum + e.notificationsSent, 0);

    return {
      totalExecutions: recentExecutions.length,
      successful: successful.length,
      failed: failed.length,
      successRate: recentExecutions.length > 0 ? (successful.length / recentExecutions.length) * 100 : 0,
      totalAlertsGenerated,
      totalAlertsResolved,
      totalNotificationsSent,
      averageAlertsPerExecution: successful.length > 0 ? totalAlertsGenerated / successful.length : 0,
      lastExecution: recentExecutions[0]?.startedAt || null,
      period: `${days} days`
    };
  }
}