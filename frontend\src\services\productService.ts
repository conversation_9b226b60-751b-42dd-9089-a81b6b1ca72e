import axios from 'axios';

// Definir a URL base da API (ajuste conforme necessário para o seu ambiente)
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000/api';

interface Product {
  id: string;
  sku: string;
  title: string;
  price: number;
  availableQuantity: number;
  category: string;
  status: string;
  description?: string;
}

interface CreateProductDto {
  title: string;
  sku: string;
  price: number;
  availableQuantity: number;
  category: string;
  status: string;
  description?: string;
}

interface UpdateProductDto {
  title?: string;
  sku?: string;
  price?: number;
  availableQuantity?: number;
  category?: string;
  status?: string;
  description?: string;
}

const productService = {
  /**
   * Busca todos os produtos da API.
   * @returns Uma Promise que resolve para um array de produtos.
   */
  getAllProducts: async (): Promise<Product[]> => {
    try {
      const response = await axios.get<Product[]>(`${API_BASE_URL}/products`);
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar produtos:", error);
      throw error;
    }
  },

  /**
   * Busca um produto específico por ID.
   * @param id O ID do produto.
   * @returns Uma Promise que resolve para o produto encontrado ou null.
   */
  getProductById: async (id: string): Promise<Product | null> => {
    try {
      const response = await axios.get<Product>(`${API_BASE_URL}/products/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar produto com ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Cria um novo produto.
   * @param product Os dados do novo produto.
   * @returns Uma Promise que resolve para o produto criado.
   */
  createProduct: async (product: CreateProductDto): Promise<Product> => {
    try {
      const response = await axios.post<Product>(`${API_BASE_URL}/products`, product);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar produto:", error);
      throw error;
    }
  },

  /**
   * Atualiza um produto existente.
   * @param id O ID do produto a ser atualizado.
   * @param product Os dados atualizados do produto.
   * @returns Uma Promise que resolve para o produto atualizado.
   */
  updateProduct: async (id: string, product: UpdateProductDto): Promise<Product> => {
    try {
      const response = await axios.put<Product>(`${API_BASE_URL}/products/${id}`, product);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar produto com ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Exclui um produto.
   * @param id O ID do produto a ser excluído.
   * @returns Uma Promise que resolve para void.
   */
  deleteProduct: async (id: string): Promise<void> => {
    try {
      await axios.delete(`${API_BASE_URL}/products/${id}`);
    } catch (error) {
      console.error(`Erro ao excluir produto com ID ${id}:`, error);
      throw error;
    }
  },
};

export default productService;
export type { Product, CreateProductDto, UpdateProductDto }; 