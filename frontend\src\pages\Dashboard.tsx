import { useEffect, useMemo, useCallback } from 'react';
import { useDashboardStore } from '../store/dashboardStore';
import { useMercadoLivreStore } from '../store/mercadoLivreStore';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent
} from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import StockGapCard from '../components/dashboard/StockGapCard';
import StockAlertsCard from '../components/dashboard/StockAlertsCard';
import SalesChart from '../components/dashboard/SalesChart';
import StockChart from '../components/dashboard/StockChart';
import DashboardFilters from '../components/dashboard/DashboardFilters';
import MercadoLivreConnect from '../components/MercadoLivreConnect';
import { useUrlFilters } from '../hooks/useUrlFilters';
import { StatSkeleton, CardSkeleton } from '../components/ui/Loading';
import { FadeIn, StaggeredContainer, SlideIn } from '../components/ui/Animations';
import {
  CubeIcon,
  ExclamationTriangleIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import {
  ShoppingBag,
  TrendingUp,
  Users,
  RefreshCw
} from 'lucide-react';
import type { StockAlert } from '../types/api';

export default function Dashboard() {
  const {
    stats,
    statsLoading,
    statsError,
    gapProducts,
    gapProductsLoading,
    gapProductsError,
    alerts,
    alertsLoading,
    alertsError,
    loadDashboardStats,
    loadGapProducts,
    loadAlerts,
    markAlertAsRead
  } = useDashboardStore();

  // MOCK DATA FOR DEVELOPMENT - Comment out for production
  // const {
  //   accounts,
  //   selectedAccount,
  //   products,
  //   orders,
  //   stats: mlStats,
  //   syncStatus,
  //   loadAccounts
  // } = useMercadoLivreStore();

  // Mock ML Store data for development
  const mockSelectedAccount = {
    id: 'mock-account-001',
    nickname: 'Loja Demo',
    email: '<EMAIL>',
  };
  const selectedAccount = mockSelectedAccount;
  const accounts = [mockSelectedAccount];
  const products = [];
  const orders = [];
  const mlStats = null;
  const syncStatus = { isLoading: false };

  // Hooks para gerenciamento de filtros
  const { filters, updateFilters } = useUrlFilters();

  // Callbacks memoizados para otimização de performance
  const handleRetryLoadData = useCallback(() => {
    loadDashboardStats();
    loadGapProducts();
    loadAlerts();
  }, [loadDashboardStats, loadGapProducts, loadAlerts]);

  const handleGapDetails = useCallback((gap: any) => {
    console.log('Visualizar detalhes do gap:', gap);
    // TODO: Implementar modal ou navegação para detalhes
  }, []);

  const handlePeriodChange = useCallback((period: string) => {
    console.log('Período alterado para:', period);
    // TODO: Implementar mudança de período
  }, []);

  useEffect(() => {
    // MOCK DATA FOR DEVELOPMENT - Load mock data automatically
    let isMounted = true;

    const loadData = async () => {
      // Só carregar dados se houver token de autenticação
      // const token = localStorage.getItem('auth_token');
      // if (token && isMounted) {
      if (isMounted) {
        try {
          await Promise.all([
            loadDashboardStats(),
            loadGapProducts(),
            loadAlerts()
          ]);
        } catch (error) {
          console.error('Erro ao carregar dados do dashboard:', error);
        }
      }
      // }
    };

    loadData();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [loadDashboardStats, loadGapProducts, loadAlerts]);





  // Filtrar dados baseado nos filtros aplicados - usando dados do store
  const filteredGaps = useMemo(() => {
    let filtered = gapProducts;

    // Aplicar filtro de pesquisa
    if (filters.search) {
      const search = filters.search.toLowerCase();
      filtered = filtered.filter(gap =>
        gap.product.title.toLowerCase().includes(search) ||
        (gap.product.sku && gap.product.sku.toLowerCase().includes(search)) ||
        gap.product.category.toLowerCase().includes(search)
      );
    }

    // Aplicar filtros específicos
    const { filters: appliedFilters } = filters;

    if (appliedFilters.category && appliedFilters.category.length > 0) {
      filtered = filtered.filter(gap =>
        appliedFilters.category.includes(gap.product.category.toLowerCase())
      );
    }

    if (appliedFilters.priority) {
      filtered = filtered.filter(gap => gap.priority === appliedFilters.priority);
    }

    if (appliedFilters.priceRange) {
      const { min, max } = appliedFilters.priceRange;
      filtered = filtered.filter(gap => {
        const price = gap.product.price;
        return (!min || price >= min) && (!max || price <= max);
      });
    }

    if (appliedFilters.hasGap) {
      filtered = filtered.filter(gap => gap.gap > 0);
    }

    return filtered;
  }, [gapProducts, filters]);

  const filteredAlerts = useMemo(() => {
    let filtered = alerts;

    // Aplicar filtro de pesquisa
    if (filters.search) {
      const search = filters.search.toLowerCase();
      filtered = filtered.filter(alert =>
        alert.product.title.toLowerCase().includes(search) ||
        alert.message.toLowerCase().includes(search) ||
        (alert.product.sku && alert.product.sku.toLowerCase().includes(search))
      );
    }

    // Aplicar filtro de alertas
    if (filters.filters.hasAlerts) {
      filtered = filtered.filter(alert => !alert.isRead);
    }

    return filtered;
  }, [alerts, filters]);

  return (
    <div className="space-y-6">
      {/* Header com animação */}
      <FadeIn>
        <div>
          <h1 className="text-2xl font-bold text-foreground">Dashboard</h1>
          <p className="mt-1 text-sm text-muted-foreground">
            Visão geral do seu estoque e vendas
          </p>
        </div>
      </FadeIn>

      {/* Sistema de Filtros */}
      <SlideIn direction="up" delay={100}>
        <DashboardFilters
          onFiltersChange={updateFilters}
          className="bg-card p-4 rounded-lg shadow-sm border"
        />
      </SlideIn>

      {/* Tratamento de Erros */}
      {(statsError || gapProductsError || alertsError) && (
        <SlideIn direction="up" delay={50}>
          <Card variant="destructive" className="border-red-200 bg-red-50 dark:bg-red-950/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-800 dark:text-red-200">
                <ExclamationTriangleIcon className="h-5 w-5" />
                Erro ao Carregar Dados
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {statsError && (
                  <div className="text-sm text-red-700 dark:text-red-300">
                    <strong>Estatísticas:</strong> {statsError}
                  </div>
                )}
                {gapProductsError && (
                  <div className="text-sm text-red-700 dark:text-red-300">
                    <strong>Gaps de Estoque:</strong> {gapProductsError}
                  </div>
                )}
                {alertsError && (
                  <div className="text-sm text-red-700 dark:text-red-300">
                    <strong>Alertas:</strong> {alertsError}
                  </div>
                )}
                <div className="flex gap-2 mt-4">
                  <button
                    onClick={handleRetryLoadData}
                    className="flex items-center gap-2 px-3 py-1 text-sm bg-red-100 hover:bg-red-200 text-red-800 rounded-md transition-colors"
                  >
                    <RefreshCw className="h-4 w-4" />
                    Tentar Novamente
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        </SlideIn>
      )}

      {/* Status Mercado Livre */}
      <SlideIn direction="up" delay={150}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Conexão ML */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <ShoppingBag className="h-4 w-4" />
                Mercado Livre
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Contas:</span>
                  <Badge variant={accounts.length > 0 ? "default" : "secondary"}>
                    {accounts.length} conectada{accounts.length !== 1 ? 's' : ''}
                  </Badge>
                </div>
                {selectedAccount && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Ativa:</span>
                    <span className="text-sm font-medium">{selectedAccount.nickname}</span>
                  </div>
                )}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Produtos:</span>
                  <span className="text-sm font-medium">{products.length}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vendas ML */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <TrendingUp className="h-4 w-4" />
                Vendas Hoje
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  {orders.filter(order => {
                    const today = new Date().toDateString();
                    return new Date(order.dateCreated).toDateString() === today;
                  }).length}
                </div>
                <p className="text-xs text-muted-foreground">
                  {orders.length} pedidos total
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Sincronização */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-sm">
                <RefreshCw className={`h-4 w-4 ${syncStatus.isLoading ? 'animate-spin' : ''}`} />
                Sincronização
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  <Badge variant={syncStatus.isLoading ? "secondary" : syncStatus.error ? "destructive" : "default"}>
                    {syncStatus.isLoading ? 'Sincronizando' : syncStatus.error ? 'Erro' : 'OK'}
                  </Badge>
                </div>
                {syncStatus.lastSync && (
                  <p className="text-xs text-muted-foreground">
                    Última: {new Date(syncStatus.lastSync).toLocaleString()}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </SlideIn>

      {/* Estatísticas Gerais com animação escalonada */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statsLoading ? (
          <>
            <StatSkeleton />
            <StatSkeleton />
            <StatSkeleton />
            <StatSkeleton />
          </>
        ) : (
          <>
            <FadeIn delay={100}>
              <Card role="region" aria-labelledby="stock-value-title">
                <CardHeader>
                  <CardTitle id="stock-value-title" className="flex items-center">
                    <CurrencyDollarIcon className="h-6 w-6 mr-2" aria-hidden="true" />
                    Valor do Estoque
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold" aria-label={`Valor do estoque: ${stats?.totalRevenue?.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) ?? 'R$ 0,00'}`}>
                    {stats?.totalRevenue?.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' }) ?? 'R$ 0,00'}
                  </p>
                  <p className="text-sm text-muted-foreground">Valor total dos produtos em estoque</p>
                </CardContent>
              </Card>
            </FadeIn>

            <FadeIn delay={200}>
              <Card role="region" aria-labelledby="active-products-title">
                <CardHeader>
                  <CardTitle id="active-products-title" className="flex items-center">
                    <CubeIcon className="h-6 w-6 mr-2" aria-hidden="true" />
                    Produtos Ativos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold" aria-label={`Produtos ativos: ${stats?.activeProducts?.toLocaleString('pt-BR') ?? '0'}`}>
                    {stats?.activeProducts?.toLocaleString('pt-BR') ?? '0'}
                  </p>
                  <p className="text-sm text-muted-foreground">de {stats?.totalProducts?.toLocaleString('pt-BR') ?? '0'} SKUs totais</p>
                </CardContent>
              </Card>
            </FadeIn>

            <FadeIn delay={300}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ChartBarIcon className="h-6 w-6 mr-2" />
                    Ruptura de Estoque (SKUs)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{stats?.gapProducts?.toLocaleString('pt-BR') ?? '0'}</p>
                  <p className="text-sm text-muted-foreground">Produtos com estoque abaixo do ideal</p>
                </CardContent>
              </Card>
            </FadeIn>

            <FadeIn delay={400}>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <ExclamationTriangleIcon className="h-6 w-6 mr-2" />
                    Alertas Ativos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-3xl font-bold">{stats?.alertsCount?.toLocaleString('pt-BR') ?? '0'}</p>
                  <p className="text-sm text-muted-foreground">Notificações importantes sobre seu estoque</p>
                </CardContent>
              </Card>
            </FadeIn>
          </>
        )}
      </div>

      {/* Gaps de Estoque e Alertas */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SlideIn direction="left" delay={200}>
          {gapProductsLoading ? (
            <CardSkeleton className="h-96" />
          ) : (
            <StockGapCard
              gaps={filteredGaps}
              loading={gapProductsLoading}
              onViewDetails={handleGapDetails}
            />
          )}
        </SlideIn>
        
        <SlideIn direction="right" delay={250}>
          {alertsLoading ? (
            <CardSkeleton className="h-96" />
          ) : (
            <StockAlertsCard
              alerts={filteredAlerts}
              loading={alertsLoading}
              onMarkAsRead={markAlertAsRead}
            />
          )}
        </SlideIn>
      </div>

      {/* Gráficos de Vendas e Estoque */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <SlideIn direction="up" delay={300}>
          <SalesChart
            data={stats?.trendsData?.sales?.map(item => ({
              date: item.date,
              sales: item.sales,
              revenue: item.revenue,
              orders: item.products // Using products as orders approximation
            })) || []}
            period="7d"
            loading={statsLoading}
            onPeriodChange={handlePeriodChange}
          />
        </SlideIn>

        <SlideIn direction="up" delay={350}>
          <StockChart
            data={stats?.trendsData?.categories?.map(cat => ({
              category: cat.category,
              totalProducts: cat.products,
              inStock: Math.max(0, cat.products - cat.gapProducts),
              lowStock: Math.min(cat.gapProducts, Math.floor(cat.products * 0.2)),
              outOfStock: Math.max(0, cat.gapProducts - Math.floor(cat.products * 0.2)),
              totalValue: cat.revenue
            })) || []}
            distribution={stats ? [
              {
                name: 'Em Estoque',
                value: stats.activeProducts - stats.gapProducts,
                color: '#10B981'
              },
              {
                name: 'Estoque Baixo',
                value: stats.gapProducts - stats.criticalProducts,
                color: '#F59E0B'
              },
              {
                name: 'Crítico',
                value: stats.criticalProducts,
                color: '#EF4444'
              }
            ] : []}
            loading={statsLoading}
          />
        </SlideIn>
      </div>
    </div>
  );
}