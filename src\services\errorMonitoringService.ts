import { PrismaClient } from '@prisma/client';
import { Request } from 'express';
import { logger, logError, logSecurityEvent } from '../utils/logger';

// Tipos de erro categorizados
export enum ErrorCategory {
  SECURITY = 'SECURITY',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE',
  DATABASE = 'DATABASE',
  BUSINESS_LOGIC = 'BUSINESS_LOGIC',
  NETWORK = 'NETWORK',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN'
}

// Severidade do erro
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Interface para detalhes do erro
interface ErrorDetails {
  id: string;
  message: string;
  stack?: string | undefined;
  category: ErrorCategory;
  severity: ErrorSeverity;
  code?: string | number | undefined;
  statusCode?: number | undefined;
  userId?: string | undefined;
  tenantId?: string | undefined;
  requestId?: string | undefined;
  endpoint?: string | undefined;
  method?: string | undefined;
  userAgent?: string | undefined;
  ip?: string | undefined;
  timestamp: Date;
  context: Record<string, any>;
  isRecurrent?: boolean | undefined;
  occurrenceCount?: number | undefined;
  firstOccurrence?: Date | undefined;
  lastOccurrence?: Date | undefined;
}

// Interface para métricas de erro
export interface ErrorMetrics {
  totalErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByHour: Array<{ hour: string; count: number }>;
  mostFrequentErrors: Array<{ message: string; count: number; category: ErrorCategory }>;
  errorRate: number; // Porcentagem de requests com erro
  criticalErrors: number;
  resolvedErrors: number;
  averageResolutionTime?: number;
}

export class ErrorMonitoringService {
  private prisma: PrismaClient;
  private errorCache: Map<string, ErrorDetails>;
  private alertThresholds: Map<ErrorSeverity, number>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.errorCache = new Map();
    
    // Configurar thresholds para alertas
    this.alertThresholds = new Map([
      [ErrorSeverity.CRITICAL, 1], // Alerta imediato para erros críticos
      [ErrorSeverity.HIGH, 5],     // Alerta após 5 ocorrências
      [ErrorSeverity.MEDIUM, 20],  // Alerta após 20 ocorrências
      [ErrorSeverity.LOW, 50]      // Alerta após 50 ocorrências
    ]);
  }

  /**
   * Captura e processa um erro
   */
  public async captureError(
    error: Error,
    request?: Request,
    context: Record<string, any> = {}
  ): Promise<void> {
    try {
      const errorDetails = this.processError(error, request, context);
      
      // Salvar no cache para análise de recorrência
      this.updateErrorCache(errorDetails);
      
      // Log estruturado do erro
      this.logErrorDetails(errorDetails);
      
      // Verificar se precisa gerar alerta
      await this.checkAlertThresholds(errorDetails);
      
      // Salvar no banco para análises históricas (opcional, mas recomendado)
      await this.persistError(errorDetails);
      
    } catch (processingError) {
      // Fallback para evitar loop infinito de erros
      logger.error('Erro ao processar monitoramento de erro', {
        originalError: error.message,
        processingError: processingError instanceof Error ? processingError.message : 'Unknown',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Processa o erro e extrai informações relevantes
   */
  private processError(
    error: Error,
    request?: Request,
    context: Record<string, any> = {}
  ): ErrorDetails {
    const errorId = this.generateErrorId(error);
    const category = this.categorizeError(error);
    const severity = this.determineSeverity(error, category);
    
    return {
      id: errorId,
      message: error.message || 'Unknown error',
      stack: error.stack,
      category,
      severity,
      code: (error as any).code,
      statusCode: (error as any).statusCode || (error as any).status,
      userId: request?.user?.id || context.userId,
      tenantId: request?.user?.tenantId || context.tenantId,
      requestId: request?.headers['x-request-id'] as string,
      endpoint: request?.originalUrl || request?.url,
      method: request?.method,
      userAgent: request?.get('User-Agent'),
      ip: this.getClientIP(request),
      timestamp: new Date(),
      context: {
        ...context,
        errorName: error.name,
        errorConstructor: error.constructor.name
      }
    };
  }

  /**
   * Categoriza o erro baseado no tipo e mensagem
   */
  private categorizeError(error: Error): ErrorCategory {
    const errorName = error.name.toLowerCase();
    const errorMessage = error.message.toLowerCase();
    
    // Erros de segurança
    if (errorName.includes('forbidden') || 
        errorMessage.includes('unauthorized') ||
        errorMessage.includes('access denied') ||
        errorMessage.includes('csrf') ||
        errorMessage.includes('xss')) {
      return ErrorCategory.SECURITY;
    }
    
    // Erros de validação
    if (errorName.includes('validation') || 
        errorMessage.includes('invalid') ||
        errorMessage.includes('required') ||
        errorMessage.includes('malformed')) {
      return ErrorCategory.VALIDATION;
    }
    
    // Erros de autenticação
    if (errorName.includes('auth') || 
        errorMessage.includes('token') ||
        errorMessage.includes('login') ||
        errorMessage.includes('credential')) {
      return ErrorCategory.AUTHENTICATION;
    }
    
    // Erros de banco de dados
    if (errorName.includes('prisma') || 
        errorName.includes('database') ||
        errorMessage.includes('connection') ||
        errorMessage.includes('query') ||
        errorMessage.includes('constraint')) {
      return ErrorCategory.DATABASE;
    }
    
    // Erros de serviços externos
    if (errorMessage.includes('api') ||
        errorMessage.includes('external') ||
        errorMessage.includes('mercado livre') ||
        errorMessage.includes('timeout') ||
        errorMessage.includes('502') ||
        errorMessage.includes('503')) {
      return ErrorCategory.EXTERNAL_SERVICE;
    }
    
    // Erros de rede
    if (errorMessage.includes('network') ||
        errorMessage.includes('econnrefused') ||
        errorMessage.includes('enotfound') ||
        errorMessage.includes('etimedout')) {
      return ErrorCategory.NETWORK;
    }
    
    return ErrorCategory.UNKNOWN;
  }

  /**
   * Determina a severidade do erro
   */
  private determineSeverity(error: Error, category: ErrorCategory): ErrorSeverity {
    const statusCode = (error as any).statusCode || (error as any).status;
    
    // Erros críticos
    if (category === ErrorCategory.SECURITY ||
        category === ErrorCategory.DATABASE ||
        statusCode >= 500) {
      return ErrorSeverity.CRITICAL;
    }
    
    // Erros altos
    if (category === ErrorCategory.AUTHENTICATION ||
        category === ErrorCategory.EXTERNAL_SERVICE ||
        statusCode === 401 || statusCode === 403) {
      return ErrorSeverity.HIGH;
    }
    
    // Erros médios
    if (category === ErrorCategory.VALIDATION ||
        category === ErrorCategory.BUSINESS_LOGIC ||
        statusCode >= 400) {
      return ErrorSeverity.MEDIUM;
    }
    
    return ErrorSeverity.LOW;
  }

  /**
   * Atualiza cache de erros para detectar recorrência
   */
  private updateErrorCache(errorDetails: ErrorDetails): void {
    const errorKey = this.generateErrorKey(errorDetails);
    const existing = this.errorCache.get(errorKey);
    
    if (existing) {
      existing.occurrenceCount = (existing.occurrenceCount || 1) + 1;
      existing.lastOccurrence = errorDetails.timestamp;
      existing.isRecurrent = existing.occurrenceCount > 1;
      this.errorCache.set(errorKey, existing);
    } else {
      errorDetails.occurrenceCount = 1;
      errorDetails.firstOccurrence = errorDetails.timestamp;
      errorDetails.lastOccurrence = errorDetails.timestamp;
      this.errorCache.set(errorKey, errorDetails);
    }
  }

  /**
   * Faz log estruturado do erro
   */
  private logErrorDetails(errorDetails: ErrorDetails): void {
    const logLevel = this.getLogLevel(errorDetails.severity);
    
    logger.log(logLevel, `[${errorDetails.category}] ${errorDetails.message}`, {
      errorId: errorDetails.id,
      category: errorDetails.category,
      severity: errorDetails.severity,
      code: errorDetails.code,
      statusCode: errorDetails.statusCode,
      userId: errorDetails.userId,
      tenantId: errorDetails.tenantId,
      requestId: errorDetails.requestId,
      endpoint: errorDetails.endpoint,
      method: errorDetails.method,
      ip: errorDetails.ip,
      isRecurrent: errorDetails.isRecurrent,
      occurrenceCount: errorDetails.occurrenceCount,
      context: errorDetails.context,
      stack: errorDetails.stack,
      timestamp: errorDetails.timestamp.toISOString()
    });
    
    // Log especial para erros de segurança
    if (errorDetails.category === ErrorCategory.SECURITY) {
      logSecurityEvent(
        'Security Error Detected',
        'error',
        {
          errorId: errorDetails.id,
          message: errorDetails.message,
          endpoint: errorDetails.endpoint,
          method: errorDetails.method
        },
        errorDetails.userId,
        errorDetails.tenantId,
        errorDetails.ip
      );
    }
  }

  /**
   * Verifica se precisa disparar alertas
   */
  private async checkAlertThresholds(errorDetails: ErrorDetails): Promise<void> {
    const threshold = this.alertThresholds.get(errorDetails.severity) || 1;
    const occurrenceCount = errorDetails.occurrenceCount || 1;
    
    if (occurrenceCount >= threshold) {
      await this.triggerAlert(errorDetails);
    }
  }

  /**
   * Dispara alerta para erro crítico
   */
  private async triggerAlert(errorDetails: ErrorDetails): Promise<void> {
    const alertMessage = `ALERT: ${errorDetails.severity} error detected`;
    
    logger.error(alertMessage, {
      errorId: errorDetails.id,
      category: errorDetails.category,
      severity: errorDetails.severity,
      message: errorDetails.message,
      occurrenceCount: errorDetails.occurrenceCount,
      tenantId: errorDetails.tenantId,
      endpoint: errorDetails.endpoint,
      alertTriggered: true,
      timestamp: new Date().toISOString()
    });
    
    // Aqui você pode integrar com serviços de notificação:
    // - Slack
    // - Discord
    // - Email
    // - SMS
    // - PagerDuty
    // - Webhook
  }

  /**
   * Persiste erro no banco para análises históricas
   */
  private async persistError(errorDetails: ErrorDetails): Promise<void> {
    try {
      // Implementar se necessário - criar tabela de errors no schema
      // await this.prisma.errorLog.create({
      //   data: {
      //     id: errorDetails.id,
      //     message: errorDetails.message,
      //     category: errorDetails.category,
      //     severity: errorDetails.severity,
      //     ...
      //   }
      // });
    } catch (dbError) {
      logger.error('Falha ao persistir erro no banco', {
        errorId: errorDetails.id,
        dbError: dbError instanceof Error ? dbError.message : 'Unknown'
      });
    }
  }

  /**
   * Gera métricas de erro para dashboard
   */
  public async generateErrorMetrics(
    tenantId?: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<ErrorMetrics> {
    const errors = Array.from(this.errorCache.values())
      .filter(error => {
        if (tenantId && error.tenantId !== tenantId) return false;
        if (startDate && error.timestamp < startDate) return false;
        if (endDate && error.timestamp > endDate) return false;
        return true;
      });

    const errorsByCategory = {} as Record<ErrorCategory, number>;
    const errorsBySeverity = {} as Record<ErrorSeverity, number>;
    
    // Inicializar contadores
    Object.values(ErrorCategory).forEach(cat => errorsByCategory[cat] = 0);
    Object.values(ErrorSeverity).forEach(sev => errorsBySeverity[sev] = 0);
    
    errors.forEach(error => {
      errorsByCategory[error.category]++;
      errorsBySeverity[error.severity]++;
    });

    return {
      totalErrors: errors.length,
      errorsByCategory,
      errorsBySeverity,
      errorsByHour: this.calculateErrorsByHour(errors),
      mostFrequentErrors: this.getMostFrequentErrors(errors),
      errorRate: this.calculateErrorRate(errors.length),
      criticalErrors: errorsBySeverity[ErrorSeverity.CRITICAL],
      resolvedErrors: 0 // Implementar sistema de resolução se necessário
    };
  }

  // Métodos auxiliares
  private generateErrorId(error: Error): string {
    const timestamp = Date.now();
    const hash = this.simpleHash(error.message + error.stack);
    return `err_${timestamp}_${hash}`;
  }

  private generateErrorKey(errorDetails: ErrorDetails): string {
    return `${errorDetails.category}_${errorDetails.message}_${errorDetails.endpoint}`;
  }

  private getClientIP(request?: Request): string | undefined {
    if (!request) return undefined;
    return (request.headers['x-forwarded-for'] as string)?.split(',')[0] || 
           request.connection.remoteAddress ||
           request.socket.remoteAddress;
  }

  private getLogLevel(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL: return 'error';
      case ErrorSeverity.HIGH: return 'error';
      case ErrorSeverity.MEDIUM: return 'warn';
      case ErrorSeverity.LOW: return 'info';
      default: return 'debug';
    }
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private calculateErrorsByHour(errors: ErrorDetails[]): Array<{ hour: string; count: number }> {
    const hourCounts = new Map<string, number>();
    
    errors.forEach(error => {
      const hour = error.timestamp.getHours().toString().padStart(2, '0');
      hourCounts.set(hour, (hourCounts.get(hour) || 0) + 1);
    });
    
    return Array.from(hourCounts.entries())
      .map(([hour, count]) => ({ hour, count }))
      .sort((a, b) => a.hour.localeCompare(b.hour));
  }

  private getMostFrequentErrors(errors: ErrorDetails[]): Array<{ message: string; count: number; category: ErrorCategory }> {
    const messageCounts = new Map<string, { count: number; category: ErrorCategory }>();
    
    errors.forEach(error => {
      const existing = messageCounts.get(error.message);
      if (existing) {
        existing.count++;
      } else {
        messageCounts.set(error.message, { count: 1, category: error.category });
      }
    });
    
    return Array.from(messageCounts.entries())
      .map(([message, data]) => ({ message, count: data.count, category: data.category }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10); // Top 10
  }

  private calculateErrorRate(errorCount: number): number {
    // Implementar baseado no número total de requests (seria necessário um contador global)
    // Por agora, retorna 0 como placeholder
    return 0;
  }

  /**
   * Limpa cache de erros antigos
   */
  public cleanupOldErrors(maxAge: number = 24 * 60 * 60 * 1000): void {
    const cutoff = new Date(Date.now() - maxAge);
    
    for (const [key, error] of this.errorCache.entries()) {
      if (error.timestamp < cutoff) {
        this.errorCache.delete(key);
      }
    }
  }
}

export default ErrorMonitoringService; 