import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Progress } from '../ui/progress';
import {
  EyeIcon,
  EyeSlashIcon,
  KeyIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { useConfigStore } from '../../store/configStore';
import { useNotificationStore } from '../../store/notificationStore';
import {
  passwordChangeSchema,
  getPasswordStrength,
  type PasswordChangeFormData,
} from '../../schemas/profileSchemas';
import { FadeIn } from '../ui/Animations';

interface PasswordChangeFormProps {
  showTitle?: boolean;
  onChangeSuccess?: () => void;
  onChangeError?: (error: string) => void;
  className?: string;
}

const PasswordChangeForm: React.FC<PasswordChangeFormProps> = ({
  showTitle = true,
  onChangeSuccess,
  onChangeError,
  className = '',
}) => {
  const { passwordChangeLoading, passwordChangeError, changePassword } = useConfigStore();
  const { addNotification } = useNotificationStore();

  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<PasswordChangeFormData>({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
  });

  const newPassword = watch('newPassword');
  const passwordStrength = newPassword ? getPasswordStrength(newPassword) : { score: 0, feedback: [] };

  const getStrengthColor = (score: number) => {
    if (score <= 1) return 'bg-red-500';
    if (score <= 2) return 'bg-orange-500';
    if (score <= 3) return 'bg-yellow-500';
    if (score <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getStrengthText = (score: number) => {
    if (score <= 1) return 'Muito fraca';
    if (score <= 2) return 'Fraca';
    if (score <= 3) return 'Média';
    if (score <= 4) return 'Forte';
    return 'Muito forte';
  };

  const onSubmit = async (data: PasswordChangeFormData) => {
    try {
      await changePassword(data);

      addNotification({
        title: 'Senha Alterada',
        message: 'Sua senha foi alterada com sucesso.',
        type: 'system',
        severity: 'success',
        duration: 5000,
      });

      reset();
      onChangeSuccess?.();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      addNotification({
        title: 'Erro ao Alterar Senha',
        message: 'Não foi possível alterar sua senha. Verifique os dados e tente novamente.',
        type: 'error',
        severity: 'error',
        duration: 8000,
      });

      onChangeError?.(errorMessage);
    }
  };

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <KeyIcon className="h-5 w-5" />
            <span>Alterar Senha</span>
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Mantenha sua conta segura alterando sua senha regularmente.
          </p>
        </CardHeader>
      )}
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Current Password */}
          <FadeIn>
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Senha Atual *</Label>
              <div className="relative">
                <Input
                  id="currentPassword"
                  type={showCurrentPassword ? 'text' : 'password'}
                  {...register('currentPassword')}
                  disabled={passwordChangeLoading || isSubmitting}
                  className={errors.currentPassword ? 'border-red-500 pr-10' : 'pr-10'}
                  placeholder="Digite sua senha atual"
                />
                <button
                  type="button"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  disabled={passwordChangeLoading || isSubmitting}
                >
                  {showCurrentPassword ? (
                    <EyeSlashIcon className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <EyeIcon className="h-4 w-4 text-muted-foreground" />
                  )}
                </button>
              </div>
              {errors.currentPassword && (
                <p className="text-sm text-red-600">{errors.currentPassword.message}</p>
              )}
            </div>
          </FadeIn>

          {/* New Password */}
          <FadeIn delay={50}>
            <div className="space-y-2">
              <Label htmlFor="newPassword">Nova Senha *</Label>
              <div className="relative">
                <Input
                  id="newPassword"
                  type={showNewPassword ? 'text' : 'password'}
                  {...register('newPassword')}
                  disabled={passwordChangeLoading || isSubmitting}
                  className={errors.newPassword ? 'border-red-500 pr-10' : 'pr-10'}
                  placeholder="Digite sua nova senha"
                />
                <button
                  type="button"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  disabled={passwordChangeLoading || isSubmitting}
                >
                  {showNewPassword ? (
                    <EyeSlashIcon className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <EyeIcon className="h-4 w-4 text-muted-foreground" />
                  )}
                </button>
              </div>
              {errors.newPassword && (
                <p className="text-sm text-red-600">{errors.newPassword.message}</p>
              )}

              {/* Password Strength Indicator */}
              {newPassword && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Força da senha:</span>
                    <span className={`text-sm font-medium ${
                      passwordStrength.score <= 2 ? 'text-red-600' : 
                      passwordStrength.score <= 3 ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {getStrengthText(passwordStrength.score)}
                    </span>
                  </div>
                  <Progress 
                    value={(passwordStrength.score / 5) * 100} 
                    className="h-2"
                  />
                  
                  {passwordStrength.feedback.length > 0 && (
                    <div className="space-y-1">
                      {passwordStrength.feedback.map((feedback, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <XCircleIcon className="h-3 w-3 text-red-500 flex-shrink-0" />
                          <span className="text-xs text-red-600">{feedback}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </FadeIn>

          {/* Confirm Password */}
          <FadeIn delay={100}>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirmar Nova Senha *</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  {...register('confirmPassword')}
                  disabled={passwordChangeLoading || isSubmitting}
                  className={errors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
                  placeholder="Confirme sua nova senha"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  disabled={passwordChangeLoading || isSubmitting}
                >
                  {showConfirmPassword ? (
                    <EyeSlashIcon className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <EyeIcon className="h-4 w-4 text-muted-foreground" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-sm text-red-600">{errors.confirmPassword.message}</p>
              )}
            </div>
          </FadeIn>

          {/* Password Requirements */}
          <FadeIn delay={150}>
            <div className="bg-muted/50 rounded-lg p-4 space-y-2">
              <h4 className="text-sm font-medium">Requisitos da senha:</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-muted-foreground">Pelo menos 8 caracteres</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-muted-foreground">Uma letra minúscula</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-muted-foreground">Uma letra maiúscula</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-muted-foreground">Um número</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircleIcon className="h-3 w-3 text-green-500" />
                  <span className="text-xs text-muted-foreground">Um caractere especial</span>
                </div>
              </div>
            </div>
          </FadeIn>

          {/* Error Display */}
          {passwordChangeError && (
            <FadeIn>
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-sm text-red-600">{passwordChangeError}</p>
              </div>
            </FadeIn>
          )}

          {/* Submit Button */}
          <FadeIn delay={200}>
            <div className="flex justify-end">
              <Button
                type="submit"
                disabled={passwordChangeLoading || isSubmitting}
                className="min-w-[140px]"
              >
                {passwordChangeLoading || isSubmitting ? 'Alterando...' : 'Alterar Senha'}
              </Button>
            </div>
          </FadeIn>
        </form>
      </CardContent>
    </Card>
  );
};

export default PasswordChangeForm;
