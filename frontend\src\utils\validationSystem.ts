import type { ProductWithStock } from '../types/api';
import type { ProductQuantity, MLFullProduct } from '../store/mlFullWizardStore';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  suggestion?: string;
}

// Validation rules
export class MLFullValidationSystem {
  // Product selection validations
  static validateProductSelection(products: MLFullProduct[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check if any products are selected
    if (products.length === 0) {
      errors.push({
        field: 'products',
        message: 'Pelo menos um produto deve ser selecionado',
        code: 'NO_PRODUCTS_SELECTED',
        severity: 'error'
      });
    }

    // Check for ineligible products
    const ineligibleProducts = products.filter(p => !p.isEligible);
    if (ineligibleProducts.length > 0) {
      errors.push({
        field: 'products',
        message: `${ineligibleProducts.length} produto(s) não são elegíveis para ML Full`,
        code: 'INELIGIBLE_PRODUCTS',
        severity: 'error'
      });
    }

    // Check for products without stock
    const outOfStockProducts = products.filter(p => p.availableQuantity === 0);
    if (outOfStockProducts.length > 0) {
      warnings.push({
        field: 'products',
        message: `${outOfStockProducts.length} produto(s) sem estoque selecionado(s)`,
        code: 'OUT_OF_STOCK_PRODUCTS',
        suggestion: 'Remova produtos sem estoque ou aguarde reposição'
      });
    }

    // Check for products with low stock
    const lowStockProducts = products.filter(p => p.availableQuantity > 0 && p.availableQuantity <= 5);
    if (lowStockProducts.length > 0) {
      warnings.push({
        field: 'products',
        message: `${lowStockProducts.length} produto(s) com estoque baixo (≤ 5 unidades)`,
        code: 'LOW_STOCK_PRODUCTS',
        suggestion: 'Considere reduzir as quantidades ou reabastecer'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Quantity configuration validations
  static validateQuantityConfiguration(
    products: MLFullProduct[],
    quantities: Record<string, ProductQuantity>
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    products.forEach(product => {
      const quantity = quantities[product.id];
      
      if (!quantity) {
        errors.push({
          field: `quantity_${product.id}`,
          message: `Quantidade não definida para ${product.title}`,
          code: 'QUANTITY_NOT_SET',
          severity: 'error'
        });
        return;
      }

      // Validate quantity value
      if (quantity.quantity <= 0) {
        errors.push({
          field: `quantity_${product.id}`,
          message: `Quantidade deve ser maior que zero para ${product.title}`,
          code: 'INVALID_QUANTITY',
          severity: 'error'
        });
      }

      // Check if quantity exceeds available stock
      if (quantity.quantity > product.availableQuantity) {
        errors.push({
          field: `quantity_${product.id}`,
          message: `Quantidade (${quantity.quantity}) excede estoque disponível (${product.availableQuantity}) para ${product.title}`,
          code: 'QUANTITY_EXCEEDS_STOCK',
          severity: 'error'
        });
      }

      // Check if quantity exceeds maximum allowed
      if (quantity.quantity > product.maxQuantity) {
        errors.push({
          field: `quantity_${product.id}`,
          message: `Quantidade (${quantity.quantity}) excede máximo permitido (${product.maxQuantity}) para ${product.title}`,
          code: 'QUANTITY_EXCEEDS_MAX',
          severity: 'error'
        });
      }

      // Warning for quantities below suggested
      if (quantity.quantity < product.suggestedQuantity) {
        warnings.push({
          field: `quantity_${product.id}`,
          message: `Quantidade abaixo da sugerida (${product.suggestedQuantity}) para ${product.title}`,
          code: 'QUANTITY_BELOW_SUGGESTED',
          suggestion: `Considere usar ${product.suggestedQuantity} unidades`
        });
      }

      // Warning for high quantities that might affect stock coverage
      const stockCoverage = product.availableQuantity / (product.metrics?.averageDailySales || 1);
      if (quantity.quantity > product.availableQuantity * 0.8 && stockCoverage < 30) {
        warnings.push({
          field: `quantity_${product.id}`,
          message: `Quantidade alta pode afetar cobertura de estoque para ${product.title}`,
          code: 'HIGH_QUANTITY_LOW_COVERAGE',
          suggestion: 'Considere reduzir a quantidade ou reabastecer'
        });
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // File generation validations
  static validateFileGeneration(
    products: MLFullProduct[],
    quantities: Record<string, ProductQuantity>,
    format: 'excel' | 'csv'
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Validate products and quantities first
    const productValidation = this.validateProductSelection(products);
    const quantityValidation = this.validateQuantityConfiguration(products, quantities);

    errors.push(...productValidation.errors, ...quantityValidation.errors);
    warnings.push(...productValidation.warnings, ...quantityValidation.warnings);

    // Check total value
    const totalValue = products.reduce((sum, product) => {
      const quantity = quantities[product.id]?.quantity || 0;
      return sum + (quantity * product.price);
    }, 0);

    if (totalValue === 0) {
      errors.push({
        field: 'total_value',
        message: 'Valor total não pode ser zero',
        code: 'ZERO_TOTAL_VALUE',
        severity: 'error'
      });
    }

    // Warning for very high values
    if (totalValue > 100000) {
      warnings.push({
        field: 'total_value',
        message: `Valor total muito alto: R$ ${totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
        code: 'HIGH_TOTAL_VALUE',
        suggestion: 'Verifique se as quantidades estão corretas'
      });
    }

    // Check total quantity
    const totalQuantity = products.reduce((sum, product) => {
      return sum + (quantities[product.id]?.quantity || 0);
    }, 0);

    if (totalQuantity === 0) {
      errors.push({
        field: 'total_quantity',
        message: 'Quantidade total não pode ser zero',
        code: 'ZERO_TOTAL_QUANTITY',
        severity: 'error'
      });
    }

    // Warning for very high quantities
    if (totalQuantity > 1000) {
      warnings.push({
        field: 'total_quantity',
        message: `Quantidade total muito alta: ${totalQuantity} unidades`,
        code: 'HIGH_TOTAL_QUANTITY',
        suggestion: 'Considere dividir em múltiplos envios'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // PDF upload validations
  static validatePDFUpload(file: File): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check file type
    if (file.type !== 'application/pdf') {
      errors.push({
        field: 'pdf_file',
        message: 'Arquivo deve ser um PDF',
        code: 'INVALID_FILE_TYPE',
        severity: 'error'
      });
    }

    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      errors.push({
        field: 'pdf_file',
        message: `Arquivo muito grande. Máximo permitido: 10MB. Tamanho atual: ${(file.size / 1024 / 1024).toFixed(2)}MB`,
        code: 'FILE_TOO_LARGE',
        severity: 'error'
      });
    }

    // Check minimum file size (should have some content)
    const minSize = 1024; // 1KB
    if (file.size < minSize) {
      warnings.push({
        field: 'pdf_file',
        message: 'Arquivo muito pequeno, pode estar corrompido',
        code: 'FILE_TOO_SMALL',
        suggestion: 'Verifique se o arquivo foi gerado corretamente'
      });
    }

    // Check file name
    if (!file.name.toLowerCase().includes('ml') && !file.name.toLowerCase().includes('mercado')) {
      warnings.push({
        field: 'pdf_file',
        message: 'Nome do arquivo não parece ser do Mercado Livre',
        code: 'SUSPICIOUS_FILENAME',
        suggestion: 'Verifique se é o arquivo correto do ML Full'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Shipment completion validations
  static validateShipmentCompletion(
    hasGeneratedFile: boolean,
    hasUploadedPDF: boolean,
    expectedDelivery?: string,
    notes?: string
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // Check required files
    if (!hasGeneratedFile) {
      errors.push({
        field: 'generated_file',
        message: 'Planilha deve ser gerada antes de finalizar',
        code: 'MISSING_GENERATED_FILE',
        severity: 'error'
      });
    }

    if (!hasUploadedPDF) {
      errors.push({
        field: 'uploaded_pdf',
        message: 'PDF de instruções deve ser enviado',
        code: 'MISSING_PDF',
        severity: 'error'
      });
    }

    // Validate expected delivery date
    if (expectedDelivery) {
      const deliveryDate = new Date(expectedDelivery);
      const today = new Date();
      const maxDeliveryDate = new Date();
      maxDeliveryDate.setDate(today.getDate() + 90); // 90 days from now

      if (deliveryDate < today) {
        errors.push({
          field: 'expected_delivery',
          message: 'Data de entrega não pode ser no passado',
          code: 'INVALID_DELIVERY_DATE',
          severity: 'error'
        });
      }

      if (deliveryDate > maxDeliveryDate) {
        warnings.push({
          field: 'expected_delivery',
          message: 'Data de entrega muito distante (mais de 90 dias)',
          code: 'DISTANT_DELIVERY_DATE',
          suggestion: 'Verifique se a data está correta'
        });
      }
    }

    // Validate notes length
    if (notes && notes.length > 500) {
      warnings.push({
        field: 'notes',
        message: 'Observações muito longas',
        code: 'LONG_NOTES',
        suggestion: 'Considere resumir as observações'
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Comprehensive validation for the entire wizard
  static validateWizardStep(
    step: number,
    data: {
      products?: MLFullProduct[];
      quantities?: Record<string, ProductQuantity>;
      format?: 'excel' | 'csv';
      generatedFile?: boolean;
      uploadedPDF?: boolean;
      pdfFile?: File;
      expectedDelivery?: string;
      notes?: string;
    }
  ): ValidationResult {
    switch (step) {
      case 1: // Product Selection
        return data.products ? this.validateProductSelection(data.products) : {
          isValid: false,
          errors: [{ field: 'products', message: 'Dados de produtos não fornecidos', code: 'MISSING_DATA', severity: 'error' }],
          warnings: []
        };

      case 2: // Quantity Configuration
        return (data.products && data.quantities) 
          ? this.validateQuantityConfiguration(data.products, data.quantities)
          : {
              isValid: false,
              errors: [{ field: 'data', message: 'Dados incompletos para validação', code: 'MISSING_DATA', severity: 'error' }],
              warnings: []
            };

      case 3: // Review and Generation
        return (data.products && data.quantities && data.format)
          ? this.validateFileGeneration(data.products, data.quantities, data.format)
          : {
              isValid: false,
              errors: [{ field: 'data', message: 'Dados incompletos para geração', code: 'MISSING_DATA', severity: 'error' }],
              warnings: []
            };

      case 4: // Completion
        return this.validateShipmentCompletion(
          data.generatedFile || false,
          data.uploadedPDF || false,
          data.expectedDelivery,
          data.notes
        );

      default:
        return {
          isValid: false,
          errors: [{ field: 'step', message: 'Step inválido', code: 'INVALID_STEP', severity: 'error' }],
          warnings: []
        };
    }
  }
}
