import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import MLFullProductCard from './MLFullProductCard';
import { useMLFullWizardStore } from '../../store/mlFullWizardStore';
import {
  Calculator,
  Package,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Settings,
  Grid3X3,
  List
} from 'lucide-react';

interface QuantityConfigurationStepProps {
  onNext: () => void;
  onBack: () => void;
}

export default function QuantityConfigurationStep({ onNext, onBack }: QuantityConfigurationStepProps) {
  const {
    selectedProducts,
    productQuantities,
    filteredProducts,
    updateQuantity,
    toggleProductSelection,
    selectAllProducts,
    unselectAllProducts,
  } = useMLFullWizardStore();

  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [bulkQuantity, setBulkQuantity] = useState('');

  // Get selected products with their data
  const selectedProductsData = useMemo(() => {
    return filteredProducts.filter(product => selectedProducts.includes(product.id));
  }, [filteredProducts, selectedProducts]);

  // Calculate totals
  const totals = useMemo(() => {
    const totalProducts = selectedProducts.length;
    const totalQuantity = selectedProducts.reduce((sum, productId) => {
      return sum + (productQuantities[productId]?.quantity || 0);
    }, 0);
    const totalValue = selectedProducts.reduce((sum, productId) => {
      const product = filteredProducts.find(p => p.id === productId);
      const quantity = productQuantities[productId]?.quantity || 0;
      return sum + (product ? product.price * quantity : 0);
    }, 0);

    return { totalProducts, totalQuantity, totalValue };
  }, [selectedProducts, productQuantities, filteredProducts]);

  // Validation
  const validation = useMemo(() => {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (selectedProducts.length === 0) {
      errors.push('Selecione pelo menos um produto');
    }

    selectedProducts.forEach(productId => {
      const product = filteredProducts.find(p => p.id === productId);
      const quantity = productQuantities[productId]?.quantity || 0;

      if (!product) return;

      if (quantity === 0) {
        errors.push(`${product.title}: Quantidade deve ser maior que 0`);
      }

      if (quantity > product.availableQuantity) {
        errors.push(`${product.title}: Quantidade excede estoque disponível (${product.availableQuantity})`);
      }

      if (product.availableQuantity <= 5) {
        warnings.push(`${product.title}: Estoque baixo (${product.availableQuantity} unidades)`);
      }
    });

    return { errors, warnings, isValid: errors.length === 0 };
  }, [selectedProducts, productQuantities, filteredProducts]);

  const handleQuantityChange = (productId: string, quantity: number) => {
    updateQuantity(productId, quantity);
  };

  const handleBulkQuantityApply = () => {
    const quantity = parseInt(bulkQuantity);
    if (isNaN(quantity) || quantity < 1) return;

    selectedProducts.forEach(productId => {
      const product = filteredProducts.find(p => p.id === productId);
      if (product) {
        const maxQuantity = Math.min(quantity, product.availableQuantity);
        updateQuantity(productId, maxQuantity);
      }
    });

    setBulkQuantity('');
  };

  const handleSelectAll = () => {
    const eligibleProducts = filteredProducts.filter(p => p.isEligible !== false && p.availableQuantity > 0);
    selectAllProducts(eligibleProducts.map(p => p.id));

    // Set default quantity of 1 for newly selected products
    eligibleProducts.forEach(product => {
      if (!productQuantities[product.id]) {
        updateQuantity(product.id, 1);
      }
    });
  };

  const handleUnselectAll = () => {
    unselectAllProducts();
  };

  const renderViewModeToggle = () => (
    <div className="flex items-center gap-1 border rounded-md p-1">
      <Button
        variant={viewMode === 'grid' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setViewMode('grid')}
        className="h-8"
      >
        <Grid3X3 className="h-4 w-4" />
      </Button>
      <Button
        variant={viewMode === 'list' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => setViewMode('list')}
        className="h-8"
      >
        <List className="h-4 w-4" />
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold mb-2">Configurar Quantidades</h2>
        <p className="text-muted-foreground">
          Defina as quantidades para cada produto selecionado. Você pode editar individualmente ou aplicar em lote.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Produtos</p>
                <p className="text-2xl font-bold">{totals.totalProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calculator className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Quantidade Total</p>
                <p className="text-2xl font-bold">{totals.totalQuantity}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor Total</p>
                <p className="text-2xl font-bold">
                  R$ {totals.totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Controles
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Selection Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
              >
                Selecionar Todos
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleUnselectAll}
              >
                Desmarcar Todos
              </Button>
            </div>
            {renderViewModeToggle()}
          </div>

          {/* Bulk Quantity */}
          <div className="flex items-center gap-2">
            <Label htmlFor="bulk-quantity" className="text-sm font-medium">
              Aplicar quantidade em lote:
            </Label>
            <Input
              id="bulk-quantity"
              type="number"
              value={bulkQuantity}
              onChange={(e) => setBulkQuantity(e.target.value)}
              placeholder="Ex: 5"
              className="w-24"
              min="1"
            />
            <Button
              onClick={handleBulkQuantityApply}
              disabled={!bulkQuantity || selectedProducts.length === 0}
              size="sm"
            >
              Aplicar
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Validation Messages */}
      {(validation.errors.length > 0 || validation.warnings.length > 0) && (
        <Card>
          <CardContent className="p-4">
            {validation.errors.length > 0 && (
              <div className="mb-3">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="font-medium text-red-700">Erros encontrados:</span>
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm text-red-600">
                  {validation.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {validation.warnings.length > 0 && (
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <span className="font-medium text-yellow-700">Avisos:</span>
                </div>
                <ul className="list-disc list-inside space-y-1 text-sm text-yellow-600">
                  {validation.warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Products Grid */}
      <div className={
        viewMode === 'grid'
          ? 'grid grid-cols-1 lg:grid-cols-2 gap-6'
          : 'space-y-4'
      }>
        {selectedProductsData.map((product) => (
          <MLFullProductCard
            key={product.id}
            product={product}
            quantity={productQuantities[product.id]?.quantity || 0}
            onQuantityChange={handleQuantityChange}
            isSelected={selectedProducts.includes(product.id)}
            onToggleSelection={toggleProductSelection}
            showQuantityInput={true}
            maxQuantity={product.availableQuantity}
            minQuantity={1}
          />
        ))}
      </div>

      {selectedProductsData.length === 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12 text-muted-foreground">
              <Package className="h-16 w-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">Nenhum produto selecionado</h3>
              <p className="text-sm mb-4">
                Volte ao passo anterior para selecionar produtos
              </p>
              <Button onClick={onBack} variant="outline">
                Voltar à Seleção
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Navigation */}
      <div className="flex justify-between">
        <Button onClick={onBack} variant="outline">
          Voltar
        </Button>
        <Button
          onClick={onNext}
          disabled={!validation.isValid}
          className="flex items-center gap-2"
        >
          <CheckCircle className="h-4 w-4" />
          Continuar para Revisão
        </Button>
      </div>
    </div>
  );
}
