/**
 * Middleware de Validação de Tenant
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { AuthenticationError, AuthorizationError } from './errorHandler';

/**
 * Middleware para validar se o usuário tem acesso ao tenant
 * Deve ser usado após o middleware de autenticação
 * 
 * Versão simplificada que valida apenas a presença do tenantId e userId
 * TODO: Implementar validação completa com banco de dados quando Prisma estiver configurado
 */
export const validateTenant = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { tenantId, userId } = req.user || {};

    if (!tenantId || !userId) {
      logger.warn('Tentativa de acesso sem tenant/usuário válido', {
        path: req.path,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      throw new AuthenticationError('Token de acesso inválido');
    }

    // TODO: Implementar validações de banco quando Prisma estiver configurado:
    // - Verificar se tenant existe e está ativo
    // - Verificar se assinatura está válida
    // - Verificar se usuário pertence ao tenant
    // - Verificar se usuário está ativo no tenant

    // Por enquanto, apenas adiciona informações básicas ao request
    (req as any).tenant = {
      id: tenantId,
      name: `Tenant ${tenantId}`,
      status: 'ACTIVE'
    };

    (req as any).userRole = {
      role: 'ADMIN', // Temporário - será obtido do banco
      permissions: ['*'] // Temporário - todas as permissões
    };

    logger.debug('Validação de tenant bem-sucedida (modo simplificado)', {
      tenantId,
      userId,
      path: req.path,
      method: req.method
    });

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Middleware para validar permissões específicas
 * TODO: Implementar quando sistema de permissões estiver completo
 */
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Por enquanto, permite tudo (modo desenvolvimento)
    // TODO: Implementar validação real de permissões
    next();
  };
};

/**
 * Middleware para validar roles específicas
 * TODO: Implementar quando sistema de roles estiver completo
 */
export const requireRole = (roles: string | string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    // Por enquanto, permite tudo (modo desenvolvimento)
    // TODO: Implementar validação real de roles
    next();
  };
};

export default validateTenant; 