import { render, screen } from '@testing-library/react';
import LogsPage from '../../pages/Logs';
import MonitoringPage from '../../pages/Monitoring';

describe('Logs and Monitoring pages', () => {
  it('renders logs page', () => {
    render(<LogsPage />);
    expect(screen.getByText(/Logs de Ações/i)).toBeInTheDocument();
  });

  it('renders monitoring page', () => {
    render(<MonitoringPage />);
    expect(screen.getByText(/Monitoramento em Tempo Real/i)).toBeInTheDocument();
  });
}); 