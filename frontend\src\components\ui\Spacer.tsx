 
import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const spacerVariants = cva(
  'block',
  {
    variants: {
      size: {
        xs: 'h-1 w-1',
        sm: 'h-2 w-2',
        md: 'h-4 w-4',
        lg: 'h-8 w-8',
        xl: 'h-12 w-12',
        '2xl': 'h-16 w-16',
      },
      axis: {
        x: 'h-0',
        y: 'w-0',
        both: '',
      },
    },
    defaultVariants: {
      size: 'md',
      axis: 'both',
    },
  }
);

export interface SpacerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spacerVariants> {
  asChild?: boolean;
}

const Spacer = React.forwardRef<HTMLDivElement, SpacerProps>(
  ({ className, size, axis, asChild = false, ...props }, ref) => {
    const Comp = asChild ? 'div' : 'div';
    const dynamicClass = axis === 'x' ? `w-${size}` : axis === 'y' ? `h-${size}` : `h-${size} w-${size}`;

    return (
      <Comp
        className={cn(spacerVariants({ size, axis, className }), dynamicClass)}
        ref={ref}
        {...props}
      />
    );
  }
);
Spacer.displayName = 'Spacer';

export { Spacer, spacerVariants }; 
