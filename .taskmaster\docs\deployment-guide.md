# Guia de Deploy - Magnow

## Sistema de Controle Inteligente de Estoque para Mercado Livre

### Guia Completo de Deploy e Infraestrutura

---

## 📋 Índice

1. [Pré-requisitos](#pré-requisitos)
2. [Ambiente de Desenvolvimento](#ambiente-de-desenvolvimento)
3. [Ambiente de Staging](#ambiente-de-staging)
4. [Ambiente de Produção](#ambiente-de-produção)
5. [CI/CD Pipeline](#cicd-pipeline)
6. [Monitoramento](#monitoramento)
7. [Backup e Recuperação](#backup-e-recuperação)
8. [Troubleshooting](#troubleshooting)

---

## 🔧 Pré-requisitos

### Ferramentas Necessárias
- **Docker** v20.10+
- **Docker Compose** v2.0+
- **Node.js** v18+
- **npm** v9+
- **Git** v2.30+
- **AWS CLI** v2.0+ (para produção)

### Contas e Acessos
- **Conta AWS** com permissões adequadas
- **Conta Mercado Livre Developers** configurada
- **Domínio registrado** para produção
- **Certificado SSL** válido

---

## 🏠 Ambiente de Desenvolvimento

### Setup Local Completo

#### 1. **Clone e Configuração**
```bash
# Clone do repositório
git clone https://github.com/empresa/magnow.git
cd magnow

# Copiar arquivo de ambiente
cp .env.example .env

# Instalar dependências
npm install
```

#### 2. **Configuração do .env**
```env
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/magnow_dev
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=dev-jwt-secret-super-strong
JWT_REFRESH_SECRET=dev-refresh-secret-super-strong

# Mercado Livre (Sandbox)
ML_CLIENT_ID=your-sandbox-client-id
ML_CLIENT_SECRET=your-sandbox-client-secret
ML_REDIRECT_URI=http://localhost:3000/auth/callback

# Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key-here!!

# Environment
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# Logs
LOG_LEVEL=debug
```

#### 3. **Subir Serviços**
```bash
# Subir banco e Redis
docker-compose -f docker-compose.dev.yml up -d postgres redis

# Aguardar inicialização
sleep 10

# Executar migrações
npm run migrate

# Executar seeds
npm run seed

# Iniciar desenvolvimento
npm run dev
```

#### 4. **Verificação**
```bash
# Verificar saúde da aplicação
curl http://localhost:3001/api/health

# Verificar frontend
curl http://localhost:3000

# Verificar banco
docker-compose exec postgres psql -U postgres -d magnow_dev -c "\dt"
```

---

## 🧪 Ambiente de Staging

### Configuração AWS - Staging

#### 1. **Infraestrutura como Código (Terraform)**

```hcl
# terraform/staging/main.tf
provider "aws" {
  region = "us-east-1"
}

# VPC
resource "aws_vpc" "staging" {
  cidr_block           = "********/16"
  enable_dns_hostnames = true
  enable_dns_support   = true

  tags = {
    Name        = "magnow-staging"
    Environment = "staging"
  }
}

# Subnets
resource "aws_subnet" "staging_public" {
  count             = 2
  vpc_id            = aws_vpc.staging.id
  cidr_block        = "10.1.${count.index + 1}.0/24"
  availability_zone = data.aws_availability_zones.available.names[count.index]
  
  map_public_ip_on_launch = true

  tags = {
    Name = "magnow-staging-public-${count.index + 1}"
  }
}

# RDS
resource "aws_db_instance" "staging" {
  identifier = "magnow-staging"
  
  engine         = "postgres"
  engine_version = "15.4"
  instance_class = "db.t3.micro"
  
  allocated_storage     = 20
  max_allocated_storage = 50
  storage_encrypted     = true
  
  db_name  = "magnow"
  username = "postgres"
  password = var.db_password
  
  vpc_security_group_ids = [aws_security_group.rds.id]
  db_subnet_group_name   = aws_db_subnet_group.staging.name
  
  backup_retention_period = 7
  backup_window          = "03:00-04:00"
  maintenance_window     = "sun:04:00-sun:05:00"
  
  skip_final_snapshot = true
  
  tags = {
    Name = "magnow-staging-db"
  }
}

# ElastiCache Redis
resource "aws_elasticache_subnet_group" "staging" {
  name       = "magnow-staging-cache-subnet"
  subnet_ids = aws_subnet.staging_public[*].id
}

resource "aws_elasticache_cluster" "staging" {
  cluster_id           = "magnow-staging"
  engine               = "redis"
  node_type            = "cache.t3.micro"
  num_cache_nodes      = 1
  parameter_group_name = "default.redis7"
  port                 = 6379
  subnet_group_name    = aws_elasticache_subnet_group.staging.name
  security_group_ids   = [aws_security_group.redis.id]
  
  tags = {
    Name = "magnow-staging-redis"
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "staging" {
  name = "magnow-staging"
  
  setting {
    name  = "containerInsights"
    value = "enabled"
  }
}
```

#### 2. **Deploy via GitHub Actions**

```yaml
# .github/workflows/staging.yml
name: Deploy to Staging

on:
  push:
    branches: [develop]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test:all
      
      - name: Run linting
        run: npm run lint
  
  deploy:
    needs: test
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      
      - name: Login to Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Build and push Docker images
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: magnow-staging
          IMAGE_TAG: ${{ github.sha }}
        run: |
          # Build backend
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:backend-$IMAGE_TAG ./backend
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:backend-$IMAGE_TAG
          
          # Build frontend
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:frontend-$IMAGE_TAG ./frontend
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:frontend-$IMAGE_TAG
      
      - name: Deploy to ECS
        run: |
          # Atualizar task definition
          aws ecs update-service \
            --cluster magnow-staging \
            --service magnow-staging-backend \
            --force-new-deployment
          
          aws ecs update-service \
            --cluster magnow-staging \
            --service magnow-staging-frontend \
            --force-new-deployment
      
      - name: Run database migrations
        run: |
          # Executar migrações via ECS task
          aws ecs run-task \
            --cluster magnow-staging \
            --task-definition magnow-staging-migrations \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[subnet-xxx],securityGroups=[sg-xxx],assignPublicIp=ENABLED}"
```

---

## 🚀 Ambiente de Produção

### Arquitetura de Produção

```
Internet
    │
    ▼
┌─────────────────┐
│   CloudFront    │ ← CDN Global
│   (CDN)         │
└─────────────────┘
    │
    ▼
┌─────────────────┐
│ Application     │ ← Load Balancer
│ Load Balancer   │
└─────────────────┘
    │
    ▼
┌─────────────────┐
│   ECS Cluster   │ ← Containers
│   (Multi-AZ)    │
└─────────────────┘
    │
    ▼
┌─────────────────┐
│   RDS Postgres  │ ← Database
│   (Multi-AZ)    │
└─────────────────┘
    │
    ▼
┌─────────────────┐
│ ElastiCache     │ ← Cache
│ (Redis)         │
└─────────────────┘
```

### 1. **Configuração de Produção**

#### Task Definition (ECS)
```json
{
  "family": "magnow-production",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "1024",
  "memory": "2048",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "YOUR_ECR_REGISTRY/magnow:backend-latest",
      "portMappings": [
        {
          "containerPort": 3001,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        },
        {
          "name": "PORT",
          "value": "3001"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT:secret:magnow/database-url"
        },
        {
          "name": "JWT_SECRET",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:ACCOUNT:secret:magnow/jwt-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/magnow-production",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "curl -f http://localhost:3001/api/health || exit 1"],
        "interval": 30,
        "timeout": 5,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

### 2. **Configuração de Segurança**

#### WAF Rules
```json
{
  "Name": "MagnowWAF",
  "Scope": "CLOUDFRONT",
  "DefaultAction": {
    "Allow": {}
  },
  "Rules": [
    {
      "Name": "RateLimitRule",
      "Priority": 1,
      "Statement": {
        "RateBasedStatement": {
          "Limit": 2000,
          "AggregateKeyType": "IP"
        }
      },
      "Action": {
        "Block": {}
      }
    },
    {
      "Name": "SQLInjectionRule",
      "Priority": 2,
      "Statement": {
        "SqliMatchStatement": {
          "FieldToMatch": {
            "AllQueryArguments": {}
          },
          "TextTransformations": [
            {
              "Priority": 0,
              "Type": "URL_DECODE"
            }
          ]
        }
      },
      "Action": {
        "Block": {}
      }
    }
  ]
}
```

### 3. **Monitoramento e Alertas**

#### CloudWatch Alarms
```bash
# CPU Alta
aws cloudwatch put-metric-alarm \
  --alarm-name "magnow-high-cpu" \
  --alarm-description "CPU usage above 80%" \
  --metric-name CPUUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2

# Memória Alta
aws cloudwatch put-metric-alarm \
  --alarm-name "magnow-high-memory" \
  --alarm-description "Memory usage above 80%" \
  --metric-name MemoryUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2

# Errors de API
aws cloudwatch put-metric-alarm \
  --alarm-name "magnow-api-errors" \
  --alarm-description "API error rate above 5%" \
  --metric-name 4XXError \
  --namespace AWS/CloudFront \
  --statistic Sum \
  --period 300 \
  --threshold 50 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 1
```

---

## 🔄 CI/CD Pipeline

### Pipeline Completo

```yaml
# .github/workflows/production.yml
name: Production Deploy

on:
  push:
    branches: [main]
    tags: [v*]

env:
  AWS_REGION: us-east-1
  ECR_REPOSITORY: magnow-production

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: magnow_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: |
          npm ci
          cd frontend && npm ci
      
      - name: Run linting
        run: |
          npm run lint
          cd frontend && npm run lint
      
      - name: Run tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/magnow_test
          REDIS_URL: redis://localhost:6379
        run: |
          npm run test:unit
          npm run test:integration
      
      - name: Run E2E tests
        run: |
          npm run test:e2e:headless
  
  build:
    needs: test
    runs-on: ubuntu-latest
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix=git-
      
      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
  
  deploy:
    needs: build
    runs-on: ubuntu-latest
    environment: production
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Download task definition
        run: |
          aws ecs describe-task-definition \
            --task-definition magnow-production \
            --query taskDefinition > task-definition.json
      
      - name: Fill in the new image ID in the Amazon ECS task definition
        id: task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: task-definition.json
          container-name: backend
          image: ${{ needs.build.outputs.image-tag }}
      
      - name: Deploy Amazon ECS task definition
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.task-def.outputs.task-definition }}
          service: magnow-production-backend
          cluster: magnow-production
          wait-for-service-stability: true
      
      - name: Run database migrations
        run: |
          aws ecs run-task \
            --cluster magnow-production \
            --task-definition magnow-production-migrations \
            --launch-type FARGATE \
            --network-configuration "awsvpcConfiguration={subnets=[subnet-xxx],securityGroups=[sg-xxx],assignPublicIp=ENABLED}" \
            --wait
  
  notify:
    needs: [test, build, deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

---

## 📊 Monitoramento

### 1. **CloudWatch Dashboard**

```json
{
  "widgets": [
    {
      "type": "metric",
      "properties": {
        "metrics": [
          ["AWS/ECS", "CPUUtilization", "ServiceName", "magnow-production-backend"],
          [".", "MemoryUtilization", ".", "."]
        ],
        "period": 300,
        "stat": "Average",
        "region": "us-east-1",
        "title": "ECS Service Metrics"
      }
    },
    {
      "type": "metric",
      "properties": {
        "metrics": [
          ["AWS/ApplicationELB", "RequestCount", "LoadBalancer", "magnow-production-alb"],
          [".", "TargetResponseTime", ".", "."],
          [".", "HTTPCode_Target_4XX_Count", ".", "."],
          [".", "HTTPCode_Target_5XX_Count", ".", "."]
        ],
        "period": 300,
        "stat": "Sum",
        "region": "us-east-1",
        "title": "Application Load Balancer"
      }
    },
    {
      "type": "log",
      "properties": {
        "query": "SOURCE '/ecs/magnow-production'\n| fields @timestamp, @message\n| filter @message like /ERROR/\n| sort @timestamp desc\n| limit 20",
        "region": "us-east-1",
        "title": "Recent Errors"
      }
    }
  ]
}
```

### 2. **Alertas Personalizados**

```python
# scripts/custom-alerts.py
import boto3
import json

def create_custom_alerts():
    cloudwatch = boto3.client('cloudwatch')
    
    # Alert para usuários ativos
    cloudwatch.put_metric_alarm(
        AlarmName='magnow-low-active-users',
        ComparisonOperator='LessThanThreshold',
        EvaluationPeriods=1,
        MetricName='ActiveUsers',
        Namespace='Magnow/Custom',
        Period=3600,
        Statistic='Average',
        Threshold=10.0,
        ActionsEnabled=True,
        AlarmActions=[
            'arn:aws:sns:us-east-1:ACCOUNT:magnow-alerts'
        ],
        AlarmDescription='Baixo número de usuários ativos'
    )
    
    # Alert para sync failures
    cloudwatch.put_metric_alarm(
        AlarmName='magnow-sync-failures',
        ComparisonOperator='GreaterThanThreshold',
        EvaluationPeriods=1,
        MetricName='SyncFailures',
        Namespace='Magnow/Custom',
        Period=300,
        Statistic='Sum',
        Threshold=5.0,
        ActionsEnabled=True,
        AlarmActions=[
            'arn:aws:sns:us-east-1:ACCOUNT:magnow-alerts'
        ],
        AlarmDescription='Muitas falhas de sincronização'
    )

if __name__ == "__main__":
    create_custom_alerts()
```

---

## 💾 Backup e Recuperação

### 1. **Backup Automatizado**

```bash
#!/bin/bash
# scripts/backup-production.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_BUCKET="magnow-backups-production"
DB_ENDPOINT="magnow-production.cluster-xxxxx.us-east-1.rds.amazonaws.com"

# Backup do banco
pg_dump -h $DB_ENDPOINT -U postgres -d magnow \
    --format=custom \
    --compress=9 \
    --no-owner \
    --no-privileges \
    --file="/tmp/magnow_backup_$DATE.dump"

# Upload para S3
aws s3 cp "/tmp/magnow_backup_$DATE.dump" \
    "s3://$BACKUP_BUCKET/database/magnow_backup_$DATE.dump"

# Backup de arquivos (se houver)
tar -czf "/tmp/magnow_files_$DATE.tar.gz" /app/uploads/
aws s3 cp "/tmp/magnow_files_$DATE.tar.gz" \
    "s3://$BACKUP_BUCKET/files/magnow_files_$DATE.tar.gz"

# Limpeza local
rm -f "/tmp/magnow_backup_$DATE.dump"
rm -f "/tmp/magnow_files_$DATE.tar.gz"

# Limpeza S3 (manter apenas últimos 30 dias)
aws s3 ls "s3://$BACKUP_BUCKET/database/" --recursive | \
    awk '$1 <= "'$(date -d '30 days ago' '+%Y-%m-%d')'"' | \
    awk '{print $4}' | \
    xargs -I {} aws s3 rm "s3://$BACKUP_BUCKET/{}"
```

### 2. **Processo de Recuperação**

```bash
#!/bin/bash
# scripts/restore-production.sh

BACKUP_FILE=$1
RESTORE_DB_NAME="magnow_restore_$(date +%Y%m%d_%H%M%S)"

if [ -z "$BACKUP_FILE" ]; then
    echo "Uso: $0 <s3://bucket/path/to/backup.dump>"
    exit 1
fi

# Download do backup
aws s3 cp "$BACKUP_FILE" "/tmp/restore.dump"

# Criar banco temporário
createdb -h $DB_ENDPOINT -U postgres $RESTORE_DB_NAME

# Restaurar backup
pg_restore -h $DB_ENDPOINT -U postgres -d $RESTORE_DB_NAME \
    --verbose \
    --clean \
    --if-exists \
    "/tmp/restore.dump"

echo "Banco restaurado como: $RESTORE_DB_NAME"
echo "Para usar em produção, execute:"
echo "ALTER DATABASE magnow RENAME TO magnow_old;"
echo "ALTER DATABASE $RESTORE_DB_NAME RENAME TO magnow;"

# Cleanup
rm -f "/tmp/restore.dump"
```

---

## 🔧 Troubleshooting

### Problemas Comuns

#### 1. **Serviço não está respondendo**
```bash
# Verificar logs
aws logs get-log-events \
    --log-group-name /ecs/magnow-production \
    --log-stream-name ecs/backend/xxxx \
    --start-time $(date -d '1 hour ago' +%s)000

# Verificar health checks
aws elbv2 describe-target-health \
    --target-group-arn arn:aws:elasticloadbalancing:us-east-1:ACCOUNT:targetgroup/magnow/xxxxx

# Restart do serviço
aws ecs update-service \
    --cluster magnow-production \
    --service magnow-production-backend \
    --force-new-deployment
```

#### 2. **Banco de dados com alta latência**
```sql
-- Verificar conexões ativas
SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active';

-- Verificar queries lentas
SELECT query, query_start, now() - query_start AS duration 
FROM pg_stat_activity 
WHERE now() - query_start > interval '5 minutes';

-- Verificar locks
SELECT * FROM pg_locks WHERE NOT granted;
```

#### 3. **Cache não funcionando**
```bash
# Verificar conexão Redis
redis-cli -h magnow-production.xxxxx.cache.amazonaws.com ping

# Verificar uso de memória
redis-cli -h magnow-production.xxxxx.cache.amazonaws.com info memory

# Limpar cache se necessário
redis-cli -h magnow-production.xxxxx.cache.amazonaws.com flushall
```

### Scripts de Diagnóstico

```bash
#!/bin/bash
# scripts/diagnose-production.sh

echo "=== DIAGNÓSTICO MAGNOW PRODUÇÃO ==="

# Verificar serviços ECS
echo "--- Serviços ECS ---"
aws ecs describe-services \
    --cluster magnow-production \
    --services magnow-production-backend magnow-production-frontend

# Verificar RDS
echo "--- Status RDS ---"
aws rds describe-db-instances \
    --db-instance-identifier magnow-production

# Verificar CloudFront
echo "--- Status CloudFront ---"
aws cloudfront get-distribution \
    --id EXXXXXXXXXXXXX

# Verificar alarmes ativos
echo "--- Alarmes Ativos ---"
aws cloudwatch describe-alarms \
    --state-value ALARM \
    --alarm-names magnow-high-cpu magnow-high-memory magnow-api-errors

echo "=== FIM DO DIAGNÓSTICO ==="
```

---

## 📈 Otimizações de Performance

### 1. **Auto Scaling**

```json
{
  "ServiceName": "magnow-production-backend",
  "PolicyName": "magnow-scaling-policy",
  "PolicyType": "TargetTrackingScaling",
  "TargetTrackingConfiguration": {
    "TargetValue": 70,
    "PredefinedMetricSpecification": {
      "PredefinedMetricType": "ECSServiceAverageCPUUtilization"
    },
    "ScaleOutCooldown": 300,
    "ScaleInCooldown": 300
  }
}
```

### 2. **Otimização de Banco**

```sql
-- Configurações recomendadas para PostgreSQL
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Recarregar configuração
SELECT pg_reload_conf();
```

---

**Última atualização:** Janeiro 2025
**Versão do Guia:** 1.0.0 