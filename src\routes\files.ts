/**
 * Rotas de Arquivos
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Router } from 'express';
import { FilesController } from '../controllers/filesController';
import { requireAuth } from '../middleware/auth';
import { validateTenant } from '../middleware/validateTenant';
import { rateLimiter } from '../middleware/rateLimiter';
import { 
  userActivityLogger, 
  sensitiveOperationLogger 
} from '@/middleware/logger';
import { uploadAvatar, uploadDocument, uploadSpreadsheet } from '../middleware/uploadMiddleware';

const router: Router = Router();
const filesController = new FilesController();

// Aplicar middlewares globais
router.use(userActivityLogger);

// =====================================================
// ROTAS PÚBLICAS (sem autenticação)
// =====================================================

// Health check
router.get('/health', (req, res) => {
  res.json({
    service: 'Files API',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// =====================================================
// ROTAS PROTEGIDAS (requerem autenticação)
// =====================================================

router.use(requireAuth);
router.use(validateTenant);

// =====================================================
// UPLOAD DE ARQUIVOS
// =====================================================

/**
 * POST /api/files/upload/avatar
 * Upload de avatar de usuário
 */
router.post('/upload/avatar',
  rateLimiter({ windowMs: 15 * 60 * 1000, max: 10 }), // 10 uploads por 15 minutos
  sensitiveOperationLogger,
  uploadAvatar,
  filesController.uploadAvatar
);

/**
 * POST /api/files/upload/document
 * Upload de documentos (PDFs de comprovante, etc.)
 */
router.post('/upload/document',
  rateLimiter({ windowMs: 15 * 60 * 1000, max: 20 }), // 20 uploads por 15 minutos
  sensitiveOperationLogger,
  uploadDocument,
  filesController.uploadDocument
);

/**
 * POST /api/files/upload/spreadsheet
 * Upload de planilhas
 */
router.post('/upload/spreadsheet',
  rateLimiter({ windowMs: 15 * 60 * 1000, max: 5 }), // 5 uploads por 15 minutos
  sensitiveOperationLogger,
  uploadSpreadsheet,
  filesController.uploadSpreadsheet
);

// =====================================================
// DOWNLOAD E ACESSO A ARQUIVOS
// =====================================================

/**
 * GET /api/files/download/:fileId
 * Download de arquivo por ID
 */
router.get('/download/:fileId',
  rateLimiter({ windowMs: 60 * 1000, max: 100 }), // 100 downloads por minuto
  filesController.downloadFile
);

/**
 * GET /api/files/stream/:fileId
 * Stream de arquivo (para arquivos grandes)
 */
router.get('/stream/:fileId',
  rateLimiter({ windowMs: 60 * 1000, max: 50 }), // 50 streams por minuto
  filesController.streamFile
);

/**
 * GET /api/files/preview/:fileId
 * Preview de arquivo (para imagens)
 */
router.get('/preview/:fileId',
  rateLimiter({ windowMs: 60 * 1000, max: 200 }), // 200 previews por minuto
  filesController.previewFile
);

// =====================================================
// GERENCIAMENTO DE ARQUIVOS
// =====================================================

/**
 * GET /api/files
 * Listar arquivos do tenant
 */
router.get('/',
  rateLimiter({ windowMs: 60 * 1000, max: 60 }), // 60 requests por minuto
  filesController.listFiles
);

/**
 * GET /api/files/:fileId
 * Obter informações de um arquivo específico
 */
router.get('/:fileId',
  rateLimiter({ windowMs: 60 * 1000, max: 100 }), // 100 requests por minuto
  filesController.getFileInfo
);

/**
 * DELETE /api/files/:fileId
 * Deletar arquivo
 */
router.delete('/:fileId',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 deletes por minuto
  sensitiveOperationLogger,
  filesController.deleteFile
);

/**
 * PUT /api/files/:fileId/metadata
 * Atualizar metadados do arquivo
 */
router.put('/:fileId/metadata',
  rateLimiter({ windowMs: 60 * 1000, max: 50 }), // 50 updates por minuto
  sensitiveOperationLogger,
  filesController.updateFileMetadata
);

// =====================================================
// ESTATÍSTICAS E RELATÓRIOS
// =====================================================

/**
 * GET /api/files/stats/overview
 * Estatísticas gerais de arquivos do tenant
 */
router.get('/stats/overview',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  filesController.getFileStats
);

/**
 * GET /api/files/stats/usage
 * Estatísticas de uso de storage
 */
router.get('/stats/usage',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  filesController.getStorageUsage
);

// =====================================================
// OPERAÇÕES ADMINISTRATIVAS
// =====================================================

/**
 * POST /api/files/cleanup/expired
 * Limpar arquivos expirados (apenas admins)
 */
router.post('/cleanup/expired',
  rateLimiter({ windowMs: 60 * 1000, max: 5 }), // 5 requests por minuto
  sensitiveOperationLogger,
  filesController.cleanupExpiredFiles
);

/**
 * POST /api/files/cleanup/temp
 * Limpar arquivos temporários (apenas admins)
 */
router.post('/cleanup/temp',
  rateLimiter({ windowMs: 60 * 1000, max: 5 }), // 5 requests por minuto
  sensitiveOperationLogger,
  filesController.cleanupTempFiles
);

// =====================================================
// ROTAS DE DESENVOLVIMENTO (remover em produção)
// =====================================================

if (process.env.NODE_ENV === 'development') {
  /**
   * GET /api/files/dev/test-upload
   * Página de teste de upload (apenas desenvolvimento)
   */
  router.get('/dev/test-upload', (req, res) => {
    res.send(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Test File Upload</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; }
          .upload-form { margin: 20px 0; padding: 20px; border: 1px solid #ddd; }
          input[type="file"] { margin: 10px 0; }
          button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
          button:hover { background: #0056b3; }
        </style>
      </head>
      <body>
        <h1>File Upload Test</h1>
        
        <div class="upload-form">
          <h3>Upload Avatar</h3>
          <form action="/api/files/upload/avatar" method="post" enctype="multipart/form-data">
            <input type="file" name="file" accept="image/*" required>
            <button type="submit">Upload Avatar</button>
          </form>
        </div>
        
        <div class="upload-form">
          <h3>Upload Document</h3>
          <form action="/api/files/upload/document" method="post" enctype="multipart/form-data">
            <input type="file" name="file" accept=".pdf" required>
            <button type="submit">Upload Document</button>
          </form>
        </div>
        
        <div class="upload-form">
          <h3>Upload Spreadsheet</h3>
          <form action="/api/files/upload/spreadsheet" method="post" enctype="multipart/form-data">
            <input type="file" name="file" accept=".xlsx,.xls,.csv" required>
            <button type="submit">Upload Spreadsheet</button>
          </form>
        </div>
      </body>
      </html>
    `);
  });
}

export default router;
