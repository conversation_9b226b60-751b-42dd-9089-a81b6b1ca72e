# Database Schema - Magnow

## Sistema de Controle Inteligente de Estoque para Mercado Livre

### PostgreSQL Schema v1.0.0

---

## 📋 Índice

1. [Visão Geral](#visão-geral)
2. [Diagrama de Relacionamentos](#diagrama-de-relacionamentos)
3. [Tabelas Principais](#tabelas-principais)
4. [Índices](#índices)
5. [Constraints](#constraints)
6. [Views](#views)
7. [Stored Procedures](#stored-procedures)
8. [Migrations](#migrations)

---

## 🎯 Visão Geral

O banco de dados do Magnow foi projetado com arquitetura multi-tenant, onde cada cliente (tenant) tem seus dados completamente isolados através do campo `tenant_id` presente em todas as tabelas principais.

### Características Principais:
- **Multi-tenant**: Isolamento por `tenant_id`
- **Normalizado**: Estrutura relacional otimizada
- **Auditável**: Campos de timestamp em todas as tabelas
- **Escalável**: Preparado para crescimento horizontal
- **Seguro**: Dados sensíveis criptografados

---

## 🔗 Diagrama de Relacionamentos

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│   tenants   │◄────┤    users    │     │ ml_accounts │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
       │                   │                   │
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  products   │◄────┤    sales    │     │spreadsheets │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
       │                   │                   │
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│stock_calc   │     │audit_logs   │     │   configs   │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
```

---

## 📊 Tabelas Principais

### 1. **tenants** - Clientes do Sistema

```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    plan VARCHAR(20) DEFAULT 'basic' CHECK (plan IN ('basic', 'pro', 'enterprise')),
    max_ml_accounts INTEGER DEFAULT 5,
    max_products INTEGER DEFAULT 1000,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Comentários
COMMENT ON TABLE tenants IS 'Clientes do sistema (arquitetura multi-tenant)';
COMMENT ON COLUMN tenants.slug IS 'Identificador único para URLs e subdomínios';
COMMENT ON COLUMN tenants.settings IS 'Configurações personalizadas do tenant';
```

### 2. **users** - Usuários do Sistema

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user' CHECK (role IN ('admin', 'manager', 'user', 'viewer')),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP WITH TIME ZONE,
    email_verified BOOLEAN DEFAULT false,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, email)
);

COMMENT ON TABLE users IS 'Usuários do sistema com acesso baseado em roles';
COMMENT ON COLUMN users.password_hash IS 'Hash bcrypt da senha (salt rounds: 12)';
COMMENT ON COLUMN users.settings IS 'Preferências pessoais do usuário';
```

### 3. **ml_accounts** - Contas do Mercado Livre

```sql
CREATE TABLE ml_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id VARCHAR(255) NOT NULL, -- ML User ID
    nickname VARCHAR(255),
    email VARCHAR(255),
    access_token_encrypted TEXT NOT NULL,
    refresh_token_encrypted TEXT NOT NULL,
    token_expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    scopes TEXT[] DEFAULT ARRAY['read', 'write'],
    is_active BOOLEAN DEFAULT true,
    last_sync TIMESTAMP WITH TIME ZONE,
    sync_status VARCHAR(20) DEFAULT 'pending' CHECK (sync_status IN ('pending', 'syncing', 'completed', 'error')),
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, user_id)
);

COMMENT ON TABLE ml_accounts IS 'Contas do Mercado Livre conectadas via OAuth';
COMMENT ON COLUMN ml_accounts.access_token_encrypted IS 'Token OAuth criptografado (AES-256)';
COMMENT ON COLUMN ml_accounts.metadata IS 'Dados adicionais da conta ML';
```

### 4. **products** - Produtos Sincronizados

```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    ml_account_id UUID NOT NULL REFERENCES ml_accounts(id) ON DELETE CASCADE,
    ml_item_id VARCHAR(255) NOT NULL,
    sku VARCHAR(255),
    title TEXT NOT NULL,
    description TEXT,
    current_stock INTEGER DEFAULT 0,
    price DECIMAL(10,2),
    currency VARCHAR(3) DEFAULT 'BRL',
    status VARCHAR(20) CHECK (status IN ('active', 'paused', 'closed', 'under_review')),
    condition VARCHAR(20) CHECK (condition IN ('new', 'used')),
    category_id VARCHAR(255),
    category_name VARCHAR(255),
    permalink VARCHAR(500),
    thumbnail VARCHAR(500),
    pictures JSONB DEFAULT '[]',
    attributes JSONB DEFAULT '{}',
    variations JSONB DEFAULT '[]',
    shipping JSONB DEFAULT '{}',
    tags TEXT[] DEFAULT '{}',
    is_eligible_full BOOLEAN DEFAULT false,
    last_sync TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    sync_error TEXT,
    custom_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, ml_item_id)
);

COMMENT ON TABLE products IS 'Produtos sincronizados do Mercado Livre';
COMMENT ON COLUMN products.is_eligible_full IS 'Elegível para Mercado Envios Full';
COMMENT ON COLUMN products.custom_data IS 'Dados customizados do usuário (fornecedor, etc)';
```

### 5. **sales** - Vendas dos Produtos

```sql
CREATE TABLE sales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    ml_order_id VARCHAR(255) NOT NULL,
    ml_order_item_id VARCHAR(255),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'BRL',
    buyer_id VARCHAR(255),
    buyer_nickname VARCHAR(255),
    sale_date TIMESTAMP WITH TIME ZONE NOT NULL,
    order_status VARCHAR(50),
    shipping_status VARCHAR(50),
    payment_status VARCHAR(50),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, ml_order_item_id)
);

COMMENT ON TABLE sales IS 'Histórico de vendas dos produtos';
COMMENT ON COLUMN sales.sale_date IS 'Data da venda (não do pagamento)';
```

### 6. **stock_calculations** - Cálculos de Estoque

```sql
CREATE TABLE stock_calculations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    calculation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    period_days INTEGER NOT NULL,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    total_sales INTEGER NOT NULL DEFAULT 0,
    average_daily_sales DECIMAL(10,2) NOT NULL DEFAULT 0,
    coverage_days INTEGER NOT NULL,
    safety_stock INTEGER DEFAULT 0,
    ideal_stock INTEGER NOT NULL,
    current_stock INTEGER NOT NULL,
    in_transit INTEGER DEFAULT 0,
    gap_quantity INTEGER NOT NULL,
    gap_priority VARCHAR(10) CHECK (gap_priority IN ('low', 'medium', 'high', 'critical')),
    days_of_stock DECIMAL(5,2),
    stockout_risk DECIMAL(5,2),
    calculation_method VARCHAR(50) DEFAULT 'moving_average',
    parameters JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE stock_calculations IS 'Resultados dos cálculos de estoque ideal';
COMMENT ON COLUMN stock_calculations.gap_quantity IS 'Quantidade necessária (positivo) ou excesso (negativo)';
COMMENT ON COLUMN stock_calculations.parameters IS 'Parâmetros usados no cálculo';
```

### 7. **spreadsheets** - Planilhas Geradas

```sql
CREATE TABLE spreadsheets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'generating' CHECK (status IN ('generating', 'ready', 'error', 'expired')),
    file_path VARCHAR(500),
    file_size BIGINT,
    products_count INTEGER DEFAULT 0,
    total_quantity INTEGER DEFAULT 0,
    total_value DECIMAL(12,2) DEFAULT 0,
    warehouse VARCHAR(100),
    generation_params JSONB DEFAULT '{}',
    error_message TEXT,
    downloaded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE spreadsheets IS 'Planilhas geradas para envio ao Mercado Envios Full';
COMMENT ON COLUMN spreadsheets.generation_params IS 'Parâmetros usados na geração';
```

### 8. **spreadsheet_items** - Itens das Planilhas

```sql
CREATE TABLE spreadsheet_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    spreadsheet_id UUID NOT NULL REFERENCES spreadsheets(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id),
    sku VARCHAR(255) NOT NULL,
    title VARCHAR(500) NOT NULL,
    current_stock INTEGER NOT NULL,
    recommended_quantity INTEGER NOT NULL,
    final_quantity INTEGER NOT NULL,
    unit_value DECIMAL(10,2),
    total_value DECIMAL(12,2),
    priority VARCHAR(10),
    notes TEXT,
    position INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE spreadsheet_items IS 'Itens individuais de cada planilha';
COMMENT ON COLUMN spreadsheet_items.final_quantity IS 'Quantidade após ajustes manuais';
```

### 9. **audit_logs** - Logs de Auditoria

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(255),
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE audit_logs IS 'Log de todas as ações importantes do sistema';
COMMENT ON COLUMN audit_logs.action IS 'create, update, delete, login, etc';
```

### 10. **tenant_configs** - Configurações dos Tenants

```sql
CREATE TABLE tenant_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    config_key VARCHAR(100) NOT NULL,
    config_value JSONB NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, config_key)
);

COMMENT ON TABLE tenant_configs IS 'Configurações específicas por tenant';
COMMENT ON COLUMN tenant_configs.is_system IS 'true para configs que não podem ser alteradas pelo usuário';
```

---

## 🔍 Índices

### Índices Primários
```sql
-- Performance em consultas multi-tenant
CREATE INDEX idx_users_tenant_id ON users(tenant_id);
CREATE INDEX idx_ml_accounts_tenant_id ON ml_accounts(tenant_id);
CREATE INDEX idx_products_tenant_id ON products(tenant_id);
CREATE INDEX idx_sales_tenant_id ON sales(tenant_id);
CREATE INDEX idx_stock_calculations_tenant_id ON stock_calculations(tenant_id);

-- Consultas frequentes
CREATE INDEX idx_products_ml_account_id ON products(ml_account_id);
CREATE INDEX idx_products_status ON products(tenant_id, status);
CREATE INDEX idx_products_sku ON products(tenant_id, sku) WHERE sku IS NOT NULL;

-- Vendas por período
CREATE INDEX idx_sales_product_date ON sales(product_id, sale_date);
CREATE INDEX idx_sales_date_range ON sales(tenant_id, sale_date);

-- Cálculos recentes
CREATE INDEX idx_stock_calc_product_date ON stock_calculations(product_id, calculation_date DESC);
CREATE INDEX idx_stock_calc_gaps ON stock_calculations(tenant_id, gap_quantity) WHERE gap_quantity > 0;

-- Auditoria
CREATE INDEX idx_audit_logs_tenant_date ON audit_logs(tenant_id, created_at);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);

-- Performance em autenticação
CREATE INDEX idx_users_email_active ON users(email) WHERE is_active = true;
```

### Índices Parciais
```sql
-- Apenas produtos ativos
CREATE INDEX idx_products_active ON products(tenant_id, ml_account_id) 
    WHERE status = 'active';

-- Apenas contas ativas do ML
CREATE INDEX idx_ml_accounts_active ON ml_accounts(tenant_id) 
    WHERE is_active = true;

-- Planilhas não expiradas
CREATE INDEX idx_spreadsheets_active ON spreadsheets(tenant_id, created_at) 
    WHERE status != 'expired';
```

---

## ⚠️ Constraints

### Check Constraints
```sql
-- Validações de negócio
ALTER TABLE products ADD CONSTRAINT chk_products_stock_positive 
    CHECK (current_stock >= 0);

ALTER TABLE sales ADD CONSTRAINT chk_sales_quantity_positive 
    CHECK (quantity > 0);

ALTER TABLE sales ADD CONSTRAINT chk_sales_amounts_positive 
    CHECK (unit_price >= 0 AND total_amount >= 0);

ALTER TABLE stock_calculations ADD CONSTRAINT chk_stock_calc_period_valid 
    CHECK (period_days > 0 AND period_days <= 365);

ALTER TABLE stock_calculations ADD CONSTRAINT chk_stock_calc_coverage_valid 
    CHECK (coverage_days > 0 AND coverage_days <= 180);
```

### Foreign Key Constraints
```sql
-- Integridade referencial em cascata
ALTER TABLE users ADD CONSTRAINT fk_users_tenant 
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;

ALTER TABLE ml_accounts ADD CONSTRAINT fk_ml_accounts_tenant 
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;

-- Proteção contra remoção acidental
ALTER TABLE sales ADD CONSTRAINT fk_sales_product 
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT;
```

---

## 👁️ Views

### 1. **v_products_summary** - Resumo de Produtos

```sql
CREATE VIEW v_products_summary AS
SELECT 
    p.id,
    p.tenant_id,
    p.sku,
    p.title,
    p.current_stock,
    p.price,
    p.status,
    ma.nickname as ml_account_nickname,
    sc.ideal_stock,
    sc.gap_quantity,
    sc.gap_priority,
    sc.calculation_date as last_calculated,
    COALESCE(s.sales_30d, 0) as sales_30d,
    COALESCE(s.avg_daily_sales, 0) as avg_daily_sales
FROM products p
LEFT JOIN ml_accounts ma ON p.ml_account_id = ma.id
LEFT JOIN LATERAL (
    SELECT *
    FROM stock_calculations sc2
    WHERE sc2.product_id = p.id
    ORDER BY sc2.calculation_date DESC
    LIMIT 1
) sc ON true
LEFT JOIN LATERAL (
    SELECT 
        COUNT(*) as sales_30d,
        AVG(quantity) as avg_daily_sales
    FROM sales s2
    WHERE s2.product_id = p.id
    AND s2.sale_date >= NOW() - INTERVAL '30 days'
) s ON true;
```

### 2. **v_tenant_dashboard** - Dashboard do Tenant

```sql
CREATE VIEW v_tenant_dashboard AS
SELECT 
    t.id as tenant_id,
    t.name as tenant_name,
    COUNT(DISTINCT ma.id) as active_ml_accounts,
    COUNT(DISTINCT p.id) as total_products,
    COUNT(DISTINCT CASE WHEN p.status = 'active' THEN p.id END) as active_products,
    COUNT(DISTINCT CASE WHEN sc.gap_quantity > 0 THEN p.id END) as products_with_gap,
    SUM(CASE WHEN sc.gap_quantity > 0 THEN sc.gap_quantity * p.price ELSE 0 END) as total_gap_value,
    MAX(ma.last_sync) as last_sync
FROM tenants t
LEFT JOIN ml_accounts ma ON t.id = ma.tenant_id AND ma.is_active = true
LEFT JOIN products p ON ma.id = p.ml_account_id
LEFT JOIN LATERAL (
    SELECT *
    FROM stock_calculations sc2
    WHERE sc2.product_id = p.id
    ORDER BY sc2.calculation_date DESC
    LIMIT 1
) sc ON true
GROUP BY t.id, t.name;
```

---

## 🔧 Stored Procedures

### 1. **calculate_stock_for_product** - Calcular Estoque

```sql
CREATE OR REPLACE FUNCTION calculate_stock_for_product(
    p_product_id UUID,
    p_period_days INTEGER DEFAULT 30,
    p_coverage_days INTEGER DEFAULT 15,
    p_safety_stock INTEGER DEFAULT 0
) RETURNS UUID AS $$
DECLARE
    v_tenant_id UUID;
    v_current_stock INTEGER;
    v_total_sales INTEGER;
    v_avg_daily_sales DECIMAL(10,2);
    v_ideal_stock INTEGER;
    v_gap_quantity INTEGER;
    v_gap_priority VARCHAR(10);
    v_calculation_id UUID;
BEGIN
    -- Obter dados do produto
    SELECT tenant_id, current_stock 
    INTO v_tenant_id, v_current_stock
    FROM products 
    WHERE id = p_product_id;
    
    IF v_tenant_id IS NULL THEN
        RAISE EXCEPTION 'Produto não encontrado: %', p_product_id;
    END IF;
    
    -- Calcular vendas no período
    SELECT 
        COALESCE(SUM(quantity), 0),
        COALESCE(SUM(quantity)::DECIMAL / p_period_days, 0)
    INTO v_total_sales, v_avg_daily_sales
    FROM sales 
    WHERE product_id = p_product_id 
    AND sale_date >= NOW() - (p_period_days || ' days')::INTERVAL;
    
    -- Calcular estoque ideal
    v_ideal_stock := CEIL(v_avg_daily_sales * p_coverage_days) + p_safety_stock;
    
    -- Calcular gap
    v_gap_quantity := v_ideal_stock - v_current_stock;
    
    -- Determinar prioridade
    v_gap_priority := CASE 
        WHEN v_gap_quantity <= 0 THEN 'low'
        WHEN v_gap_quantity <= 5 THEN 'medium'
        WHEN v_gap_quantity <= 20 THEN 'high'
        ELSE 'critical'
    END;
    
    -- Inserir resultado
    INSERT INTO stock_calculations (
        tenant_id, product_id, period_days, period_start, period_end,
        total_sales, average_daily_sales, coverage_days, safety_stock,
        ideal_stock, current_stock, gap_quantity, gap_priority,
        days_of_stock, calculation_method
    ) VALUES (
        v_tenant_id, p_product_id, p_period_days,
        NOW() - (p_period_days || ' days')::INTERVAL, NOW(),
        v_total_sales, v_avg_daily_sales, p_coverage_days, p_safety_stock,
        v_ideal_stock, v_current_stock, v_gap_quantity, v_gap_priority,
        CASE WHEN v_avg_daily_sales > 0 THEN v_current_stock / v_avg_daily_sales ELSE NULL END,
        'moving_average'
    ) RETURNING id INTO v_calculation_id;
    
    RETURN v_calculation_id;
END;
$$ LANGUAGE plpgsql;
```

---

## 📈 Migrations

### Estrutura de Migration

```sql
-- migrations/001_initial_schema.sql
-- migrations/002_add_audit_logs.sql
-- migrations/003_add_spreadsheets.sql
-- migrations/004_optimize_indexes.sql
```

### Exemplo de Migration

```sql
-- migrations/001_initial_schema.sql
BEGIN;

-- Criar extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Criar tabelas principais
-- (código das tabelas aqui)

-- Inserir dados iniciais
INSERT INTO tenants (name, slug, email, status) VALUES 
    ('Sistema Admin', 'system', '<EMAIL>', 'active');

COMMIT;
```

---

## 🔄 Backup e Restore

### Script de Backup
```bash
#!/bin/bash
# backup-database.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/magnow"
DB_NAME="magnow"

# Backup completo
pg_dump -h localhost -U postgres -d $DB_NAME \
    --format=custom \
    --compress=9 \
    --file="$BACKUP_DIR/magnow_full_$DATE.dump"

# Backup apenas dados
pg_dump -h localhost -U postgres -d $DB_NAME \
    --data-only \
    --format=custom \
    --file="$BACKUP_DIR/magnow_data_$DATE.dump"
```

### Script de Restore
```bash
#!/bin/bash
# restore-database.sh

BACKUP_FILE=$1
DB_NAME="magnow"

if [ -z "$BACKUP_FILE" ]; then
    echo "Uso: $0 <arquivo_backup>"
    exit 1
fi

# Restore
pg_restore -h localhost -U postgres -d $DB_NAME \
    --clean \
    --if-exists \
    --verbose \
    $BACKUP_FILE
```

---

**Última atualização:** Janeiro 2025
**Versão do Schema:** 1.0.0 