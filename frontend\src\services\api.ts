import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import type {
  ApiResponse,
  ApiError,
  AuthResponse,
  Product,
  ProductWithStock,
  StockCalculation,
  StockAlert,
  StockUpdateRequest,
  StockUpdateResponse,
  BulkStockUpdateRequest,
  BulkStockUpdateResponse,
  StockHistoryEntry,
  MLProductFilters,
  Spreadsheet,
  SpreadsheetHistory,
  SpreadsheetStatistics,
  Warehouse,
  DashboardStats,
  ProductFilters,
  StockFilters,
  PaginatedResponse
} from '../types/api';

// Extensão do AxiosRequestConfig para incluir propriedades customizadas
interface CustomAxiosRequestConfig extends AxiosRequestConfig {
  metadata?: {
    cache?: boolean;
    cacheTtl?: number;
  };
  retryConfig?: RetryConfig;
  cache?: boolean;
  cacheTtl?: number;
  _retryCount?: number;
}

// Cache simples em memória
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  set(key: string, data: any, ttl = 5 * 60 * 1000) { // 5 minutos padrão
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  clear() {
    this.cache.clear();
  }

  delete(key: string) {
    this.cache.delete(key);
  }
}

// Retry configuration
interface RetryConfig {
  retries: number;
  retryDelay: number;
  retryCondition?: (error: any) => boolean;
}

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;
  private cache = new ApiCache();
  private defaultRetryConfig: RetryConfig = {
    retries: 3,
    retryDelay: 1000,
    retryCondition: (error) => {
      return !error.response || error.response.status >= 500 || error.code === 'ECONNABORTED';
    }
  };

  constructor() {
    this.baseURL = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor - adiciona token de autenticação e headers
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Adicionar tenant ID se disponível
        const tenantId = localStorage.getItem('tenant_id');
        if (tenantId) {
          config.headers['X-Tenant-ID'] = tenantId;
        }

        // Adicionar request ID para rastreamento
        config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor - trata erros globalmente e cache
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        // Cache GET requests se configurado
        const customConfig = response.config as CustomAxiosRequestConfig;
        if (response.config.method === 'get' && customConfig.metadata?.cache) {
          const cacheKey = this.getCacheKey(response.config);
          this.cache.set(cacheKey, response.data, customConfig.metadata.cacheTtl);
        }

        return response;
      },
      async (error) => {
        const originalRequest = error.config as CustomAxiosRequestConfig;

        // Retry logic
        if (this.shouldRetry(error, originalRequest)) {
          return this.retryRequest(originalRequest);
        }

        // Handle specific error cases
        if (error.response?.status === 401) {
          await this.handleUnauthorized();
          return Promise.reject(error);
        }

        // Os toasts foram removidos daqui. A lógica de UI deve ser tratada no local da chamada.

        return Promise.reject(error);
      }
    );
  }

  private shouldRetry(error: any, config: CustomAxiosRequestConfig): boolean {
    if ((config._retryCount || 0) >= (config.retryConfig?.retries || this.defaultRetryConfig.retries)) {
      return false;
    }

    const retryCondition = config.retryConfig?.retryCondition || this.defaultRetryConfig.retryCondition;
    return retryCondition ? retryCondition(error) : false;
  }

  private async retryRequest(config: CustomAxiosRequestConfig): Promise<any> {
    config._retryCount = (config._retryCount || 0) + 1;

    const delay = (config.retryConfig?.retryDelay || this.defaultRetryConfig.retryDelay) * config._retryCount;
    
    await new Promise(resolve => setTimeout(resolve, delay));
    
    return this.api.request(config);
  }

  private async handleUnauthorized(): Promise<void> {
    try {
      // Tentar renovar o token
      const refreshToken = localStorage.getItem('refresh_token');
      if (refreshToken) {
        const response = await this.refreshToken();
        if (response.success && response.data) {
          localStorage.setItem('auth_token', response.data.token);
          if (response.data.refreshToken) {
            localStorage.setItem('refresh_token', response.data.refreshToken);
          }
          return;
        }
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
    }

    // Se não conseguir renovar, fazer logout
    this.logout();
    localStorage.removeItem('auth_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    this.cache.clear();
    
    // Redirecionar para login se não estiver já na página de login
    // Em desenvolvimento, não redirecionar automaticamente para evitar loops
    const isDevelopment = import.meta.env.DEV;
    if (!isDevelopment && !window.location.pathname.includes('/login')) {
      window.location.href = '/login';
    }
  }

  private getCacheKey(config: AxiosRequestConfig): string {
    const { url, params, data } = config;
    return `${url}_${JSON.stringify(params || {})}_${JSON.stringify(data || {})}`;
  }

  // Método genérico para requisições com cache e retry
  private async request<T>(
    config: CustomAxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      // Verificar cache para GET requests
      if (config.method === 'GET' && config.cache) {
        const cacheKey = this.getCacheKey(config);
        const cached = this.cache.get(cacheKey);
        if (cached) {
          return cached;
        }
      }

      // Adicionar metadata para interceptors
      config.metadata = {
        cache: config.cache,
        cacheTtl: config.cacheTtl
      };

      const response = await this.api.request<ApiResponse<T>>(config);
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        throw error.response.data as ApiError;
      }
      throw {
        success: false,
        error: 'network_error',
        message: 'Erro de conexão com o servidor',
      } as ApiError;
    }
  }

  // Método para invalidar cache
  public invalidateCache(pattern?: string): void {
    if (pattern) {
      // Invalidar cache por padrão (implementação simples)
      for (const [key] of this.cache['cache']) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  // Autenticação
  async login(email: string, password: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse['data']>({
      method: 'POST',
      url: '/api/auth/login',
      data: { email, password },
    });

    if (response.success && response.data) {
      localStorage.setItem('auth_token', response.data.token);
      if (response.data.refreshToken) {
        localStorage.setItem('refresh_token', response.data.refreshToken);
      }
      if (response.data.user) {
        localStorage.setItem('user', JSON.stringify(response.data.user));
      }
      if (response.data.user?.tenantId) {
        localStorage.setItem('tenant_id', response.data.user.tenantId);
      }
    }

    return response as AuthResponse;
  }

  async register(userData: { name: string; email: string; password: string; companyName: string }): Promise<AuthResponse> {
    const response = await this.request<AuthResponse['data']>({
      method: 'POST',
      url: '/api/auth/register',
      data: userData,
    });
    return response as AuthResponse;
  }

  async logout(): Promise<ApiResponse<void>> {
    try {
      await this.request<void>({
        method: 'POST',
        url: '/api/auth/logout',
      });
    } catch (error) {
      // Ignorar erros de logout
      console.warn('Logout error:', error);
    } finally {
      // Limpar dados locais independentemente do resultado
      localStorage.removeItem('auth_token');
      localStorage.removeItem('refresh_token');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant_id');
      this.cache.clear();
    }

    return { success: true, data: undefined };
  }

  async refreshToken(): Promise<AuthResponse> {
    return this.request<AuthResponse>({
      url: '/auth/refresh',
      method: 'POST',
      data: { refreshToken: localStorage.getItem('refresh_token') },
    });
  }

  // Solicitar link de recuperação de senha
  async forgotPassword(email: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.request<ApiResponse<{ success: boolean }>>({
      url: '/auth/forgot-password',
      method: 'POST',
      data: { email },
    });
  }

  // Resetar senha usando token enviado por e-mail
  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<{ success: boolean }>> {
    return this.request<ApiResponse<{ success: boolean }>>({
      url: '/auth/reset-password',
      method: 'POST',
      data: { token, newPassword },
    });
  }

  // Produtos com cache
  async getProducts(filters?: ProductFilters, page = 1, limit = 20): Promise<PaginatedResponse<Product>> {
    const response = await this.request<PaginatedResponse<Product>>({
      method: 'GET',
      url: '/api/products',
      params: { ...filters, page, limit },
      cache: true,
      cacheTtl: 2 * 60 * 1000, // 2 minutos
    });
    return response.data as PaginatedResponse<Product>;
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request<Product>({
      method: 'GET',
      url: `/api/products/${id}`,
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutos
    });
  }

  async syncProducts(): Promise<ApiResponse<{ synced: number; errors: string[] }>> {
    const response = await this.request<{ synced: number; errors: string[] }>({
      method: 'POST',
      url: '/api/products/sync',
      retryConfig: {
        retries: 1, // Menos tentativas para sync
        retryDelay: 2000,
      }
    });

    // Invalidar cache de produtos após sync
    this.invalidateCache('/api/products');
    
    return response;
  }

  // Cálculos de estoque com cache
  async getStockCalculations(filters?: StockFilters, page = 1, limit = 20): Promise<PaginatedResponse<StockCalculation>> {
    const response = await this.request<PaginatedResponse<StockCalculation>>({
      method: 'GET',
      url: '/api/stock/calculations',
      params: { ...filters, page, limit },
      cache: true,
      cacheTtl: 1 * 60 * 1000, // 1 minuto
    });
    return response.data as PaginatedResponse<StockCalculation>;
  }

  async calculateStock(productIds?: string[]): Promise<ApiResponse<{ calculated: number; errors: string[] }>> {
    const response = await this.request<{ calculated: number; errors: string[] }>({
      method: 'POST',
      url: '/api/stock/calculate',
      data: { productIds },
      retryConfig: {
        retries: 2,
        retryDelay: 1500,
      }
    });

    // Invalidar cache relacionado ao estoque
    this.invalidateCache('/api/stock');
    this.invalidateCache('/api/dashboard');

    return response;
  }

  async getStockAlerts(page = 1, limit = 20): Promise<PaginatedResponse<StockAlert>> {
    const response = await this.request<PaginatedResponse<StockAlert>>({
      method: 'GET',
      url: '/api/stock/alerts',
      params: { page, limit },
      cache: true,
      cacheTtl: 30 * 1000, // 30 segundos
    });
    return response.data as PaginatedResponse<StockAlert>;
  }

  async markAlertAsRead(alertId: string): Promise<ApiResponse<void>> {
    const response = await this.request<void>({
      method: 'PUT',
      url: `/api/stock/alerts/${alertId}/read`,
    });

    // Invalidar cache de alertas
    this.invalidateCache('/api/stock/alerts');

    return response;
  }

  // Planilhas
  async generateSpreadsheet(params: any): Promise<ApiResponse<Spreadsheet>> {
    return this.request<Spreadsheet>({
      method: 'POST',
      url: '/api/spreadsheets/generate',
      data: params,
      retryConfig: {
        retries: 1, // Menos tentativas para geração
        retryDelay: 3000,
      }
    });
  }

  async getSpreadsheetHistory(page = 1, limit = 20, filters?: any): Promise<SpreadsheetHistory> {
    const response = await this.request<SpreadsheetHistory>({
      method: 'GET',
      url: '/api/spreadsheets/history',
      params: { page, limit, ...filters },
      cache: true,
      cacheTtl: 1 * 60 * 1000, // 1 minuto
    });
    return response.data as SpreadsheetHistory;
  }

  async getSpreadsheetStatistics(): Promise<ApiResponse<SpreadsheetStatistics>> {
    return this.request<SpreadsheetStatistics>({
      method: 'GET',
      url: '/api/spreadsheets/statistics',
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutos
    });
  }

  async downloadSpreadsheet(id: string): Promise<Blob> {
    const response = await this.api.get(`/api/spreadsheets/${id}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async deleteSpreadsheet(id: string): Promise<ApiResponse<void>> {
    const response = await this.request<void>({
      method: 'DELETE',
      url: `/api/spreadsheets/${id}`,
    });

    // Invalidar cache de planilhas
    this.invalidateCache('/api/spreadsheets');

    return response;
  }

  // Warehouses
  async getWarehouses(): Promise<ApiResponse<Warehouse[]>> {
    return this.request<Warehouse[]>({
      method: 'GET',
      url: '/api/warehouses',
      cache: true,
      cacheTtl: 10 * 60 * 1000, // 10 minutos
    });
  }

  async createWarehouse(data: Partial<Warehouse>): Promise<ApiResponse<Warehouse>> {
    const response = await this.request<Warehouse>({
      method: 'POST',
      url: '/api/warehouses',
      data,
    });

    this.invalidateCache('/api/warehouses');
    return response;
  }

  async updateWarehouse(id: string, data: Partial<Warehouse>): Promise<ApiResponse<Warehouse>> {
    const response = await this.request<Warehouse>({
      method: 'PUT',
      url: `/api/warehouses/${id}`,
      data,
    });

    this.invalidateCache('/api/warehouses');
    return response;
  }

  // Dashboard com cache curto
  async getDashboardStats(period = '30d'): Promise<ApiResponse<DashboardStats>> {
    return this.request<DashboardStats>({
      method: 'GET',
      url: '/api/dashboard/stats',
      params: { period },
      cache: true,
      cacheTtl: 30 * 1000, // 30 segundos
    });
  }

  // Mercado Livre - Integração Completa
  async getMercadoLivreAuthUrl(): Promise<ApiResponse<{ authUrl: string }>> {
    return this.request<{ authUrl: string }>({
      method: 'GET',
      url: '/api/mercadolivre/auth/initiate',
    });
  }

  async handleMercadoLivreCallback(code: string, state?: string): Promise<ApiResponse<{ success: boolean }>> {
    const response = await this.request<{ success: boolean }>({
      method: 'GET',
      url: `/api/mercadolivre/auth/callback?code=${code}&state=${state || ''}`,
    });

    // Invalidar cache após conectar ML
    this.invalidateCache('/api/products');
    this.invalidateCache('/api/dashboard');
    this.invalidateCache('/api/mercadolivre/accounts');

    return response;
  }

  // Contas do Mercado Livre
  async getMercadoLivreAccounts(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>({
      method: 'GET',
      url: '/api/mercadolivre/accounts',
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutos
    });
  }

  async disconnectMercadoLivreAccount(accountId: string): Promise<ApiResponse<{ success: boolean }>> {
    const response = await this.request<{ success: boolean }>({
      method: 'DELETE',
      url: `/api/mercadolivre/accounts/${accountId}`,
    });

    this.invalidateCache('/api/mercadolivre/accounts');
    this.invalidateCache('/api/products');
    this.invalidateCache('/api/dashboard');

    return response;
  }

  // Produtos do Mercado Livre
  async getMercadoLivreProducts(accountId: string, params?: { limit?: number; offset?: number }): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: `/api/mercadolivre/accounts/${accountId}/items`,
      params,
      cache: true,
      cacheTtl: 2 * 60 * 1000, // 2 minutos
    });
  }

  async getMercadoLivreProductDetails(accountId: string, itemId: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: `/api/mercadolivre/accounts/${accountId}/items/${itemId}`,
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutos
    });
  }

  async updateMercadoLivreStock(accountId: string, itemId: string, quantity: number): Promise<ApiResponse<any>> {
    const response = await this.request<any>({
      method: 'PUT',
      url: `/api/mercadolivre/accounts/${accountId}/items/${itemId}/stock`,
      data: { quantity },
    });

    // Invalidar cache relacionado
    this.invalidateCache(`/api/mercadolivre/accounts/${accountId}/items`);
    this.invalidateCache('/api/products');
    this.invalidateCache('/api/dashboard');

    return response;
  }

  // Vendas do Mercado Livre
  async getMercadoLivreOrders(accountId: string, params?: { limit?: number; offset?: number; sort?: string }): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: `/api/mercadolivre/accounts/${accountId}/orders`,
      params,
      cache: true,
      cacheTtl: 1 * 60 * 1000, // 1 minuto
    });
  }

  async getMercadoLivreOrderDetails(accountId: string, orderId: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: `/api/mercadolivre/accounts/${accountId}/orders/${orderId}`,
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutos
    });
  }

  // Sincronização
  async syncMercadoLivreProducts(accountId: string): Promise<ApiResponse<any>> {
    const response = await this.request<any>({
      method: 'POST',
      url: `/api/mercadolivre/accounts/${accountId}/sync/products`,
    });

    // Invalidar todos os caches relacionados
    this.invalidateCache(`/api/mercadolivre/accounts/${accountId}/items`);
    this.invalidateCache('/api/products');
    this.invalidateCache('/api/dashboard');

    return response;
  }

  // Estatísticas
  async getMercadoLivreStats(accountId: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: `/api/mercadolivre/accounts/${accountId}/stats`,
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutos
    });
  }

  // Refresh Token
  async refreshMercadoLivreToken(accountId: string): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'POST',
      url: `/api/mercadolivre/accounts/${accountId}/refresh-token`,
    });
  }

  // Configurações
  async getConfig(): Promise<ApiResponse<any>> {
    return this.request<any>({
      method: 'GET',
      url: '/api/config',
      cache: true,
      cacheTtl: 5 * 60 * 1000, // 5 minutos
    });
  }

  async updateConfig(config: any): Promise<ApiResponse<any>> {
    const response = await this.request<any>({
      method: 'PUT',
      url: '/api/config',
      data: config,
    });

    this.invalidateCache('/api/config');
    return response;
  }

  // ===== MÉTODOS INTEGRADOS PRODUTOS + ESTOQUE =====

  /**
   * Busca produtos do ML com dados de estoque integrados
   */
  async getProductsWithStock(accountId: string): Promise<ApiResponse<PaginatedResponse<ProductWithStock>>> {
    return this.request<PaginatedResponse<ProductWithStock>>({
      method: 'GET',
      url: `/api/mercadolivre/accounts/${accountId}/products-with-stock`,
      cache: true,
      cacheTtl: 30000, // 30 segundos
    });
  }

  /**
   * Busca um produto específico com dados de estoque
   */
  async getProductWithStock(productId: string): Promise<ApiResponse<ProductWithStock>> {
    return this.request<ProductWithStock>({
      method: 'GET',
      url: `/api/products/${productId}/with-stock`,
      cache: true,
      cacheTtl: 15000, // 15 segundos
    });
  }

  /**
   * Atualiza estoque de um produto
   */
  async updateProductStock(request: StockUpdateRequest): Promise<ApiResponse<StockUpdateResponse>> {
    return this.request<StockUpdateResponse>({
      method: 'PUT',
      url: `/api/products/${request.productId}/stock`,
      data: request,
      cache: false,
    });
  }

  /**
   * Atualiza estoque de múltiplos produtos
   */
  async bulkUpdateStock(request: BulkStockUpdateRequest): Promise<ApiResponse<BulkStockUpdateResponse>> {
    return this.request<BulkStockUpdateResponse>({
      method: 'PUT',
      url: '/api/products/bulk-stock-update',
      data: request,
      cache: false,
    });
  }

  /**
   * Busca cálculo de estoque de um produto
   */
  async getStockCalculation(productId: string): Promise<ApiResponse<StockCalculation>> {
    return this.request<StockCalculation>({
      method: 'GET',
      url: `/api/products/${productId}/stock-calculation`,
      cache: true,
      cacheTtl: 60000, // 1 minuto
    });
  }

  /**
   * Busca histórico de estoque de um produto
   */
  async getStockHistory(productId: string, limit?: number): Promise<ApiResponse<StockHistoryEntry[]>> {
    return this.request<StockHistoryEntry[]>({
      method: 'GET',
      url: `/api/products/${productId}/stock-history`,
      params: { limit },
      cache: true,
      cacheTtl: 30000, // 30 segundos
    });
  }

  /**
   * Sincroniza um produto específico com o ML
   */
  async syncSingleProduct(productId: string): Promise<ApiResponse<ProductWithStock>> {
    return this.request<ProductWithStock>({
      method: 'POST',
      url: `/api/products/${productId}/sync`,
      cache: false,
    });
  }

  /**
   * Busca produtos com filtros avançados ML + estoque
   */
  async searchProductsWithStock(
    accountId: string,
    filters: MLProductFilters
  ): Promise<ApiResponse<PaginatedResponse<ProductWithStock>>> {
    return this.request<PaginatedResponse<ProductWithStock>>({
      method: 'POST',
      url: `/api/mercadolivre/accounts/${accountId}/products/search`,
      data: filters,
      cache: true,
      cacheTtl: 15000, // 15 segundos
    });
  }

  // Método para verificar status da API
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request<{ status: string; timestamp: string }>({
      method: 'GET',
      url: '/api/health',
      cache: false,
    });
  }

  // Métodos HTTP básicos para compatibilidade
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.get<T>(url, config);
  }

  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, data, config);
  }

  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.put<T>(url, data, config);
  }

  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.api.delete<T>(url, config);
  }

  // Getter para baseURL e defaults para compatibilidade
  get defaults() {
    return this.api.defaults;
  }
}

// Exportar instância singleton
export const apiService = new ApiService();
export default apiService;