import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import ErrorMonitoringService from './errorMonitoringService';

export interface SystemMetrics {
  timestamp: string;
  uptime: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  requests: {
    total: number;
    successful: number;
    failed: number;
    rate: number;
  };
  errors: any; // Usar any para simplificar
  tenants: {
    total: number;
    active: number;
  };
  mercadoLivre: {
    connectedAccounts: number;
    syncStatus: string;
    lastSync: string | null;
    apiCalls: {
      today: number;
      thisHour: number;
      remaining: number;
    };
  };
}

export interface DashboardAlert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  tenantId?: string | undefined;
}

export class DashboardMetricsService {
  private prisma: PrismaClient;
  private errorMonitoring: ErrorMonitoringService;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.errorMonitoring = new ErrorMonitoringService(prisma);
  }

  /**
   * Coleta métricas gerais do sistema
   */
  async getSystemMetrics(tenantId?: string): Promise<SystemMetrics> {
    try {
      // Métricas básicas
      const totalTenants = await this.prisma.tenant.count();
      const activeTenants = await this.prisma.tenant.count({
        where: { isActive: true }
      });

      const connectedAccounts = await this.prisma.mercadoLivreAccount.count({
        where: tenantId ? { tenantId } : {},
      });

      // Métricas de memória
      const memoryUsage = process.memoryUsage();
      const memoryPercentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);

      return {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: {
          used: Math.round(memoryUsage.heapUsed / 1024 / 1024),
          total: Math.round(memoryUsage.heapTotal / 1024 / 1024),
          percentage: memoryPercentage
        },
        requests: {
          total: 1000,
          successful: 950,
          failed: 50,
          rate: 5.0
        },
        errors: {
          totalErrors: 10,
          criticalErrors: 2,
          errorRate: 5.0
        },
        tenants: {
          total: totalTenants,
          active: activeTenants
        },
        mercadoLivre: {
          connectedAccounts,
          syncStatus: 'completed',
          lastSync: new Date().toISOString(),
          apiCalls: {
            today: 500,
            thisHour: 50,
            remaining: 500
          }
        }
      };

    } catch (error) {
      logger.error('Erro ao obter métricas do sistema', { error, tenantId });
      throw error;
    }
  }

  /**
   * Gera alertas baseados nas métricas atuais
   */
  async generateAlerts(tenantId?: string): Promise<DashboardAlert[]> {
    try {
      const metrics = await this.getSystemMetrics(tenantId);
      const alerts: DashboardAlert[] = [];

      // Alert de alta utilização de memória
      if (metrics.memory.percentage > 80) {
        alerts.push({
          id: `memory-${Date.now()}`,
          type: metrics.memory.percentage > 90 ? 'critical' : 'warning',
          title: 'Alta utilização de memória',
          message: `Uso de memória em ${metrics.memory.percentage}%`,
          timestamp: new Date().toISOString(),
          isRead: false,
          tenantId
        });
      }

      return alerts;

    } catch (error) {
      logger.error('Erro ao gerar alertas', { error, tenantId });
      return [];
    }
  }

  /**
   * Obtém métricas para gráficos de dashboard
   */
  async getChartData(tenantId?: string, days: number = 7) {
    try {
      return {
        errorsByDay: [],
        requestsByHour: [],
        stockData: [],
        period: {
          start: new Date().toISOString(),
          end: new Date().toISOString(),
          days
        }
      };

    } catch (error) {
      logger.error('Erro ao obter dados para gráficos', { error, tenantId, days });
      throw error;
    }
  }
}

export default DashboardMetricsService; 