import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { 
  Bell, 
  AlertTriangle, 
  AlertCircle, 
  TrendingDown,
  Package,
  CheckCircle,
  X,
  Eye,
  Filter
} from 'lucide-react';
import { useStockStore } from '../../store/stockStore';
import type { StockAlert } from '../../types/api';

interface StockAlertsPanelProps {
  className?: string;
  maxHeight?: string;
}

export const StockAlertsPanel: React.FC<StockAlertsPanelProps> = ({ 
  className,
  maxHeight = "400px" 
}) => {
  const [selectedSeverity, setSelectedSeverity] = useState<string>('all');
  const [showOnlyUnread, setShowOnlyUnread] = useState(true);
  
  const {
    stockAlerts,
    alertsLoading,
    markAlertAsRead,
    loadStockAlerts,
  } = useStockStore();

  const filteredAlerts = useMemo(() => {
    let filtered = stockAlerts;

    // Filter by read status
    if (showOnlyUnread) {
      filtered = filtered.filter(alert => !alert.isRead);
    }

    // Filter by severity
    if (selectedSeverity !== 'all') {
      filtered = filtered.filter(alert => alert.severity === selectedSeverity);
    }

    // Sort by severity and creation date
    return filtered.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      if (severityDiff !== 0) return severityDiff;
      
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }, [stockAlerts, selectedSeverity, showOnlyUnread]);

  const alertsByType = useMemo(() => {
    return filteredAlerts.reduce((acc, alert) => {
      if (!acc[alert.type]) {
        acc[alert.type] = [];
      }
      acc[alert.type].push(alert);
      return acc;
    }, {} as Record<string, StockAlert[]>);
  }, [filteredAlerts]);

  const alertCounts = useMemo(() => {
    const counts = {
      total: stockAlerts.length,
      unread: stockAlerts.filter(a => !a.isRead).length,
      critical: stockAlerts.filter(a => a.severity === 'critical').length,
      high: stockAlerts.filter(a => a.severity === 'high').length,
    };
    return counts;
  }, [stockAlerts]);

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'gap_critical':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'stock_low':
        return <TrendingDown className="h-4 w-4 text-orange-500" />;
      case 'overstock':
        return <Package className="h-4 w-4 text-blue-500" />;
      case 'no_sales':
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Bell className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Crítico</Badge>;
      case 'high':
        return <Badge className="bg-orange-100 text-orange-800">Alto</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-100 text-yellow-800">Médio</Badge>;
      case 'low':
        return <Badge variant="outline">Baixo</Badge>;
      default:
        return <Badge variant="outline">{severity}</Badge>;
    }
  };

  const getAlertTypeLabel = (type: string) => {
    switch (type) {
      case 'gap_critical':
        return 'Gap Crítico';
      case 'stock_low':
        return 'Estoque Baixo';
      case 'overstock':
        return 'Excesso de Estoque';
      case 'no_sales':
        return 'Sem Vendas';
      default:
        return type;
    }
  };

  const handleMarkAsRead = async (alertId: string) => {
    try {
      await markAlertAsRead(alertId);
    } catch (error) {
      console.error('Erro ao marcar alerta como lido:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (alertsLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Bell className="h-8 w-8 mx-auto mb-2 opacity-50 animate-pulse" />
            <p>Carregando alertas...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Alertas de Estoque
            {alertCounts.unread > 0 && (
              <Badge variant="destructive" className="ml-2">
                {alertCounts.unread}
              </Badge>
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => loadStockAlerts()}
            disabled={alertsLoading}
          >
            <Bell className="h-4 w-4" />
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Summary */}
        <div className="grid grid-cols-4 gap-2 text-center">
          <div>
            <div className="text-lg font-bold">{alertCounts.total}</div>
            <div className="text-xs text-muted-foreground">Total</div>
          </div>
          <div>
            <div className="text-lg font-bold text-red-600">{alertCounts.unread}</div>
            <div className="text-xs text-muted-foreground">Não Lidos</div>
          </div>
          <div>
            <div className="text-lg font-bold text-red-600">{alertCounts.critical}</div>
            <div className="text-xs text-muted-foreground">Críticos</div>
          </div>
          <div>
            <div className="text-lg font-bold text-orange-600">{alertCounts.high}</div>
            <div className="text-xs text-muted-foreground">Altos</div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex items-center gap-2 flex-wrap">
          <Button
            variant={showOnlyUnread ? "default" : "outline"}
            size="sm"
            onClick={() => setShowOnlyUnread(!showOnlyUnread)}
          >
            <Filter className="h-4 w-4 mr-1" />
            {showOnlyUnread ? 'Não Lidos' : 'Todos'}
          </Button>
          
          <select
            value={selectedSeverity}
            onChange={(e) => setSelectedSeverity(e.target.value)}
            className="px-2 py-1 text-sm border rounded"
          >
            <option value="all">Todas Severidades</option>
            <option value="critical">Crítico</option>
            <option value="high">Alto</option>
            <option value="medium">Médio</option>
            <option value="low">Baixo</option>
          </select>
        </div>

        {/* Alerts List */}
        {filteredAlerts.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>
              {showOnlyUnread 
                ? 'Nenhum alerta não lido' 
                : 'Nenhum alerta encontrado'
              }
            </p>
          </div>
        ) : (
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all">Todos ({filteredAlerts.length})</TabsTrigger>
              <TabsTrigger value="gap_critical">
                Gaps ({alertsByType.gap_critical?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="stock_low">
                Baixo ({alertsByType.stock_low?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="overstock">
                Excesso ({alertsByType.overstock?.length || 0})
              </TabsTrigger>
              <TabsTrigger value="no_sales">
                Sem Vendas ({alertsByType.no_sales?.length || 0})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="all" className="mt-4">
              <div 
                className="space-y-2 overflow-y-auto"
                style={{ maxHeight }}
              >
                {filteredAlerts.map((alert) => (
                  <Alert
                    key={alert.id}
                    className={`${alert.isRead ? 'opacity-60' : ''} relative`}
                  >
                    <div className="flex items-start justify-between w-full">
                      <div className="flex items-start gap-2 flex-1">
                        {getAlertIcon(alert.type)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-sm">
                              {getAlertTypeLabel(alert.type)}
                            </span>
                            {getSeverityBadge(alert.severity)}
                          </div>
                          <AlertDescription className="text-sm">
                            {alert.message}
                          </AlertDescription>
                          <div className="text-xs text-muted-foreground mt-1">
                            {alert.product?.title} • {formatDate(alert.createdAt)}
                          </div>
                        </div>
                      </div>
                      
                      {!alert.isRead && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleMarkAsRead(alert.id)}
                          className="ml-2"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </Alert>
                ))}
              </div>
            </TabsContent>

            {Object.entries(alertsByType).map(([type, alerts]) => (
              <TabsContent key={type} value={type} className="mt-4">
                <div 
                  className="space-y-2 overflow-y-auto"
                  style={{ maxHeight }}
                >
                  {alerts.map((alert) => (
                    <Alert
                      key={alert.id}
                      className={`${alert.isRead ? 'opacity-60' : ''} relative`}
                    >
                      <div className="flex items-start justify-between w-full">
                        <div className="flex items-start gap-2 flex-1">
                          {getAlertIcon(alert.type)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              {getSeverityBadge(alert.severity)}
                            </div>
                            <AlertDescription className="text-sm">
                              {alert.message}
                            </AlertDescription>
                            <div className="text-xs text-muted-foreground mt-1">
                              {alert.product?.title} • {formatDate(alert.createdAt)}
                            </div>
                          </div>
                        </div>
                        
                        {!alert.isRead && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkAsRead(alert.id)}
                            className="ml-2"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </Alert>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
};

export default StockAlertsPanel;
