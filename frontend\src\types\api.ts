// Tipos para autenticação
export interface User {
  id: string;
  email: string;
  name: string;
  tenantId: string;
  role: UserRole;
  createdAt: string;
  updatedAt: string;
  isActive?: boolean;
  lastLoginAt?: string;
  phone?: string;
  avatar?: string;
  department?: string;
  position?: string;
}

// Tipos para roles e permissões
export type UserRole = 'admin' | 'manager' | 'user' | 'viewer';

export interface UserPermissions {
  // Produtos
  products: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
    sync: boolean;
  };
  // Estoque
  stock: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
    adjust: boolean;
  };
  // Envios
  shipments: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
    mlFull: boolean;
  };
  // Relatórios
  reports: {
    view: boolean;
    export: boolean;
    advanced: boolean;
  };
  // Configurações
  settings: {
    view: boolean;
    edit: boolean;
    tenant: boolean;
  };
  // Usuários
  users: {
    view: boolean;
    create: boolean;
    edit: boolean;
    delete: boolean;
    manage: boolean;
  };
  // Sistema
  system: {
    logs: boolean;
    monitoring: boolean;
    api: boolean;
  };
}

// Interface para criação de usuário
export interface CreateUserDto {
  name: string;
  email: string;
  role: UserRole;
  phone?: string;
  department?: string;
  position?: string;
  isActive?: boolean;
}

// Interface para atualização de usuário
export interface UpdateUserDto {
  name?: string;
  email?: string;
  role?: UserRole;
  phone?: string;
  department?: string;
  position?: string;
  isActive?: boolean;
}

// Interface para filtros de usuários
export interface UserFilters {
  search?: string;
  role?: UserRole | 'all';
  department?: string;
  isActive?: boolean;
  sortBy?: 'name' | 'email' | 'role' | 'createdAt' | 'lastLoginAt';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Interface para estatísticas de usuários
export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  adminUsers: number;
  managerUsers: number;
  regularUsers: number;
  viewerUsers: number;
  recentLogins: number;
  newUsersThisMonth: number;
}

// Tipos para configurações de usuário
export interface UserProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  timezone: string;
  language: 'pt-BR' | 'en-US' | 'es-ES';
  notifications: {
    email: boolean;
    push: boolean;
    stockAlerts: boolean;
    salesReports: boolean;
    systemUpdates: boolean;
  };
  preferences: {
    theme: 'light' | 'dark' | 'system';
    currency: 'BRL' | 'USD' | 'EUR';
    dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD';
    defaultView: 'grid' | 'list';
  };
  updatedAt: string;
}

// Tipos para configurações de tenant
export interface TenantConfig {
  id: string;
  name: string;
  cnpj?: string;
  address?: {
    street: string;
    number: string;
    complement?: string;
    neighborhood: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  contact: {
    email: string;
    phone?: string;
    website?: string;
  };
  settings: {
    maxUsers: number;
    maxProducts: number;
    features: {
      mlIntegration: boolean;
      advancedReports: boolean;
      apiAccess: boolean;
      customBranding: boolean;
    };
    billing: {
      plan: 'basic' | 'professional' | 'enterprise';
      billingCycle: 'monthly' | 'yearly';
      nextBillingDate: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

// Tipos para configurações de segurança
export interface SecuritySettings {
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    expirationDays: number;
  };
  twoFactorAuth: {
    enabled: boolean;
    method: 'sms' | 'email' | 'app';
    backupCodes?: string[];
  };
  sessionManagement: {
    maxSessions: number;
    sessionTimeout: number;
    rememberMe: boolean;
  };
  loginAttempts: {
    maxAttempts: number;
    lockoutDuration: number;
    resetOnSuccess: boolean;
  };
  lastPasswordChange: string;
  updatedAt: string;
}

// Tipos para mudança de senha
export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    user: User;
    token: string;
    refreshToken: string;
  };
}

// Tipos para produtos e estoque
export interface Product {
  id: string;
  mlId: string;
  title: string;
  sku?: string;
  price: number;
  originalPrice?: number;
  availableQuantity: number;
  soldQuantity: number;
  categoryId: string;
  categoryName: string;
  brand?: string;
  condition: 'new' | 'used';
  listingType?: string;
  permalink: string;
  thumbnail: string;
  pictures?: string[];
  status: 'active' | 'paused' | 'closed';
  createdAt: string;
  updatedAt: string;

  // Dados da conta ML
  mlAccountId: string;
  mlAccount?: {
    id: string;
    nickname: string;
    email?: string;
  };
}

// Interface unificada para produtos com dados de estoque
export interface ProductWithStock extends Product {
  // Estoque atual (pode diferir de availableQuantity)
  currentStock?: number;

  // Cálculos de estoque
  stockCalculation?: StockCalculation;

  // Alertas relacionados
  alerts?: StockAlert[];

  // Histórico de vendas recente
  recentSales?: Array<{
    date: string;
    quantity: number;
    unitPrice: number;
  }>;

  // Métricas calculadas
  metrics?: {
    salesLast7Days: number;
    salesLast14Days: number;
    salesLast30Days: number;
    salesLast60Days: number;
    stockCoverageDays: number;
    averageDailySales: number;
    reorderPoint?: number;
    stockTurnover?: number;
    profitMargin?: number;
  };

  // Status de sincronização
  syncStatus?: {
    lastSync: string;
    isOutOfSync: boolean;
    pendingChanges: string[];
  };
}

// Tipos para cálculos de estoque
export interface StockCalculation {
  productId: string;
  product: Product;
  currentStock: number;
  idealStock: number;
  gap: number;
  averageSales: number;
  coverageDays: number;
  safetyStock: number;
  unitsInTransit: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  lastCalculated: string;
}

export interface StockAlert {
  id: string;
  productId: string;
  product: Product;
  type: 'gap_critical' | 'stock_low' | 'overstock' | 'no_sales';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  threshold: number;
  currentValue: number;
  isRead: boolean;
  createdAt: string;
}

// Tipos para planilhas
export interface Spreadsheet {
  id: string;
  filename: string;
  format: 'xlsx' | 'csv';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  mlStatus?: 'pending' | 'processing' | 'completed' | 'failed';
  totalProducts: number;
  warehouseIds: string[];
  generatedBy: string;
  tags: string[];
  notes?: string;
  fileSize: number;
  downloadUrl?: string;
  mlUploadedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SpreadsheetHistory {
  spreadsheets: Spreadsheet[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SpreadsheetStatistics {
  total: number;
  byStatus: Record<string, number>;
  byFormat: Record<string, number>;
  totalProducts: number;
  averageProducts: number;
  totalFileSize: number;
  averageFileSize: number;
  byPeriod: {
    today: number;
    thisWeek: number;
    thisMonth: number;
    thisYear: number;
  };
  topUsers: Array<{
    userId: string;
    userName: string;
    count: number;
  }>;
}

// Tipos para armazéns
export interface Warehouse {
  id: string;
  name: string;
  code: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  isActive: boolean;
  isDefault: boolean;
  capacity?: number;
  currentStock?: number;
  createdAt: string;
  updatedAt: string;
}

// Tipos para filtros e pesquisa
export interface ProductFilters {
  search?: string;
  category?: string;
  brand?: string;
  status?: string;
  minPrice?: number;
  maxPrice?: number;
  minStock?: number;
  maxStock?: number;
  hasGap?: boolean;
  priority?: string[];
  dateFrom?: string;
  dateTo?: string;
}

export interface StockFilters extends ProductFilters {
  minGap?: number;
  maxGap?: number;
  coverageDays?: number;
  alertType?: string[];
}

// Tipos para gráficos e estatísticas
export interface SalesData {
  date: string;
  sales: number;
  revenue: number;
  products: number;
}

export interface StockEvolution {
  date: string;
  totalStock: number;
  totalProducts: number;
  gapProducts: number;
  criticalProducts: number;
}

export interface CategoryStats {
  category: string;
  products: number;
  sales: number;
  revenue: number;
  avgStock: number;
  gapProducts: number;
}

export interface DashboardStats {
  totalProducts: number;
  activeProducts: number;
  gapProducts: number;
  criticalProducts: number;
  totalSales: number;
  totalRevenue: number;
  avgStock: number;
  alertsCount: number;
  trendsData: {
    sales: SalesData[];
    stock: StockEvolution[];
    categories: CategoryStats[];
  };
}

// Tipos para resposta da API
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  success: false;
  error: string;
  message: string;
  code?: string;
  details?: any;
}

// Tipos para paginação
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Tipos para operações de estoque
export interface StockUpdateRequest {
  productId: string;
  quantity: number;
  reason?: string;
  syncWithML?: boolean;
}

export interface StockUpdateResponse {
  success: boolean;
  product: ProductWithStock;
  mlSyncStatus?: {
    synced: boolean;
    error?: string;
    mlQuantity?: number;
  };
}

export interface BulkStockUpdateRequest {
  updates: Array<{
    productId: string;
    quantity: number;
    reason?: string;
  }>;
  syncWithML?: boolean;
}

export interface BulkStockUpdateResponse {
  success: boolean;
  results: Array<{
    productId: string;
    success: boolean;
    error?: string;
    product?: ProductWithStock;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
  };
}

// Tipos para histórico de estoque
export interface StockHistoryEntry {
  id: string;
  productId: string;
  previousQuantity: number;
  newQuantity: number;
  change: number;
  reason: string;
  userId?: string;
  userName?: string;
  createdAt: string;
}

// Tipos para sincronização ML
export interface MLSyncStatus {
  isLoading: boolean;
  lastSync: string | null;
  error: string | null;
  progress: number;
  itemsProcessed?: number;
  totalItems?: number;
}

export interface MLSyncResult {
  success: boolean;
  itemsProcessed: number;
  itemsUpdated: number;
  itemsCreated: number;
  errors: Array<{
    mlId: string;
    error: string;
  }>;
  duration: number;
}

// Tipos para configurações de sincronização ML
export interface MLSyncConfig {
  autoSync: boolean;
  syncFrequency: '15min' | '30min' | 'hourly' | 'daily';
  syncTypes: {
    products: boolean;
    orders: boolean;
    stock: boolean;
  };
  notifications: {
    onSuccess: boolean;
    onError: boolean;
    onConflict: boolean;
  };
  advanced: {
    batchSize: number;
    timeout: number;
    retryAttempts: number;
    conflictResolution: 'local' | 'remote' | 'manual';
  };
  lastUpdated: string;
}

// Tipos para logs e auditoria
export interface LogEntry {
  id: string;
  timestamp: string;
  userId: string;
  userName: string;
  userEmail?: string;
  action: LogAction;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  severity: LogSeverity;
  category: LogCategory;
}

export type LogAction =
  | 'LOGIN'
  | 'LOGOUT'
  | 'CREATE'
  | 'UPDATE'
  | 'DELETE'
  | 'VIEW'
  | 'EXPORT'
  | 'IMPORT'
  | 'SYNC'
  | 'BACKUP'
  | 'RESTORE'
  | 'CONFIG_CHANGE'
  | 'PASSWORD_CHANGE'
  | 'PERMISSION_CHANGE';

export type LogSeverity = 'info' | 'warning' | 'error' | 'critical';

export type LogCategory =
  | 'authentication'
  | 'authorization'
  | 'data'
  | 'system'
  | 'security'
  | 'integration'
  | 'configuration';

export interface LogFilters {
  search?: string;
  action?: LogAction;
  severity?: LogSeverity;
  category?: LogCategory;
  userId?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
  offset?: number;
}

export interface LogsResponse {
  logs: LogEntry[];
  total: number;
  hasMore: boolean;
  nextOffset?: number;
}

// Tipos para configurações
export interface AppConfig {
  stockCalculation: {
    defaultCoverageDays: number;
    defaultSafetyStock: number;
    recalculationInterval: number;
  };
  alerts: {
    enabled: boolean;
    emailNotifications: boolean;
    thresholds: {
      critical: number;
      high: number;
      medium: number;
    };
  };
  spreadsheets: {
    defaultFormat: 'xlsx' | 'csv';
    maxProducts: number;
    autoGenerate: boolean;
  };
}

// Filtros específicos para produtos ML com estoque
export interface MLProductFilters {
  // Filtros básicos
  search?: string;
  category?: string;
  status?: ('active' | 'paused' | 'closed')[];

  // Filtros de preço e vendas
  minPrice?: number;
  maxPrice?: number;
  minSoldQuantity?: number;
  maxSoldQuantity?: number;

  // Filtros de estoque
  minStock?: number;
  maxStock?: number;
  hasStockAlert?: boolean;
  stockPriority?: ('low' | 'medium' | 'high' | 'critical')[];

  // Filtros de gap de estoque
  hasGap?: boolean;
  minGap?: number;
  maxGap?: number;

  // Filtros de performance
  minCoverageDays?: number;
  maxCoverageDays?: number;
  minTurnover?: number;
  maxTurnover?: number;

  // Filtros de sincronização
  isOutOfSync?: boolean;
  lastSyncBefore?: string;

  // Ordenação
  sortBy?: 'title' | 'price' | 'stock' | 'sales' | 'gap' | 'coverage' | 'lastSync';
  sortOrder?: 'asc' | 'desc';

  // Paginação
  page?: number;
  limit?: number;
}