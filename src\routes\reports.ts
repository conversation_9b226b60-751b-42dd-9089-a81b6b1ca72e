import { Router } from 'express';

const router: Router = Router();

// Rota temporária - será implementada na tarefa de relatórios
router.get('/', (req, res) => {
  res.json({
    message: 'Rotas de relatórios - Em desenvolvimento',
    routes: [
      'GET /api/reports/spreadsheet',
      'POST /api/reports/generate',
      'GET /api/reports/history',
      'GET /api/reports/download/:id',
    ]
  });
});

export default router; 