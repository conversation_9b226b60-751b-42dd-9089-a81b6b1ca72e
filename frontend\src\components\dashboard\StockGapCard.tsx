import React from 'react';
import { ExclamationTriangleIcon, ClockIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { StatusBadge } from '../ui/Table';
import type { StockCalculation } from '../../types/api';

interface StockGapCardProps {
  gaps: StockCalculation[];
  loading?: boolean;
  onViewDetails?: (gap: StockCalculation) => void;
}

export const StockGapCard: React.FC<StockGapCardProps> = ({
  gaps,
  loading = false,
  onViewDetails
}) => {
  const criticalGaps = gaps.filter(gap => gap.priority === 'critical');
  const highGaps = gaps.filter(gap => gap.priority === 'high');
  const mediumGaps = gaps.filter(gap => gap.priority === 'medium');

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'critical':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'high':
        return <ClockIcon className="h-4 w-4" />;
      default:
        return <ArrowTrendingDownIcon className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'danger';
      case 'high':
        return 'warning';
      default:
        return 'primary';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDaysAgo = (date: string) => {
    const days = Math.floor((Date.now() - new Date(date).getTime()) / (1000 * 60 * 60 * 24));
    return `${days} dia${days !== 1 ? 's' : ''} atrás`;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>Gaps de Estoque</CardTitle>
        <p className="text-sm text-muted-foreground">
          {gaps.length} produto{gaps.length !== 1 ? 's' : ''} com necessidade de reposição
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
        {/* Resumo por prioridade */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">{criticalGaps.length}</div>
            <div className="text-sm text-red-600 dark:text-red-400 font-medium">Críticos</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{highGaps.length}</div>
            <div className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">Altos</div>
          </div>
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{mediumGaps.length}</div>
            <div className="text-sm text-blue-600 dark:text-blue-400 font-medium">Médios</div>
          </div>
        </div>

        {/* Lista de gaps */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {gaps.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <ArrowTrendingDownIcon className="h-12 w-12 mx-auto mb-2 text-muted-foreground/50" />
              <p>Nenhum gap de estoque encontrado</p>
              <p className="text-sm">Todos os produtos estão com estoque adequado</p>
            </div>
          ) : (
            gaps.map((gap) => {
              // Defensive programming - ensure gap and product exist
              if (!gap || !gap.product) {
                console.warn('Gap or product is undefined:', gap);
                return null;
              }

              return (
                <div
                  key={gap.productId}
                  className="border border-border rounded-lg p-4 hover:bg-muted/50 cursor-pointer transition-colors"
                  onClick={() => onViewDetails?.(gap)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-foreground truncate">
                          {gap.product?.title || 'Produto sem título'}
                        </h4>
                        <StatusBadge
                          status={gap.priority as any}
                          variant={getPriorityColor(gap.priority) as any}
                        />
                      </div>
                      <p className="text-sm text-muted-foreground mb-2">
                        SKU: {gap.product?.sku || 'N/A'}
                      </p>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Estoque atual:</span>
                          <span className="ml-1 font-medium text-red-600 dark:text-red-400">
                            {gap.currentStock}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Ideal:</span>
                          <span className="ml-1 font-medium text-green-600 dark:text-green-400">
                            {gap.idealStock}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Gap:</span>
                          <span className="ml-1 font-medium text-orange-600 dark:text-orange-400">
                            {gap.gap}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Cobertura:</span>
                          <span className="ml-1 font-medium text-foreground">
                            {gap.coverageDays.toFixed(1)} dias
                          </span>
                        </div>
                      </div>

                      <div className="mt-2 text-xs text-muted-foreground">
                        Última atualização: {formatDaysAgo(gap.lastCalculated)}
                      </div>
                    </div>

                    <div className="ml-4 flex-shrink-0">
                      {getPriorityIcon(gap.priority)}
                    </div>
                  </div>
                </div>
              );
            }).filter(Boolean)
          )}
        </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StockGapCard;