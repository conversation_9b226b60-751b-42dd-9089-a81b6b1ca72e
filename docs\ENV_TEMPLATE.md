# Template de Variáveis de Ambiente

Copie este conteúdo para um arquivo `.env` na raiz do projeto e ajuste os valores conforme necessário.

```env
# =================================
# CONFIGURAÇÕES DO SERVIDOR
# =================================
NODE_ENV=development
PORT=3000

# =================================
# BANCO DE DADOS
# =================================
# URL de conexão principal (development)
DATABASE_URL="postgresql://username:password@localhost:5432/magnow_dev"

# URL de conexão para testes
DATABASE_URL_TEST="postgresql://username:password@localhost:5432/magnow_test"

# =================================
# CACHE (REDIS)
# =================================
REDIS_URL="redis://localhost:6379"

# =================================
# AUTENTICAÇÃO JWT
# =================================
# Chave secreta para tokens JWT (mínimo 32 caracteres)
JWT_SECRET="sua_chave_jwt_muito_segura_com_pelo_menos_32_caracteres"

# Chave secreta para refresh tokens (mínimo 32 caracteres)
JWT_REFRESH_SECRET="sua_chave_refresh_jwt_muito_segura_com_pelo_menos_32_caracteres"

# Tempo de expiração dos tokens
JWT_EXPIRES_IN="1h"
JWT_REFRESH_EXPIRES_IN="7d"

# =================================
# INTEGRAÇÃO MERCADO LIVRE
# =================================
# Credenciais da aplicação Mercado Livre
ML_CLIENT_ID="seu_client_id_do_mercado_livre"
ML_CLIENT_SECRET="seu_client_secret_do_mercado_livre"

# URL de callback para OAuth
ML_REDIRECT_URI="http://localhost:3000/api/auth/ml/callback"

# URL base da API do Mercado Livre
ML_API_BASE_URL="https://api.mercadolibre.com"

# =================================
# RATE LIMITING
# =================================
# Janela de tempo para rate limiting (em milissegundos)
RATE_LIMIT_WINDOW_MS=900000  # 15 minutos

# Máximo de requests por janela
RATE_LIMIT_MAX_REQUESTS=100

# =================================
# CACHE
# =================================
# TTL padrão para cache (em segundos)
CACHE_TTL_SECONDS=300  # 5 minutos

# Máximo de itens no cache
CACHE_MAX_ITEMS=1000

# =================================
# LOGGING
# =================================
# Nível de log (debug, info, warn, error)
LOG_LEVEL="info"

# Arquivo de log
LOG_FILE="logs/app.log"

# Máximo tamanho do arquivo de log (em MB)
LOG_MAX_SIZE=20

# Máximo de arquivos de log para manter
LOG_MAX_FILES=5

# =================================
# CORS
# =================================
# Origens permitidas (separadas por vírgula)
CORS_ORIGINS="http://localhost:3000,http://localhost:3001"

# =================================
# SWAGGER/DOCUMENTAÇÃO
# =================================
# Habilitar Swagger UI
SWAGGER_ENABLED="true"

# =================================
# SSL (APENAS PRODUÇÃO)
# =================================
# Caminhos para certificados SSL
# SSL_CERT_PATH="/path/to/cert.pem"
# SSL_KEY_PATH="/path/to/key.pem"

# =================================
# SEGURANÇA
# =================================
# Chave de criptografia para dados sensíveis (32 caracteres)
ENCRYPTION_KEY="sua_chave_de_criptografia_32_chars"

# Salt rounds para bcrypt
BCRYPT_SALT_ROUNDS=12

# =================================
# WEBHOOKS
# =================================
# Secret para validar webhooks do Mercado Livre
ML_WEBHOOK_SECRET="seu_webhook_secret_do_mercado_livre"

# =================================
# UPLOADS
# =================================
# Diretório para uploads
UPLOADS_DIR="uploads"

# Tamanho máximo de upload (em MB)
MAX_UPLOAD_SIZE=10

# =================================
# NOTIFICAÇÕES
# =================================
# Configurações de email (opcional)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT=587
# SMTP_USER="<EMAIL>"
# SMTP_PASS="sua_senha_de_app"

# =================================
# MONITORAMENTO
# =================================
# Habilitar métricas de performance
ENABLE_METRICS="true"

# =================================
# AMBIENTE ESPECÍFICO
# =================================
# Nome da aplicação
APP_NAME="Magnow"

# Versão da aplicação
APP_VERSION="1.0.0"

# URL base da aplicação
APP_URL="http://localhost:3000"

# =================================
# EXEMPLO DE PRODUÇÃO
# =================================
# Descomente e modifique para ambiente de produção:

# NODE_ENV=production
# DATABASE_URL="*************************************/magnow?ssl=true"
# REDIS_URL="redis://user:pass@prod-redis:6379"
# JWT_SECRET="chave_jwt_producao_muito_mais_segura_e_complexa"
# JWT_REFRESH_SECRET="chave_refresh_jwt_producao_muito_mais_segura_e_complexa"
# CORS_ORIGINS="https://app.magnow.com,https://admin.magnow.com"
# LOG_LEVEL="warn"
# RATE_LIMIT_MAX_REQUESTS=50
# SSL_CERT_PATH="/etc/ssl/certs/magnow.crt"
# SSL_KEY_PATH="/etc/ssl/private/magnow.key"
```

## Como Usar

1. Copie o conteúdo acima para um arquivo chamado `.env` na raiz do projeto
2. Substitua os valores de exemplo pelas suas configurações reais
3. **Nunca** commite o arquivo `.env` no Git (já está no .gitignore)
4. Para produção, use valores mais seguros e complexos para secrets e senhas 