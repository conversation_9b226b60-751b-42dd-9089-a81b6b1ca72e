import { describe, it, expect, beforeEach, vi } from 'vitest';
import { useUserManagementStore } from '../userManagementStore';
import type { CreateUserDto, UpdateUserDto } from '../../types/api';

// Mock the simulateApiDelay function
vi.mock('../../mocks/usersMock', async () => {
  const actual = await vi.importActual('../../mocks/usersMock');
  return {
    ...actual,
    simulateApiDelay: vi.fn(() => Promise.resolve()),
  };
});

describe('useUserManagementStore', () => {
  beforeEach(() => {
    // Reset store state before each test
    useUserManagementStore.setState({
      users: [],
      filteredUsers: [],
      selectedUser: null,
      usersLoading: false,
      usersError: null,
      stats: {
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        adminUsers: 0,
        managerUsers: 0,
        regularUsers: 0,
        viewerUsers: 0,
        recentLogins: 0,
        newUsersThisMonth: 0,
      },
      statsLoading: false,
      statsError: null,
      filters: {
        search: '',
        role: 'all',
        department: '',
        isActive: undefined,
        sortBy: 'name',
        sortOrder: 'asc',
        page: 1,
        limit: 12,
      },
      pagination: {
        page: 1,
        limit: 12,
        total: 0,
        totalPages: 0,
      },
      departments: [],
      isCreateModalOpen: false,
      isEditModalOpen: false,
      isDeleteModalOpen: false,
      formLoading: false,
      formError: null,
    });
  });

  it('should have initial state', () => {
    const state = useUserManagementStore.getState();
    
    expect(state.users).toEqual([]);
    expect(state.filteredUsers).toEqual([]);
    expect(state.selectedUser).toBeNull();
    expect(state.usersLoading).toBe(false);
    expect(state.usersError).toBeNull();
  });

  it('should load users successfully', async () => {
    const { loadUsers } = useUserManagementStore.getState();
    
    await loadUsers();
    
    const state = useUserManagementStore.getState();
    expect(state.users.length).toBeGreaterThan(0);
    expect(state.filteredUsers.length).toBeGreaterThan(0);
    expect(state.usersLoading).toBe(false);
    expect(state.usersError).toBeNull();
  });

  it('should load stats successfully', async () => {
    const { loadStats } = useUserManagementStore.getState();
    
    await loadStats();
    
    const state = useUserManagementStore.getState();
    expect(state.stats.totalUsers).toBeGreaterThan(0);
    expect(state.statsLoading).toBe(false);
    expect(state.statsError).toBeNull();
  });

  it('should create user successfully', async () => {
    const { createUser, loadUsers } = useUserManagementStore.getState();
    
    // Load initial users
    await loadUsers();
    const initialCount = useUserManagementStore.getState().users.length;
    
    const newUserData: CreateUserDto = {
      name: 'New User',
      email: '<EMAIL>',
      role: 'user',
      department: 'TI',
      position: 'Developer',
      isActive: true,
    };
    
    await createUser(newUserData);
    
    const state = useUserManagementStore.getState();
    expect(state.users.length).toBe(initialCount + 1);
    expect(state.formLoading).toBe(false);
    expect(state.formError).toBeNull();
    
    // Check if the new user was added
    const newUser = state.users.find(u => u.email === '<EMAIL>');
    expect(newUser).toBeDefined();
    expect(newUser?.name).toBe('New User');
  });

  it('should update user successfully', async () => {
    const { loadUsers, updateUser } = useUserManagementStore.getState();
    
    // Load users first
    await loadUsers();
    const state = useUserManagementStore.getState();
    const userToUpdate = state.users[0];
    
    const updateData: UpdateUserDto = {
      name: 'Updated Name',
      department: 'Updated Department',
    };
    
    await updateUser(userToUpdate.id, updateData);
    
    const updatedState = useUserManagementStore.getState();
    const updatedUser = updatedState.users.find(u => u.id === userToUpdate.id);
    
    expect(updatedUser?.name).toBe('Updated Name');
    expect(updatedUser?.department).toBe('Updated Department');
    expect(updatedState.formLoading).toBe(false);
    expect(updatedState.formError).toBeNull();
  });

  it('should delete user successfully', async () => {
    const { loadUsers, deleteUser } = useUserManagementStore.getState();
    
    // Load users first
    await loadUsers();
    const initialState = useUserManagementStore.getState();
    const userToDelete = initialState.users[0];
    const initialCount = initialState.users.length;
    
    await deleteUser(userToDelete.id);
    
    const state = useUserManagementStore.getState();
    expect(state.users.length).toBe(initialCount - 1);
    expect(state.users.find(u => u.id === userToDelete.id)).toBeUndefined();
    expect(state.formLoading).toBe(false);
    expect(state.formError).toBeNull();
  });

  it('should toggle user status successfully', async () => {
    const { loadUsers, toggleUserStatus } = useUserManagementStore.getState();
    
    // Load users first
    await loadUsers();
    const state = useUserManagementStore.getState();
    const userToToggle = state.users[0];
    const initialStatus = userToToggle.isActive;
    
    await toggleUserStatus(userToToggle.id);
    
    const updatedState = useUserManagementStore.getState();
    const updatedUser = updatedState.users.find(u => u.id === userToToggle.id);
    
    expect(updatedUser?.isActive).toBe(!initialStatus);
    expect(updatedState.formLoading).toBe(false);
    expect(updatedState.formError).toBeNull();
  });

  it('should set filters correctly', () => {
    const { setFilters } = useUserManagementStore.getState();
    
    setFilters({ search: 'test', role: 'admin' });
    
    const state = useUserManagementStore.getState();
    expect(state.filters.search).toBe('test');
    expect(state.filters.role).toBe('admin');
    expect(state.filters.page).toBe(1); // Should reset page when filters change
  });

  it('should clear filters correctly', () => {
    const { setFilters, clearFilters } = useUserManagementStore.getState();
    
    // Set some filters first
    setFilters({ search: 'test', role: 'admin' });
    
    // Clear filters
    clearFilters();
    
    const state = useUserManagementStore.getState();
    expect(state.filters.search).toBe('');
    expect(state.filters.role).toBe('all');
    expect(state.filters.page).toBe(1);
  });

  it('should manage modal states correctly', () => {
    const { openCreateModal, openEditModal, openDeleteModal, closeModals } = useUserManagementStore.getState();
    
    // Test create modal
    openCreateModal();
    let state = useUserManagementStore.getState();
    expect(state.isCreateModalOpen).toBe(true);
    expect(state.selectedUser).toBeNull();
    
    // Test edit modal
    const mockUser = { id: '1', name: 'Test', email: '<EMAIL>' } as any;
    openEditModal(mockUser);
    state = useUserManagementStore.getState();
    expect(state.isEditModalOpen).toBe(true);
    expect(state.selectedUser).toBe(mockUser);
    
    // Test delete modal
    openDeleteModal(mockUser);
    state = useUserManagementStore.getState();
    expect(state.isDeleteModalOpen).toBe(true);
    expect(state.selectedUser).toBe(mockUser);
    
    // Test close modals
    closeModals();
    state = useUserManagementStore.getState();
    expect(state.isCreateModalOpen).toBe(false);
    expect(state.isEditModalOpen).toBe(false);
    expect(state.isDeleteModalOpen).toBe(false);
    expect(state.selectedUser).toBeNull();
  });

  it('should handle pagination correctly', () => {
    const { setPage, setLimit } = useUserManagementStore.getState();
    
    setPage(3);
    let state = useUserManagementStore.getState();
    expect(state.filters.page).toBe(3);
    
    setLimit(24);
    state = useUserManagementStore.getState();
    expect(state.filters.limit).toBe(24);
    expect(state.filters.page).toBe(1); // Should reset page when limit changes
  });
});
