import * as crypto from 'crypto';
import axios, { AxiosResponse } from 'axios';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';

// Interfaces básicas
interface MercadoLivreOAuthConfig {
    clientId: string;
    clientSecret: string;
    redirectUri: string;
    baseUrl: string;
    authUrl: string;
    tokenUrl: string;
    scope: string[];
}

interface MercadoLivreTokenResponse {
    access_token: string;
    refresh_token: string;
    expires_in: number;
    token_type: string;
    scope: string;
    user_id: string;
}

interface TokenCacheEntry {
    token: string;
    expiresAt: Date;
    userId: string;
}

// Classes de erro locais
class ValidationError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'ValidationError';
    }
}

class AuthenticationError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AuthenticationError';
    }
}

export class MercadoLivreOAuthService {
    private prisma: PrismaClient;
    private config: MercadoLivreOAuthConfig;
    private tokenCache: Map<string, TokenCacheEntry>;
    private rateLimitInfo: Map<string, { remaining: number; reset: number; limit: number }>;

    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
        this.tokenCache = new Map();
        this.rateLimitInfo = new Map();
        
        // Configuração baseada em variáveis de ambiente
        this.config = {
            clientId: process.env.MERCADO_LIVRE_CLIENT_ID || '',
            clientSecret: process.env.MERCADO_LIVRE_CLIENT_SECRET || '',
            redirectUri: process.env.MERCADO_LIVRE_REDIRECT_URI || '',
            baseUrl: process.env.MERCADO_LIVRE_API_URL || 'https://api.mercadolibre.com',
            authUrl: process.env.MERCADO_LIVRE_AUTH_URL || 'https://auth.mercadolibre.com.ar',
            tokenUrl: process.env.MERCADO_LIVRE_TOKEN_URL || 'https://api.mercadolibre.com/oauth/token',
            scope: ['offline_access', 'read', 'write']
        };

        this.validateConfig();
    }

    /**
     * Valida a configuração do OAuth
     */
    private validateConfig(): void {
        if (!this.config.clientId) {
            throw new ValidationError('MERCADO_LIVRE_CLIENT_ID é obrigatório');
        }
        if (!this.config.clientSecret) {
            throw new ValidationError('MERCADO_LIVRE_CLIENT_SECRET é obrigatório');
        }
        if (!this.config.redirectUri) {
            throw new ValidationError('MERCADO_LIVRE_REDIRECT_URI é obrigatório');
        }
    }

    /**
     * Gera um state único para segurança OAuth
     */
    public generateState(): string {
        return crypto.randomBytes(32).toString('hex');
    }

    /**
     * Gera URL de autorização
     */
    public async getAuthorizationUrl(tenantId: string, userId: string): Promise<string> {
        try {
            const state = this.generateState();
            
            // Armazena o state temporariamente (em produção, usar Redis)
            // Por enquanto, vamos usar um cache em memória simples
            
            const params = new URLSearchParams({
                response_type: 'code',
                client_id: this.config.clientId,
                redirect_uri: this.config.redirectUri,
                scope: this.config.scope.join(' '),
                state: `${tenantId}_${userId}_${state}`
            });

            const authUrl = `${this.config.authUrl}/authorization?${params.toString()}`;
            
            logger.info('URL de autorização gerada', {
                tenantId,
                userId,
                state
            });

            return authUrl;
        } catch (error) {
            logger.error('Erro ao gerar URL de autorização:', error);
            throw error;
        }
    }

    /**
     * Processa o callback do OAuth
     */
    public async handleCallback(code: string, state: string): Promise<any> {
        try {
            // Extrai informações do state
            const [tenantId, userId] = state.split('_');
            
            if (!tenantId || !userId) {
                throw new ValidationError('State inválido');
            }

            // Troca o código por tokens
            const tokens = await this.exchangeCodeForTokens(code);
            
            // Busca informações do usuário
            const userInfo = await this.getUserInfo(tokens.access_token);
            
            // Salva a conta no banco de dados
            const account = await this.saveAccount(tenantId, userId, tokens, userInfo);
            
            logger.info('Callback OAuth processado com sucesso', {
                tenantId,
                userId,
                accountId: account.id
            });

            return {
                account,
                tokens: {
                    access_token: tokens.access_token,
                    expires_in: tokens.expires_in
                }
            };
        } catch (error) {
            logger.error('Erro no callback OAuth:', error);
            throw error;
        }
    }

    /**
     * Troca código por tokens
     */
    private async exchangeCodeForTokens(code: string): Promise<MercadoLivreTokenResponse> {
        try {
            const response = await axios.post(this.config.tokenUrl, {
                grant_type: 'authorization_code',
                client_id: this.config.clientId,
                client_secret: this.config.clientSecret,
                code,
                redirect_uri: this.config.redirectUri
            });

            return response.data;
        } catch (error) {
            logger.error('Erro ao trocar código por tokens:', error);
            throw new AuthenticationError('Falha na autenticação com Mercado Livre');
        }
    }

    /**
     * Busca informações do usuário
     */
    private async getUserInfo(accessToken: string): Promise<any> {
        try {
            const response = await axios.get(`${this.config.baseUrl}/users/me`, {
                headers: {
                    Authorization: `Bearer ${accessToken}`
                }
            });

            return response.data;
        } catch (error) {
            logger.error('Erro ao buscar informações do usuário:', error);
            throw new AuthenticationError('Falha ao obter informações do usuário');
        }
    }

    /**
     * Salva a conta no banco de dados
     */
    private async saveAccount(tenantId: string, userId: string, tokens: MercadoLivreTokenResponse, userInfo: any): Promise<any> {
        try {
            const expiresAt = new Date(Date.now() + tokens.expires_in * 1000);

            const account = await this.prisma.mercadoLivreAccount.upsert({
                where: {
                    tenantId_mlUserId: {
                        tenantId,
                        mlUserId: tokens.user_id.toString()
                    }
                },
                update: {
                    accessToken: tokens.access_token,
                    refreshToken: tokens.refresh_token,
                    expiresAt,
                    nickname: userInfo.nickname,
                    email: userInfo.email,
                    isActive: true,
                    updatedAt: new Date()
                },
                create: {
                    tenantId,
                    mlUserId: tokens.user_id.toString(),
                    accessToken: tokens.access_token,
                    refreshToken: tokens.refresh_token,
                    expiresAt,
                    nickname: userInfo.nickname,
                    email: userInfo.email,
                    isActive: true
                }
            });

            return account;
        } catch (error) {
            logger.error('Erro ao salvar conta:', error);
            throw error;
        }
    }

    /**
     * Atualiza token de acesso
     */
    public async refreshAccessToken(refreshToken: string): Promise<MercadoLivreTokenResponse> {
        try {
            const response = await axios.post(this.config.tokenUrl, {
                grant_type: 'refresh_token',
                client_id: this.config.clientId,
                client_secret: this.config.clientSecret,
                refresh_token: refreshToken
            });

            return response.data;
        } catch (error) {
            logger.error('Erro ao atualizar token:', error);
            throw new AuthenticationError('Falha ao atualizar token de acesso');
        }
    }

    /**
     * Health check do serviço
     */
    public async healthCheck(): Promise<{ status: 'ok' | 'error'; details: any }> {
        try {
            // Testa conectividade com API
            const response = await axios.get(`${this.config.baseUrl}/sites`, {
                timeout: 5000
            });

            return {
                status: 'ok',
                details: {
                    apiUrl: this.config.baseUrl,
                    responseTime: response.headers['x-response-time'] || 'unknown',
                    cacheSize: this.tokenCache.size,
                    rateLimitCacheSize: this.rateLimitInfo.size
                }
            };
        } catch (error) {
            return {
                status: 'error',
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    apiUrl: this.config.baseUrl
                }
            };
        }
    }
}