import React, { useState } from 'react';
import ProductCard from './ProductCard';
import { Card, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select-radix';
import { 
  RefreshCw, 
  Package, 
  ChevronLeft, 
  ChevronRight,
  Grid3X3,
  List,
  MoreHorizontal
} from 'lucide-react';
import type { ProductWithStock, PaginatedResponse } from '../../types/api';

interface ProductGridProps {
  products: ProductWithStock[];
  isLoading?: boolean;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  onPageChange?: (page: number) => void;
  onLimitChange?: (limit: number) => void;
  onUpdateStock?: (productId: string, quantity: number) => Promise<void>;
  onViewDetails?: (product: ProductWithStock) => void;
  onSyncProduct?: (productId: string) => Promise<void>;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
}

export default function ProductGrid({
  products,
  isLoading = false,
  pagination,
  onPageChange,
  onLimitChange,
  onUpdateStock,
  onViewDetails,
  onSyncProduct,
  viewMode = 'grid',
  onViewModeChange
}: ProductGridProps) {
  const [updatingProducts, setUpdatingProducts] = useState<Set<string>>(new Set());

  const handleUpdateStock = async (productId: string, quantity: number) => {
    if (!onUpdateStock) return;
    
    setUpdatingProducts(prev => new Set(prev).add(productId));
    try {
      await onUpdateStock(productId, quantity);
    } finally {
      setUpdatingProducts(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  };

  const renderPagination = () => {
    if (!pagination || pagination.totalPages <= 1) return null;

    const { page, totalPages, hasNext, hasPrev } = pagination;
    const maxVisiblePages = 5;
    const startPage = Math.max(1, page - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    return (
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>
            Página {page} de {totalPages} ({pagination.total} produtos)
          </span>
        </div>

        <div className="flex items-center gap-2">
          {/* Items per page */}
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Mostrar:</span>
            <Select
              value={pagination.limit.toString()}
              onValueChange={(value) => onLimitChange?.(parseInt(value))}
            >
              <SelectTrigger className="w-20">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="12">12</SelectItem>
                <SelectItem value="24">24</SelectItem>
                <SelectItem value="48">48</SelectItem>
                <SelectItem value="96">96</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Pagination buttons */}
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(page - 1)}
              disabled={!hasPrev}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>

            {Array.from({ length: endPage - startPage + 1 }, (_, i) => {
              const pageNum = startPage + i;
              return (
                <Button
                  key={pageNum}
                  variant={pageNum === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => onPageChange?.(pageNum)}
                  className="w-8"
                >
                  {pageNum}
                </Button>
              );
            })}

            {endPage < totalPages && (
              <>
                {endPage < totalPages - 1 && (
                  <Button variant="ghost" size="sm" disabled className="w-8">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                )}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onPageChange?.(totalPages)}
                  className="w-8"
                >
                  {totalPages}
                </Button>
              </>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange?.(page + 1)}
              disabled={!hasNext}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    );
  };

  const renderViewModeToggle = () => {
    if (!onViewModeChange) return null;

    return (
      <div className="flex items-center gap-1 border rounded-md p-1">
        <Button
          variant={viewMode === 'grid' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onViewModeChange('grid')}
          className="h-8 w-8 p-0"
        >
          <Grid3X3 className="h-4 w-4" />
        </Button>
        <Button
          variant={viewMode === 'list' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => onViewModeChange('list')}
          className="h-8 w-8 p-0"
        >
          <List className="h-4 w-4" />
        </Button>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {/* Header skeleton */}
        <div className="flex items-center justify-between">
          <div className="h-6 w-32 bg-muted rounded animate-pulse" />
          <div className="h-8 w-24 bg-muted rounded animate-pulse" />
        </div>

        {/* Grid skeleton */}
        <div className={`grid ${
          viewMode === 'grid'
            ? 'grid-cols-1 lg:grid-cols-2 gap-6'
            : 'grid-cols-1 gap-3'
        }`}>
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                {viewMode === 'list' ? (
                  // List skeleton - horizontal layout
                  <div className="flex items-center gap-4">
                    <div className="w-16 h-16 bg-muted rounded flex-shrink-0" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-3 bg-muted rounded w-1/2" />
                    </div>
                    <div className="flex gap-4">
                      <div className="space-y-1">
                        <div className="h-3 bg-muted rounded w-12" />
                        <div className="h-4 bg-muted rounded w-8" />
                      </div>
                      <div className="space-y-1">
                        <div className="h-3 bg-muted rounded w-12" />
                        <div className="h-4 bg-muted rounded w-16" />
                      </div>
                      <div className="h-6 bg-muted rounded w-20" />
                    </div>
                  </div>
                ) : (
                  // Grid skeleton - vertical layout
                  <div className="space-y-4">
                    <div className="flex justify-between items-start">
                      <div className="w-8 h-8 bg-muted rounded" />
                      <div className="w-16 h-16 bg-muted rounded" />
                    </div>
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded w-3/4" />
                      <div className="h-3 bg-muted rounded w-1/2" />
                    </div>
                    <div className="flex gap-2">
                      <div className="h-5 w-16 bg-muted rounded" />
                      <div className="h-5 w-20 bg-muted rounded" />
                    </div>
                    <div className="h-8 bg-muted rounded w-24 mx-auto" />
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center py-16 text-muted-foreground">
            <div className="max-w-md mx-auto">
              <Package className="h-16 w-16 mx-auto mb-6 opacity-50" />
              <h3 className="text-xl font-semibold mb-3 text-foreground">Nenhum produto encontrado</h3>
              <p className="text-sm mb-6">
                Tente ajustar os filtros ou sincronizar seus produtos do Mercado Livre para visualizar o catálogo completo.
              </p>
              <div className="text-xs">
                <p className="mb-2 font-medium">Dicas:</p>
                <ul className="space-y-1 text-left">
                  <li>• Verifique se os filtros estão muito restritivos</li>
                  <li>• Sincronize produtos recentes do ML</li>
                  <li>• Conecte sua conta se ainda não fez</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold">
            {products.length} produto{products.length !== 1 ? 's' : ''}
          </h2>
          {pagination && (
            <span className="text-sm text-muted-foreground">
              de {pagination.total} total
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {renderViewModeToggle()}
        </div>
      </div>

      {/* Grid */}
      <div className={`grid ${
        viewMode === 'grid'
          ? 'grid-cols-1 lg:grid-cols-2 gap-6'
          : 'grid-cols-1 gap-3'
      }`}>
        {products.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
            onUpdateStock={handleUpdateStock}
            onViewDetails={onViewDetails}
            onSyncProduct={onSyncProduct}
            isUpdatingStock={updatingProducts.has(product.id)}
            viewMode={viewMode}
          />
        ))}
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="mt-6 pt-4 border-t">
          {renderPagination()}
        </div>
      )}
    </div>
  );
}
