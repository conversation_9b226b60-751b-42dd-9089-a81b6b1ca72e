import { Router, Request, Response } from 'express';
import { authenticate } from '../middleware/auth';
import { getErrorMetrics } from '../middleware/errorHandler';
import { asyncHandler } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import DashboardMetricsService, { DashboardAlert } from '../services/dashboardMetricsService';
import { PrismaClient } from '@prisma/client';
import * as os from 'os';

const router = Router();
const prisma = new PrismaClient();
const dashboardMetrics = new DashboardMetricsService(prisma);

// Middleware de autenticação para todas as rotas de monitoramento
router.use(authenticate({ required: true }));

/**
 * GET /api/monitoring/dashboard
 * Métricas consolidadas para o dashboard principal
 */
router.get('/dashboard', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = (req as any).user?.tenantId;
  
  try {
    const [metrics, alerts, chartData] = await Promise.all([
      dashboardMetrics.getSystemMetrics(tenantId),
      dashboardMetrics.generateAlerts(tenantId),
      dashboardMetrics.getChartData(tenantId, 7)
    ]);
    
    logger.info('Dashboard metrics solicitadas', {
      userId: (req as any).user?.id,
      tenantId,
      alertsCount: alerts.length,
      uptime: metrics.uptime
    });
    
    res.json({
      success: true,
      data: {
        metrics,
        alerts: alerts.slice(0, 10), // Últimos 10 alertas
        charts: chartData,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Erro ao obter métricas do dashboard', {
      error: error instanceof Error ? error.message : 'Unknown',
      userId: (req as any).user?.id,
      tenantId
    });
    
    throw error;
  }
}));

/**
 * GET /api/monitoring/alerts
 * Alertas do sistema com paginação
 */
router.get('/alerts', asyncHandler(async (req: Request, res: Response) => {
  const tenantId = (req as any).user?.tenantId;
  const { page = '1', limit = '20', unreadOnly = 'false' } = req.query;
  
  try {
    const alerts = await dashboardMetrics.generateAlerts(tenantId);
    
    // Filtrar apenas não lidos se solicitado
    const filteredAlerts = unreadOnly === 'true' 
      ? alerts.filter((alert: DashboardAlert) => !alert.isRead)
      : alerts;
    
    // Paginação simples
    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    
    const paginatedAlerts = filteredAlerts.slice(startIndex, endIndex);
    
    logger.info('Alertas solicitados', {
      userId: (req as any).user?.id,
      tenantId,
      page: pageNum,
      limit: limitNum,
      unreadOnly,
      totalAlerts: filteredAlerts.length
    });
    
    res.json({
      success: true,
      data: {
        alerts: paginatedAlerts,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: filteredAlerts.length,
          pages: Math.ceil(filteredAlerts.length / limitNum),
          hasNext: endIndex < filteredAlerts.length,
          hasPrev: pageNum > 1
        },
        filters: {
          unreadOnly: unreadOnly === 'true',
          tenantId
        }
      }
    });
    
  } catch (error) {
    logger.error('Erro ao obter alertas', {
      error: error instanceof Error ? error.message : 'Unknown',
      userId: (req as any).user?.id,
      tenantId
    });
    
    throw error;
  }
}));

/**
 * GET /api/monitoring/charts/:type
 * Dados específicos para gráficos
 */
router.get('/charts/:type', asyncHandler(async (req: Request, res: Response) => {
  const { type } = req.params;
  const { days = '7' } = req.query;
  const tenantId = (req as any).user?.tenantId;
  
  try {
    const chartData = await dashboardMetrics.getChartData(tenantId, parseInt(days as string));
    
    let data;
    switch (type) {
      case 'errors':
        data = chartData.errorsByDay;
        break;
      case 'requests':
        data = chartData.requestsByHour;
        break;
      case 'stock':
        data = chartData.stockData;
        break;
      default:
        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid chart type. Available: errors, requests, stock',
            code: 'INVALID_CHART_TYPE'
          }
        });
    }
    
    logger.info('Dados de gráfico solicitados', {
      userId: (req as any).user?.id,
      tenantId,
      chartType: type,
      days: parseInt(days as string),
      dataPoints: data.length
    });
    
    res.json({
      success: true,
      data: {
        chart: data,
        type,
        period: chartData.period
      }
    });
    
  } catch (error) {
    logger.error('Erro ao obter dados do gráfico', {
      error: error instanceof Error ? error.message : 'Unknown',
      userId: (req as any).user?.id,
      tenantId,
      chartType: type
    });
    
    throw error;
  }
}));

/**
 * GET /api/monitoring/errors
 * Obter métricas de erro do sistema
 */
router.get('/errors', asyncHandler(async (req: Request, res: Response) => {
  const { tenantId, startDate, endDate } = req.query;
  
  // Usar tenantId do usuário logado se não especificado (segurança)
  const effectiveTenantId = tenantId as string || (req as any).user?.tenantId;
  
  // Converter datas se fornecidas
  const start = startDate ? new Date(startDate as string) : undefined;
  const end = endDate ? new Date(endDate as string) : undefined;
  
  try {
    const metrics = await getErrorMetrics(effectiveTenantId, start, end);
    
    logger.info('Métricas de erro solicitadas', {
      userId: (req as any).user?.id,
      tenantId: effectiveTenantId,
      startDate: start?.toISOString(),
      endDate: end?.toISOString(),
      totalErrors: metrics.totalErrors
    });
    
    res.json({
      success: true,
      data: {
        metrics,
        filters: {
          tenantId: effectiveTenantId,
          startDate: start?.toISOString(),
          endDate: end?.toISOString()
        },
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Erro ao obter métricas de erro', {
      error: error instanceof Error ? error.message : 'Unknown',
      userId: (req as any).user?.id,
      tenantId: effectiveTenantId
    });
    
    throw error;
  }
}));

/**
 * GET /api/monitoring/health
 * Status de saúde do sistema
 */
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  const healthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: process.version,
    environment: process.env.NODE_ENV || 'development'
  };
  
  logger.info('Health check solicitado', {
    userId: (req as any).user?.id,
    tenantId: (req as any).user?.tenantId,
    uptime: healthStatus.uptime
  });
  
  res.json({
    success: true,
    data: healthStatus
  });
}));

/**
 * GET /api/monitoring/system
 * Métricas do sistema
 */
router.get('/system', asyncHandler(async (req: Request, res: Response) => {
  const systemMetrics = {
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: {
      ...process.memoryUsage(),
      totalMB: Math.round(process.memoryUsage().heapTotal / 1024 / 1024),
      usedMB: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      usagePercentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
    },
    cpu: {
      loadAverage: process.platform !== 'win32' ? os.loadavg() : [0, 0, 0],
      platform: process.platform,
      arch: process.arch
    },
    node: {
      version: process.version,
      versions: process.versions
    },
    environment: process.env.NODE_ENV || 'development'
  };
  
  logger.info('Métricas do sistema solicitadas', {
    userId: (req as any).user?.id,
    tenantId: (req as any).user?.tenantId,
    memoryUsage: systemMetrics.memory.usagePercentage + '%'
  });
  
  res.json({
    success: true,
    data: systemMetrics
  });
}));

/**
 * GET /api/monitoring/logs
 * Últimos logs do sistema (apenas para admins)
 */
router.get('/logs', asyncHandler(async (req: Request, res: Response) => {
  // Verificar se o usuário é admin (implementar conforme necessário)
  const user = (req as any).user;
  if (!user?.isAdmin) {
    return res.status(403).json({
      success: false,
      error: {
        message: 'Access denied. Admin privileges required.',
        code: 'ADMIN_REQUIRED'
      }
    });
  }
  
  const { level = 'info', limit = '100' } = req.query;
  
  // Aqui você pode implementar a leitura dos arquivos de log
  // Por agora, retornamos uma resposta placeholder
  
  logger.info('Logs solicitados por admin', {
    userId: user.id,
    tenantId: user.tenantId,
    level,
    limit
  });
  
  res.json({
    success: true,
    data: {
      logs: [], // Implementar leitura dos logs se necessário
      filters: {
        level,
        limit: parseInt(limit as string)
      },
      message: 'Log access feature - implement log file reading if needed'
    }
  });
}));

/**
 * GET /api/monitoring/performance
 * Métricas de performance em tempo real
 */
router.get('/performance', asyncHandler(async (req: Request, res: Response) => {
  const startTime = Date.now();
  
  try {
    // Métricas básicas de performance
    const performanceMetrics = {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: {
        ...process.memoryUsage(),
        freeMemory: Math.round((process.memoryUsage().heapTotal - process.memoryUsage().heapUsed) / 1024 / 1024),
        usagePercentage: Math.round((process.memoryUsage().heapUsed / process.memoryUsage().heapTotal) * 100)
      },
      eventLoop: {
        delay: await new Promise<number>((resolve) => {
          const start = process.hrtime.bigint();
          setImmediate(() => {
            const delay = Number(process.hrtime.bigint() - start) / 1000000;
            resolve(delay);
          });
        })
      },
      response: {
        timeMs: Date.now() - startTime
      }
    };
    
    logger.info('Métricas de performance solicitadas', {
      userId: (req as any).user?.id,
      tenantId: (req as any).user?.tenantId,
      responseTime: performanceMetrics.response.timeMs
    });
    
    res.json({
      success: true,
      data: performanceMetrics
    });
    
  } catch (error) {
    logger.error('Erro ao obter métricas de performance', {
      error: error instanceof Error ? error.message : 'Unknown',
      userId: (req as any).user?.id
    });
    
    throw error;
  }
}));

/**
 * GET /api/monitoring/cache
 * Métricas de cache
 */
router.get('/cache', asyncHandler(async (req: Request, res: Response) => {
  try {
    // Importar cache service dinamicamente para evitar problemas de dependência
    const { default: CacheService } = await import('../services/cacheService');
    const cache = new CacheService();
    
    const cacheMetrics = cache.getMetrics();
    
    logger.info('Métricas de cache solicitadas', {
      userId: (req as any).user?.id,
      tenantId: (req as any).user?.tenantId,
      hitRate: cacheMetrics.hitRate
    });
    
    res.json({
      success: true,
      data: {
        metrics: cacheMetrics,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('Erro ao obter métricas de cache', {
      error: error instanceof Error ? error.message : 'Unknown',
      userId: (req as any).user?.id
    });
    
    // Retornar métricas vazias se houver erro
    res.json({
      success: true,
      data: {
        metrics: {
          hits: 0,
          misses: 0,
          sets: 0,
          deletes: 0,
          hitRate: 0,
          totalRequests: 0
        },
        timestamp: new Date().toISOString(),
        error: 'Cache metrics unavailable'
      }
    });
  }
}));

export default router; 