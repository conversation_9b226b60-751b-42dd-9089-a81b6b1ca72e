/**
 * Middleware de Rate Limiting
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { RateLimitError } from './errorHandler';

interface RateLimitOptions {
  windowMs: number;
  max: number;
  message?: string;
}

// Store simples em memória para rate limiting
// TODO: Migrar para Redis em produção
const requestCounts = new Map<string, { count: number; resetTime: number }>();

/**
 * Cria um middleware de rate limiting personalizado
 * Versão simplificada usando Map em memória
 */
export const rateLimiter = (options: RateLimitOptions) => {
  const {
    windowMs,
    max,
    message = 'Muitas requisições, tente novamente mais tarde'
  } = options;

  return (req: Request, res: Response, next: NextFunction) => {
    // Ignorar requisições OPTIONS (pre-flight do CORS)
    if (req.method === 'OPTIONS') {
      return next();
    }
    
    try {
      const { tenantId, userId } = req.user || {};
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      
      // Gera chave única combinando tenant, usuário e IP
      const key = tenantId ? `${tenantId}:${userId}:${ip}` : ip;
      const now = Date.now();
      
      // Busca ou cria contador para esta chave
      let record = requestCounts.get(key);
      
      if (!record || now > record.resetTime) {
        // Cria novo record ou reseta se janela expirou
        record = {
          count: 1,
          resetTime: now + windowMs
        };
        requestCounts.set(key, record);
      } else {
        // Incrementa contador existente
        record.count++;
      }

      // Adiciona headers de rate limit
      res.set({
        'X-RateLimit-Limit': max.toString(),
        'X-RateLimit-Remaining': Math.max(0, max - record.count).toString(),
        'X-RateLimit-Reset': new Date(record.resetTime).toISOString()
      });

      // Verifica se limite foi excedido
      if (record.count > max) {
        logger.warn('Rate limit excedido', {
          tenantId,
          userId,
          ip,
          path: req.path,
          method: req.method,
          count: record.count,
          limit: max,
          userAgent: req.get('User-Agent')
        });

        throw new RateLimitError(message);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

/**
 * Rate limiters pré-configurados para diferentes tipos de operação
 */
export const rateLimiters = {
  // Para operações de leitura leves
  light: rateLimiter({ windowMs: 60 * 1000, max: 100 }),
  
  // Para operações de leitura normais
  normal: rateLimiter({ windowMs: 60 * 1000, max: 60 }),
  
  // Para operações de escrita
  write: rateLimiter({ windowMs: 60 * 1000, max: 30 }),
  
  // Para operações pesadas (cálculos, relatórios)
  heavy: rateLimiter({ windowMs: 60 * 1000, max: 10 }),
  
  // Para operações críticas (configurações, bulk operations)
  critical: rateLimiter({ windowMs: 60 * 1000, max: 5 }),
  
  // Para autenticação
  auth: rateLimiter({ 
    windowMs: 15 * 60 * 1000, // 15 minutos
    max: 5,
    message: 'Muitas tentativas de login, tente novamente em 15 minutos'
  })
};

/**
 * Rate limiter global para toda a aplicação
 */
export const globalRateLimit = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 1000, // 1000 requests por IP por 15 minutos
  message: 'Limite global de requisições excedido'
});

export default rateLimiter; 