import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Label } from '../ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select-radix';
import { Checkbox } from '../ui/checkbox';
import { Slider } from '../ui/slider';
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown,
  ChevronUp,
  RotateCcw,
  Package,
  AlertTriangle,
  TrendingDown,
  TrendingUp
} from 'lucide-react';
import type { StockFilters } from '../../store/stockStore';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';

interface StockFiltersMLProps {
  filters: StockFilters;
  onFiltersChange: (filters: StockFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
  itemCount?: number;
  locations?: string[];
}

export default function StockFiltersML({
  filters,
  onFiltersChange,
  onClearFilters,
  isLoading = false,
  itemCount = 0,
  locations = []
}: StockFiltersMLProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState<StockFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleFilterChange = (key: keyof StockFilters, value: any) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const handleStatusChange = (status: string, checked: boolean) => {
    const currentStatus = localFilters.status || [];
    let newStatus: string[];
    
    if (checked) {
      newStatus = [...currentStatus, status];
    } else {
      newStatus = currentStatus.filter(s => s !== status);
    }
    
    handleFilterChange('status', newStatus);
  };

  const handleQuantityRangeChange = (values: number[]) => {
    handleFilterChange('minQuantity', values[0]);
    handleFilterChange('maxQuantity', values[1]);
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (localFilters.search) count++;
    if (localFilters.location) count++;
    if (localFilters.status && localFilters.status.length > 0) count++;
    if (localFilters.minQuantity !== undefined) count++;
    if (localFilters.maxQuantity !== undefined) count++;
    if (localFilters.sortBy && localFilters.sortBy !== 'lastUpdated') count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();
  const maxQuantity = 1000; // Could be dynamic based on actual data

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filtros de Estoque
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {itemCount > 0 && (
              <Badge variant="outline" className="text-xs">
                {itemCount} {itemCount === 1 ? 'item' : 'itens'}
              </Badge>
            )}
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  {isExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Buscar</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Buscar por título, SKU ou localização..."
              value={localFilters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-10"
              disabled={isLoading}
            />
            {localFilters.search && (
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => handleFilterChange('search', '')}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-4">
            {/* Location Filter */}
            <div className="space-y-2">
              <Label>Localização</Label>
              <Select
                value={localFilters.location || ''}
                onValueChange={(value) => handleFilterChange('location', value === 'all' ? '' : value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Todas as localizações" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas as localizações</SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status Filter */}
            <div className="space-y-3">
              <Label>Status do Estoque</Label>
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="in_stock"
                    checked={localFilters.status?.includes('in_stock') || false}
                    onCheckedChange={(checked) => handleStatusChange('in_stock', checked as boolean)}
                    disabled={isLoading}
                  />
                  <Label htmlFor="in_stock" className="flex items-center gap-2 cursor-pointer">
                    <Package className="h-4 w-4 text-green-500" />
                    Em Estoque
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="low_stock"
                    checked={localFilters.status?.includes('low_stock') || false}
                    onCheckedChange={(checked) => handleStatusChange('low_stock', checked as boolean)}
                    disabled={isLoading}
                  />
                  <Label htmlFor="low_stock" className="flex items-center gap-2 cursor-pointer">
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                    Baixo Estoque
                  </Label>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="out_of_stock"
                    checked={localFilters.status?.includes('out_of_stock') || false}
                    onCheckedChange={(checked) => handleStatusChange('out_of_stock', checked as boolean)}
                    disabled={isLoading}
                  />
                  <Label htmlFor="out_of_stock" className="flex items-center gap-2 cursor-pointer">
                    <TrendingDown className="h-4 w-4 text-red-500" />
                    Fora de Estoque
                  </Label>
                </div>
              </div>
            </div>

            {/* Quantity Range */}
            <div className="space-y-3">
              <Label>Faixa de Quantidade</Label>
              <div className="px-2">
                <Slider
                  value={[
                    localFilters.minQuantity || 0,
                    localFilters.maxQuantity || maxQuantity
                  ]}
                  onValueChange={handleQuantityRangeChange}
                  max={maxQuantity}
                  min={0}
                  step={1}
                  className="w-full"
                  disabled={isLoading}
                />
                <div className="flex justify-between text-xs text-muted-foreground mt-1">
                  <span>{localFilters.minQuantity || 0}</span>
                  <span>{localFilters.maxQuantity || maxQuantity}</span>
                </div>
              </div>
            </div>

            {/* Sort Options */}
            <div className="space-y-2">
              <Label>Ordenar por</Label>
              <Select
                value={localFilters.sortBy || 'lastUpdated'}
                onValueChange={(value) => handleFilterChange('sortBy', value)}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="lastUpdated">Última Atualização</SelectItem>
                  <SelectItem value="productTitle">Título do Produto</SelectItem>
                  <SelectItem value="currentQuantity">Quantidade</SelectItem>
                  <SelectItem value="location">Localização</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Sort Order */}
            <div className="space-y-2">
              <Label>Ordem</Label>
              <div className="flex gap-2">
                <Button
                  variant={localFilters.sortOrder === 'asc' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleFilterChange('sortOrder', 'asc')}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <TrendingUp className="h-4 w-4 mr-1" />
                  Crescente
                </Button>
                <Button
                  variant={localFilters.sortOrder === 'desc' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleFilterChange('sortOrder', 'desc')}
                  disabled={isLoading}
                  className="flex-1"
                >
                  <TrendingDown className="h-4 w-4 mr-1" />
                  Decrescente
                </Button>
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Actions */}
        <div className="flex gap-2 pt-2 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={onClearFilters}
            disabled={isLoading || activeFiltersCount === 0}
            className="flex-1"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            Limpar Filtros
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
