# Configurações do Servidor
NODE_ENV=development
PORT=3000
HOST=127.0.0.1

# Configurações do Banco de Dados PostgreSQL
DB_HOST=127.0.0.1
DB_PORT=5432
DB_NAME=magnow
DB_USER=postgres
DB_PASSWORD=postgres123
DB_SSL=false

# Configurações do Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Configurações JWT
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_refresh_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# Configurações de Criptografia
ENCRYPTION_KEY=EgX/8Bzri4vASt/UXOPZRcIXyIF0Ad7S

# Configurações da API do Mercado Livre
MERCADO_LIVRE_CLIENT_ID=1004389723920274
MERCADO_LIVRE_CLIENT_SECRET=5oVktPfoZ1lk5bPYdyE27ZmV9sngExkQ
MERCADO_LIVRE_REDIRECT_URI=http://localhost:3000/api/mercadolivre/auth/callback
MERCADO_LIVRE_API_URL=https://api.mercadolivre.com
MERCADO_LIVRE_AUTH_URL=https://auth.mercadolivre.com.br
MERCADO_LIVRE_TOKEN_URL=https://api.mercadolivre.com/oauth/token

# Configurações de Email (opcional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your_email_password

# Configurações de Logs
LOG_LEVEL=info
LOG_FILE=logs/app.log

# Configurações de Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Configurações de Cache
CACHE_TTL=3600
CACHE_PREFIX=magnow:

# Configurações de Upload
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,application/pdf,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

# Configurações de Monitoramento
HEALTH_CHECK_INTERVAL=30000
METRICS_ENABLED=true 

# URL de conexão do Prisma
DATABASE_URL="postgresql://postgres:postgres123@127.0.0.1:5432/magnow"