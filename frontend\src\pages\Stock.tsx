import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import StockTable from '../components/stock/StockTable';
import StockFiltersML from '../components/stock/StockFiltersML';
import StockGrid from '../components/stock/StockGrid';
import StockStatisticsCards from '../components/stock/StockStatisticsCards';
import StockAdjustmentModal from '../components/stock/StockAdjustmentModal';
import { StockAlertSettings } from '../components/stock/StockAlertSettings';
import StockSyncManager from '../components/stock/StockSyncManager';
import StockMetricsDashboard from '../components/stock/StockMetricsDashboard';
import StockAlertsPanel from '../components/stock/StockAlertsPanel';
import StockConfigurationPanel from '../components/stock/StockConfigurationPanel';
import AlertConfigurationPanel from '../components/stock/AlertConfigurationPanel';
import { Button } from '../components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Package, Bell, BarChart3, Settings, RefreshCw, Grid3X3, List, Truck } from 'lucide-react';
import type { StockItem } from '../types/api';
import { useStockStore } from '../store/stockStore';
// MOCK DATA FOR DEVELOPMENT - Comment out for production
// import { useMercadoLivreStore } from '../store/mercadoLivreStore';
// Mock data for development
import { mockMLProducts } from '../mocks/mlProductsMock';

export default function Stock() {
  const navigate = useNavigate();

  // Store state
  const {
    filteredItems,
    statistics,
    stockItemsLoading: isLoading,
    filters: storeFilters,
    pagination,
    error,
    loadStockItems,
    loadStatistics,
    loadProductsWithStock,
    loadStockCalculations,
    loadStockAlerts,
    setFilters,
    setPage,
    updateStockItem,
    deleteStockItem,
    clearError,
  } = useStockStore();

  // MOCK DATA FOR DEVELOPMENT - ML Store state
  // const {
  //   selectedAccount,
  //   products: mlProducts,
  // } = useMercadoLivreStore();

  const mockSelectedAccount = {
    id: 'mock-account-001',
    nickname: 'Loja Demo',
    email: '<EMAIL>',
  };
  const mlProducts = mockMLProducts;

  // Local state for modals and view mode
  const [isAdjustmentModalOpen, setIsAdjustmentModalOpen] = useState(false);
  const [adjustingStockItem, setAdjustingStockItem] = useState<StockItem | undefined>(undefined);
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'table'>('table');

  // MOCK DATA FOR DEVELOPMENT - Comment out for production
  // Load initial data
  useEffect(() => {
    const loadAllData = async () => {
      // MOCK: Simulate loading delay
      setTimeout(async () => {
        await Promise.all([
          loadStockItems(),
          loadStatistics(),
          loadProductsWithStock(),
          loadStockCalculations(),
          loadStockAlerts(),
        ]);
      }, 500);
    };

    loadAllData();
  }, [loadStockItems, loadStatistics, loadProductsWithStock, loadStockCalculations, loadStockAlerts]);



  // Handle sort changes
  const handleSort = useCallback((column: keyof StockItem) => {
    // Toggle sort direction
    const currentSortBy = storeFilters.sortBy;
    const currentSortOrder = storeFilters.sortOrder;

    let newSortOrder: 'asc' | 'desc' = 'asc';
    if (currentSortBy === column && currentSortOrder === 'asc') {
      newSortOrder = 'desc';
    }

    setFilters({
      sortBy: column as any, // Type assertion needed due to store interface limitation
      sortOrder: newSortOrder,
    });
  }, [setFilters, storeFilters.sortBy, storeFilters.sortOrder]);

  // Handle pagination
  const handlePageChange = useCallback((page: number) => {
    setPage(page);
  }, [setPage]);

  // Handle stock adjustment
  const handleAdjustStock = useCallback((item: StockItem) => {
    setAdjustingStockItem(item);
    setIsAdjustmentModalOpen(true);
  }, []);

  const handleSaveAdjustment = useCallback(async (adjustedItem: StockItem) => {
    try {
      if (adjustedItem.id) {
        await updateStockItem(adjustedItem.id, {
          currentQuantity: adjustedItem.currentQuantity,
          location: adjustedItem.location
        });
      }
      setIsAdjustmentModalOpen(false);
      setAdjustingStockItem(undefined);
    } catch (err) {
      console.error('Erro ao ajustar estoque:', err);
    }
  }, [updateStockItem]);

  const handleDeleteStock = useCallback(async (itemId: string) => {
    try {
      await deleteStockItem(itemId);
    } catch (err) {
      console.error('Erro ao excluir item de estoque:', err);
    }
  }, [deleteStockItem]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Controle de Estoque</h1>
          <p className="mt-1 text-sm text-muted-foreground">
            Gerencie seus produtos, alertas e configurações de estoque
          </p>
        </div>

        {/* ML Full Button */}
        {mockSelectedAccount && mlProducts.length > 0 && (
          <Button
            onClick={() => navigate('/ml-full-wizard')}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Truck className="h-4 w-4" />
            Criar Envio Full
          </Button>
        )}
      </div>
      
      <Tabs defaultValue="inventory" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="inventory" className="flex items-center space-x-2">
            <Package className="h-4 w-4" />
            <span>Inventário</span>
          </TabsTrigger>
          <TabsTrigger value="sync" className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4" />
            <span>Sincronização</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center space-x-2">
            <Settings className="h-4 w-4" />
            <span>Configurações</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="inventory" className="space-y-6">
          {/* Statistics Cards */}
          {statistics && (
            <StockStatisticsCards
              statistics={statistics}
              isLoading={isLoading}
              className="mb-6"
            />
          )}

          {/* Filters and View Toggle */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <StockFiltersML
                filters={storeFilters}
                onFiltersChange={setFilters}
                onClearFilters={() => setFilters({})}
                isLoading={isLoading}
                itemCount={filteredItems.length}
                locations={statistics?.locations || []}
              />
            </div>

            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Visualização:</span>
                <div className="flex items-center border rounded-md">
                  <Button
                    variant={viewMode === 'table' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('table')}
                    className="rounded-r-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <Package className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {error && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md">
              {error}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="ml-2"
              >
                Fechar
              </Button>
            </div>
          )}

          {/* Content based on view mode */}
          {viewMode === 'table' ? (
            <StockTable
              stockItems={filteredItems}
              onAdjust={handleAdjustStock}
              onDelete={handleDeleteStock}
              loading={isLoading}
              currentPage={pagination.page}
              totalPages={pagination.totalPages}
              onPageChange={handlePageChange}
              sortConfig={undefined}
              onSort={handleSort}
            />
          ) : (
            <StockGrid
              stockItems={filteredItems}
              isLoading={isLoading}
              pagination={pagination}
              onPageChange={handlePageChange}
              onLimitChange={(limit) => setFilters({ limit })}
              onUpdateStock={async (itemId, quantity) => {
                const item = filteredItems.find(i => i.id === itemId);
                if (item) {
                  await updateStockItem(itemId, { currentQuantity: quantity });
                }
              }}
              onViewDetails={(item) => {
                setAdjustingStockItem(item);
                setIsAdjustmentModalOpen(true);
              }}
              onDeleteItem={deleteStockItem}
              onRefreshItem={async (itemId) => {
                // Refresh individual item logic - implemented
                try {
                  // TODO: Replace with specific item refresh API call
                  // await stockService.refreshStockItem(itemId);

                  // For now, refresh all items
                  await loadStockItems();
                  await loadStatistics();
                } catch (error) {
                  console.error('Erro ao atualizar item:', error);
                }
              }}
              viewMode={viewMode === 'grid' ? 'grid' : 'list'}
              onViewModeChange={(mode) => setViewMode(mode)}
              onRefresh={loadStockItems}
            />
          )}
        </TabsContent>

        <TabsContent value="sync" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <StockSyncManager />
            <StockAlertsPanel />
          </div>
        </TabsContent>



        <TabsContent value="analytics" className="space-y-6">
          <StockMetricsDashboard />
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <StockConfigurationPanel />
            <AlertConfigurationPanel />
          </div>

          {/* Additional Settings Row */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <StockAlertSettings />
            <div className="space-y-4">
              {/* Location Management - Placeholder for future implementation */}
              <div className="p-6 border-2 border-dashed border-muted rounded-lg text-center text-muted-foreground">
                <Settings className="h-8 w-8 mx-auto mb-3" />
                <h3 className="font-semibold mb-2">Gerenciamento de Localizações</h3>
                <p className="text-sm">Em desenvolvimento...</p>
                <p className="text-xs mt-1">CRUD de depósitos e localizações</p>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      <StockAdjustmentModal
        isOpen={isAdjustmentModalOpen}
        onClose={() => setIsAdjustmentModalOpen(false)}
        onSubmit={handleSaveAdjustment}
        initialData={adjustingStockItem}
      />
    </div>
  );
}