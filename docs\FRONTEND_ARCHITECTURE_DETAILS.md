# Documentação da Arquitetura do Frontend - Magnow

## Introdução

Este documento detalha a arquitetura e os componentes do frontend da aplicação **Magnow**, um sistema inteligente de controle de estoque para vendedores do Mercado Livre. Para uma compreensão completa do produto e sua implementação, recomenda-se a leitura dos seguintes documentos relacionados:

- **[BUSINESS_REQUIREMENTS.md](./BUSINESS_REQUIREMENTS.md)**: Especificação completa do produto, requisitos de negócio e visão estratégica
- **[ARCHITECTURE_DETAILS.md](./ARCHITECTURE_DETAILS.md)**: Arquitetura técnica completa do sistema backend
- **[USER_GUIDE.md](./USER_GUIDE.md)**: Manual do usuário com instruções de uso da plataforma

## 1. Visão Geral do Frontend

O frontend do Magnow é uma Single Page Application (SPA) desenvolvida com React e TypeScript, utilizando Vite como ferramenta de build. Ele oferece uma interface de usuário rica e interativa para gerenciar produtos, estoque, relatórios e outras funcionalidades do sistema. A aplicação é modular, com componentes reutilizáveis e um fluxo de dados claro, visando facilitar a manutenção e o desenvolvimento de novas funcionalidades.

## 2. Tecnologias Utilizadas

O frontend do Magnow é construído com as seguintes tecnologias:

*   **Framework:** React (com Hooks)
*   **Linguagem:** TypeScript
*   **Build Tool:** Vite
*   **Gerenciamento de Estado:** Zustand
*   **Roteamento:** React Router DOM
*   **Estilização:** Tailwind CSS, PostCSS
*   **Componentes UI:** Radix UI, Headless UI, Heroicons, Lucide React
*   **Requisições HTTP:** Axios
*   **Validação de Formulários:** React Hook Form, Zod
*   **Gráficos:** Recharts
*   **Notificações/Toasts:** React Hot Toast, Sonner
*   **Testes:** Vitest, React Testing Library, Jest DOM
*   **Storybook:** Para desenvolvimento e documentação de componentes
*   **Linting:** ESLint

## 3. Estrutura de Pastas e Arquivos

A estrutura de pastas do frontend é organizada para promover a modularidade e a separação de responsabilidades:

*   `public/`: Contém assets estáticos que são servidos diretamente.
*   `src/`: Contém todo o código-fonte da aplicação.
    *   `src/main.tsx`: Ponto de entrada da aplicação, onde o React é inicializado e o componente `App` é renderizado.
    *   `src/App.tsx`: Componente principal que define as rotas da aplicação usando `react-router-dom` e gerencia o layout geral.
    *   `src/assets/`: Ativos estáticos como imagens e ícones.
    *   `src/components/`: Componentes reutilizáveis da UI.
        *   `src/components/layout/`: Componentes de layout (e.g., `Layout.tsx`, `AuthLayout.tsx`).
        *   `src/components/ui/`: Componentes de UI genéricos baseados em Radix UI e Tailwind CSS (e.g., `Button.tsx`, `Input.tsx`, `Modal.tsx`).
        *   `src/components/dashboard/`, `src/components/products/`, `src/components/spreadsheets/`, `src/components/stock/`: Componentes específicos de cada módulo/página.
    *   `src/hooks/`: Hooks personalizados para lógica reutilizável.
    *   `src/pages/`: Componentes que representam as diferentes páginas da aplicação (e.g., `Dashboard.tsx`, `Login.tsx`, `Products.tsx`).
    *   `src/services/`: Módulos para interação com a API de backend (e.g., `api.ts`, `productService.ts`).
    *   `src/store/`: Gerenciamento de estado global usando Zustand (e.g., `authStore.ts`, `dashboardStore.ts`).
    *   `src/styles/`: Arquivos de estilo globais e tokens de design (e.g., `index.css`, `App.css`, `design-tokens.css`).
    *   `src/types/`: Definições de tipos TypeScript para a aplicação.
    *   `src/utils/`: Funções utilitárias diversas.
    *   `src/__tests__/`: Testes unitários e de integração para componentes e páginas.
    *   `src/stories/`: Arquivos Storybook para documentação e desenvolvimento isolado de componentes.
*   `.storybook/`: Configurações do Storybook.
*   `tailwind.config.js`, `postcss.config.js`: Configurações do Tailwind CSS e PostCSS.
*   `vite.config.ts`: Configurações do Vite.
*   `tsconfig.json`, `tsconfig.app.json`, `tsconfig.node.json`: Configurações do TypeScript.
*   `eslint.config.js`: Configurações do ESLint.
*   `vitest.shims.d.ts`, `vitest.setup.ts`: Configurações do Vitest para testes.

## 4. Fluxo de Dados e Interações

### 4.1. Autenticação

1.  O usuário acessa a página de login (`/login`).
2.  O componente `Login` (dentro de `src/pages/Login.tsx`) coleta as credenciais.
3.  Uma requisição é enviada ao backend via `authService` (no backend) ou diretamente via `api.ts` (no frontend).
4.  Em caso de sucesso, o token JWT e as informações do usuário são armazenados no `authStore` (Zustand).
5.  O usuário é redirecionado para o `Dashboard` (`/dashboard`).
6.  As rotas protegidas são controladas pelo `Layout` e `AuthLayout`, que verificam a presença do token de autenticação.

### 4.2. Interação com a API (Exemplo: Produtos)

1.  O usuário navega para a página de Produtos (`/products`).
2.  O componente `Products` (dentro de `src/pages/Products.tsx`) ou um componente filho (e.g., `ProductTable` em `src/components/products/ProductTable.tsx`) faz uma chamada para `productService.ts` (dentro de `src/services/`).
3.  `productService.ts` utiliza a instância do Axios configurada em `api.ts` para fazer requisições HTTP ao backend.
4.  Os dados retornados são processados e exibidos na interface do usuário.
5.  Qualquer erro de API é capturado e tratado, geralmente exibindo uma notificação ao usuário via `react-hot-toast` ou `sonner`.

## 5. Gerenciamento de Estado

O frontend utiliza **Zustand** para gerenciamento de estado global. Os principais stores incluem:

*   `authStore.ts`: Gerencia o estado de autenticação do usuário (token, informações do usuário, status de login).
*   `dashboardStore.ts`: Pode gerenciar estados relacionados ao dashboard, como filtros aplicados ou dados de gráficos.

Para estados locais de componentes, são utilizados os Hooks `useState` e `useReducer` do React. Para estados de formulário, `react-hook-form` é a solução preferencial.

## 6. Componentes UI e Design System

O projeto adota uma abordagem de Design System, com componentes de UI reutilizáveis localizados em `src/components/ui/`. Estes componentes são construídos com **Radix UI** (para acessibilidade e comportamento) e estilizados com **Tailwind CSS** (para flexibilidade e rapidez no desenvolvimento). O **Storybook** é utilizado para documentar e desenvolver esses componentes isoladamente, garantindo consistência e reusabilidade.

## 7. Testes

Os testes do frontend são escritos com **Vitest** e **React Testing Library**, permitindo testes unitários e de integração para componentes e funcionalidades. Os arquivos de teste estão localizados na pasta `src/__tests__/` e seguem a convenção de nomenclatura `*.test.tsx`.
## 8. Fluxo de Desenvolvimento

### 8.1. Instalação e Execução

Para configurar e executar o frontend localmente:

1.  **Instalar Dependências:** Navegue até a pasta `frontend` e execute:
    ```bash
    npm install
    # ou yarn install
    ```
2.  **Variáveis de Ambiente:** Crie um arquivo `.env.local` na raiz da pasta `frontend` com as variáveis de ambiente necessárias, como a URL da API de backend. Exemplo:
    ```
    VITE_API_BASE_URL=http://localhost:3000/api
    ```
3.  **Executar em Modo de Desenvolvimento:**
    ```bash
    npm run dev
    # ou yarn dev
    ```
    Isso iniciará o servidor de desenvolvimento do Vite, geralmente em `http://localhost:5173`.

### 8.2. Testes

Para executar os testes unitários e de integração:

```bash
npm test
# ou yarn test
```

Os testes são configurados com Vitest e React Testing Library. É recomendado escrever testes para novos componentes e funcionalidades para garantir a qualidade do código.

### 8.3. Linting

Para verificar e corrigir problemas de estilo e qualidade de código:

```bash
npm run lint
# ou yarn lint
```

### 8.4. Storybook

Para visualizar e desenvolver componentes isoladamente usando Storybook:

```bash
npm run storybook
# ou yarn storybook
```

Isso iniciará a interface do Storybook, permitindo a exploração interativa dos componentes da UI.

## 9. Considerações de Performance e Otimização

*   **Code Splitting:** O Vite já realiza code splitting por padrão, mas pode ser otimizado para carregamento sob demanda de rotas e componentes.
*   **Otimização de Imagens:** Utilizar formatos de imagem modernos e otimizados.
*   **Virtualização de Listas:** Para listas grandes, usar bibliotecas de virtualização para renderizar apenas os itens visíveis.
*   **Memoização:** Utilizar `React.memo`, `useMemo` e `useCallback` para evitar re-renderizações desnecessárias de componentes e funções.

## 10. Próximos Passos e Melhorias

*   **Documentação de Componentes:** Expandir a documentação de componentes no Storybook com exemplos de uso e propriedades.
*   **Testes E2E:** Implementar testes end-to-end (E2E) com ferramentas como Playwright ou Cypress para validar fluxos de usuário completos.
*   **Acessibilidade:** Realizar auditorias de acessibilidade e garantir que todos os componentes sigam as diretrizes WCAG.
*   **Internacionalização (i18n):** Adicionar suporte a múltiplos idiomas, se necessário.
*   **Otimização de Build:** Explorar otimizações adicionais no processo de build do Vite para reduzir o tamanho final do bundle e melhorar o tempo de carregamento.

Este documento serve como um guia para entender a arquitetura do frontend do Magnow e auxiliar no desenvolvimento e manutenção contínuos.