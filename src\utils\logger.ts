import * as winston from 'winston';
const DailyRotateFile = require('winston-daily-rotate-file');
import * as path from 'path';
import * as fs from 'fs';

// Criar diretório de logs se não existir
const logDir = 'logs';
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}

// Definir níveis de log customizados
const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
  audit: 5,
};

// Definir cores para cada nível
const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
  audit: 'cyan',
};

// Adicionar cores ao winston
winston.addColors(colors);

// Formato para console (desenvolvimento)
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf(
    (info) => `${info.timestamp} [${info.level}]: ${info.message}`,
  ),
);

// Formato para arquivos (produção)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Configuração de rotação de arquivos
const rotateFileConfig = {
  datePattern: 'YYYY-MM-DD',
  zippedArchive: true,
  maxSize: '20m',
  maxFiles: '14d', // Manter logs por 14 dias
  auditFile: path.join(logDir, 'audit.json'),
};

// Transports
const transports: winston.transport[] = [
  // Console transport (sempre ativo)
  new winston.transports.Console({
    format: process.env.NODE_ENV === 'production' 
      ? winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        )
      : consoleFormat,
    level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  }),
];

// Adicionar transports de arquivo apenas se não estivermos em teste
if (process.env.NODE_ENV !== 'test') {
  // Transport para logs combinados com rotação
  transports.push(new DailyRotateFile({
    ...rotateFileConfig,
    filename: path.join(logDir, 'app-%DATE%.log'),
    format: fileFormat,
    level: 'info',
  }));

  // Transport para logs de erro com rotação
  transports.push(new DailyRotateFile({
    ...rotateFileConfig,
    filename: path.join(logDir, 'error-%DATE%.log'),
    format: fileFormat,
    level: 'error',
  }));

  // Transport para logs de auditoria com rotação
  transports.push(new DailyRotateFile({
    ...rotateFileConfig,
    filename: path.join(logDir, 'audit-%DATE%.log'),
    format: fileFormat,
    level: 'audit',
    maxFiles: '30d', // Auditoria mantida por 30 dias
  }));

  // Transport para logs HTTP com rotação
  transports.push(new DailyRotateFile({
    ...rotateFileConfig,
    filename: path.join(logDir, 'http-%DATE%.log'),
    format: fileFormat,
    level: 'http',
  }));
}

// Criar logger principal
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  levels,
  transports,
  exitOnError: false,
  // Capturar exceções não tratadas
  handleExceptions: true,
  handleRejections: true,
});

// Event listeners para rotação de arquivos
if (process.env.NODE_ENV !== 'test') {
  transports.forEach(transport => {
    if (transport instanceof DailyRotateFile) {
      transport.on('rotate', (oldFilename, newFilename) => {
        logger.info('Log file rotated', { oldFilename, newFilename });
      });

      transport.on('archive', (zipFilename) => {
        logger.info('Log file archived', { zipFilename });
      });

      transport.on('logRemoved', (removedFilename) => {
        logger.info('Log file removed', { removedFilename });
      });
    }
  });
}

// Interfaces para tipagem
interface RequestLogData {
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  ip: string | undefined;
  userAgent?: string;
  userId?: string;
  tenantId?: string;
}

interface UserActivityData {
  userId: string;
  action: string;
  resource?: string;
  details?: any;
  tenantId?: string;
  ip?: string | undefined;
  userAgent?: string | undefined;
}

interface ApiActivityData {
  action: string;
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  requestId?: string;
  userId?: string;
  tenantId?: string;
  errorDetails?: any;
}

interface ErrorLogData {
  error: Error;
  context?: string;
  userId?: string;
  tenantId?: string;
  requestId?: string;
  additionalData?: any;
}

// Função para logging de requisições HTTP
export const logRequest = (data: RequestLogData) => {
  const message = `${data.method} ${data.url} - ${data.statusCode} - ${data.responseTime}ms - ${data.ip || 'unknown'}`;
  
  const logData = {
    method: data.method,
    url: data.url,
    statusCode: data.statusCode,
    responseTime: data.responseTime,
    ip: data.ip || 'unknown',
    userAgent: data.userAgent,
    userId: data.userId,
    tenantId: data.tenantId,
    timestamp: new Date().toISOString(),
  };

  if (data.statusCode >= 500) {
    logger.error(message, logData);
  } else if (data.statusCode >= 400) {
    logger.warn(message, logData);
  } else {
    logger.http(message, logData);
  }
};

// Função para logging de erros com contexto completo
export const logError = (data: ErrorLogData) => {
  const message = data.context 
    ? `[${data.context}] ${data.error.message}` 
    : data.error.message;
    
  logger.error(message, {
    error: {
      name: data.error.name,
      message: data.error.message,
      stack: data.error.stack,
    },
    context: data.context,
    userId: data.userId,
    tenantId: data.tenantId,
    requestId: data.requestId,
    additionalData: data.additionalData,
    timestamp: new Date().toISOString(),
  });
};

// Função para logging de atividades do usuário (auditoria)
export const logUserActivity = (data: UserActivityData) => {
  const message = `User ${data.userId} performed ${data.action}`;
  
  logger.log('audit', message, {
    userId: data.userId,
    action: data.action,
    resource: data.resource,
    details: data.details,
    tenantId: data.tenantId,
    ip: data.ip,
    userAgent: data.userAgent,
    timestamp: new Date().toISOString(),
  });
};

// Função para logging de atividades da API do Mercado Livre
export const logMercadoLivreActivity = (data: ApiActivityData) => {
  const message = `ML API ${data.method} ${data.endpoint} - ${data.statusCode} - ${data.responseTime}ms`;
  
  const logData = {
    action: data.action,
    endpoint: data.endpoint,
    method: data.method,
    statusCode: data.statusCode,
    responseTime: data.responseTime,
    requestId: data.requestId,
    userId: data.userId,
    tenantId: data.tenantId,
    errorDetails: data.errorDetails,
    timestamp: new Date().toISOString(),
  };

  if (data.statusCode >= 400) {
    logger.error(message, logData);
  } else {
    logger.info(message, logData);
  }
};

// Função para logging de operações de cache
export const logCacheActivity = (
  operation: 'GET' | 'SET' | 'DEL' | 'FLUSH',
  key: string,
  hit?: boolean,
  ttl?: number,
  userId?: string,
  tenantId?: string
) => {
  logger.debug('Cache Activity', {
    operation,
    key,
    hit,
    ttl,
    userId,
    tenantId,
    timestamp: new Date().toISOString(),
  });
};

// Função para logging de performance
export const logPerformance = (
  operation: string,
  duration: number,
  details?: any,
  userId?: string,
  tenantId?: string
) => {
  const message = `Performance: ${operation} took ${duration}ms`;
  
  if (duration > 5000) { // > 5 segundos
    logger.warn(message, { operation, duration, details, userId, tenantId, timestamp: new Date().toISOString() });
  } else if (duration > 2000) { // > 2 segundos
    logger.info(message, { operation, duration, details, userId, tenantId, timestamp: new Date().toISOString() });
  } else {
    logger.debug(message, { operation, duration, details, userId, tenantId, timestamp: new Date().toISOString() });
  }
};

// Função para logging de eventos de segurança
export const logSecurityEvent = (
  event: string,
  level: 'info' | 'warn' | 'error',
  details: any,
  userId?: string,
  tenantId?: string,
  ip?: string
) => {
  const message = `Security Event: ${event}`;
  
  logger.log(level, message, {
    event,
    details,
    userId,
    tenantId,
    ip,
    timestamp: new Date().toISOString(),
  });
};

// Função para criar child logger com contexto
export const createChildLogger = (context: string, metadata?: any) => {
  return logger.child({ context, ...metadata });
};

// Função para logging estruturado com contexto
export const logWithContext = (
  level: string,
  message: string,
  context: string,
  metadata?: any
) => {
  logger.log(level, `[${context}] ${message}`, {
    context,
    ...metadata,
    timestamp: new Date().toISOString(),
  });
};

// === SISTEMA DE LOGS ESPECÍFICOS PARA MERCADO LIVRE ===

// Interfaces específicas para ML
interface MLOAuthData {
  action: 'GENERATE_URL' | 'EXCHANGE_CODE' | 'REFRESH_TOKEN' | 'REVOKE_TOKEN';
  clientId?: string;
  userId?: string | undefined;
  tenantId?: string | undefined;
  success: boolean;
  error?: string;
  tokenType?: 'access' | 'refresh';
  expiresIn?: number;
  scope?: string[];
  ip?: string;
}

interface MLRateLimitData {
  endpoint: string;
  remaining: number;
  limit: number;
  reset: number;
  resetDate?: Date;
  userId?: string | undefined;
  tenantId?: string | undefined;
  isNearLimit?: boolean;
  percentUsed?: number;
}

interface MLSyncData {
  operation: 'SYNC_ITEMS' | 'SYNC_ORDERS' | 'SYNC_STOCK' | 'SYNC_USER_DATA';
  totalItems: number;
  syncedItems: number;
  failedItems: number;
  duration: number;
  userId?: string | undefined;
  tenantId?: string | undefined;
  errors?: any[] | undefined;
  lastSyncId?: string;
}

interface MLApiPerformanceData {
  endpoint: string;
  method: string;
  responseTime: number;
  retryCount?: number;
  cacheHit?: boolean;
  dataSize?: number;
  userId?: string | undefined;
  tenantId?: string | undefined;
  requestId?: string;
}

interface MLQuotaData {
  quotaType: 'API_CALLS' | 'LISTINGS' | 'QUESTIONS' | 'ORDERS';
  used: number;
  limit: number;
  resetPeriod: string;
  remainingPercent: number;
  userId?: string | undefined;
  tenantId?: string | undefined;
  isNearQuota?: boolean;
}

// Função para logging de operações OAuth do ML
export const logMLOAuth = (data: MLOAuthData) => {
  const message = `ML OAuth ${data.action} - ${data.success ? 'SUCCESS' : 'FAILED'}`;
  
  const logData = {
    action: data.action,
    clientId: data.clientId ? data.clientId.substring(0, 8) + '...' : undefined, // Mascarar client ID
    userId: data.userId,
    tenantId: data.tenantId,
    success: data.success,
    error: data.error,
    tokenType: data.tokenType,
    expiresIn: data.expiresIn,
    scope: data.scope,
    ip: data.ip,
    timestamp: new Date().toISOString(),
  };

  if (!data.success) {
    logger.error(message, logData);
  } else {
    logger.info(message, logData);
  }
};

// Função para logging de rate limiting do ML
export const logMLRateLimit = (data: MLRateLimitData) => {
  const percentUsed = Math.round((1 - data.remaining / data.limit) * 100);
  const isNearLimit = percentUsed >= 80;
  
  const message = `ML Rate Limit - ${data.endpoint} - ${data.remaining}/${data.limit} (${percentUsed}% used)`;
  
  const logData = {
    endpoint: data.endpoint,
    remaining: data.remaining,
    limit: data.limit,
    reset: data.reset,
    resetDate: data.resetDate || new Date(data.reset * 1000),
    userId: data.userId,
    tenantId: data.tenantId,
    percentUsed,
    isNearLimit,
    timestamp: new Date().toISOString(),
  };

  if (isNearLimit) {
    logger.warn(message, logData);
  } else {
    logger.debug(message, logData);
  }
};

// Função para logging de sincronização do ML
export const logMLSync = (data: MLSyncData) => {
  const successRate = Math.round((data.syncedItems / data.totalItems) * 100);
  const message = `ML Sync ${data.operation} - ${data.syncedItems}/${data.totalItems} (${successRate}%) - ${data.duration}ms`;
  
  const logData = {
    operation: data.operation,
    totalItems: data.totalItems,
    syncedItems: data.syncedItems,
    failedItems: data.failedItems,
    successRate,
    duration: data.duration,
    userId: data.userId,
    tenantId: data.tenantId,
    errors: data.errors,
    lastSyncId: data.lastSyncId,
    timestamp: new Date().toISOString(),
  };

  if (data.failedItems > 0) {
    logger.warn(message, logData);
  } else {
    logger.info(message, logData);
  }
};

// Função para logging de performance da API do ML
export const logMLApiPerformance = (data: MLApiPerformanceData) => {
  const message = `ML API Performance - ${data.method} ${data.endpoint} - ${data.responseTime}ms`;
  
  const logData = {
    endpoint: data.endpoint,
    method: data.method,
    responseTime: data.responseTime,
    retryCount: data.retryCount,
    cacheHit: data.cacheHit,
    dataSize: data.dataSize,
    userId: data.userId,
    tenantId: data.tenantId,
    requestId: data.requestId,
    timestamp: new Date().toISOString(),
  };

  // Log baseado na performance
  if (data.responseTime > 10000) { // > 10 segundos
    logger.error(message, logData);
  } else if (data.responseTime > 5000) { // > 5 segundos
    logger.warn(message, logData);
  } else if (data.responseTime > 2000) { // > 2 segundos
    logger.info(message, logData);
  } else {
    logger.debug(message, logData);
  }
};

// Função para logging de quotas da API do ML
export const logMLQuota = (data: MLQuotaData) => {
  const message = `ML Quota ${data.quotaType} - ${data.used}/${data.limit} (${data.remainingPercent}% remaining)`;
  
  const logData = {
    quotaType: data.quotaType,
    used: data.used,
    limit: data.limit,
    resetPeriod: data.resetPeriod,
    remainingPercent: data.remainingPercent,
    userId: data.userId,
    tenantId: data.tenantId,
    isNearQuota: data.remainingPercent <= 20, // Alerta quando restam menos de 20%
    timestamp: new Date().toISOString(),
  };

  if (data.remainingPercent <= 10) {
    logger.error(message, logData);
  } else if (data.remainingPercent <= 20) {
    logger.warn(message, logData);
  } else {
    logger.debug(message, logData);
  }
};

// Função para logging de erros específicos da API do ML
export const logMLError = (
  endpoint: string,
  method: string,
  statusCode: number,
  error: any,
  retryCount?: number,
  userId?: string,
  tenantId?: string
) => {
  const message = `ML API Error - ${method} ${endpoint} - ${statusCode}`;
  
  const logData = {
    endpoint,
    method,
    statusCode,
    error: {
      message: error.message || 'Unknown error',
      code: error.code || error.error?.code,
      details: error.response?.data || error.details,
    },
    retryCount,
    userId,
    tenantId,
    timestamp: new Date().toISOString(),
  };

  // Classificar erro baseado no status code
  if (statusCode === 429) {
    logger.warn(`${message} - Rate Limit Exceeded`, logData);
  } else if (statusCode === 401 || statusCode === 403) {
    logger.error(`${message} - Authentication Error`, logData);
  } else if (statusCode >= 500) {
    logger.error(`${message} - Server Error`, logData);
  } else if (statusCode >= 400) {
    logger.warn(`${message} - Client Error`, logData);
  } else {
    logger.error(message, logData);
  }
};

// Função para logging de tentativas de retry na API do ML
export const logMLRetry = (
  endpoint: string,
  method: string,
  attempt: number,
  maxAttempts: number,
  delay: number,
  reason: string,
  userId?: string,
  tenantId?: string
) => {
  const message = `ML API Retry - ${method} ${endpoint} - Attempt ${attempt}/${maxAttempts} - Delay: ${delay}ms`;
  
  const logData = {
    endpoint,
    method,
    attempt,
    maxAttempts,
    delay,
    reason,
    userId,
    tenantId,
    timestamp: new Date().toISOString(),
  };

  if (attempt === maxAttempts) {
    logger.error(`${message} - Final Attempt`, logData);
  } else {
    logger.warn(message, logData);
  }
};

export { logger };
export default logger; 