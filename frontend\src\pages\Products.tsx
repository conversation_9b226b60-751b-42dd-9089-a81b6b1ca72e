import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import MercadoLivreConnect from '../components/MercadoLivreConnect';
import ProductFiltersML from '../components/products/ProductFiltersML';
import ProductGrid from '../components/products/ProductGrid';
import ProductDetailsModal from '../components/products/ProductDetailsModal';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { RefreshCw, ShoppingBag, AlertCircle, Package, Truck } from 'lucide-react';
import { useMercadoLivreStore } from '../store/mercadoLivreStore';
import type { ProductWithStock } from '../types/api';
import type { MLProductFilters } from '../types/api';
// Mock data for development
import { mockMLProducts } from '../mocks/mlProductsMock';

export default function Products() {
  const navigate = useNavigate();

  // COMMENTED FOR MOCK DEVELOPMENT - Real ML Store integration
  const {
    accounts,
    selectedAccount,
    // products: mlProducts,
    productsLoading,
    syncStatus,
    selectAccount,
    loadProductsWithStock,
    updateProductStock,
    syncProducts,
    syncSingleProduct
  } = useMercadoLivreStore();

  // MOCK DATA FOR DEVELOPMENT
  const mlProducts = mockMLProducts;
  const mockSelectedAccount = {
    id: 'mock-account-001',
    userId: 'mock-user-001',
    nickname: 'Loja Demo',
    email: '<EMAIL>',
    isActive: true,
    expiresAt: '2024-12-31T23:59:59Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-18T12:00:00Z',
  };

  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<MLProductFilters>({});
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filteredProducts, setFilteredProducts] = useState<ProductWithStock[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<ProductWithStock | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  // COMMENTED FOR MOCK DEVELOPMENT - Real product loading
  // useEffect(() => {
  //   if (selectedAccount) {
  //     loadProductsWithStock(selectedAccount.id).catch(err => {
  //       console.error('Erro ao carregar produtos:', err);
  //       setError('Erro ao carregar produtos. Tente novamente.');
  //     });
  //   }
  // }, [selectedAccount, loadProductsWithStock]);

  // MOCK: Initialize with mock products
  useEffect(() => {
    setFilteredProducts(mlProducts);
  }, []);

  // Aplicar filtros aos produtos
  useEffect(() => {
    let filtered = [...mlProducts];

    // Filtro de busca
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(product =>
        product.title.toLowerCase().includes(searchLower) ||
        product.sku?.toLowerCase().includes(searchLower) ||
        product.mlId.toLowerCase().includes(searchLower)
      );
    }

    // Filtro de status
    if (filters.status?.length) {
      filtered = filtered.filter(product =>
        filters.status!.includes(product.status as any)
      );
    }

    // Filtro de gap
    if (filters.hasGap) {
      filtered = filtered.filter(product =>
        product.stockCalculation?.gap && product.stockCalculation.gap > 0
      );
    }

    // Filtro de alertas
    if (filters.hasStockAlert) {
      filtered = filtered.filter(product =>
        product.alerts && product.alerts.length > 0
      );
    }

    // Filtro de sincronização
    if (filters.isOutOfSync) {
      filtered = filtered.filter(product =>
        product.syncStatus?.isOutOfSync
      );
    }

    // Filtros de preço
    if (filters.minPrice) {
      filtered = filtered.filter(product => product.price >= filters.minPrice!);
    }
    if (filters.maxPrice) {
      filtered = filtered.filter(product => product.price <= filters.maxPrice!);
    }

    // Filtros de estoque
    if (filters.minStock) {
      filtered = filtered.filter(product => product.availableQuantity >= filters.minStock!);
    }
    if (filters.maxStock) {
      filtered = filtered.filter(product => product.availableQuantity <= filters.maxStock!);
    }

    // Ordenação
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filters.sortBy) {
          case 'title':
            aValue = a.title;
            bValue = b.title;
            break;
          case 'price':
            aValue = a.price;
            bValue = b.price;
            break;
          case 'stock':
            aValue = a.availableQuantity;
            bValue = b.availableQuantity;
            break;
          case 'sales':
            aValue = a.soldQuantity;
            bValue = b.soldQuantity;
            break;
          case 'gap':
            aValue = a.stockCalculation?.gap || 0;
            bValue = b.stockCalculation?.gap || 0;
            break;
          case 'coverage':
            aValue = a.metrics?.stockCoverageDays || 0;
            bValue = b.metrics?.stockCoverageDays || 0;
            break;
          default:
            aValue = a.title;
            bValue = b.title;
        }

        if (typeof aValue === 'string' && typeof bValue === 'string') {
          return filters.sortOrder === 'desc'
            ? bValue.localeCompare(aValue)
            : aValue.localeCompare(bValue);
        } else {
          return filters.sortOrder === 'desc'
            ? bValue - aValue
            : aValue - bValue;
        }
      });
    }

    setFilteredProducts(filtered);
  }, [mlProducts, filters]);

  const handleSyncProducts = async () => {
    // MOCK: Simulate sync process
    console.log('MOCK: Sincronizando produtos...');
    setError(null);
    // Simulate loading state would be handled by real store

    // COMMENTED FOR MOCK DEVELOPMENT - Real sync
    // if (!selectedAccount) return;
    // try {
    //   setError(null);
    //   await syncProducts(selectedAccount.id);
    // } catch (error) {
    //   console.error('Erro ao sincronizar produtos:', error);
    //   setError('Erro ao sincronizar produtos. Tente novamente.');
    // }
  };

  const handleUpdateStock = async (productId: string, quantity: number) => {
    try {
      await updateProductStock(productId, quantity, 'Atualização manual via interface');
    } catch (error) {
      console.error('Erro ao atualizar estoque:', error);
      throw error;
    }
  };

  const handleViewDetails = (product: ProductWithStock) => {
    setSelectedProduct(product);
    setIsDetailsModalOpen(true);
  };

  const handleSyncSingleProduct = async (productId: string) => {
    try {
      await syncSingleProduct(productId);
    } catch (error) {
      console.error('Erro ao sincronizar produto:', error);
    }
  };

  const handleFiltersChange = (newFilters: MLProductFilters) => {
    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setFilters({});
  };

  const getStockHistory = async (productId: string) => {
    try {
      // TODO: Implementar chamada para API de histórico
      return [];
    } catch (error) {
      console.error('Erro ao carregar histórico:', error);
      return [];
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Produtos do Mercado Livre</h1>
          <p className="mt-1 text-sm text-muted-foreground">
            Gerencie seus produtos sincronizados do Mercado Livre com controle de estoque integrado
          </p>
        </div>
        {/* MOCK: Always show buttons for development */}
        {mockSelectedAccount && (
          <div className="flex items-center gap-2">
            <Button
              onClick={() => navigate('/ml-full-wizard')}
              disabled={mlProducts.length === 0}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Truck className="h-4 w-4" />
              Criar Envio Full
            </Button>

            <Button
              onClick={handleSyncProducts}
              disabled={false} // MOCK: Never disabled
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Sincronizar (Mock)
            </Button>
          </div>
        )}

        {/* COMMENTED FOR MOCK DEVELOPMENT - Real account check
        {selectedAccount && (
          <div className="flex items-center gap-2">
            <Button
              onClick={() => setIsMLFullWizardOpen(true)}
              disabled={mlProducts.length === 0}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Truck className="h-4 w-4" />
              Enviar para ML Full
            </Button>

            <Button
              onClick={handleSyncProducts}
              disabled={syncStatus.isLoading}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${syncStatus.isLoading ? 'animate-spin' : ''}`} />
              {syncStatus.isLoading ? 'Sincronizando...' : 'Sincronizar'}
            </Button>
          </div>
        )}
        */}
      </div>

      {error && (
        <div className="flex items-center gap-2 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <AlertCircle className="h-4 w-4 text-destructive" />
          <span className="text-sm text-destructive">{error}</span>
        </div>
      )}

      {/* COMMENTED FOR MOCK DEVELOPMENT - Sync loading state
      {syncStatus.isLoading && !productsLoading && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center space-x-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Sincronizando produtos...</span>
              {syncStatus.progress > 0 && (
                <span className="text-sm text-muted-foreground">
                  {syncStatus.progress}%
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      )}
      */}

      {/* MOCK: Always show products section for development */}
      {mockSelectedAccount && (
        <div className="space-y-6">
          {/* Header da conta selecionada */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <ShoppingBag className="h-5 w-5 text-primary" />
                  <div>
                    <h3 className="font-medium">{mockSelectedAccount.nickname}</h3>
                    <p className="text-sm text-muted-foreground">{mockSelectedAccount.email}</p>
                  </div>
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Package className="h-4 w-4" />
                    <span>{mlProducts.length} produtos</span>
                  </div>
                  <span>Dados Mock para Desenvolvimento</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Filtros */}
          <ProductFiltersML
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onClearFilters={handleClearFilters}
            isLoading={false} // MOCK: Never loading
            productCount={filteredProducts.length}
          />

          {/* Grid de produtos */}
          <ProductGrid
            products={filteredProducts}
            isLoading={false} // MOCK: Never loading
            onUpdateStock={handleUpdateStock}
            onViewDetails={handleViewDetails}
            onSyncProduct={handleSyncSingleProduct}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        </div>
      )}

      {/* COMMENTED FOR MOCK DEVELOPMENT - Real account logic
      {accounts.length === 0 ? (
        <MercadoLivreConnect />
      ) : !selectedAccount ? (
        <Card>
          <CardHeader>
            <CardTitle>Selecionar Conta</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {accounts.map((account) => (
                <Card
                  key={account.id}
                  className="cursor-pointer hover:border-primary/50 transition-colors"
                  onClick={() => selectAccount(account)}
                >
                  <CardContent className="pt-6">
                    <div className="text-center">
                      <h3 className="font-medium">{account.nickname}</h3>
                      <p className="text-sm text-muted-foreground">{account.email}</p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      */}

      {/* Modal de detalhes do produto */}
      <ProductDetailsModal
        product={selectedProduct}
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedProduct(null);
        }}
        onUpdateStock={handleUpdateStock}
        onGetHistory={getStockHistory}
        onSyncProduct={handleSyncSingleProduct}
      />
    </div>
  );
}
