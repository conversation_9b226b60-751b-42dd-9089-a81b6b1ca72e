// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Modelo de Tenant (Multi-tenant)
model Tenant {
  id        String   @id @default(cuid())
  name      String
  domain    String   @unique
  subdomain String?  @unique
  settings  Json?
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relacionamentos
  users               User[]
  mercadoLivreAccounts MercadoLivreAccount[]
  products            Product[]
  stockCalculations   StockCalculation[]
  reports             Report[]
  auditLogs           AuditLog[]

  @@map("tenants")
}

// Modelo de Usuário
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  passwordHash  String
  firstName     String
  lastName      String
  role          UserRole  @default(USER)
  isActive      Boolean   @default(true)
  lastLoginAt   DateTime?
  emailVerified <PERSON>ole<PERSON>   @default(false)
  
  // Multi-tenant
  tenantId      String
  tenant        Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relacionamentos
  refreshTokens    RefreshToken[]
  createdReports   Report[]
  auditLogs        AuditLog[]

  @@map("users")
}

// Modelo de Refresh Token
model RefreshToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  expiresAt DateTime
  createdAt DateTime @default(now())
  isRevoked Boolean  @default(false)

  @@map("refresh_tokens")
}

// Modelo de Conta do Mercado Livre
model MercadoLivreAccount {
  id           String  @id @default(cuid())
  mlUserId     String
  accessToken  String  // Criptografado
  refreshToken String  // Criptografado
  expiresAt    DateTime
  nickname     String
  email        String?
  isActive     Boolean @default(true)
  
  // Multi-tenant
  tenantId     String
  tenant       Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relacionamentos
  products     Product[]

  @@unique([tenantId, mlUserId])
  @@map("mercado_livre_accounts")
}

// Modelo de Produto
model Product {
  id              String  @id @default(cuid())
  mlId            String  // ID do produto no Mercado Livre
  title           String
  sku             String?
  category        String
  price           Decimal
  availableQuantity Int
  soldQuantity    Int     @default(0)
  status          String
  permalink       String?
  thumbnail       String?
  
  // Multi-tenant
  tenantId        String
  tenant          Tenant  @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // Relacionamento com conta ML
  mlAccountId     String
  mlAccount       MercadoLivreAccount @relation(fields: [mlAccountId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relacionamentos
  stockCalculations StockCalculation[]
  salesHistory      Sale[]

  @@unique([tenantId, mlId])
  @@map("products")
}

// Modelo de Venda
model Sale {
  id          String   @id @default(cuid())
  mlOrderId   String
  quantity    Int
  unitPrice   Decimal
  totalPrice  Decimal
  saleDate    DateTime
  status      String
  
  // Relacionamento com produto
  productId   String
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("sales")
}

// Modelo de Cálculo de Estoque
model StockCalculation {
  id                    String   @id @default(cuid())
  currentStock          Int
  averageSales          Decimal
  idealStock            Int
  stockGap              Int
  daysOfCoverage        Int
  safetyStock           Int      @default(0)
  unitsInTransit        Int      @default(0)
  calculationDate       DateTime @default(now())
  
  // Multi-tenant
  tenantId              String
  tenant                Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // Relacionamento com produto
  productId             String
  product               Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  // Timestamps
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@map("stock_calculations")
}

// Modelo de Relatório
model Report {
  id          String     @id @default(cuid())
  type        ReportType
  title       String
  description String?
  filePath    String?
  fileSize    Int?
  mimeType    String?
  status      ReportStatus @default(PENDING)
  
  // Multi-tenant
  tenantId    String
  tenant      Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // Relacionamento com usuário criador
  createdById String
  createdBy   User       @relation(fields: [createdById], references: [id])
  
  // Timestamps
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  @@map("reports")
}

// Modelo de Log de Auditoria
model AuditLog {
  id          String   @id @default(cuid())
  action      String
  entity      String
  entityId    String?
  oldValues   Json?
  newValues   Json?
  ipAddress   String?
  userAgent   String?
  
  // Multi-tenant
  tenantId    String
  tenant      Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  
  // Relacionamento com usuário
  userId      String?
  user        User?    @relation(fields: [userId], references: [id])
  
  // Timestamps
  createdAt   DateTime @default(now())

  @@map("audit_logs")
}

// Enums
enum UserRole {
  ADMIN
  USER
  VIEWER
}

enum ReportType {
  STOCK_CALCULATION
  SALES_ANALYSIS
  MERCADO_ENVIOS_SPREADSHEET
  CUSTOM
}

enum ReportStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
} 