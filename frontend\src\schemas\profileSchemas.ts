import { z } from 'zod';

// Schema para informações pessoais
export const personalInfoSchema = z.object({
  name: z
    .string()
    .min(2, 'Nome deve ter pelo menos 2 caracteres')
    .max(100, 'Nome deve ter no máximo 100 caracteres')
    .regex(/^[a-zA-ZÀ-ÿ\s]+$/, 'Nome deve conter apenas letras e espaços'),
  
  email: z
    .string()
    .email('Email inválido')
    .max(255, 'Email deve ter no máximo 255 caracteres'),
  
  phone: z
    .string()
    .optional()
    .refine((phone) => {
      if (!phone) return true;
      // Regex para telefone brasileiro: +55 11 99999-9999 ou (11) 99999-9999 ou 11999999999
      const phoneRegex = /^(\+55\s?)?(\(?\d{2}\)?\s?)?\d{4,5}-?\d{4}$/;
      return phoneRegex.test(phone.replace(/\s/g, ''));
    }, 'Formato de telefone inválido'),
  
  department: z
    .string()
    .max(100, 'Departamento deve ter no máximo 100 caracteres')
    .optional(),
  
  position: z
    .string()
    .max(100, 'Cargo deve ter no máximo 100 caracteres')
    .optional(),
});

// Schema para preferências do usuário
export const preferencesSchema = z.object({
  language: z.enum(['pt-BR', 'en-US', 'es-ES'], {
    errorMap: () => ({ message: 'Idioma deve ser pt-BR, en-US ou es-ES' }),
  }),

  dateFormat: z.enum(['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'], {
    errorMap: () => ({ message: 'Formato de data inválido' }),
  }),

  defaultView: z.enum(['grid', 'list'], {
    errorMap: () => ({ message: 'Visualização padrão deve ser grid ou list' }),
  }),

  timezone: z
    .string()
    .min(1, 'Fuso horário é obrigatório')
    .max(50, 'Fuso horário deve ter no máximo 50 caracteres'),
});

// Schema para configurações de notificação
export const notificationsSchema = z.object({
  email: z.boolean(),
  stockAlerts: z.boolean(),
  salesReports: z.boolean(),
});

// Schema completo do perfil
export const profileSchema = z.object({
  personalInfo: personalInfoSchema,
  preferences: preferencesSchema,
  notifications: notificationsSchema,
});

// Schema para upload de avatar
export const avatarUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine((file) => file.size <= 5 * 1024 * 1024, 'Arquivo deve ter no máximo 5MB')
    .refine(
      (file) => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
      'Arquivo deve ser uma imagem (JPEG, PNG ou WebP)'
    )
    .refine(
      (file) => {
        // Verificar dimensões da imagem (opcional)
        return true; // Por enquanto aceitar qualquer dimensão
      },
      'Imagem deve ter dimensões adequadas'
    ),
});

// Schema para alteração de senha
export const passwordChangeSchema = z.object({
  currentPassword: z
    .string()
    .min(1, 'Senha atual é obrigatória'),
  
  newPassword: z
    .string()
    .min(8, 'Nova senha deve ter pelo menos 8 caracteres')
    .max(128, 'Nova senha deve ter no máximo 128 caracteres')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Nova senha deve conter pelo menos: 1 letra minúscula, 1 maiúscula, 1 número e 1 caractere especial'
    ),
  
  confirmPassword: z
    .string()
    .min(1, 'Confirmação de senha é obrigatória'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Senhas não coincidem',
  path: ['confirmPassword'],
});

// Tipos TypeScript derivados dos schemas
export type PersonalInfoFormData = z.infer<typeof personalInfoSchema>;
export type PreferencesFormData = z.infer<typeof preferencesSchema>;
export type NotificationsFormData = z.infer<typeof notificationsSchema>;
export type ProfileFormData = z.infer<typeof profileSchema>;
export type AvatarUploadFormData = z.infer<typeof avatarUploadSchema>;
export type PasswordChangeFormData = z.infer<typeof passwordChangeSchema>;

// Função para validar email em tempo real
export const validateEmail = (email: string): boolean => {
  try {
    z.string().email().parse(email);
    return true;
  } catch {
    return false;
  }
};

// Função para validar telefone em tempo real
export const validatePhone = (phone: string): boolean => {
  if (!phone) return true; // Telefone é opcional
  
  try {
    personalInfoSchema.shape.phone.parse(phone);
    return true;
  } catch {
    return false;
  }
};

// Função para formatar telefone
export const formatPhone = (phone: string): string => {
  // Remove todos os caracteres não numéricos
  const numbers = phone.replace(/\D/g, '');
  
  // Aplica formatação brasileira
  if (numbers.length === 11) {
    return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  } else if (numbers.length === 10) {
    return numbers.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
  }
  
  return phone;
};

// Função para validar força da senha
export const getPasswordStrength = (password: string): {
  score: number;
  feedback: string[];
} => {
  const feedback: string[] = [];
  let score = 0;
  
  if (password.length >= 8) {
    score += 1;
  } else {
    feedback.push('Deve ter pelo menos 8 caracteres');
  }
  
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Deve conter pelo menos uma letra minúscula');
  }
  
  if (/[A-Z]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Deve conter pelo menos uma letra maiúscula');
  }
  
  if (/\d/.test(password)) {
    score += 1;
  } else {
    feedback.push('Deve conter pelo menos um número');
  }
  
  if (/[@$!%*?&]/.test(password)) {
    score += 1;
  } else {
    feedback.push('Deve conter pelo menos um caractere especial (@$!%*?&)');
  }
  
  return { score, feedback };
};

// Constantes para validação
export const VALIDATION_CONSTANTS = {
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 128,
  MIN_NAME_LENGTH: 2,
  MAX_NAME_LENGTH: 100,
  MAX_EMAIL_LENGTH: 255,
} as const;
