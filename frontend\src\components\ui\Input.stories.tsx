 
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Input } from './Input';

const meta = {
  title: 'Components/Input',
  component: Input,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'ghost', 'pill', 'underline'],
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
    },
    status: {
      control: 'select',
      options: ['default', 'error', 'success', 'warning'],
    },
    disabled: { control: 'boolean' },
    label: { control: 'text' },
    helpText: { control: 'text' },
    errorText: { control: 'text' },
    placeholder: { control: 'text' },
    type: { control: 'text' },
    className: { control: 'text' },
  },
} satisfies Meta<typeof Input>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Digite aqui...',
  },
};

export const WithLabel: Story = {
  args: {
    label: 'Seu Nome',
    placeholder: '<PERSON>',
  },
};

export const WithHelpText: Story = {
  args: {
    label: 'Email',
    placeholder: '<EMAIL>',
    helpText: 'Nunca compartilharemos seu email com mais ninguém.',
  },
};

export const WithError: Story = {
  args: {
    label: 'Senha',
    type: 'password',
    placeholder: '********',
    status: 'error',
    errorText: 'A senha deve ter pelo menos 8 caracteres.',
  },
};

export const Success: Story = {
  args: {
    label: 'Código de Verificação',
    placeholder: '123456',
    status: 'success',
    helpText: 'Código válido!',
  },
};

export const Disabled: Story = {
  args: {
    label: 'Campo Desabilitado',
    placeholder: 'Você não pode digitar aqui',
    disabled: true,
  },
};

export const PillVariant: Story = {
  args: {
    variant: 'pill',
    placeholder: 'Campo estilo pílula',
  },
};

export const UnderlineVariant: Story = {
  args: {
    variant: 'underline',
    placeholder: 'Campo estilo sublinhado',
  },
};

export const GhostVariant: Story = {
  args: {
    variant: 'ghost',
    placeholder: 'Campo estilo ghost',
  },
};

export const SmallSize: Story = {
  args: {
    size: 'sm',
    placeholder: 'Campo pequeno',
  },
};

export const LargeSize: Story = {
  args: {
    size: 'lg',
    placeholder: 'Campo grande',
  },
}; 
