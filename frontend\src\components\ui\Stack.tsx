 
import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const stackVariants = cva(
  'flex',
  {
    variants: {
      direction: {
        row: 'flex-row',
        col: 'flex-col',
      },
      spacing: {
        none: 'gap-0',
        xs: 'gap-1',
        sm: 'gap-2',
        md: 'gap-4',
        lg: 'gap-8',
        xl: 'gap-12',
      },
      wrap: {
        true: 'flex-wrap',
        false: 'flex-nowrap',
      },
      justify: {
        start: 'justify-start',
        center: 'justify-center',
        end: 'justify-end',
        between: 'justify-between',
        around: 'justify-around',
      },
      align: {
        start: 'items-start',
        center: 'items-center',
        end: 'items-end',
        stretch: 'items-stretch',
        baseline: 'items-baseline',
      },
    },
    defaultVariants: {
      direction: 'col',
      spacing: 'md',
      wrap: false,
    },
  }
);

export interface StackProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof stackVariants> {
  asChild?: boolean;
}

const Stack = React.forwardRef<HTMLDivElement, StackProps>(
  ({ className, direction, spacing, wrap, justify, align, asChild = false, ...props }, ref) => {
    const Comp = asChild ? 'div' : 'div'; // You can change this to `Slot` from @radix-ui/react-slot if you install it
    return (
      <Comp
        className={cn(stackVariants({ direction, spacing, wrap, justify, align, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Stack.displayName = 'Stack';

export { Stack, stackVariants }; 
