import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Label } from '../components/ui/label';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import apiService from '../services/api';

interface SalesRecord {
  date: string;
  total: number;
}

export default function ReportsSales() {
  const [data, setData] = useState<SalesRecord[]>([]);
  const [from, setFrom] = useState('');
  const [to, setTo] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  async function loadData() {
    // TODO: substituir por chamada real
    const fake = [
      { date: '2024-05', total: 1200 },
      { date: '2024-06', total: 980 },
      { date: '2024-07', total: 1430 },
    ];
    setData(fake);
  }

  const handleFilter = (e: React.FormEvent) => {
    e.preventDefault();
    loadData();
  };

  const exportCSV = () => {
    const csvContent = 'data:text/csv;charset=utf-8,' +
      ['Data,Total', ...data.map(d => `${d.date},${d.total}`)].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', 'relatorio_vendas.csv');
    link.click();
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Relatório de Vendas</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={handleFilter} className="flex items-end gap-4">
            <div className="space-y-2">
              <Label>De</Label>
              <Input type="date" value={from} onChange={(e) => setFrom(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Até</Label>
              <Input type="date" value={to} onChange={(e) => setTo(e.target.value)} />
            </div>
            <Button type="submit">Filtrar</Button>
            <Button type="button" variant="secondary" onClick={exportCSV}>Exportar CSV</Button>
          </form>

          <div className="h-72 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data}>
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="total" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 