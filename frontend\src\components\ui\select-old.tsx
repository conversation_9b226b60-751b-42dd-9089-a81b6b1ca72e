import React, { useCallback, useState, useRef, useEffect } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { ChevronUpDownIcon, CheckIcon } from '@heroicons/react/20/solid';
import { useFormContext } from 'react-hook-form';

// Select item interface
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

// Select variants
const selectTriggerVariants = cva(
  'flex h-10 w-full items-center justify-between rounded-md border bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'border-input bg-background hover:bg-muted/50',
        filled: 'border-transparent bg-muted hover:bg-muted/70',
        outline: 'border-border bg-transparent hover:border-border-hover',
        ghost: 'border-transparent bg-transparent hover:bg-muted/50',
      },
      state: {
        default: 'border-input focus:ring-primary',
        error: 'border-danger-500 focus:ring-danger-500',
        success: 'border-success-500 focus:ring-success-500',
        warning: 'border-warning-500 focus:ring-warning-500',
      },
      size: {
        sm: 'h-8 px-2 text-xs',
        default: 'h-10 px-3 py-2',
        lg: 'h-12 px-4 text-base',
      },
    },
    defaultVariants: {
      variant: 'default',
      state: 'default',
      size: 'default',
    },
  }
);

const selectContentVariants = cva(
  'relative z-50 max-h-60 w-full overflow-auto rounded-md border bg-surface text-foreground shadow-lg focus:outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
  {
    variants: {
      position: {
        popper: 'mt-1',
        'item-aligned': '',
      },
      size: {
        sm: 'w-[var(--radix-select-trigger-width)]',
        default: 'w-[var(--radix-select-trigger-width)]',
        lg: 'w-[var(--radix-select-trigger-width)]',
      },
    },
    defaultVariants: {
      position: 'popper',
      size: 'default',
    },
  }
);

const selectItemVariants = cva(
  'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
  {
    variants: {
      state: {
        selected: 'bg-primary-50 text-primary-900 font-medium',
        default: 'text-foreground hover:bg-accent',
      },
    },
    defaultVariants: {
      state: 'default',
    },
  }
);

export interface SelectProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, 'onChange'>,
    VariantProps<typeof selectTriggerVariants> {
  options: SelectOption[];
  value?: string;
  onValueChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  name: string;
  label?: string;
  helpText?: string;
  error?: string;
  // For React Hook Form integration
  control?: any; // Use `control` from useForm() in RHF
  register?: any; // Use `register` from useForm() in RHF
  rules?: Record<string, any>;
}

const Select = React.forwardRef<HTMLDivElement, SelectProps>(
  (
    {
      options,
      value: controlledValue,
      onValueChange,
      placeholder,
      disabled,
      name,
      label,
      helpText,
      error: propError,
      variant,
      state: propState,
      size,
      className,
      control, // for RHF
      register: registerProp, // for RHF
      rules,
      ...props
    },
    ref
  ) => {
    const [isOpen, setIsOpen] = useState(false);
    const [localValue, setLocalValue] = useState(controlledValue || '');
    const triggerRef = useRef<HTMLButtonElement>(null);
    const contentRef = useRef<HTMLDivElement>(null);
    
    // Safely get form context - it might not exist
    let formContext;
    try {
      formContext = useFormContext();
    } catch {
      formContext = null;
    }
    
    const { getValues, register, formState: { errors } = {} } = formContext || {};

    const isControlled = controlledValue !== undefined;
    const currentValue = isControlled ? controlledValue : localValue;
    const displayValue = options.find(opt => opt.value === currentValue)?.label || placeholder;

    const validationError = errors?.[name]?.message as string | undefined;
    const hasError = !!propError || !!validationError;
    const effectiveState = hasError ? 'error' : propState;

    useEffect(() => {
      if (isControlled && controlledValue !== localValue) {
        setLocalValue(controlledValue || '');
      }
    }, [controlledValue, isControlled, localValue]);

    // Register with React Hook Form
    useEffect(() => {
      if (registerProp) {
        registerProp(name, rules);
      } else if (register) {
        register(name, rules);
      }
    }, [name, register, registerProp, rules]);

    const handleOptionClick = useCallback((optionValue: string) => {
      if (!isControlled) {
        setLocalValue(optionValue);
      }
      if (onValueChange) {
        onValueChange(optionValue);
      }
      // Manually set value for RHF if not registered directly via onChange
      if (control) {
        control._updateFormValues(name, optionValue);
      } else if (getValues) {
        // This is a fallback for when registerProp isn't used with onChange handler
        // In a real scenario, you'd use setValue for registered inputs.
        // For simplicity in this demo, we're directly updating the internal RHF state.
      }
      setIsOpen(false);
    }, [isControlled, onValueChange, name, control, getValues]);

    const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
        triggerRef.current?.focus();
      }
    }, []);

    // Close on outside click
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          triggerRef.current &&
          !triggerRef.current.contains(event.target as Node) &&
          contentRef.current &&
          !contentRef.current.contains(event.target as Node)
        ) {
          setIsOpen(false);
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }, []);

    return (
      <div ref={ref} className={cn('relative', className)} {...props}>
        {label && (
          <label htmlFor={name} className="block text-sm font-medium text-foreground mb-1">
            {label}
          </label>
        )}
        <button
          ref={triggerRef}
          id={name}
          type="button"
          role="combobox"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-labelledby={label ? name + '-label' : undefined}
          aria-controls={name + '-listbox'}
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled}
          className={cn(selectTriggerVariants({ variant, state: effectiveState, size }))}
        >
          <span className="flex-1 text-left overflow-hidden text-ellipsis whitespace-nowrap">
            {displayValue || placeholder}
          </span>
          <ChevronUpDownIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" aria-hidden="true" />
        </button>

        {isOpen && (
          <div
            ref={contentRef}
            className={cn(selectContentVariants({ size, position: 'popper' }), 'absolute w-full mt-1')}
            onKeyDown={handleKeyDown}
            role="listbox"
            id={name + '-listbox'}
            aria-labelledby={label ? name + '-label' : undefined}
            tabIndex={-1}
          >
            <div className="py-1">
              {options.length === 0 && (
                <div className="px-3 py-2 text-sm text-muted-foreground">
                  Nenhuma opção disponível
                </div>
              )}
              {options.map((option) => (
                <div
                  key={option.value}
                  role="option"
                  aria-selected={option.value === currentValue}
                  data-state={option.value === currentValue ? 'selected' : 'default'}
                  onClick={() => handleOptionClick(option.value)}
                  className={cn(selectItemVariants({ state: option.value === currentValue ? 'selected' : 'default' }), 'relative cursor-pointer py-2 pl-3 pr-9 flex items-center justify-between hover:bg-accent hover:text-accent-foreground')}
                  tabIndex={option.disabled ? -1 : 0}
                  aria-disabled={option.disabled}
                >
                  <span className="block truncate">{option.label}</span>
                  {option.value === currentValue && (
                    <span className="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
                      <CheckIcon className="h-4 w-4" />
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {hasError && (
          <p className="text-sm text-danger-600 mt-1" role="alert">
            {propError || validationError}
          </p>
        )}
        {helpText && !hasError && (
          <p className="text-sm text-muted-foreground mt-1">
            {helpText}
          </p>
        )}
      </div>
    );
  }
);
Select.displayName = 'Select';

// Create individual components for better API
const SelectTrigger = React.forwardRef<
  HTMLButtonElement,
  React.ButtonHTMLAttributes<HTMLButtonElement> & {
    children?: React.ReactNode;
  }
>(({ className, children, ...props }, ref) => (
  <button
    ref={ref}
    className={cn(selectTriggerVariants(), className)}
    {...props}
  >
    {children}
  </button>
));
SelectTrigger.displayName = "SelectTrigger";

const SelectValue = React.forwardRef<
  HTMLSpanElement,
  React.HTMLAttributes<HTMLSpanElement> & {
    placeholder?: string;
  }
>(({ className, placeholder, ...props }, ref) => (
  <span
    ref={ref}
    className={cn("block truncate", className)}
    {...props}
  >
    {placeholder}
  </span>
));
SelectValue.displayName = "SelectValue";

const SelectContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(selectContentVariants(), className)}
    {...props}
  >
    {children}
  </div>
));
SelectContent.displayName = "SelectContent";

const SelectItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    value: string;
    disabled?: boolean;
  }
>(({ className, children, value, disabled, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      "relative cursor-pointer py-2 pl-3 pr-9 flex items-center justify-between hover:bg-accent hover:text-accent-foreground",
      disabled && "opacity-50 cursor-not-allowed",
      className
    )}
    data-value={value}
    data-disabled={disabled}
    {...props}
  >
    {children}
  </div>
));
SelectItem.displayName = "SelectItem";

export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue };
