import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import {
  UserIcon,
  ClockIcon,
  KeyIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import ProfileForm from './ProfileForm';
import ActivityHistory from './ActivityHistory';
import PasswordChangeForm from './PasswordChangeForm';
import { useAuthStore } from '../../store/authStore';
import { FadeIn, SlideIn } from '../ui/Animations';

interface ProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'profile' | 'activity' | 'security';
}

const ProfileModal: React.FC<ProfileModalProps> = ({
  isOpen,
  onClose,
  defaultTab = 'profile',
}) => {
  const { user } = useAuthStore();
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleSaveSuccess = () => {
    // Optionally close modal after successful save
    // onClose();
  };

  const handleSaveError = (error: string) => {
    console.error('Erro ao salvar perfil:', error);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-semibold">
              Perfil do Usuário
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <XMarkIcon className="h-4 w-4" />
            </Button>
          </div>
          
          {user && (
            <FadeIn>
              <div className="flex items-center space-x-3 pt-2">
                {user.avatar ? (
                  <img
                    src={user.avatar}
                    alt={user.name}
                    className="w-12 h-12 rounded-full object-cover border-2 border-border"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center font-semibold">
                    {user.name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)}
                  </div>
                )}
                
                <div>
                  <h3 className="font-medium text-foreground">{user.name}</h3>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                      {user.role === 'admin' ? 'Administrador' : 
                       user.role === 'manager' ? 'Gerente' : 
                       user.role === 'user' ? 'Usuário' : 'Visualizador'}
                    </span>
                    {user.department && (
                      <span className="text-xs text-muted-foreground">
                        {user.department}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </FadeIn>
          )}
        </DialogHeader>

        <div className="flex-1 overflow-hidden">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <TabsList className="grid w-full grid-cols-3 flex-shrink-0">
              <TabsTrigger value="profile" className="flex items-center space-x-2">
                <UserIcon className="h-4 w-4" />
                <span>Perfil</span>
              </TabsTrigger>
              <TabsTrigger value="activity" className="flex items-center space-x-2">
                <ClockIcon className="h-4 w-4" />
                <span>Atividades</span>
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center space-x-2">
                <KeyIcon className="h-4 w-4" />
                <span>Segurança</span>
              </TabsTrigger>
            </TabsList>

            <div className="flex-1 overflow-y-auto mt-4">
              <TabsContent value="profile" className="mt-0">
                <SlideIn direction="left">
                  <ProfileForm
                    showTitle={false}
                    onSaveSuccess={handleSaveSuccess}
                    onSaveError={handleSaveError}
                    className="border-0 shadow-none"
                  />
                </SlideIn>
              </TabsContent>

              <TabsContent value="activity" className="mt-0">
                <SlideIn direction="left" delay={100}>
                  <ActivityHistory
                    showTitle={false}
                    maxActivities={15}
                    className="border-0 shadow-none"
                  />
                </SlideIn>
              </TabsContent>

              <TabsContent value="security" className="mt-0">
                <SlideIn direction="left" delay={200}>
                  <PasswordChangeForm
                    showTitle={false}
                    onChangeSuccess={() => {
                      // Optionally show success message or close modal
                    }}
                    onChangeError={(error) => {
                      console.error('Erro ao alterar senha:', error);
                    }}
                    className="border-0 shadow-none"
                  />
                </SlideIn>
              </TabsContent>
            </div>
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileModal;
