import React from 'react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { 
  ChevronLeft, 
  ChevronRight, 
  RotateCcw,
  Package,
  Calculator,
  FileText,
  Download
} from 'lucide-react';
import { useMLFullWizardStore } from '../../store/mlFullWizardStore';

const stepActions = {
  1: { icon: Package, label: 'Selecionar Produtos', nextLabel: 'Configurar Quantidades' },
  2: { icon: Calculator, label: 'Configurar Quantidades', nextLabel: 'Revisar e Gerar' },
  3: { icon: FileText, label: 'Revisar e Gerar', nextLabel: 'Finalizar' },
  4: { icon: Download, label: 'Finalizar', nextLabel: 'Concluir' },
};

export default function WizardNavigation() {
  const {
    currentStep,
    canProceed,
    canGoBack,
    selectedProducts,
    isGenerating,
    generatedFile,
    nextStep,
    previousStep,
    resetWizard,
    getTotalQuantity,
    getTotalValue,
  } = useMLFullWizardStore();

  const currentAction = stepActions[currentStep as keyof typeof stepActions];
  const totalQuantity = getTotalQuantity();
  const totalValue = getTotalValue();

  const getNextButtonText = () => {
    switch (currentStep) {
      case 1:
        return selectedProducts.length > 0 
          ? `Configurar ${selectedProducts.length} ${selectedProducts.length === 1 ? 'produto' : 'produtos'}`
          : 'Selecione produtos';
      case 2:
        return totalQuantity > 0 
          ? `Revisar ${totalQuantity} ${totalQuantity === 1 ? 'unidade' : 'unidades'}`
          : 'Configure quantidades';
      case 3:
        return isGenerating ? 'Gerando...' : 'Gerar Planilha';
      case 4:
        return 'Concluir';
      default:
        return 'Próximo';
    }
  };

  const getStepSummary = () => {
    switch (currentStep) {
      case 1:
        return selectedProducts.length > 0 
          ? `${selectedProducts.length} ${selectedProducts.length === 1 ? 'produto selecionado' : 'produtos selecionados'}`
          : 'Nenhum produto selecionado';
      case 2:
        if (totalQuantity > 0) {
          return (
            <div className="flex items-center gap-4 text-sm">
              <span>{totalQuantity} {totalQuantity === 1 ? 'unidade' : 'unidades'}</span>
              <span>•</span>
              <span>R$ {totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</span>
            </div>
          );
        }
        return 'Configure as quantidades';
      case 3:
        if (generatedFile) {
          return (
            <div className="flex items-center gap-4 text-sm">
              <span>{generatedFile.productCount} produtos</span>
              <span>•</span>
              <span>{generatedFile.totalQuantity} unidades</span>
              <span>•</span>
              <span>{generatedFile.format.toUpperCase()}</span>
            </div>
          );
        }
        return 'Pronto para gerar planilha';
      case 4:
        return generatedFile 
          ? `Planilha ${generatedFile.filename} gerada com sucesso`
          : 'Finalizando processo';
      default:
        return '';
    }
  };

  return (
    <div className="flex items-center justify-between">
      {/* Left Side - Step Info */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          {currentAction.icon && <currentAction.icon className="h-4 w-4 text-muted-foreground" />}
          <span className="font-medium">{currentAction.label}</span>
        </div>
        
        <div className="hidden md:block text-muted-foreground">
          {getStepSummary()}
        </div>
      </div>

      {/* Right Side - Actions */}
      <div className="flex items-center gap-3">
        {/* Reset Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={resetWizard}
          className="text-muted-foreground hover:text-foreground"
        >
          <RotateCcw className="h-4 w-4 mr-1" />
          Reiniciar
        </Button>

        {/* Back Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={previousStep}
          disabled={!canGoBack}
          className="flex items-center gap-1"
        >
          <ChevronLeft className="h-4 w-4" />
          Voltar
        </Button>

        {/* Next Button */}
        <Button
          onClick={nextStep}
          disabled={!canProceed || isGenerating}
          size="sm"
          className="flex items-center gap-1 min-w-[120px]"
        >
          {isGenerating ? (
            <>
              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-1"></div>
              Gerando...
            </>
          ) : (
            <>
              {getNextButtonText()}
              {currentStep < 4 && <ChevronRight className="h-4 w-4" />}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
