import React, { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../ui/Form';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '../ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select-radix';
import type { StockItem } from '../../types/api';

interface StockAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: StockItem) => void;
  initialData?: StockItem; // Para ajuste de um item existente
}

const stockAdjustmentSchema = z.object({
  id: z.string().optional(),
  productId: z.string(),
  productSku: z.string(),
  productTitle: z.string(),
  currentQuantity: z.preprocess(
    (val) => parseInt(String(val), 10),
    z.number().int().min(0, { message: "A quantidade atual não pode ser negativa." })
  ),
  newQuantity: z.preprocess(
    (val) => parseInt(String(val), 10),
    z.number().int().min(0, { message: "A nova quantidade deve ser um número inteiro não negativo." })
  ),
  newLocation: z.string().min(1, { message: "A localização é obrigatória." }),
});

type StockAdjustmentFormValues = z.infer<typeof stockAdjustmentSchema>;

export default function StockAdjustmentModal({ isOpen, onClose, onSubmit, initialData }: StockAdjustmentModalProps) {
  const form = useForm<StockAdjustmentFormValues>({
    resolver: zodResolver(stockAdjustmentSchema),
    defaultValues: {
      id: '',
      productId: '',
      productSku: '',
      productTitle: '',
      currentQuantity: 0,
      newQuantity: 0,
      newLocation: '',
    },
  });

  useEffect(() => {
    if (initialData) {
      form.reset({
        id: initialData.id,
        productId: initialData.productId,
        productSku: initialData.productSku,
        productTitle: initialData.productTitle,
        currentQuantity: initialData.currentQuantity,
        newQuantity: initialData.currentQuantity, // Sugerir a quantidade atual como valor inicial para a nova quantidade
        newLocation: initialData.location,
      });
    } else {
      form.reset(); // Limpar formulário para novo item se não houver initialData
    }
  }, [initialData, form]);

  const locationOptions = [
    { label: 'Armazém 1 - Prateleira A1', value: 'Armazém 1 - Prateleira A1' },
    { label: 'Armazém 2 - Prateleira B2', value: 'Armazém 2 - Prateleira B2' },
    { label: 'Loja - Mostruário', value: 'Loja - Mostruário' },
    { label: 'Loja - Depósito', value: 'Loja - Depósito' },
    // Adicione mais opções conforme necessário
  ];

  const handleSubmit = (values: StockAdjustmentFormValues) => {
    // Criar um objeto StockItem atualizado para passar para onSubmit
    const updatedStockItem: StockItem = {
      id: values.id || '',
      productId: values.productId,
      productSku: values.productSku,
      productTitle: values.productTitle,
      currentQuantity: values.newQuantity, // A nova quantidade se torna a quantidade atual
      location: values.newLocation,
      lastUpdated: new Date().toISOString(),
    };
    onSubmit(updatedStockItem);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Ajustar Estoque</DialogTitle>
          {initialData && (
            <DialogDescription>
              Ajustar estoque para: {initialData.productTitle} ({initialData.productSku})
            </DialogDescription>
          )}
        </DialogHeader>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="productTitle"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Produto</FormLabel>
                  <FormControl>
                    <Input {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="productSku"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>SKU</FormLabel>
                  <FormControl>
                    <Input {...field} disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="currentQuantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantidade Atual</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" disabled />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="newQuantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nova Quantidade</FormLabel>
                  <FormControl>
                    <Input {...field} type="number" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="newLocation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Localização</FormLabel>
                  <FormControl>
                    <Select value={field.value} onValueChange={field.onChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Selecione uma localização" />
                      </SelectTrigger>
                      <SelectContent>
                        {locationOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="flex justify-end gap-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancelar
            </Button>
            <Button type="submit">Salvar Ajuste</Button>
          </div>
        </form>
      </Form>
      </DialogContent>
    </Dialog>
  );
}