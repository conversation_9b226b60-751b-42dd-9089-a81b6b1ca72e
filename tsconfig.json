{"compilerOptions": {"target": "ES2022", "module": "CommonJS", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": false, "baseUrl": "./src", "paths": {"@/*": ["*"], "@config/*": ["config/*"], "@controllers/*": ["controllers/*"], "@middleware/*": ["middleware/*"], "@models/*": ["models/*"], "@routes/*": ["routes/*"], "@services/*": ["services/*"], "@utils/*": ["utils/*"], "@types/*": ["types/*"]}}, "include": ["src/**/*", "tests/**/*"], "exclude": ["node_modules", "dist", "**/*.spec.ts", "**/*.test.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}