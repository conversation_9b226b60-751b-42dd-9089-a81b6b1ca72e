import {
  SpreadsheetHistory,
  SpreadsheetGenerationResult,
  MLProcessingStatus,
  ShippingProduct
} from '../types/stock';

/**
 * Filtros para busca de histórico
 */
interface HistoryFilters {
  tenantId: string;
  startDate?: Date;
  endDate?: Date;
  generatedBy?: string;
  status?: 'generated' | 'downloaded' | 'sent' | 'processed';
  mlStatus?: 'pending' | 'processing' | 'completed' | 'error';
  tags?: string[];
  hasErrors?: boolean;
  limit?: number;
  offset?: number;
}

/**
 * Estatísticas do histórico
 */
interface HistoryStatistics {
  totalGenerated: number;
  totalDownloaded: number;
  totalProcessed: number;
  totalWithErrors: number;
  averageProductsPerSpreadsheet: number;
  totalProducts: number;
  totalValue: number;
  generationsByUser: Record<string, number>;
  generationsByDay: Array<{ date: string; count: number }>;
  mostUsedTags: Array<{ tag: string; count: number }>;
  processingSuccessRate: number;
}

/**
 * Serviço para gerenciamento de histórico de planilhas
 * do Mercado Envios Full
 */
class SpreadsheetHistoryService {
  private historyRecords: SpreadsheetHistory[] = [];
  private mlProcessingStatus: MLProcessingStatus[] = [];

  /**
   * Registra uma nova planilha gerada no histórico
   */
  async recordSpreadsheetGeneration(
    generationResult: SpreadsheetGenerationResult,
    tenantId: string
  ): Promise<SpreadsheetHistory> {
    try {
      const historyRecord: SpreadsheetHistory = {
        id: this.generateHistoryId(),
        tenantId,
        generationResult,
        createdAt: new Date(),
        accessCount: 0,
        mlStatus: 'pending'
      };

      this.historyRecords.push(historyRecord);

      // Inicializar status de processamento do ML
      const mlStatus: MLProcessingStatus = {
        spreadsheetId: historyRecord.id,
        status: 'uploaded',
        progress: 0,
        message: 'Planilha enviada para processamento',
        processedProducts: 0,
        totalProducts: generationResult.totalProducts,
        lastUpdated: new Date()
      };

      this.mlProcessingStatus.push(mlStatus);

      return historyRecord;
    } catch (error) {
      console.error('Erro ao registrar planilha no histórico:', error);
      throw new Error('Falha ao registrar histórico de planilha');
    }
  }

  /**
   * Busca histórico de planilhas com filtros
   */
  async getSpreadsheetHistory(filters: HistoryFilters): Promise<{
    records: SpreadsheetHistory[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      let filteredRecords = this.historyRecords.filter(record => 
        record.tenantId === filters.tenantId
      );

      // Aplicar filtros
      if (filters.startDate) {
        filteredRecords = filteredRecords.filter(record => 
          record.createdAt >= filters.startDate!
        );
      }

      if (filters.endDate) {
        filteredRecords = filteredRecords.filter(record => 
          record.createdAt <= filters.endDate!
        );
      }

      if (filters.generatedBy) {
        filteredRecords = filteredRecords.filter(record => 
          record.generationResult.generatedBy === filters.generatedBy
        );
      }

      if (filters.status) {
        filteredRecords = filteredRecords.filter(record => 
          record.generationResult.status === filters.status
        );
      }

      if (filters.mlStatus) {
        filteredRecords = filteredRecords.filter(record => 
          record.mlStatus === filters.mlStatus
        );
      }

      if (filters.tags && filters.tags.length > 0) {
        filteredRecords = filteredRecords.filter(record => 
          record.tags && record.tags.some(tag => filters.tags!.includes(tag))
        );
      }

      if (filters.hasErrors !== undefined) {
        filteredRecords = filteredRecords.filter(record => 
          record.generationResult.hasErrors === filters.hasErrors
        );
      }

      // Ordenar por data de criação (mais recente primeiro)
      filteredRecords.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      const total = filteredRecords.length;
      const offset = filters.offset || 0;
      const limit = filters.limit || 50;

      const paginatedRecords = filteredRecords.slice(offset, offset + limit);
      const hasMore = offset + limit < total;

      return {
        records: paginatedRecords,
        total,
        hasMore
      };
    } catch (error) {
      console.error('Erro ao buscar histórico:', error);
      throw new Error('Falha ao buscar histórico de planilhas');
    }
  }

  /**
   * Busca uma planilha específica por ID
   */
  async getSpreadsheetById(id: string, tenantId: string): Promise<SpreadsheetHistory | null> {
    try {
      const record = this.historyRecords.find(r => 
        r.id === id && r.tenantId === tenantId
      );

      if (record) {
        // Atualizar contador de acesso
        record.accessCount++;
        record.lastAccessedAt = new Date();
      }

      return record || null;
    } catch (error) {
      console.error('Erro ao buscar planilha:', error);
      return null;
    }
  }

  /**
   * Atualiza status de download de uma planilha
   */
  async markAsDownloaded(id: string, tenantId: string, downloadedBy: string): Promise<boolean> {
    try {
      const record = this.historyRecords.find(r => 
        r.id === id && r.tenantId === tenantId
      );

      if (!record) {
        return false;
      }

      record.generationResult.status = 'downloaded';
      record.generationResult.downloadedAt = new Date();
      record.generationResult.downloadedBy = downloadedBy;
      record.accessCount++;
      record.lastAccessedAt = new Date();

      return true;
    } catch (error) {
      console.error('Erro ao marcar como baixado:', error);
      return false;
    }
  }

  /**
   * Atualiza status de processamento do Mercado Livre
   */
  async updateMLProcessingStatus(
    spreadsheetId: string,
    status: MLProcessingStatus['status'],
    options: {
      progress?: number;
      message?: string;
      error?: string;
      processedProducts?: number;
      mlReferenceId?: string;
    } = {}
  ): Promise<boolean> {
    try {
      const mlStatus = this.mlProcessingStatus.find(s => 
        s.spreadsheetId === spreadsheetId
      );

      if (!mlStatus) {
        return false;
      }

      mlStatus.status = status;
      mlStatus.lastUpdated = new Date();

      if (options.progress !== undefined) {
        mlStatus.progress = options.progress;
      }

      if (options.message) {
        mlStatus.message = options.message;
      }

      if (options.error) {
        mlStatus.error = options.error;
      }

      if (options.processedProducts !== undefined) {
        mlStatus.processedProducts = options.processedProducts;
      }

      if (options.mlReferenceId) {
        mlStatus.mlReferenceId = options.mlReferenceId;
      }

      // Atualizar também no histórico
      const historyRecord = this.historyRecords.find(r => r.id === spreadsheetId);
      if (historyRecord) {
        // Mapear status do MLProcessingStatus para o tipo aceito em SpreadsheetHistory
        if (status === 'uploaded' || status === 'validating') {
          historyRecord.mlStatus = 'pending';
        } else if (status === 'processing') {
          historyRecord.mlStatus = 'processing';
        } else if (status === 'completed') {
          historyRecord.mlStatus = 'completed';
          historyRecord.generationResult.status = 'processed';
          historyRecord.mlProcessedAt = new Date();
        } else if (status === 'error') {
          historyRecord.mlStatus = 'error';
          historyRecord.mlError = options.error || 'Erro não especificado';
        }
      }

      return true;
    } catch (error) {
      console.error('Erro ao atualizar status do ML:', error);
      return false;
    }
  }

  /**
   * Busca status de processamento do ML
   */
  async getMLProcessingStatus(spreadsheetId: string): Promise<MLProcessingStatus | null> {
    try {
      return this.mlProcessingStatus.find(s => s.spreadsheetId === spreadsheetId) || null;
    } catch (error) {
      console.error('Erro ao buscar status do ML:', error);
      return null;
    }
  }

  /**
   * Adiciona tags a uma planilha
   */
  async addTags(id: string, tenantId: string, tags: string[]): Promise<boolean> {
    try {
      const record = this.historyRecords.find(r => 
        r.id === id && r.tenantId === tenantId
      );

      if (!record) {
        return false;
      }

      if (!record.tags) {
        record.tags = [];
      }

      // Adicionar apenas tags que não existem
      const newTags = tags.filter(tag => !record.tags!.includes(tag));
      record.tags.push(...newTags);

      return true;
    } catch (error) {
      console.error('Erro ao adicionar tags:', error);
      return false;
    }
  }

  /**
   * Remove tags de uma planilha
   */
  async removeTags(id: string, tenantId: string, tags: string[]): Promise<boolean> {
    try {
      const record = this.historyRecords.find(r => 
        r.id === id && r.tenantId === tenantId
      );

      if (!record || !record.tags) {
        return false;
      }

      record.tags = record.tags.filter(tag => !tags.includes(tag));

      return true;
    } catch (error) {
      console.error('Erro ao remover tags:', error);
      return false;
    }
  }

  /**
   * Adiciona notas a uma planilha
   */
  async addNotes(id: string, tenantId: string, notes: string): Promise<boolean> {
    try {
      const record = this.historyRecords.find(r => 
        r.id === id && r.tenantId === tenantId
      );

      if (!record) {
        return false;
      }

      record.notes = notes;
      return true;
    } catch (error) {
      console.error('Erro ao adicionar notas:', error);
      return false;
    }
  }

  /**
   * Remove uma planilha do histórico
   */
  async deleteSpreadsheet(id: string, tenantId: string): Promise<boolean> {
    try {
      const recordIndex = this.historyRecords.findIndex(r => 
        r.id === id && r.tenantId === tenantId
      );

      if (recordIndex === -1) {
        return false;
      }

      // Remover do histórico
      this.historyRecords.splice(recordIndex, 1);

      // Remover status do ML
      const mlStatusIndex = this.mlProcessingStatus.findIndex(s => 
        s.spreadsheetId === id
      );
      if (mlStatusIndex !== -1) {
        this.mlProcessingStatus.splice(mlStatusIndex, 1);
      }

      return true;
    } catch (error) {
      console.error('Erro ao deletar planilha:', error);
      return false;
    }
  }

  /**
   * Calcula estatísticas do histórico
   */
  async getHistoryStatistics(tenantId: string, days: number = 30): Promise<HistoryStatistics> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const recentRecords = this.historyRecords.filter(r => 
        r.tenantId === tenantId && r.createdAt >= cutoffDate
      );

      const totalGenerated = recentRecords.length;
      const totalDownloaded = recentRecords.filter(r => 
        r.generationResult.downloadedAt
      ).length;
      const totalProcessed = recentRecords.filter(r => 
        r.mlStatus === 'completed'
      ).length;
      const totalWithErrors = recentRecords.filter(r => 
        r.generationResult.hasErrors || r.mlStatus === 'error'
      ).length;

      const totalProducts = recentRecords.reduce((sum, r) => 
        sum + r.generationResult.totalProducts, 0
      );
      const averageProductsPerSpreadsheet = totalGenerated > 0 ? 
        totalProducts / totalGenerated : 0;

      const totalValue = recentRecords.reduce((sum, r) => 
        sum + r.generationResult.totalValue, 0
      );

      // Estatísticas por usuário
      const generationsByUser: Record<string, number> = {};
      recentRecords.forEach(r => {
        const user = r.generationResult.generatedBy;
        generationsByUser[user] = (generationsByUser[user] || 0) + 1;
      });

      // Estatísticas por dia
      const generationsByDay: Array<{ date: string; count: number }> = [];
      const dayGroups: Record<string, number> = {};
      
      recentRecords.forEach(r => {
        const dateKey = r.createdAt.toISOString().split('T')[0];
        dayGroups[dateKey] = (dayGroups[dateKey] || 0) + 1;
      });

      Object.entries(dayGroups).forEach(([date, count]) => {
        generationsByDay.push({ date, count });
      });

      generationsByDay.sort((a, b) => a.date.localeCompare(b.date));

      // Tags mais usadas
      const tagCounts: Record<string, number> = {};
      recentRecords.forEach(r => {
        if (r.tags) {
          r.tags.forEach(tag => {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          });
        }
      });

      const mostUsedTags = Object.entries(tagCounts)
        .map(([tag, count]) => ({ tag, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      // Taxa de sucesso de processamento
      const processedOrError = recentRecords.filter(r => 
        r.mlStatus === 'completed' || r.mlStatus === 'error'
      ).length;
      const processingSuccessRate = processedOrError > 0 ? 
        (totalProcessed / processedOrError) * 100 : 0;

      return {
        totalGenerated,
        totalDownloaded,
        totalProcessed,
        totalWithErrors,
        averageProductsPerSpreadsheet,
        totalProducts,
        totalValue,
        generationsByUser,
        generationsByDay,
        mostUsedTags,
        processingSuccessRate
      };
    } catch (error) {
      console.error('Erro ao calcular estatísticas:', error);
      throw new Error('Falha ao calcular estatísticas do histórico');
    }
  }

  /**
   * Busca planilhas similares baseadas em produtos
   */
  async findSimilarSpreadsheets(
    products: ShippingProduct[],
    tenantId: string,
    limit: number = 5
  ): Promise<Array<{
    record: SpreadsheetHistory;
    similarity: number;
    commonProducts: number;
  }>> {
    try {
      const productSkus = new Set(products.map(p => p.sku));
      const similarities: Array<{
        record: SpreadsheetHistory;
        similarity: number;
        commonProducts: number;
      }> = [];

      for (const record of this.historyRecords) {
        if (record.tenantId !== tenantId) continue;

        const recordSkus = new Set(record.generationResult.products.map(p => p.sku));
        const commonProducts = Array.from(productSkus).filter(sku => 
          recordSkus.has(sku)
        ).length;

        if (commonProducts > 0) {
          const similarity = commonProducts / Math.max(productSkus.size, recordSkus.size);
          similarities.push({
            record,
            similarity,
            commonProducts
          });
        }
      }

      return similarities
        .sort((a, b) => b.similarity - a.similarity)
        .slice(0, limit);
    } catch (error) {
      console.error('Erro ao buscar planilhas similares:', error);
      return [];
    }
  }

  /**
   * Exporta histórico para CSV
   */
  async exportHistoryToCSV(tenantId: string, filters?: Partial<HistoryFilters>): Promise<string> {
    try {
      const { records } = await this.getSpreadsheetHistory({
        tenantId,
        ...filters,
        limit: 10000 // Buscar todos
      });

      const csvHeader = [
        'ID',
        'Data de Geração',
        'Gerado Por',
        'Nome do Arquivo',
        'Total de Produtos',
        'Quantidade Total',
        'Valor Total',
        'Status',
        'Status ML',
        'Tem Erros',
        'Downloads',
        'Tags',
        'Notas'
      ].join(',');

      const csvRows = records.map(record => [
        record.id,
        record.createdAt.toISOString(),
        record.generationResult.generatedBy,
        record.generationResult.fileName,
        record.generationResult.totalProducts,
        record.generationResult.totalQuantity,
        record.generationResult.totalValue.toFixed(2),
        record.generationResult.status,
        record.mlStatus || 'N/A',
        record.generationResult.hasErrors ? 'Sim' : 'Não',
        record.accessCount,
        record.tags?.join(';') || '',
        record.notes || ''
      ].map(field => `"${field}"`).join(','));

      return [csvHeader, ...csvRows].join('\n');
    } catch (error) {
      console.error('Erro ao exportar histórico:', error);
      throw new Error('Falha ao exportar histórico');
    }
  }

  /**
   * Limpa histórico antigo
   */
  async cleanupOldHistory(tenantId: string, daysToKeep: number = 90): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const initialCount = this.historyRecords.length;

      // Remover registros antigos
      this.historyRecords = this.historyRecords.filter(r => 
        r.tenantId !== tenantId || r.createdAt >= cutoffDate
      );

      // Remover status ML correspondentes
      const remainingIds = new Set(this.historyRecords.map(r => r.id));
      this.mlProcessingStatus = this.mlProcessingStatus.filter(s => 
        remainingIds.has(s.spreadsheetId)
      );

      const removedCount = initialCount - this.historyRecords.length;
      return removedCount;
    } catch (error) {
      console.error('Erro ao limpar histórico:', error);
      return 0;
    }
  }

  /**
   * Gera ID único para histórico
   */
  private generateHistoryId(): string {
    return `sph-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const spreadsheetHistoryService = new SpreadsheetHistoryService();
