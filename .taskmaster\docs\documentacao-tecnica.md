# Documentação Técnica - Magnow

## Sistema de Controle Inteligente de Estoque para Mercado Livre

### Versão: 1.0.0 | Data: Janeiro 2025

---

## 📋 Índice

1. [Visão Geral](#visão-geral)
2. [Arquitetura do Sistema](#arquitetura-do-sistema)
3. [Stack Tecnológico](#stack-tecnológico)
4. [Estrutura do Projeto](#estrutura-do-projeto)
5. [Banco de Dados](#banco-de-dados)
6. [APIs e Integrações](#apis-e-integrações)
7. [Segurança](#segurança)
8. [Sistema Multi-tenant](#sistema-multi-tenant)
9. [Cache e Performance](#cache-e-performance)
10. [Monitoramento e Logs](#monitoramento-e-logs)
11. [Testes](#testes)
12. [Deploy e Infraestrutura](#deploy-e-infraestrutura)
13. [G<PERSON>as de Desenvolvimento](#guias-de-desenvolvimento)

---

## 🎯 Visão Geral

O **Magnow** é um sistema SaaS desenvolvido para otimizar o controle de estoque de vendedores do Mercado Livre, especificamente para usuários do Mercado Envios Full. O sistema oferece:

- **Integração nativa** com a API oficial do Mercado Livre
- **Cálculos inteligentes** de estoque ideal baseados em vendas históricas
- **Geração automática** de planilhas para envio ao Mercado Envios Full
- **Arquitetura multi-tenant** para isolamento de dados entre clientes
- **Dashboard intuitivo** para controle visual do estoque

### Objetivos Principais

1. **Automatizar** o processo de cálculo de necessidade de estoque
2. **Reduzir** o tempo de geração de planilhas em 80%
3. **Aumentar** a precisão dos cálculos de gap de estoque para 95%
4. **Suportar** múltiplas contas do Mercado Livre simultaneamente

---

## 🏗️ Arquitetura do Sistema

### Arquitetura Geral

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Banco de      │
│   React/TS      │◄──►│   Node.js       │◄──►│   Dados         │
│                 │    │   Express       │    │   PostgreSQL    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │     Cache       │
                    │     Redis       │
                    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  API Externa    │
                    │ Mercado Livre   │
                    └─────────────────┘
```

### Componentes Principais

#### 1. **Frontend (React/TypeScript)**
- Interface de usuário moderna e responsiva
- Dashboard para visualização de dados
- Formulários para configuração de parâmetros
- Visualização de gráficos e relatórios

#### 2. **Backend (Node.js/Express)**
- API RESTful com arquitetura multi-tenant
- Integração com API do Mercado Livre
- Motor de cálculo de estoque
- Sistema de autenticação e autorização

#### 3. **Banco de Dados (PostgreSQL)**
- Armazenamento de dados relacionais
- Schema multi-tenant
- Otimizado para consultas complexas

#### 4. **Cache (Redis)**
- Cache distribuído para performance
- Armazenamento de sessões
- Cache de dados da API do ML

---

## 💻 Stack Tecnológico

### Backend
- **Node.js** v18+ - Runtime JavaScript
- **Express.js** - Framework web
- **TypeScript** - Tipagem estática
- **Prisma** - ORM para banco de dados
- **PostgreSQL** - Banco de dados relacional
- **Redis** - Cache e armazenamento de sessões
- **JWT** - Autenticação stateless
- **bcrypt** - Hash de senhas
- **crypto** - Criptografia de dados sensíveis

### Frontend
- **React** v18+ - Biblioteca para UI
- **TypeScript** - Tipagem estática
- **Vite** - Build tool e dev server
- **Tailwind CSS** - Framework CSS
- **Recharts** - Biblioteca de gráficos
- **React Hook Form** - Gerenciamento de formulários
- **React Query** - Gerenciamento de estado servidor
- **Axios** - Cliente HTTP

### Ferramentas de Desenvolvimento
- **Docker** - Containerização
- **Docker Compose** - Orquestração local
- **Jest** - Framework de testes
- **Supertest** - Testes de API
- **Cypress** - Testes E2E
- **ESLint** - Linting de código
- **Prettier** - Formatação de código

### Infraestrutura
- **AWS/Azure** - Provedores de nuvem
- **GitHub Actions** - CI/CD
- **Nginx** - Proxy reverso
- **PM2** - Gerenciador de processos Node.js

---

## 📁 Estrutura do Projeto

```
magnow/
├── backend/
│   ├── src/
│   │   ├── controllers/      # Controladores da API
│   │   ├── services/         # Lógica de negócio
│   │   ├── models/           # Modelos de dados
│   │   ├── middleware/       # Middlewares
│   │   ├── routes/           # Rotas da API
│   │   ├── utils/            # Utilitários
│   │   ├── config/           # Configurações
│   │   └── types/            # Tipos TypeScript
│   ├── prisma/
│   │   ├── schema.prisma     # Schema do banco
│   │   └── migrations/       # Migrações
│   ├── tests/                # Testes backend
│   ├── Dockerfile
│   ├── package.json
│   └── tsconfig.json
├── frontend/
│   ├── src/
│   │   ├── components/       # Componentes React
│   │   ├── pages/            # Páginas da aplicação
│   │   ├── hooks/            # Custom hooks
│   │   ├── services/         # Serviços de API
│   │   ├── utils/            # Utilitários
│   │   ├── types/            # Tipos TypeScript
│   │   └── styles/           # Estilos CSS
│   ├── public/               # Arquivos públicos
│   ├── tests/                # Testes frontend
│   ├── Dockerfile
│   ├── package.json
│   └── vite.config.ts
├── docker-compose.yml
├── .github/
│   └── workflows/            # CI/CD workflows
└── docs/                     # Documentação
```

---

## 🗄️ Banco de Dados

### Schema Principal

#### Tabela: `tenants`
```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Tabela: `users`
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    name VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Tabela: `ml_accounts`
```sql
CREATE TABLE ml_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    user_id VARCHAR(255) NOT NULL, -- ML User ID
    access_token_encrypted TEXT NOT NULL,
    refresh_token_encrypted TEXT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    nickname VARCHAR(255),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Tabela: `products`
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    ml_account_id UUID REFERENCES ml_accounts(id),
    ml_item_id VARCHAR(255) NOT NULL,
    sku VARCHAR(255),
    title TEXT,
    current_stock INTEGER DEFAULT 0,
    price DECIMAL(10,2),
    status VARCHAR(20),
    category_id VARCHAR(255),
    last_sync TIMESTAMP DEFAULT NOW(),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Tabela: `sales`
```sql
CREATE TABLE sales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    product_id UUID REFERENCES products(id),
    ml_order_id VARCHAR(255) NOT NULL,
    quantity INTEGER NOT NULL,
    price DECIMAL(10,2),
    sale_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### Tabela: `stock_calculations`
```sql
CREATE TABLE stock_calculations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id),
    product_id UUID REFERENCES products(id),
    calculation_date TIMESTAMP DEFAULT NOW(),
    period_days INTEGER NOT NULL,
    average_sales DECIMAL(10,2),
    coverage_days INTEGER NOT NULL,
    ideal_stock INTEGER,
    current_stock INTEGER,
    in_transit INTEGER DEFAULT 0,
    gap_quantity INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Índices Otimizados

```sql
-- Índices para performance
CREATE INDEX idx_tenants_slug ON tenants(slug);
CREATE INDEX idx_users_tenant_email ON users(tenant_id, email);
CREATE INDEX idx_products_tenant_sku ON products(tenant_id, sku);
CREATE INDEX idx_sales_product_date ON sales(product_id, sale_date);
CREATE INDEX idx_stock_calc_tenant_date ON stock_calculations(tenant_id, calculation_date);
```

---

## 🔌 APIs e Integrações

### API Interna - Endpoints Principais

#### Autenticação
```
POST /api/auth/login
POST /api/auth/register
POST /api/auth/refresh
POST /api/auth/logout
```

#### Contas do Mercado Livre
```
GET    /api/ml-accounts
POST   /api/ml-accounts/connect
PUT    /api/ml-accounts/:id/refresh
DELETE /api/ml-accounts/:id
```

#### Produtos
```
GET    /api/products
GET    /api/products/:id
POST   /api/products/sync
PUT    /api/products/:id
```

#### Cálculos de Estoque
```
GET    /api/stock-calculations
POST   /api/stock-calculations/calculate
GET    /api/stock-calculations/gaps
```

#### Planilhas
```
GET    /api/spreadsheets
POST   /api/spreadsheets/generate
GET    /api/spreadsheets/:id/download
```

### Integração com Mercado Livre

#### Endpoints Utilizados

1. **OAuth 2.0**
   - `https://auth.mercadolivre.com.br/authorization`
   - `https://api.mercadolibre.com/oauth/token`

2. **Dados do Usuário**
   - `GET /users/me`
   - `GET /users/{user_id}`

3. **Produtos**
   - `GET /users/{user_id}/items/search`
   - `GET /items/{item_id}`
   - `GET /items/{item_id}/available_quantity`

4. **Vendas**
   - `GET /orders/search`
   - `GET /orders/{order_id}`

#### Rate Limiting
- **Limite**: 10.000 requisições por hora por aplicação
- **Estratégia**: Implementação de circuit breaker e retry com backoff exponencial
- **Cache**: TTL de 5 minutos para dados de produtos, 1 hora para dados de vendas

---

## 🔐 Segurança

### Autenticação
- **JWT** com refresh tokens
- **Tempo de expiração**: 15 minutos (access token), 7 dias (refresh token)
- **Algoritmo**: RS256 (chaves assimétricas)

### Autorização
- **RBAC** (Role-Based Access Control)
- **Roles**: `admin`, `manager`, `user`, `viewer`
- **Middleware** de autorização por rota

### Criptografia
- **Dados em trânsito**: HTTPS obrigatório
- **Dados em repouso**: AES-256-GCM para tokens OAuth
- **Senhas**: bcrypt com salt rounds 12
- **Chaves**: Rotação automática a cada 90 dias

### Validação de Entrada
- **Sanitização** de todos os inputs
- **Validação** de schemas com Joi
- **Rate limiting** por IP e usuário
- **CORS** configurado adequadamente

### Logs de Segurança
- **Tentativas de login** falhadas
- **Alterações de permissões**
- **Acessos a dados sensíveis**
- **Operações administrativas**

---

## 🏢 Sistema Multi-tenant

### Estratégia de Isolamento

#### 1. **Shared Database, Shared Schema**
- Todos os tenants compartilham o mesmo banco
- Isolamento via `tenant_id` em todas as tabelas
- Middleware automático de filtragem

#### 2. **Middleware de Tenant**
```javascript
const tenantMiddleware = (req, res, next) => {
    const tenantId = req.user?.tenantId;
    if (!tenantId) {
        return res.status(403).json({ error: 'Tenant não identificado' });
    }
    req.tenantId = tenantId;
    next();
};
```

#### 3. **Query Filtering**
```javascript
// Automático em todas as consultas
const products = await prisma.product.findMany({
    where: {
        tenantId: req.tenantId,
        // outros filtros...
    }
});
```

### Configurações por Tenant
- **Parâmetros de cálculo** personalizáveis
- **Limites de uso** configuráveis
- **Integrações** isoladas por tenant
- **Logs** separados por tenant

---

## ⚡ Cache e Performance

### Estratégia de Cache

#### 1. **Cache de Dados da API ML**
- **TTL**: 5 minutos para produtos, 1 hora para vendas
- **Invalidação**: Manual via webhook ou tempo
- **Chaves**: `ml:products:{tenant_id}:{item_id}`

#### 2. **Cache de Cálculos**
- **TTL**: 30 minutos
- **Invalidação**: Após nova sincronização
- **Chaves**: `calc:stock:{tenant_id}:{product_id}`

#### 3. **Cache de Sessões**
- **TTL**: 24 horas
- **Armazenamento**: Redis
- **Chaves**: `session:{user_id}:{session_id}`

### Otimizações de Performance

#### Database
- **Connection pooling**: Máximo 20 conexões
- **Query optimization**: Índices adequados
- **Paginação**: Limit 50 registros por página

#### API
- **Compressão**: gzip habilitado
- **Rate limiting**: 100 req/min por usuário
- **Timeout**: 30 segundos para operações longas

---

## 📊 Monitoramento e Logs

### Sistema de Logs

#### Estrutura de Log
```json
{
    "timestamp": "2025-01-08T10:30:00Z",
    "level": "info",
    "tenantId": "uuid",
    "userId": "uuid",
    "action": "stock_calculation",
    "resource": "product_123",
    "metadata": {
        "oldValue": 100,
        "newValue": 150,
        "ip": "***********"
    }
}
```

#### Níveis de Log
- **ERROR**: Erros críticos do sistema
- **WARN**: Avisos e situações anômalas
- **INFO**: Operações importantes
- **DEBUG**: Informações detalhadas para desenvolvimento

### Métricas de Monitoramento

#### Sistema
- **CPU**: < 70% de uso médio
- **Memória**: < 80% de uso
- **Disco**: < 85% de uso
- **Rede**: Latência < 100ms

#### Aplicação
- **Tempo de resposta**: < 500ms (95th percentile)
- **Taxa de erro**: < 1%
- **Disponibilidade**: > 99.9%
- **Throughput**: Requisições por segundo

#### Negócio
- **Sincronizações**: Taxa de sucesso > 98%
- **Cálculos**: Precisão > 95%
- **Usuários ativos**: Métricas diárias
- **Uso de recursos**: Por tenant

---

## 🧪 Testes

### Estratégia de Testes

#### 1. **Testes Unitários** (Jest)
- **Cobertura**: > 80%
- **Foco**: Lógica de negócio, utils, services
- **Mock**: APIs externas e banco de dados

#### 2. **Testes de Integração** (Supertest)
- **Foco**: Endpoints da API
- **Banco**: Banco de teste isolado
- **Cenários**: Fluxos completos

#### 3. **Testes E2E** (Cypress)
- **Foco**: Fluxos de usuário
- **Ambiente**: Staging environment
- **Automação**: CI/CD pipeline

### Estrutura de Testes

```
tests/
├── unit/
│   ├── services/
│   ├── utils/
│   └── controllers/
├── integration/
│   ├── api/
│   └── database/
└── e2e/
    ├── auth/
    ├── dashboard/
    └── calculations/
```

### Comandos de Teste

```bash
# Testes unitários
npm run test:unit

# Testes de integração
npm run test:integration

# Testes E2E
npm run test:e2e

# Cobertura
npm run test:coverage

# Todos os testes
npm run test:all
```

---

## 🚀 Deploy e Infraestrutura

### Arquitetura de Deploy

#### Produção (AWS)
```
┌─────────────────┐    ┌─────────────────┐
│   CloudFront    │    │   Application   │
│   (CDN)         │◄──►│   Load Balancer │
└─────────────────┘    └─────────────────┘
                               │
                               ▼
                    ┌─────────────────┐
                    │   ECS Cluster   │
                    │   (2+ instâncias)│
                    └─────────────────┘
                               │
                               ▼
                    ┌─────────────────┐
                    │   RDS Postgres  │
                    │   (Multi-AZ)    │
                    └─────────────────┘
                               │
                               ▼
                    ┌─────────────────┐
                    │ ElastiCache     │
                    │ (Redis)         │
                    └─────────────────┘
```

#### Staging/Development
```
┌─────────────────┐
│   Docker        │
│   Compose       │
├─────────────────┤
│ • Frontend      │
│ • Backend       │
│ • PostgreSQL    │
│ • Redis         │
└─────────────────┘
```

### CI/CD Pipeline

#### GitHub Actions Workflow
```yaml
name: Deploy
on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: npm run test:all
      
  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build Docker images
        run: docker build -t magnow .
      
  deploy:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to AWS
        run: aws ecs update-service
```

### Configuração de Ambiente

#### Variáveis de Ambiente
```env
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/magnow
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret

# Mercado Livre
ML_CLIENT_ID=your-ml-client-id
ML_CLIENT_SECRET=your-ml-client-secret
ML_REDIRECT_URI=http://localhost:3000/auth/callback

# Encryption
ENCRYPTION_KEY=your-32-byte-encryption-key

# Monitoring
LOG_LEVEL=info
SENTRY_DSN=your-sentry-dsn
```

---

## 👨‍💻 Guias de Desenvolvimento

### Setup Local

#### 1. **Clone do Repositório**
```bash
git clone https://github.com/empresa/magnow.git
cd magnow
```

#### 2. **Configuração do Ambiente**
```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Editar variáveis de ambiente
nano .env
```

#### 3. **Inicialização com Docker**
```bash
# Subir todos os serviços
docker-compose up -d

# Executar migrações
docker-compose exec backend npm run migrate

# Executar seeds
docker-compose exec backend npm run seed
```

#### 4. **Verificação**
```bash
# Frontend: http://localhost:3000
# Backend: http://localhost:3001
# Banco: localhost:5432
# Redis: localhost:6379
```

### Convenções de Código

#### Estrutura de Commit
```
type(scope): description

# Tipos: feat, fix, docs, style, refactor, test, chore
# Exemplo:
feat(auth): implementar autenticação JWT
fix(api): corrigir validação de entrada
docs(readme): atualizar guia de instalação
```

#### Padrões de Nomenclatura
- **Variáveis**: camelCase
- **Funções**: camelCase
- **Classes**: PascalCase
- **Constantes**: UPPER_SNAKE_CASE
- **Arquivos**: kebab-case

#### Estrutura de Função
```typescript
/**
 * Calcula o gap de estoque para um produto
 * @param productId - ID do produto
 * @param tenantId - ID do tenant
 * @param options - Opções de cálculo
 * @returns Promise com o gap calculado
 */
async function calculateStockGap(
    productId: string,
    tenantId: string,
    options: StockOptions
): Promise<StockGap> {
    // Implementação...
}
```

### Fluxo de Desenvolvimento

#### 1. **Nova Feature**
```bash
# Criar branch
git checkout -b feature/nova-funcionalidade

# Desenvolver
# ...

# Testes
npm run test:all

# Commit
git commit -m "feat: implementar nova funcionalidade"

# Push
git push origin feature/nova-funcionalidade

# Pull Request
# Criar PR para main
```

#### 2. **Hotfix**
```bash
# Criar branch
git checkout -b hotfix/correcao-urgente

# Corrigir
# ...

# Testes
npm run test:all

# Commit
git commit -m "fix: corrigir bug crítico"

# Deploy direto
```

---

## 📚 Referências e Links Úteis

### Documentação Externa
- [API Mercado Livre](https://developers.mercadolivre.com.br)
- [Node.js Documentation](https://nodejs.org/docs)
- [React Documentation](https://react.dev)
- [PostgreSQL Documentation](https://postgresql.org/docs)
- [Redis Documentation](https://redis.io/docs)

### Ferramentas de Desenvolvimento
- [Postman Collection](./postman/magnow-collection.json)
- [Database Schema](./database/schema.sql)
- [Docker Compose](../docker-compose.yml)

---

**Última atualização:** Junho 2025
**Versão da documentação:** 1.0.0
**Revisado por:** Equipe de Desenvolvimento 