import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';

// Instância global do Prisma Client
let prisma: PrismaClient;

// Configuração do Prisma Client
const createPrismaClient = (): PrismaClient => {
  return new PrismaClient({
    log: [
      {
        emit: 'event',
        level: 'query',
      },
      {
        emit: 'event',
        level: 'error',
      },
      {
        emit: 'event',
        level: 'info',
      },
      {
        emit: 'event',
        level: 'warn',
      },
    ],
    errorFormat: 'pretty',
  });
};

export const connectDatabase = async (): Promise<PrismaClient> => {
  try {
    if (!prisma) {
      prisma = createPrismaClient();
      
      // Configurar logs do Prisma
      prisma.$on('query', (e: any) => {
        logger.debug('Query:', {
          query: e.query,
          params: e.params,
          duration: `${e.duration}ms`,
        });
      });

      prisma.$on('error', (e: any) => {
        logger.error('Prisma Error:', e);
      });

      prisma.$on('info', (e: any) => {
        logger.info('Prisma Info:', e);
      });

      prisma.$on('warn', (e: any) => {
        logger.warn('Prisma Warning:', e);
      });
      
      // Testar conexão
      await prisma.$connect();
      logger.info('✅ Conexão com PostgreSQL (Prisma) estabelecida com sucesso');
    }
    
    return prisma;
  } catch (error) {
    logger.error('❌ Erro ao conectar com o banco de dados:', error);
    throw new Error('Falha na conexão com o banco de dados');
  }
};

export const getDatabase = (): PrismaClient => {
  if (!prisma) {
    throw new Error('Banco de dados não inicializado. Chame connectDatabase() primeiro.');
  }
  return prisma;
};

export const disconnectDatabase = async (): Promise<void> => {
  try {
    if (prisma) {
      await prisma.$disconnect();
      logger.info('✅ Conexão com o banco de dados encerrada');
    }
  } catch (error) {
    logger.error('❌ Erro ao encerrar conexão com o banco:', error);
  }
};

// Função para verificar a saúde da conexão
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    logger.error('❌ Verificação de saúde do banco falhou:', error);
    return false;
  }
};

export default prisma;