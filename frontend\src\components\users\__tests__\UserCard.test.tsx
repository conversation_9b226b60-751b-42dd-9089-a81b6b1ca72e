import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import UserCard from '../UserCard';
import type { User } from '../../../types/api';

// Mock user data
const mockUser: User = {
  id: 'user-001',
  email: '<EMAIL>',
  name: 'Test User',
  tenantId: 'tenant-1',
  role: 'user',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  isActive: true,
  phone: '+55 11 99999-9999',
  department: 'TI',
  position: 'Desenvolvedor',
  lastLoginAt: '2024-07-31T10:00:00Z',
};

describe('UserCard', () => {
  it('renders user information correctly', () => {
    render(<UserCard user={mockUser} />);
    
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('TI')).toBeInTheDocument();
    expect(screen.getByText('Desenvolvedor')).toBeInTheDocument();
    expect(screen.getByText('+55 11 99999-9999')).toBeInTheDocument();
  });

  it('shows active status correctly', () => {
    render(<UserCard user={mockUser} />);
    
    expect(screen.getByText('Ativo')).toBeInTheDocument();
  });

  it('shows inactive status correctly', () => {
    const inactiveUser = { ...mockUser, isActive: false };
    render(<UserCard user={inactiveUser} />);
    
    expect(screen.getByText('Inativo')).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', () => {
    const onEdit = vi.fn();
    render(<UserCard user={mockUser} onEdit={onEdit} />);
    
    const editButton = screen.getByRole('button', { name: /editar/i });
    fireEvent.click(editButton);
    
    expect(onEdit).toHaveBeenCalledWith(mockUser);
  });

  it('calls onDelete when delete button is clicked', () => {
    const onDelete = vi.fn();
    render(<UserCard user={mockUser} onDelete={onDelete} />);
    
    const deleteButton = screen.getByRole('button', { name: /trash2/i });
    fireEvent.click(deleteButton);
    
    expect(onDelete).toHaveBeenCalledWith(mockUser);
  });

  it('calls onToggleStatus when toggle status button is clicked', () => {
    const onToggleStatus = vi.fn();
    render(<UserCard user={mockUser} onToggleStatus={onToggleStatus} />);
    
    const toggleButton = screen.getByRole('button', { name: /desativar/i });
    fireEvent.click(toggleButton);
    
    expect(onToggleStatus).toHaveBeenCalledWith(mockUser);
  });

  it('renders compact view correctly', () => {
    render(<UserCard user={mockUser} compact={true} />);
    
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('TI')).toBeInTheDocument();
    
    // In compact view, position and phone should not be visible
    expect(screen.queryByText('Desenvolvedor')).not.toBeInTheDocument();
    expect(screen.queryByText('+55 11 99999-9999')).not.toBeInTheDocument();
  });

  it('hides actions when showActions is false', () => {
    render(<UserCard user={mockUser} showActions={false} />);
    
    expect(screen.queryByRole('button', { name: /editar/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /trash2/i })).not.toBeInTheDocument();
  });

  it('displays correct role badge', () => {
    const adminUser = { ...mockUser, role: 'admin' as const };
    render(<UserCard user={adminUser} />);
    
    expect(screen.getByText('Administrador')).toBeInTheDocument();
  });

  it('handles user without avatar', () => {
    const userWithoutAvatar = { ...mockUser, avatar: undefined };
    render(<UserCard user={userWithoutAvatar} />);
    
    // Should render default avatar icon
    expect(screen.getByText('Test User')).toBeInTheDocument();
  });

  it('handles user with avatar', () => {
    const userWithAvatar = { 
      ...mockUser, 
      avatar: 'https://example.com/avatar.jpg' 
    };
    render(<UserCard user={userWithAvatar} />);
    
    const avatarImage = screen.getByAltText('Test User');
    expect(avatarImage).toBeInTheDocument();
    expect(avatarImage).toHaveAttribute('src', 'https://example.com/avatar.jpg');
  });
});
