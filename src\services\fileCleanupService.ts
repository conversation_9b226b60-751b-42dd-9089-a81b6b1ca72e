/**
 * File Cleanup Service - Limpeza Automática de Arquivos
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import fs from 'fs';
import path from 'path';
import cron from 'node-cron';
import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';

export interface CleanupStats {
  expiredFiles: number;
  tempFiles: number;
  orphanedFiles: number;
  totalSizeFreed: number;
  errors: number;
}

export interface CleanupOptions {
  maxTempFileAge: number; // em horas
  maxOrphanedFileAge: number; // em horas
  dryRun: boolean;
  tenantId?: string;
}

export class FileCleanupService {
  private prisma: PrismaClient;
  private uploadsDir: string;
  private isRunning: boolean = false;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    this.uploadsDir = path.join(process.cwd(), process.env.UPLOADS_DIR || 'uploads');
  }

  /**
   * Inicializar serviço de limpeza automática
   */
  public startScheduledCleanup(): void {
    // Executar limpeza diária às 2:00 AM
    cron.schedule('0 2 * * *', async () => {
      if (this.isRunning) {
        logger.warn('Cleanup already running, skipping scheduled execution');
        return;
      }

      logger.info('Starting scheduled file cleanup');
      try {
        const stats = await this.performCleanup({
          maxTempFileAge: 24, // 24 horas
          maxOrphanedFileAge: 72, // 72 horas
          dryRun: false
        });

        logger.info('Scheduled cleanup completed', { stats });
      } catch (error) {
        logger.error('Error in scheduled cleanup', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    // Executar limpeza de arquivos temporários a cada 4 horas
    cron.schedule('0 */4 * * *', async () => {
      if (this.isRunning) {
        return;
      }

      logger.info('Starting temp files cleanup');
      try {
        const stats = await this.cleanupTempFiles({
          maxTempFileAge: 4, // 4 horas
          maxOrphanedFileAge: 24,
          dryRun: false
        });

        logger.info('Temp files cleanup completed', { 
          tempFiles: stats.tempFiles,
          sizeFreed: stats.totalSizeFreed 
        });
      } catch (error) {
        logger.error('Error in temp files cleanup', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    });

    logger.info('File cleanup service started with scheduled tasks');
  }

  /**
   * Executar limpeza completa
   */
  public async performCleanup(options: CleanupOptions): Promise<CleanupStats> {
    if (this.isRunning) {
      throw new Error('Cleanup is already running');
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      logger.info('Starting file cleanup', { options });

      const stats: CleanupStats = {
        expiredFiles: 0,
        tempFiles: 0,
        orphanedFiles: 0,
        totalSizeFreed: 0,
        errors: 0
      };

      // 1. Limpar arquivos expirados
      const expiredStats = await this.cleanupExpiredFiles(options);
      stats.expiredFiles = expiredStats.expiredFiles;
      stats.totalSizeFreed += expiredStats.totalSizeFreed;
      stats.errors += expiredStats.errors;

      // 2. Limpar arquivos temporários
      const tempStats = await this.cleanupTempFiles(options);
      stats.tempFiles = tempStats.tempFiles;
      stats.totalSizeFreed += tempStats.totalSizeFreed;
      stats.errors += tempStats.errors;

      // 3. Limpar arquivos órfãos (existem no disco mas não no banco)
      const orphanedStats = await this.cleanupOrphanedFiles(options);
      stats.orphanedFiles = orphanedStats.orphanedFiles;
      stats.totalSizeFreed += orphanedStats.totalSizeFreed;
      stats.errors += orphanedStats.errors;

      const duration = Date.now() - startTime;
      logger.info('File cleanup completed', { 
        stats, 
        duration: `${duration}ms`,
        dryRun: options.dryRun 
      });

      return stats;

    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Limpar arquivos expirados
   */
  private async cleanupExpiredFiles(options: CleanupOptions): Promise<CleanupStats> {
    const stats: CleanupStats = {
      expiredFiles: 0,
      tempFiles: 0,
      orphanedFiles: 0,
      totalSizeFreed: 0,
      errors: 0
    };

    try {
      // TODO: Implementar quando Prisma estiver configurado
      // Por enquanto, simular limpeza
      logger.info('Cleaning expired files (simulated)', { 
        tenantId: options.tenantId,
        dryRun: options.dryRun 
      });

      // Simular alguns arquivos expirados encontrados
      stats.expiredFiles = 0;
      stats.totalSizeFreed = 0;

    } catch (error) {
      logger.error('Error cleaning expired files', {
        error: error instanceof Error ? error.message : 'Unknown error',
        tenantId: options.tenantId
      });
      stats.errors++;
    }

    return stats;
  }

  /**
   * Limpar arquivos temporários
   */
  private async cleanupTempFiles(options: CleanupOptions): Promise<CleanupStats> {
    const stats: CleanupStats = {
      expiredFiles: 0,
      tempFiles: 0,
      orphanedFiles: 0,
      totalSizeFreed: 0,
      errors: 0
    };

    try {
      const tempDir = path.join(this.uploadsDir, 'temp');
      
      if (!fs.existsSync(tempDir)) {
        logger.debug('Temp directory does not exist', { tempDir });
        return stats;
      }

      const maxAge = options.maxTempFileAge * 60 * 60 * 1000; // Converter para ms
      const cutoffTime = Date.now() - maxAge;

      const files = fs.readdirSync(tempDir, { withFileTypes: true });

      for (const file of files) {
        if (!file.isFile()) continue;

        const filePath = path.join(tempDir, file.name);
        
        try {
          const fileStat = fs.statSync(filePath);
          
          if (fileStat.mtime.getTime() < cutoffTime) {
            const fileSize = fileStat.size;
            
            if (!options.dryRun) {
              fs.unlinkSync(filePath);
            }
            
            stats.tempFiles++;
            stats.totalSizeFreed += fileSize;
            
            logger.debug('Temp file cleaned', {
              filePath,
              size: fileSize,
              age: Date.now() - fileStat.mtime.getTime(),
              dryRun: options.dryRun
            });
          }
        } catch (error) {
          logger.error('Error processing temp file', {
            filePath,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          stats.errors++;
        }
      }

      logger.info('Temp files cleanup completed', {
        tempFiles: stats.tempFiles,
        totalSizeFreed: stats.totalSizeFreed,
        errors: stats.errors,
        dryRun: options.dryRun
      });

    } catch (error) {
      logger.error('Error in temp files cleanup', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      stats.errors++;
    }

    return stats;
  }

  /**
   * Limpar arquivos órfãos (existem no disco mas não no banco)
   */
  private async cleanupOrphanedFiles(options: CleanupOptions): Promise<CleanupStats> {
    const stats: CleanupStats = {
      expiredFiles: 0,
      tempFiles: 0,
      orphanedFiles: 0,
      totalSizeFreed: 0,
      errors: 0
    };

    try {
      // TODO: Implementar quando Prisma estiver configurado
      // Por enquanto, simular limpeza
      logger.info('Cleaning orphaned files (simulated)', { 
        tenantId: options.tenantId,
        dryRun: options.dryRun 
      });

      stats.orphanedFiles = 0;
      stats.totalSizeFreed = 0;

    } catch (error) {
      logger.error('Error cleaning orphaned files', {
        error: error instanceof Error ? error.message : 'Unknown error',
        tenantId: options.tenantId
      });
      stats.errors++;
    }

    return stats;
  }

  /**
   * Obter estatísticas de uso de disco
   */
  public async getDiskUsageStats(tenantId?: string): Promise<{
    totalSize: number;
    fileCount: number;
    sizeByType: Record<string, number>;
    oldestFile: Date | null;
    newestFile: Date | null;
  }> {
    try {
      const stats = {
        totalSize: 0,
        fileCount: 0,
        sizeByType: {} as Record<string, number>,
        oldestFile: null as Date | null,
        newestFile: null as Date | null
      };

      // TODO: Implementar estatísticas reais quando Prisma estiver configurado
      logger.info('Getting disk usage stats (simulated)', { tenantId });

      return stats;
    } catch (error) {
      logger.error('Error getting disk usage stats', {
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Verificar integridade dos arquivos
   */
  public async verifyFileIntegrity(tenantId?: string): Promise<{
    totalFiles: number;
    validFiles: number;
    corruptedFiles: number;
    missingFiles: number;
    errors: string[];
  }> {
    try {
      const result = {
        totalFiles: 0,
        validFiles: 0,
        corruptedFiles: 0,
        missingFiles: 0,
        errors: [] as string[]
      };

      // TODO: Implementar verificação real quando Prisma estiver configurado
      logger.info('Verifying file integrity (simulated)', { tenantId });

      return result;
    } catch (error) {
      logger.error('Error verifying file integrity', {
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Parar serviço de limpeza
   */
  public stopScheduledCleanup(): void {
    // Note: node-cron não tem método direto para parar tarefas específicas
    // Em uma implementação real, seria necessário manter referências às tarefas
    logger.info('File cleanup service stop requested');
  }

  /**
   * Verificar se limpeza está em execução
   */
  public isCleanupRunning(): boolean {
    return this.isRunning;
  }
}

export default FileCleanupService;
