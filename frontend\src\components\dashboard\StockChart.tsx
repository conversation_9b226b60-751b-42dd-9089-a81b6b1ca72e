import React from 'react';
import { CustomBar<PERSON>hart, CustomPieChart } from '../ui/Chart';
import { Card } from '../ui/Card';
import { CubeIcon, ExclamationTriangleIcon, CheckCircleIcon } from '@heroicons/react/24/outline';

interface StockDataPoint {
  category: string;
  totalProducts: number;
  inStock: number;
  lowStock: number;
  outOfStock: number;
  totalValue: number;
}

interface StockDistribution {
  name: string;
  value: number;
  color: string;
}

interface StockChartProps {
  data: StockDataPoint[];
  distribution: StockDistribution[];
  loading?: boolean;
}

export const StockChart: React.FC<StockChartProps> = ({
  data,
  distribution,
  loading = false
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0
    }).format(value);
  };

  // Calcular totais
  const totals = data.reduce(
    (acc, item) => ({
      totalProducts: acc.totalProducts + item.totalProducts,
      inStock: acc.inStock + item.inStock,
      lowStock: acc.lowStock + item.lowStock,
      outOfStock: acc.outOfStock + item.outOfStock,
      totalValue: acc.totalValue + item.totalValue
    }),
    { totalProducts: 0, inStock: 0, lowStock: 0, outOfStock: 0, totalValue: 0 }
  );

  const stockHealthPercentage = totals.totalProducts > 0 
    ? Math.round((totals.inStock / totals.totalProducts) * 100)
    : 0;

  return (
    <Card
      title="Análise de Estoque"
      subtitle="Distribuição e níveis por categoria"
      loading={loading}
      className="h-full"
    >
      <div className="space-y-6">
        {/* Resumo geral */}
        <div className="grid grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">{totals.totalProducts}</div>
            <div className="text-sm text-blue-600 dark:text-blue-400">Total de Produtos</div>
          </div>
          <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="text-lg font-bold text-green-600 dark:text-green-400">{totals.inStock}</div>
            <div className="text-sm text-green-600 dark:text-green-400">Em Estoque</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="text-lg font-bold text-yellow-600 dark:text-yellow-400">{totals.lowStock}</div>
            <div className="text-sm text-yellow-600 dark:text-yellow-400">Estoque Baixo</div>
          </div>
          <div className="text-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
            <div className="text-lg font-bold text-red-600 dark:text-red-400">{totals.outOfStock}</div>
            <div className="text-sm text-red-600 dark:text-red-400">Sem Estoque</div>
          </div>
        </div>

        {/* Indicador de saúde do estoque */}
        <div className="flex items-center justify-center gap-3 p-4 bg-muted/30 rounded-lg">
          {stockHealthPercentage >= 80 ? (
            <CheckCircleIcon className="h-6 w-6 text-green-500 dark:text-green-400" />
          ) : stockHealthPercentage >= 60 ? (
            <ExclamationTriangleIcon className="h-6 w-6 text-yellow-500 dark:text-yellow-400" />
          ) : (
            <ExclamationTriangleIcon className="h-6 w-6 text-red-500 dark:text-red-400" />
          )}
          <div className="text-center">
            <div className="text-lg font-bold text-foreground">
              {stockHealthPercentage}%
            </div>
            <div className="text-sm text-muted-foreground">Saúde do Estoque</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-foreground">
              {formatCurrency(totals.totalValue)}
            </div>
            <div className="text-sm text-muted-foreground">Valor Total</div>
          </div>
        </div>

        {/* Gráfico de barras por categoria */}
        <div className="h-64">
          <h4 className="text-sm font-medium text-foreground mb-3">Estoque por Categoria</h4>
          <CustomBarChart
            data={data}
            xKey="category"
            bars={[
              {
                key: 'inStock',
                name: 'Em Estoque',
                color: '#10B981'
              },
              {
                key: 'lowStock',
                name: 'Estoque Baixo',
                color: '#F59E0B'
              },
              {
                key: 'outOfStock',
                name: 'Sem Estoque',
                color: '#EF4444'
              }
            ]}
            height={200}
          />
        </div>

        {/* Gráfico de pizza - distribuição de estoque */}
        <div className="h-64">
          <h4 className="text-sm font-medium text-foreground mb-3">Distribuição do Estoque</h4>
          <CustomPieChart
            data={distribution}
            nameKey="name"
            valueKey="value"
            height={200}
          />
        </div>

        {/* Lista detalhada por categoria */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-foreground">Detalhes por Categoria</h4>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {data.map((category) => {
              const healthPercentage = category.totalProducts > 0 
                ? Math.round((category.inStock / category.totalProducts) * 100)
                : 0;
              
              return (
                <div
                  key={category.category}
                  className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50"
                >
                  <div className="flex items-center gap-3">
                    <CubeIcon className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <div className="font-medium text-foreground">
                        {category.category}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {category.totalProducts} produto{category.totalProducts !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="text-sm font-medium text-foreground">
                        {formatCurrency(category.totalValue)}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Valor total
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        healthPercentage >= 80 ? 'text-green-600 dark:text-green-400' :
                        healthPercentage >= 60 ? 'text-yellow-600 dark:text-yellow-400' : 'text-red-600 dark:text-red-400'
                      }`}>
                        {healthPercentage}%
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Saúde
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default StockChart;