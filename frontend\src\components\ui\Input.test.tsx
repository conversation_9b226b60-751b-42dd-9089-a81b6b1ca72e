 
import { render, screen, fireEvent } from '@testing-library/react';
import { Input } from './Input';
import { describe, it, expect } from 'vitest';

describe('Input', () => {
  it('should render an input element', () => {
    render(<Input placeholder="Test" />);
    expect(screen.getByPlaceholderText('Test')).toBeInTheDocument();
  });

  it('should display a label when provided', () => {
    render(<Input label="Username" />);
    expect(screen.getByLabelText('Username')).toBeInTheDocument();
  });

  it('should display help text when provided', () => {
    render(<Input helpText="Some helpful text" />);
    expect(screen.getByText('Some helpful text')).toBeInTheDocument();
  });

  it('should display error text when status is error', () => {
    render(<Input status="error" errorText="Error message" />);
    expect(screen.getByText('Error message')).toBeInTheDocument();
    expect(screen.getByRole('textbox')).toHaveAttribute('aria-invalid', 'true');
  });

  it('should be disabled when disabled prop is true', () => {
    render(<Input disabled />);
    expect(screen.getByRole('textbox')).toBeDisabled();
  });

  it('should update value on change', () => {
    render(<Input data-testid="test-input" />);
    const input = screen.getByTestId('test-input') as HTMLInputElement;
    fireEvent.change(input, { target: { value: 'New Value' } });
    expect(input.value).toBe('New Value');
  });

  it('should apply specified variant classes', () => {
    render(<Input variant="pill" data-testid="pill-input" />);
    const input = screen.getByTestId('pill-input');
    expect(input).toHaveClass('rounded-full'); // Assuming pill variant adds rounded-full
  });

  it('should apply specified size classes', () => {
    render(<Input size="lg" data-testid="lg-input" />);
    const input = screen.getByTestId('lg-input');
    expect(input).toHaveClass('h-12'); // Assuming lg size adds h-12
  });

  it('should have a unique id when no id is provided', () => {
    render(<Input label="Unique Label" />);
    const input = screen.getByLabelText('Unique Label');
    expect(input.id).not.toBeUndefined();
    expect(input.id).toMatch(/^magnow-input-/);
  });
}); 
