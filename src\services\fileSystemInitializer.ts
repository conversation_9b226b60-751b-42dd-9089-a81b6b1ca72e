/**
 * File System Initializer - Inicialização do Sistema de Arquivos
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import fs from 'fs';
import path from 'path';
import { logger } from '@/utils/logger';
import FileCleanupService from './fileCleanupService';

export class FileSystemInitializer {
  private cleanupService: FileCleanupService;
  private uploadsDir: string;

  constructor() {
    this.cleanupService = new FileCleanupService();
    this.uploadsDir = path.join(process.cwd(), process.env.UPLOADS_DIR || 'uploads');
  }

  /**
   * Inicializar sistema de arquivos completo
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('Initializing file system...');

      // 1. Criar estrutura de diretórios
      await this.createDirectoryStructure();

      // 2. Verificar permissões
      await this.verifyPermissions();

      // 3. Inicializar limpeza automática
      this.initializeCleanupService();

      // 4. Criar arquivos de configuração se necessário
      await this.createConfigFiles();

      logger.info('File system initialized successfully', {
        uploadsDir: this.uploadsDir,
        cleanupEnabled: true
      });

    } catch (error) {
      logger.error('Error initializing file system', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Criar estrutura de diretórios
   */
  private async createDirectoryStructure(): Promise<void> {
    const directories = [
      this.uploadsDir,
      path.join(this.uploadsDir, 'temp'),
      path.join(this.uploadsDir, 'demo-tenant-001', 'avatars'),
      path.join(this.uploadsDir, 'demo-tenant-001', 'documents'),
      path.join(this.uploadsDir, 'demo-tenant-001', 'spreadsheets'),
      path.join(this.uploadsDir, 'public', 'system')
    ];

    for (const dir of directories) {
      try {
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
          logger.debug('Directory created', { directory: dir });
        }
      } catch (error) {
        logger.error('Error creating directory', {
          directory: dir,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        throw new Error(`Failed to create directory: ${dir}`);
      }
    }

    logger.info('Directory structure created', {
      directories: directories.length,
      uploadsDir: this.uploadsDir
    });
  }

  /**
   * Verificar permissões de escrita
   */
  private async verifyPermissions(): Promise<void> {
    try {
      // Testar escrita no diretório principal
      const testFile = path.join(this.uploadsDir, '.write-test');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);

      logger.info('Write permissions verified', { uploadsDir: this.uploadsDir });
    } catch (error) {
      logger.error('Write permission test failed', {
        uploadsDir: this.uploadsDir,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Insufficient write permissions for uploads directory');
    }
  }

  /**
   * Inicializar serviço de limpeza automática
   */
  private initializeCleanupService(): void {
    try {
      this.cleanupService.startScheduledCleanup();
      logger.info('File cleanup service started');
    } catch (error) {
      logger.error('Error starting cleanup service', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Não falhar a inicialização por causa do cleanup
    }
  }

  /**
   * Criar arquivos de configuração
   */
  private async createConfigFiles(): Promise<void> {
    try {
      // Criar .gitkeep nos diretórios vazios
      const gitkeepDirs = [
        path.join(this.uploadsDir, 'temp'),
        path.join(this.uploadsDir, 'public', 'system')
      ];

      for (const dir of gitkeepDirs) {
        const gitkeepFile = path.join(dir, '.gitkeep');
        if (!fs.existsSync(gitkeepFile)) {
          fs.writeFileSync(gitkeepFile, '# Keep this directory in git\n');
        }
      }

      // Criar README.md no diretório de uploads
      const readmeFile = path.join(this.uploadsDir, 'README.md');
      if (!fs.existsSync(readmeFile)) {
        const readmeContent = `# Magnow Uploads Directory

Este diretório contém todos os arquivos enviados pelos usuários do sistema Magnow.

## Estrutura:

\`\`\`
uploads/
├── {tenantId}/
│   ├── avatars/          # Avatars de usuários
│   ├── documents/        # PDFs e documentos
│   └── spreadsheets/     # Planilhas Excel/CSV
├── temp/                 # Arquivos temporários
└── public/
    └── system/           # Arquivos públicos do sistema
\`\`\`

## Configurações:

- Tamanho máximo: ${process.env.UPLOAD_MAX_SIZE || '10MB'}
- Tipos permitidos: ${process.env.UPLOAD_ALLOWED_TYPES || 'images, PDFs, spreadsheets'}
- Limpeza automática: Ativada (diária às 2:00 AM)

## Segurança:

- Todos os arquivos são organizados por tenant
- Verificação de tipo MIME obrigatória
- Controle de acesso baseado em permissões
- Logs completos de todas as operações

---
Gerado automaticamente pelo Magnow File System
`;
        fs.writeFileSync(readmeFile, readmeContent);
      }

      logger.info('Configuration files created');
    } catch (error) {
      logger.error('Error creating config files', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Não falhar a inicialização por causa dos arquivos de config
    }
  }

  /**
   * Obter informações do sistema de arquivos
   */
  public getSystemInfo(): {
    uploadsDir: string;
    exists: boolean;
    writable: boolean;
    size: number;
    fileCount: number;
  } {
    try {
      const exists = fs.existsSync(this.uploadsDir);
      let writable = false;
      let size = 0;
      let fileCount = 0;

      if (exists) {
        try {
          // Testar escrita
          const testFile = path.join(this.uploadsDir, '.write-test-' + Date.now());
          fs.writeFileSync(testFile, 'test');
          fs.unlinkSync(testFile);
          writable = true;

          // Calcular tamanho e contagem (recursivo)
          const calculateSize = (dir: string): void => {
            const items = fs.readdirSync(dir, { withFileTypes: true });
            for (const item of items) {
              const fullPath = path.join(dir, item.name);
              if (item.isDirectory()) {
                calculateSize(fullPath);
              } else {
                const stats = fs.statSync(fullPath);
                size += stats.size;
                fileCount++;
              }
            }
          };

          calculateSize(this.uploadsDir);
        } catch (error) {
          // Ignorar erros de cálculo
        }
      }

      return {
        uploadsDir: this.uploadsDir,
        exists,
        writable,
        size,
        fileCount
      };
    } catch (error) {
      logger.error('Error getting system info', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return {
        uploadsDir: this.uploadsDir,
        exists: false,
        writable: false,
        size: 0,
        fileCount: 0
      };
    }
  }

  /**
   * Executar limpeza manual
   */
  public async performManualCleanup(tenantId?: string): Promise<{
    success: boolean;
    stats?: any;
    error?: string;
  }> {
    try {
      const stats = await this.cleanupService.performCleanup({
        maxTempFileAge: 24,
        maxOrphanedFileAge: 72,
        dryRun: false,
        tenantId
      });

      return {
        success: true,
        stats
      };
    } catch (error) {
      logger.error('Error in manual cleanup', {
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Parar serviços
   */
  public shutdown(): void {
    try {
      this.cleanupService.stopScheduledCleanup();
      logger.info('File system services stopped');
    } catch (error) {
      logger.error('Error stopping file system services', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }
}

export default FileSystemInitializer;
