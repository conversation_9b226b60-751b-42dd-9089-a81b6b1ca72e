import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import type { 
  UserProfile, 
  TenantConfig, 
  SecuritySettings, 
  MLSyncConfig, 
  AppConfig,
  PasswordChangeRequest 
} from '../types/api';
import apiService from '../services/api';

interface ConfigState {
  // User Profile
  userProfile: UserProfile | null;
  userProfileLoading: boolean;
  userProfileError: string | null;

  // Tenant Configuration
  tenantConfig: TenantConfig | null;
  tenantConfigLoading: boolean;
  tenantConfigError: string | null;

  // Security Settings
  securitySettings: SecuritySettings | null;
  securitySettingsLoading: boolean;
  securitySettingsError: string | null;

  // ML Sync Configuration
  mlSyncConfig: MLSyncConfig | null;
  mlSyncConfigLoading: boolean;
  mlSyncConfigError: string | null;

  // App Configuration
  appConfig: AppConfig | null;
  appConfigLoading: boolean;
  appConfigError: string | null;

  // Password Change
  passwordChangeLoading: boolean;
  passwordChangeError: string | null;
}

interface ConfigActions {
  // User Profile Actions
  loadUserProfile: () => Promise<void>;
  updateUserProfile: (profile: Partial<UserProfile>) => Promise<void>;
  
  // Tenant Configuration Actions
  loadTenantConfig: () => Promise<void>;
  updateTenantConfig: (config: Partial<TenantConfig>) => Promise<void>;
  
  // Security Settings Actions
  loadSecuritySettings: () => Promise<void>;
  updateSecuritySettings: (settings: Partial<SecuritySettings>) => Promise<void>;
  changePassword: (request: PasswordChangeRequest) => Promise<void>;
  
  // ML Sync Configuration Actions
  loadMLSyncConfig: () => Promise<void>;
  updateMLSyncConfig: (config: Partial<MLSyncConfig>) => Promise<void>;
  
  // App Configuration Actions
  loadAppConfig: () => Promise<void>;
  updateAppConfig: (config: Partial<AppConfig>) => Promise<void>;
  
  // Utility Actions
  clearErrors: () => void;
  refreshAll: () => Promise<void>;
}

type ConfigStore = ConfigState & ConfigActions;

// MOCK DATA FOR DEVELOPMENT - Remove when API is ready
const mockUserProfile: UserProfile = {
  id: 'user-1',
  name: 'João Silva',
  email: '<EMAIL>',
  phone: '+55 11 99999-9999',
  avatar: null,
  timezone: 'America/Sao_Paulo',
  language: 'pt-BR',
  notifications: {
    email: true,
    push: true,
    stockAlerts: true,
    salesReports: false,
    systemUpdates: true,
  },
  preferences: {
    theme: 'system',
    currency: 'BRL',
    dateFormat: 'DD/MM/YYYY',
    defaultView: 'grid',
  },
  updatedAt: new Date().toISOString(),
};

const mockTenantConfig: TenantConfig = {
  id: 'tenant-1',
  name: 'Magnow Empresa Demo',
  cnpj: '12.345.678/0001-90',
  address: {
    street: 'Rua das Empresas',
    number: '123',
    complement: 'Sala 456',
    neighborhood: 'Centro',
    city: 'São Paulo',
    state: 'SP',
    zipCode: '01234-567',
    country: 'Brasil',
  },
  contact: {
    email: '<EMAIL>',
    phone: '+55 11 3333-4444',
    website: 'https://magnow.com.br',
  },
  settings: {
    maxUsers: 50,
    maxProducts: 10000,
    features: {
      mlIntegration: true,
      advancedReports: true,
      apiAccess: true,
      customBranding: false,
    },
    billing: {
      plan: 'professional',
      billingCycle: 'monthly',
      nextBillingDate: '2025-08-31',
    },
  },
  createdAt: '2024-01-15T10:00:00Z',
  updatedAt: new Date().toISOString(),
};

const mockSecuritySettings: SecuritySettings = {
  passwordPolicy: {
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecialChars: false,
    expirationDays: 90,
  },
  twoFactorAuth: {
    enabled: false,
    method: 'email',
    backupCodes: [],
  },
  sessionManagement: {
    maxSessions: 3,
    sessionTimeout: 480, // 8 hours in minutes
    rememberMe: true,
  },
  loginAttempts: {
    maxAttempts: 5,
    lockoutDuration: 15, // minutes
    resetOnSuccess: true,
  },
  lastPasswordChange: '2024-12-01T10:00:00Z',
  updatedAt: new Date().toISOString(),
};

const mockMLSyncConfig: MLSyncConfig = {
  autoSync: true,
  syncFrequency: 'hourly',
  syncTypes: {
    products: true,
    orders: true,
    stock: true,
  },
  notifications: {
    onSuccess: true,
    onError: true,
    onConflict: false,
  },
  advanced: {
    batchSize: 100,
    timeout: 60000, // 60 seconds
    retryAttempts: 3,
    conflictResolution: 'remote',
  },
  lastUpdated: new Date().toISOString(),
};

const mockAppConfig: AppConfig = {
  stockCalculation: {
    defaultCoverageDays: 15,
    defaultSafetyStock: 5,
    recalculationInterval: 24, // hours
  },
  alerts: {
    enabled: true,
    emailNotifications: true,
    thresholds: {
      critical: 5,
      high: 10,
      medium: 20,
    },
  },
  spreadsheets: {
    defaultFormat: 'xlsx',
    maxProducts: 1000,
    autoGenerate: false,
  },
};

export const useConfigStore = create<ConfigStore>()(
  devtools(
    persist(
      (set, get) => ({
        // Initial state
        userProfile: null,
        userProfileLoading: false,
        userProfileError: null,

        tenantConfig: null,
        tenantConfigLoading: false,
        tenantConfigError: null,

        securitySettings: null,
        securitySettingsLoading: false,
        securitySettingsError: null,

        mlSyncConfig: null,
        mlSyncConfigLoading: false,
        mlSyncConfigError: null,

        appConfig: null,
        appConfigLoading: false,
        appConfigError: null,

        passwordChangeLoading: false,
        passwordChangeError: null,

        // User Profile Actions
        loadUserProfile: async () => {
          set({ userProfileLoading: true, userProfileError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT - Replace with API call
            await new Promise(resolve => setTimeout(resolve, 500));
            set({ 
              userProfile: mockUserProfile, 
              userProfileLoading: false 
            });
            
            // TODO: Replace with actual API call
            // const response = await apiService.getUserProfile();
            // if (response.success) {
            //   set({ userProfile: response.data, userProfileLoading: false });
            // }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar perfil';
            set({ 
              userProfileLoading: false, 
              userProfileError: errorMessage 
            });
          }
        },

        updateUserProfile: async (profile: Partial<UserProfile>) => {
          set({ userProfileLoading: true, userProfileError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT - Replace with API call
            await new Promise(resolve => setTimeout(resolve, 800));
            const currentProfile = get().userProfile;
            if (currentProfile) {
              const updatedProfile = { 
                ...currentProfile, 
                ...profile, 
                updatedAt: new Date().toISOString() 
              };
              set({ 
                userProfile: updatedProfile, 
                userProfileLoading: false 
              });
            }
            
            // TODO: Replace with actual API call
            // const response = await apiService.updateUserProfile(profile);
            // if (response.success) {
            //   set({ userProfile: response.data, userProfileLoading: false });
            // }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar perfil';
            set({ 
              userProfileLoading: false, 
              userProfileError: errorMessage 
            });
          }
        },

        // Tenant Configuration Actions
        loadTenantConfig: async () => {
          set({ tenantConfigLoading: true, tenantConfigError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 600));
            set({ 
              tenantConfig: mockTenantConfig, 
              tenantConfigLoading: false 
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar configurações da empresa';
            set({ 
              tenantConfigLoading: false, 
              tenantConfigError: errorMessage 
            });
          }
        },

        updateTenantConfig: async (config: Partial<TenantConfig>) => {
          set({ tenantConfigLoading: true, tenantConfigError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 1000));
            const currentConfig = get().tenantConfig;
            if (currentConfig) {
              const updatedConfig = { 
                ...currentConfig, 
                ...config, 
                updatedAt: new Date().toISOString() 
              };
              set({ 
                tenantConfig: updatedConfig, 
                tenantConfigLoading: false 
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar configurações da empresa';
            set({ 
              tenantConfigLoading: false, 
              tenantConfigError: errorMessage 
            });
          }
        },

        // Security Settings Actions
        loadSecuritySettings: async () => {
          set({ securitySettingsLoading: true, securitySettingsError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 400));
            set({ 
              securitySettings: mockSecuritySettings, 
              securitySettingsLoading: false 
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar configurações de segurança';
            set({ 
              securitySettingsLoading: false, 
              securitySettingsError: errorMessage 
            });
          }
        },

        updateSecuritySettings: async (settings: Partial<SecuritySettings>) => {
          set({ securitySettingsLoading: true, securitySettingsError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 700));
            const currentSettings = get().securitySettings;
            if (currentSettings) {
              const updatedSettings = { 
                ...currentSettings, 
                ...settings, 
                updatedAt: new Date().toISOString() 
              };
              set({ 
                securitySettings: updatedSettings, 
                securitySettingsLoading: false 
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar configurações de segurança';
            set({ 
              securitySettingsLoading: false, 
              securitySettingsError: errorMessage 
            });
          }
        },

        changePassword: async (request: PasswordChangeRequest) => {
          set({ passwordChangeLoading: true, passwordChangeError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 1200));
            
            // Update last password change date
            const currentSettings = get().securitySettings;
            if (currentSettings) {
              const updatedSettings = { 
                ...currentSettings, 
                lastPasswordChange: new Date().toISOString(),
                updatedAt: new Date().toISOString() 
              };
              set({ 
                securitySettings: updatedSettings, 
                passwordChangeLoading: false 
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao alterar senha';
            set({ 
              passwordChangeLoading: false, 
              passwordChangeError: errorMessage 
            });
          }
        },

        // ML Sync Configuration Actions
        loadMLSyncConfig: async () => {
          set({ mlSyncConfigLoading: true, mlSyncConfigError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 300));
            set({ 
              mlSyncConfig: mockMLSyncConfig, 
              mlSyncConfigLoading: false 
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar configurações de sincronização ML';
            set({ 
              mlSyncConfigLoading: false, 
              mlSyncConfigError: errorMessage 
            });
          }
        },

        updateMLSyncConfig: async (config: Partial<MLSyncConfig>) => {
          set({ mlSyncConfigLoading: true, mlSyncConfigError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 900));
            const currentConfig = get().mlSyncConfig;
            if (currentConfig) {
              const updatedConfig = { 
                ...currentConfig, 
                ...config, 
                lastUpdated: new Date().toISOString() 
              };
              set({ 
                mlSyncConfig: updatedConfig, 
                mlSyncConfigLoading: false 
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar configurações de sincronização ML';
            set({ 
              mlSyncConfigLoading: false, 
              mlSyncConfigError: errorMessage 
            });
          }
        },

        // App Configuration Actions
        loadAppConfig: async () => {
          set({ appConfigLoading: true, appConfigError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 350));
            set({ 
              appConfig: mockAppConfig, 
              appConfigLoading: false 
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar configurações da aplicação';
            set({ 
              appConfigLoading: false, 
              appConfigError: errorMessage 
            });
          }
        },

        updateAppConfig: async (config: Partial<AppConfig>) => {
          set({ appConfigLoading: true, appConfigError: null });
          try {
            // MOCK DATA FOR DEVELOPMENT
            await new Promise(resolve => setTimeout(resolve, 800));
            const currentConfig = get().appConfig;
            if (currentConfig) {
              const updatedConfig = { ...currentConfig, ...config };
              set({ 
                appConfig: updatedConfig, 
                appConfigLoading: false 
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar configurações da aplicação';
            set({ 
              appConfigLoading: false, 
              appConfigError: errorMessage 
            });
          }
        },

        // Utility Actions
        clearErrors: () => {
          set({
            userProfileError: null,
            tenantConfigError: null,
            securitySettingsError: null,
            mlSyncConfigError: null,
            appConfigError: null,
            passwordChangeError: null,
          });
        },

        refreshAll: async () => {
          const actions = get();
          await Promise.all([
            actions.loadUserProfile(),
            actions.loadTenantConfig(),
            actions.loadSecuritySettings(),
            actions.loadMLSyncConfig(),
            actions.loadAppConfig(),
          ]);
        },
      }),
      {
        name: 'config-store',
        // Only persist non-sensitive configuration data
        partialize: (state) => ({
          userProfile: state.userProfile ? {
            ...state.userProfile,
            // Don't persist sensitive data
          } : null,
          mlSyncConfig: state.mlSyncConfig,
          appConfig: state.appConfig,
        }),
      }
    ),
    {
      name: 'config-store',
    }
  )
);

export default useConfigStore;
