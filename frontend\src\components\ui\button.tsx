import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const buttonVariants = cva(
  [
    'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium',
    'transition-all duration-150 ease-in-out',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
    'disabled:pointer-events-none disabled:opacity-50',
    'select-none cursor-pointer',
  ],
  {
    variants: {
      variant: {
        // Usar variáveis CSS semânticas em vez de cores específicas
        default: [
          'bg-primary text-primary-foreground shadow-sm',
          'hover:bg-primary/90',
          'active:bg-primary/80',
          'focus-visible:ring-ring',
        ],
        
        secondary: [
          'bg-secondary text-secondary-foreground border border-border',
          'hover:bg-secondary/80',
          'active:bg-secondary/70',
          'focus-visible:ring-ring',
        ],
        
        outline: [
          'border border-border bg-background text-foreground',
          'hover:bg-accent hover:text-accent-foreground',
          'active:bg-accent/80',
          'focus-visible:ring-ring',
        ],
        
        ghost: [
          'bg-transparent text-foreground',
          'hover:bg-accent hover:text-accent-foreground',
          'active:bg-accent/80',
          'focus-visible:ring-ring',
        ],
        
        link: [
          'bg-transparent text-primary shadow-none',
          'hover:text-primary/80 hover:underline',
          'active:text-primary/70',
          'focus-visible:ring-ring',
        ],
        
        destructive: [
          'bg-destructive text-destructive-foreground shadow-sm',
          'hover:bg-destructive/90',
          'active:bg-destructive/80',
          'focus-visible:ring-ring',
        ],
      },
      
      size: {
        xs: 'h-6 px-2 py-1 text-xs',
        sm: 'h-8 px-3 py-1.5 text-sm',
        default: 'h-10 px-4 py-2 text-sm',
        lg: 'h-12 px-6 py-3 text-base',
        xl: 'h-14 px-8 py-4 text-lg',
        icon: 'h-10 w-10 p-0',
        'icon-sm': 'h-8 w-8 p-0',
        'icon-lg': 'h-12 w-12 p-0',
      },
      
      loading: {
        true: 'relative text-transparent',
        false: '',
      },
      
      fullWidth: {
        true: 'w-full',
        false: '',
      },
    },
    
    compoundVariants: [
      // Ajustes para botões icon com diferentes variants
      {
        size: ['icon', 'icon-sm', 'icon-lg'],
        class: 'justify-center',
      },
      
      // Loading states
      {
        loading: true,
        class: 'cursor-wait',
      },
    ],
    
    defaultVariants: {
      variant: 'default',
      size: 'default',
      loading: false,
      fullWidth: false,
    },
  }
);

export interface ButtonProps 
  extends React.ButtonHTMLAttributes<HTMLButtonElement>, 
         VariantProps<typeof buttonVariants> {
  /** Ícone à esquerda do texto */
  leftIcon?: React.ReactNode;
  /** Ícone à direita do texto */
  rightIcon?: React.ReactNode;
  /** Estado de loading */
  loading?: boolean;
  /** Texto de loading (opcional) */
  loadingText?: string;
  /** Componente de spinner customizado */
  spinner?: React.ReactNode;
}

// Componente Spinner padrão
const DefaultSpinner: React.FC<{ size?: 'xs' | 'sm' | 'default' | 'lg' | 'xl' }> = ({ 
  size = 'default' 
}) => {
  const sizeClasses = {
    xs: 'h-3 w-3',
    sm: 'h-4 w-4',
    default: 'h-4 w-4',
    lg: 'h-5 w-5',
    xl: 'h-6 w-6',
  };
  
  return (
    <svg 
      className={cn('animate-spin', sizeClasses[size])} 
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24"
    >
      <circle 
        className="opacity-25" 
        cx="12" 
        cy="12" 
        r="10" 
        stroke="currentColor" 
        strokeWidth="4"
      />
      <path 
        className="opacity-75" 
        fill="currentColor" 
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      />
    </svg>
  );
};

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ 
    className, 
    variant, 
    size, 
    loading = false,
    loadingText,
    fullWidth,
    leftIcon,
    rightIcon,
    spinner,
    disabled,
    children,
    ...props 
  }, ref) => {
    
    const isDisabled = disabled || loading;
    const spinnerSize = size === 'xs' ? 'xs' : size === 'sm' ? 'sm' : 
                      size === 'lg' ? 'lg' : size === 'xl' ? 'xl' : 'default';
    
    const renderSpinner = () => {
      if (spinner) return spinner;
      return <DefaultSpinner size={spinnerSize} />;
    };
    
    const renderContent = () => {
      if (loading) {
        return (
          <>
            {/* Conteúdo original (invisível) para manter dimensões */}
            <span className="opacity-0 flex items-center justify-center">
              {leftIcon && <span className="mr-2">{leftIcon}</span>}
              {children}
              {rightIcon && <span className="ml-2">{rightIcon}</span>}
            </span>
            
            {/* Spinner centralizado */}
            <span className="absolute inset-0 flex items-center justify-center">
              {renderSpinner()}
              {loadingText && <span className="ml-2 text-current">{loadingText}</span>}
            </span>
          </>
        );
      }
      
      return (
        <>
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="ml-2">{rightIcon}</span>}
        </>
      );
    };
    
    return (
      <button
        className={cn(buttonVariants({ 
          variant, 
          size, 
          loading, 
          fullWidth, 
          className 
        }))}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {renderContent()}
      </button>
    );
  }
);

Button.displayName = 'Button';

export { Button, buttonVariants };
export default Button;
