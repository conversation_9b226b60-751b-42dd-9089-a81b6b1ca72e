# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Production build
dist/
build/

# Environment variables
# .env
# .env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output/

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables files
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build directory
.vuepress/dist

# Serverless directories
node_modules/
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/

# Database
*.sqlite
*.sqlite3
*.db

# Uploads and user content
uploads/
public/uploads/

# Redis dump
dump.rdb

# Docker
.dockerignore

# Prisma
# prisma/migrations/

# Development methodology files (EXCLUDED FROM CLIENT REPO)
.gitignore.client
# env.example
# AGENTS.md
# CLAUDE.md
# .taskmaster/
# .cursor/
# .github/instructions/
# .trae/
# .windsurf/
# .clinerules/
# .roo/
# .roomodes

# Frontend (not ready for client)
# frontend/

logs
dev-debug.log
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific

# Task files
 tasks.json
 tasks/ 
