import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/Switch';
import { Slider } from '../ui/slider';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import {
  Settings,
  Save,
  RotateCcw,
  Info,
  Package,
  Clock,
  Shield
} from 'lucide-react';
import { useStockStore } from '../../store/stockStore';

interface StockConfiguration {
  defaultCoverageDays: number;
  defaultSafetyStockDays: number;
  minStockLevel: number;
  maxStockLevel: number;
  autoCalculationEnabled: boolean;
  reorderPointMultiplier: number;
  analysisWindowDays: number;
}

interface StockConfigurationPanelProps {
  className?: string;
}

export const StockConfigurationPanel: React.FC<StockConfigurationPanelProps> = ({ className }) => {
  const { statistics, loadStatistics } = useStockStore();
  
  // MOCK DATA FOR DEVELOPMENT - Replace with real configuration store
  const [config, setConfig] = useState<StockConfiguration>({
    defaultCoverageDays: 30,
    defaultSafetyStockDays: 7,
    minStockLevel: 1,
    maxStockLevel: 1000,
    autoCalculationEnabled: true,
    reorderPointMultiplier: 1.5,
    analysisWindowDays: 90,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const handleConfigChange = (key: keyof StockConfiguration, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: Replace with real API call
      // await stockConfigService.updateConfiguration(config);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      
      // Refresh statistics after configuration change
      await loadStatistics();
      
      console.log('Configurações salvas:', config);
    } catch (error) {
      console.error('Erro ao salvar configurações:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setConfig({
      defaultCoverageDays: 30,
      defaultSafetyStockDays: 7,
      minStockLevel: 1,
      maxStockLevel: 1000,
      autoCalculationEnabled: true,
      reorderPointMultiplier: 1.5,
      analysisWindowDays: 90,
    });
    setHasChanges(true);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Configurações de Estoque
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Configurações Básicas */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Package className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold">Configurações Básicas</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="coverageDays">Dias de Cobertura Padrão</Label>
              <Input
                id="coverageDays"
                type="number"
                value={config.defaultCoverageDays}
                onChange={(e) => handleConfigChange('defaultCoverageDays', parseInt(e.target.value))}
                min="1"
                max="365"
              />
              <p className="text-xs text-muted-foreground">
                Quantos dias de vendas o estoque deve cobrir
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="safetyDays">Dias de Estoque de Segurança</Label>
              <Input
                id="safetyDays"
                type="number"
                value={config.defaultSafetyStockDays}
                onChange={(e) => handleConfigChange('defaultSafetyStockDays', parseInt(e.target.value))}
                min="0"
                max="90"
              />
              <p className="text-xs text-muted-foreground">
                Buffer adicional para variações de demanda
              </p>
            </div>
          </div>
        </div>



        {/* Configurações Avançadas */}
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-muted-foreground" />
            <h3 className="font-semibold">Configurações Avançadas</h3>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Cálculo Automático</Label>
                <p className="text-xs text-muted-foreground">
                  Recalcular estoque ideal automaticamente
                </p>
              </div>
              <Switch
                checked={config.autoCalculationEnabled}
                onCheckedChange={(checked) => handleConfigChange('autoCalculationEnabled', checked)}
              />
            </div>
            

          </div>
        </div>

        {/* Informações de Status */}
        {statistics && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              Aplicado a {statistics.totalItems} produtos • 
              Última atualização: {new Date().toLocaleString()}
            </AlertDescription>
          </Alert>
        )}

        {/* Ações */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleReset}
            disabled={isLoading}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Restaurar Padrões
          </Button>
          
          <div className="flex items-center gap-2">
            {hasChanges && (
              <Badge variant="secondary">
                Alterações não salvas
              </Badge>
            )}
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Salvando...' : 'Salvar Configurações'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StockConfigurationPanel;
