import React, { useEffect } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Button } from './button';

// Modal overlay variants
const modalOverlayVariants = cva(
  'fixed inset-0 z-50 transition-all duration-300 ease-in-out',
  {
    variants: {
      variant: {
        default: 'bg-black/50 backdrop-blur-sm',
        dark: 'bg-black/70 backdrop-blur-md',
        light: 'bg-white/70 backdrop-blur-sm',
        blur: 'bg-black/30 backdrop-blur-lg',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

// Modal content variants
const modalContentVariants = cva(
  'fixed z-50 w-full transition-all duration-300 ease-in-out',
  {
    variants: {
      size: {
        xs: 'max-w-xs',
        sm: 'max-w-sm',
        default: 'max-w-md',
        lg: 'max-w-lg',
        xl: 'max-w-xl',
        '2xl': 'max-w-2xl',
        '3xl': 'max-w-3xl',
        '4xl': 'max-w-4xl',
        '5xl': 'max-w-5xl',
        '6xl': 'max-w-6xl',
        '7xl': 'max-w-7xl',
        full: 'max-w-full',
      },
      position: {
        center: 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2',
        top: 'top-10 left-1/2 transform -translate-x-1/2',
        bottom: 'bottom-10 left-1/2 transform -translate-x-1/2',
        'top-left': 'top-10 left-10',
        'top-right': 'top-10 right-10',
        'bottom-left': 'bottom-10 left-10',
        'bottom-right': 'bottom-10 right-10',
      },
      variant: {
        default: 'bg-surface border border-border shadow-xl rounded-lg',
        elevated: 'bg-surface border border-border shadow-2xl rounded-lg',
        minimal: 'bg-surface border border-border shadow-lg rounded-md',
        popup: 'bg-surface border border-border shadow-lg rounded-xl',
        fullscreen: 'bg-surface min-h-screen rounded-none border-0',
        drawer: 'bg-surface border-l border-border shadow-xl rounded-l-lg min-h-screen',
      },
      padding: {
        none: 'p-0',
        sm: 'p-4',
        default: 'p-6',
        lg: 'p-8',
        xl: 'p-10',
      },
    },
    defaultVariants: {
      size: 'default',
      position: 'center',
      variant: 'default',
      padding: 'default',
    },
  }
);

// Modal header variants
const modalHeaderVariants = cva(
  'flex items-center justify-between border-b border-border',
  {
    variants: {
      padding: {
        none: 'p-0',
        sm: 'pb-3',
        default: 'pb-4',
        lg: 'pb-6',
      },
    },
    defaultVariants: {
      padding: 'default',
    },
  }
);

// Modal footer variants  
const modalFooterVariants = cva(
  'flex items-center border-t border-border',
  {
    variants: {
      padding: {
        none: 'p-0',
        sm: 'pt-3',
        default: 'pt-4',
        lg: 'pt-6',
      },
      alignment: {
        start: 'justify-start',
        center: 'justify-center',
        end: 'justify-end',
        between: 'justify-between',
        around: 'justify-around',
      },
      spacing: {
        none: 'gap-0',
        sm: 'gap-2',
        default: 'gap-3',
        lg: 'gap-4',
      },
    },
    defaultVariants: {
      padding: 'default',
      alignment: 'end',
      spacing: 'default',
    },
  }
);

export interface ModalProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof modalContentVariants> {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  showCloseButton?: boolean;
  closeOnOverlayClick?: boolean;
  closeOnEscape?: boolean;
  lockScroll?: boolean;
  overlayVariant?: VariantProps<typeof modalOverlayVariants>['variant'];
  children: React.ReactNode;
  footer?: React.ReactNode;
  initialFocus?: React.RefObject<HTMLElement>;
}

const Modal = React.forwardRef<HTMLDivElement, ModalProps>(
  ({
    isOpen,
    onClose,
    title,
    description,
    showCloseButton = true,
    closeOnOverlayClick = true,
    closeOnEscape = true,
    lockScroll = true,
    overlayVariant = 'default',
    size,
    position,
    variant,
    padding,
    children,
    footer,
    className,
    initialFocus,
    ...props
  }, ref) => {
    // Handle escape key
    useEffect(() => {
      if (!isOpen || !closeOnEscape) return;

      const handleEscape = (event: KeyboardEvent) => {
        if (event.key === 'Escape') {
          onClose();
        }
      };

      document.addEventListener('keydown', handleEscape);
      return () => document.removeEventListener('keydown', handleEscape);
    }, [isOpen, closeOnEscape, onClose]);

    // Handle scroll lock
    useEffect(() => {
      if (!isOpen || !lockScroll) return;

      const originalStyle = window.getComputedStyle(document.body).overflow;
      document.body.style.overflow = 'hidden';

      return () => {
        document.body.style.overflow = originalStyle;
      };
    }, [isOpen, lockScroll]);

    // Handle initial focus
    useEffect(() => {
      if (!isOpen || !initialFocus?.current) return;

      const timer = setTimeout(() => {
        initialFocus.current?.focus();
      }, 100);

      return () => clearTimeout(timer);
    }, [isOpen, initialFocus]);

    if (!isOpen) return null;

    const handleOverlayClick = (event: React.MouseEvent) => {
      if (closeOnOverlayClick && event.target === event.currentTarget) {
        onClose();
      }
    };

    return (
      <>
        {/* Overlay */}
        <div
          className={modalOverlayVariants({ variant: overlayVariant })}
          onClick={handleOverlayClick}
          aria-hidden="true"
        />

        {/* Modal Content */}
        <div
          ref={ref}
          className={cn(modalContentVariants({ size, position, variant, padding, className }))}
          role="dialog"
          aria-modal="true"
          aria-labelledby={title ? 'modal-title' : undefined}
          aria-describedby={description ? 'modal-description' : undefined}
          {...props}
        >
          {/* Header */}
          {(title || showCloseButton) && (
            <ModalHeader>
              <div className="flex-1">
                {title && (
                  <ModalTitle id="modal-title">{title}</ModalTitle>
                )}
                {description && (
                  <ModalDescription id="modal-description">{description}</ModalDescription>
                )}
              </div>
              {showCloseButton && (
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={onClose}
                  className="flex-shrink-0"
                  aria-label="Fechar modal"
                >
                  <XMarkIcon className="h-4 w-4" />
                </Button>
              )}
            </ModalHeader>
          )}

          {/* Content */}
          <ModalContent>
            {children}
          </ModalContent>

          {/* Footer */}
          {footer && (
            <ModalFooter>
              {footer}
            </ModalFooter>
          )}
        </div>
      </>
    );
  }
);
Modal.displayName = 'Modal';

// Modal Header Component
export interface ModalHeaderProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof modalHeaderVariants> {}

const ModalHeader = React.forwardRef<HTMLDivElement, ModalHeaderProps>(
  ({ className, padding, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(modalHeaderVariants({ padding, className }))}
      {...props}
    />
  )
);
ModalHeader.displayName = 'ModalHeader';

// Modal Title Component
export interface ModalTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
}

const ModalTitle = React.forwardRef<HTMLHeadingElement, ModalTitleProps>(
  ({ className, as: Comp = 'h2', ...props }, ref) => (
    <Comp
      ref={ref}
      className={cn(
        'text-lg font-semibold leading-none tracking-tight text-foreground',
        className
      )}
      {...props}
    />
  )
);
ModalTitle.displayName = 'ModalTitle';

// Modal Description Component
export interface ModalDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {}

const ModalDescription = React.forwardRef<HTMLParagraphElement, ModalDescriptionProps>(
  ({ className, ...props }, ref) => (
    <p
      ref={ref}
      className={cn('text-sm text-muted-foreground mt-1', className)}
      {...props}
    />
  )
);
ModalDescription.displayName = 'ModalDescription';

// Modal Content Component
export interface ModalContentProps extends React.HTMLAttributes<HTMLDivElement> {}

const ModalContent = React.forwardRef<HTMLDivElement, ModalContentProps>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('py-4', className)}
      {...props}
    />
  )
);
ModalContent.displayName = 'ModalContent';

// Modal Footer Component
export interface ModalFooterProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof modalFooterVariants> {}

const ModalFooter = React.forwardRef<HTMLDivElement, ModalFooterProps>(
  ({ className, padding, alignment, spacing, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(modalFooterVariants({ padding, alignment, spacing, className }))}
      {...props}
    />
  )
);
ModalFooter.displayName = 'ModalFooter';

// Convenience hook for modal state
export const useModal = (initialState = false) => {
  const [isOpen, setIsOpen] = React.useState(initialState);

  const open = React.useCallback(() => setIsOpen(true), []);
  const close = React.useCallback(() => setIsOpen(false), []);
  const toggle = React.useCallback(() => setIsOpen(prev => !prev), []);

  return {
    isOpen,
    open,
    close,
    toggle,
    setIsOpen,
  };
};

// Pre-configured modal variants
export const ConfirmModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  confirmText?: string;
  cancelText?: string;
  variant?: 'default' | 'danger' | 'warning';
}> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  description,
  confirmText = 'Confirmar',
  cancelText = 'Cancelar',
  variant = 'default',
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  const confirmVariant = variant === 'danger' ? 'danger' : 
                        variant === 'warning' ? 'warning' : 'primary';

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="sm"
      title={title}
      description={description}
      footer={
        <>
          <Button variant="outline" onClick={onClose}>
            {cancelText}
          </Button>
          <Button variant={confirmVariant} onClick={handleConfirm}>
            {confirmText}
          </Button>
        </>
      }
    />
  );
};

export {
  Modal,
  ModalHeader,
  ModalTitle,
  ModalDescription,
  ModalContent,
  ModalFooter,
  modalOverlayVariants,
  modalContentVariants,
  modalHeaderVariants,
  modalFooterVariants,
};

export default Modal; 
