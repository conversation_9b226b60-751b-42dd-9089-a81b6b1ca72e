import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Textarea } from '../ui/textarea';
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../ui/dialog';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '../ui/alert-dialog';
import { 
  Edit3, 
  Save, 
  X, 
  History, 
  AlertTriangle, 
  CheckCircle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Package
} from 'lucide-react';
import type { ProductWithStock, StockHistoryEntry } from '../../types/api';

interface StockManagerProps {
  product: ProductWithStock;
  onUpdateStock: (productId: string, quantity: number, reason?: string) => Promise<void>;
  onGetHistory?: (productId: string) => Promise<StockHistoryEntry[]>;
  isUpdating?: boolean;
  disabled?: boolean;
}

export default function StockManager({
  product,
  onUpdateStock,
  onGetHistory,
  isUpdating = false,
  disabled = false
}: StockManagerProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [newQuantity, setNewQuantity] = useState(product.availableQuantity.toString());
  const [reason, setReason] = useState('');
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [history, setHistory] = useState<StockHistoryEntry[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [showHistoryDialog, setShowHistoryDialog] = useState(false);

  useEffect(() => {
    setNewQuantity(product.availableQuantity.toString());
  }, [product.availableQuantity]);

  const getStockStatus = () => {
    const stock = product.availableQuantity;
    if (stock === 0) return { 
      status: 'Sem estoque', 
      color: 'destructive', 
      icon: AlertTriangle,
      bgColor: 'bg-red-50'
    };
    if (stock <= 5) return { 
      status: 'Estoque baixo', 
      color: 'warning', 
      icon: AlertTriangle,
      bgColor: 'bg-yellow-50'
    };
    if (stock <= 10) return { 
      status: 'Estoque médio', 
      color: 'secondary', 
      icon: Package,
      bgColor: 'bg-blue-50'
    };
    return { 
      status: 'Estoque OK', 
      color: 'default', 
      icon: CheckCircle,
      bgColor: 'bg-green-50'
    };
  };

  const handleStartEdit = () => {
    setIsEditing(true);
    setNewQuantity(product.availableQuantity.toString());
    setReason('');
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setNewQuantity(product.availableQuantity.toString());
    setReason('');
  };

  const handleSaveClick = () => {
    const quantity = parseInt(newQuantity);
    if (isNaN(quantity) || quantity < 0) {
      alert('Quantidade deve ser um número válido maior ou igual a zero');
      return;
    }

    if (quantity !== product.availableQuantity) {
      setShowConfirmDialog(true);
    } else {
      handleCancelEdit();
    }
  };

  const handleConfirmUpdate = async () => {
    const quantity = parseInt(newQuantity);
    try {
      await onUpdateStock(product.id, quantity, reason || 'Atualização manual');
      setIsEditing(false);
      setShowConfirmDialog(false);
      setReason('');
    } catch (error) {
      console.error('Erro ao atualizar estoque:', error);
    }
  };

  const handleLoadHistory = async () => {
    if (!onGetHistory) return;
    
    setIsLoadingHistory(true);
    try {
      const historyData = await onGetHistory(product.id);
      setHistory(historyData);
      setShowHistoryDialog(true);
    } catch (error) {
      console.error('Erro ao carregar histórico:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const stockStatus = getStockStatus();
  const StatusIcon = stockStatus.icon;
  const quantityDiff = parseInt(newQuantity) - product.availableQuantity;

  return (
    <>
      <Card className={`${stockStatus.bgColor} border-l-4 ${
        stockStatus.color === 'destructive' ? 'border-l-red-500' :
        stockStatus.color === 'warning' ? 'border-l-yellow-500' :
        stockStatus.color === 'secondary' ? 'border-l-blue-500' :
        'border-l-green-500'
      }`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <StatusIcon className="h-4 w-4" />
              Controle de Estoque
            </CardTitle>
            <Badge variant={stockStatus.color as any}>
              {stockStatus.status}
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Quantidade atual */}
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-sm font-medium">Quantidade Atual</Label>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-2xl font-bold">{product.availableQuantity}</span>
                <span className="text-sm text-muted-foreground">unidades</span>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              {onGetHistory && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLoadHistory}
                  disabled={isLoadingHistory}
                >
                  {isLoadingHistory ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <History className="h-4 w-4" />
                  )}
                </Button>
              )}
              
              {!isEditing && !disabled && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStartEdit}
                  disabled={isUpdating}
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Edição de estoque */}
          {isEditing && (
            <div className="space-y-3 p-3 bg-background rounded-lg border">
              <div className="space-y-2">
                <Label htmlFor="newQuantity">Nova Quantidade</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="newQuantity"
                    type="number"
                    value={newQuantity}
                    onChange={(e) => setNewQuantity(e.target.value)}
                    min="0"
                    className="w-24"
                  />
                  {quantityDiff !== 0 && (
                    <div className={`flex items-center gap-1 text-sm ${
                      quantityDiff > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {quantityDiff > 0 ? (
                        <TrendingUp className="h-3 w-3" />
                      ) : (
                        <TrendingDown className="h-3 w-3" />
                      )}
                      <span>{quantityDiff > 0 ? '+' : ''}{quantityDiff}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason">Motivo (opcional)</Label>
                <Textarea
                  id="reason"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  placeholder="Ex: Reposição de estoque, Ajuste de inventário..."
                  className="h-20"
                />
              </div>

              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  onClick={handleSaveClick}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Salvar
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancelEdit}
                  disabled={isUpdating}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancelar
                </Button>
              </div>
            </div>
          )}

          {/* Métricas de estoque */}
          {product.metrics && (
            <div className="grid grid-cols-2 gap-4 pt-3 border-t">
              {product.metrics.stockCoverageDays && (
                <div>
                  <Label className="text-xs text-muted-foreground">Cobertura</Label>
                  <div className="text-sm font-medium">
                    {product.metrics.stockCoverageDays} dias
                  </div>
                </div>
              )}
              
              {product.metrics.averageDailySales && (
                <div>
                  <Label className="text-xs text-muted-foreground">Venda Média</Label>
                  <div className="text-sm font-medium">
                    {product.metrics.averageDailySales.toFixed(1)}/dia
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Alertas */}
          {product.alerts && product.alerts.length > 0 && (
            <div className="pt-3 border-t">
              <Label className="text-xs text-muted-foreground mb-2 block">Alertas</Label>
              <div className="space-y-1">
                {product.alerts.slice(0, 2).map((alert, index) => (
                  <div key={index} className="flex items-center gap-2 text-xs">
                    <AlertTriangle className="h-3 w-3 text-amber-500" />
                    <span>{alert.message}</span>
                  </div>
                ))}
                {product.alerts.length > 2 && (
                  <div className="text-xs text-muted-foreground">
                    +{product.alerts.length - 2} mais alertas
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog de confirmação */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirmar Atualização de Estoque</AlertDialogTitle>
            <AlertDialogDescription>
              Você está alterando o estoque de <strong>{product.availableQuantity}</strong> para{' '}
              <strong>{newQuantity}</strong> unidades.
              {quantityDiff !== 0 && (
                <span className={quantityDiff > 0 ? 'text-green-600' : 'text-red-600'}>
                  {' '}({quantityDiff > 0 ? '+' : ''}{quantityDiff} unidades)
                </span>
              )}
              <br />
              Esta ação será sincronizada com o Mercado Livre.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmUpdate}>
              Confirmar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Dialog de histórico */}
      <Dialog open={showHistoryDialog} onOpenChange={setShowHistoryDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Histórico de Estoque</DialogTitle>
          </DialogHeader>
          <div className="max-h-96 overflow-y-auto">
            {history.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Nenhum histórico encontrado</p>
              </div>
            ) : (
              <div className="space-y-3">
                {history.map((entry) => (
                  <div key={entry.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {entry.previousQuantity} → {entry.newQuantity}
                        </span>
                        <span className={`text-sm ${
                          entry.change > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          ({entry.change > 0 ? '+' : ''}{entry.change})
                        </span>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {entry.reason}
                      </div>
                      {entry.userName && (
                        <div className="text-xs text-muted-foreground">
                          por {entry.userName}
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {new Date(entry.createdAt).toLocaleString()}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
