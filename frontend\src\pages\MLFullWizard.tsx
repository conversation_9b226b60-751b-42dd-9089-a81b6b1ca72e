import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Progress } from '../components/ui/progress';
import { Badge } from '../components/ui/badge';
import { Card, CardContent } from '../components/ui/card';
import {
  ArrowLeft,
  Package,
  Settings,
  FileText,
  CheckCircle,
  AlertCircle,
  Home
} from 'lucide-react';
import { useMLFullWizardStore } from '../store/mlFullWizardStore';
import ProductSelectionStep from '../components/ml-full/ProductSelectionStep';
import QuantityConfigurationStep from '../components/ml-full/QuantityConfigurationStep';
import ReviewStep from '../components/ml-full/ReviewStep';
import CompletionStep from '../components/ml-full/CompletionStep';

const stepIcons = {
  1: Package,
  2: Settings,
  3: FileText,
  4: CheckCircle,
};

export default function MLFullWizardPage() {
  const navigate = useNavigate();
  
  const {
    currentStep,
    steps,
    error,
    isLoadingProducts,
    selectedProducts,
    resetWizard,
    loadProducts,
    clearError,
    getStepProgress,
    nextStep,
    previousStep,
  } = useMLFullWizardStore();

  // Load products when page loads
  useEffect(() => {
    loadProducts();
  }, [loadProducts]);

  // Handle browser back button and navigation
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (selectedProducts.length > 0) {
        e.preventDefault();
        e.returnValue = 'Você tem produtos selecionados. Tem certeza que deseja sair?';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [selectedProducts.length]);

  const handleGoBack = () => {
    if (selectedProducts.length > 0) {
      const confirmed = window.confirm(
        'Você tem produtos selecionados. Tem certeza que deseja voltar? Todo o progresso será perdido.'
      );
      if (!confirmed) return;
    }
    
    resetWizard();
    navigate(-1); // Go back to previous page
  };

  const handleGoHome = () => {
    if (selectedProducts.length > 0) {
      const confirmed = window.confirm(
        'Você tem produtos selecionados. Tem certeza que deseja ir para o dashboard? Todo o progresso será perdido.'
      );
      if (!confirmed) return;
    }
    
    resetWizard();
    navigate('/dashboard');
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <ProductSelectionStep onNext={nextStep} onBack={previousStep} />;
      case 2:
        return <QuantityConfigurationStep onNext={nextStep} onBack={previousStep} />;
      case 3:
        return <ReviewStep onNext={nextStep} onBack={previousStep} />;
      case 4:
        return <CompletionStep onNext={nextStep} onBack={previousStep} />;
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleGoBack}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Voltar
              </Button>
              
              <div className="h-6 w-px bg-border" />
              
              <div>
                <h1 className="text-xl font-semibold">Wizard ML Full</h1>
                <p className="text-sm text-muted-foreground">
                  Gere planilhas para envio de produtos ao Mercado Livre Full
                </p>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={handleGoHome}
              className="flex items-center gap-2"
            >
              <Home className="h-4 w-4" />
              Dashboard
            </Button>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="border-b bg-muted/30">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Progresso:</span>
              <span className="text-sm text-muted-foreground">
                Passo {currentStep} de {steps.length}
              </span>
            </div>
            <div className="text-sm text-muted-foreground">
              {getStepProgress()}% concluído
            </div>
          </div>
          
          <Progress value={getStepProgress()} className="h-2 mb-4" />
          
          {/* Step indicators */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const StepIcon = stepIcons[step.id as keyof typeof stepIcons];
              const isActive = step.id === currentStep;
              const isCompleted = step.isCompleted;
              const isPast = step.id < currentStep;
              
              return (
                <div key={step.id} className="flex items-center">
                  <div className="flex flex-col items-center">
                    <div
                      className={`
                        flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                        ${isActive 
                          ? 'border-primary bg-primary text-primary-foreground' 
                          : isCompleted || isPast
                          ? 'border-green-500 bg-green-500 text-white'
                          : 'border-muted-foreground bg-background text-muted-foreground'
                        }
                      `}
                    >
                      {isCompleted || isPast ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        <StepIcon className="h-5 w-5" />
                      )}
                    </div>
                    <div className="mt-2 text-center">
                      <div className={`text-xs font-medium ${isActive ? 'text-primary' : 'text-muted-foreground'}`}>
                        {step.title}
                      </div>
                      <div className="text-xs text-muted-foreground max-w-24 truncate">
                        {step.description}
                      </div>
                    </div>
                  </div>
                  
                  {index < steps.length - 1 && (
                    <div 
                      className={`
                        flex-1 h-px mx-4 transition-colors
                        ${isPast || (isActive && index < currentStep - 1)
                          ? 'bg-green-500' 
                          : 'bg-border'
                        }
                      `} 
                    />
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="border-b bg-destructive/10">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center gap-3">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div className="flex-1">
                <p className="text-sm font-medium text-destructive">Erro</p>
                <p className="text-sm text-destructive/80">{error}</p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="text-destructive hover:text-destructive"
              >
                Fechar
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-7xl mx-auto">
          {renderStepContent()}
        </div>
      </div>

      {/* Footer */}
      <div className="border-t bg-muted/30 mt-8">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div>
              Sistema de Estoque - ML Full Wizard
            </div>
            <div className="flex items-center gap-4">
              <span>Passo {currentStep} de {steps.length}</span>
              {selectedProducts.length > 0 && (
                <Badge variant="secondary">
                  {selectedProducts.length} produto{selectedProducts.length !== 1 ? 's' : ''} selecionado{selectedProducts.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
