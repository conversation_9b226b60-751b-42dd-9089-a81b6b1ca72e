-- =====================================================
-- MAGNOW - COMPLETE DATABASE SCHEMA MIGRATION
-- =====================================================
-- 
-- Este arquivo contém todas as migrações necessárias para implementar
-- o esquema completo da aplicação React Magnow baseado na auditoria
-- das 9 páginas principais: Dashboard, Products, Stock, Reports, 
-- UserManagement, Logs, Settings, Envios e Monitoring.
--
-- Estrutura: 17 tabelas organizadas em 5 fases respeitando dependências
-- Compatibilidade: PostgreSQL 12+
-- Execução: psql -d magnow -f complete_schema.sql
--
-- =====================================================

-- Configurações iniciais
SET client_encoding = 'UTF8';
SET timezone = 'America/Sao_Paulo';

-- Extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =====================================================
-- FASE 1: ESTRUTURAS BASE (Multi-tenancy e Usuários)
-- =====================================================

-- 1. TENANTS - Multi-tenancy base
-- Suporta: Settings, UserManagement, Dashboard (filtros por tenant)
CREATE TABLE IF NOT EXISTS tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    cnpj VARCHAR(18),
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address JSONB DEFAULT '{}',
    contact JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{
        "maxUsers": 50,
        "maxProducts": 10000,
        "features": {
            "mlIntegration": true,
            "advancedReports": true,
            "apiAccess": false,
            "customBranding": false
        },
        "billing": {
            "plan": "professional",
            "billingCycle": "monthly"
        }
    }',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_tenants_email_valid CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_tenants_cnpj_format CHECK (cnpj IS NULL OR cnpj ~ '^\d{2}\.\d{3}\.\d{3}/\d{4}-\d{2}$')
);

-- 2. DEPARTMENTS - Referência para usuários
-- Suporta: UserManagement (filtros e organização)
CREATE TABLE IF NOT EXISTS departments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. USERS - Usuários do sistema
-- Suporta: UserManagement, Logs (auditoria), Settings (perfis)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'user',
    phone VARCHAR(20),
    avatar VARCHAR(500),
    department_id UUID REFERENCES departments(id) ON DELETE SET NULL,
    position VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_users_role_valid CHECK (role IN ('admin', 'manager', 'user', 'viewer')),
    CONSTRAINT chk_users_email_valid CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT chk_users_phone_format CHECK (phone IS NULL OR phone ~ '^\+?[\d\s\(\)-]{10,20}$'),
    UNIQUE(tenant_id, email)
);

-- 4. USER_PROFILES - Perfis e preferências
-- Suporta: Settings (configurações pessoais)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo',
    language VARCHAR(10) DEFAULT 'pt-BR',
    date_format VARCHAR(20) DEFAULT 'DD/MM/YYYY',
    default_view VARCHAR(10) DEFAULT 'grid',
    notifications JSONB DEFAULT '{
        "email": true,
        "stockAlerts": true,
        "salesReports": false
    }',
    preferences JSONB DEFAULT '{
        "theme": "system",
        "currency": "BRL"
    }',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_profiles_language_valid CHECK (language IN ('pt-BR', 'en-US', 'es-ES')),
    CONSTRAINT chk_profiles_date_format_valid CHECK (date_format IN ('DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD')),
    CONSTRAINT chk_profiles_view_valid CHECK (default_view IN ('grid', 'list')),
    UNIQUE(user_id)
);

-- =====================================================
-- FASE 2: INTEGRAÇÃO MERCADO LIVRE
-- =====================================================

-- 5. ML_ACCOUNTS - Contas do Mercado Livre
-- Suporta: Products (sincronização), Dashboard (métricas ML)
CREATE TABLE IF NOT EXISTS ml_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    ml_user_id VARCHAR(255) NOT NULL,
    nickname VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMP,
    sync_config JSONB DEFAULT '{
        "autoSync": true,
        "syncFrequency": "hourly",
        "syncTypes": {
            "products": true,
            "orders": true,
            "stock": true
        }
    }',
    is_active BOOLEAN DEFAULT true,
    last_sync_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(tenant_id, ml_user_id)
);

-- 6. WAREHOUSES - Armazéns
-- Suporta: Stock (localização), Reports (filtros por armazém)
CREATE TABLE IF NOT EXISTS warehouses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(50) NOT NULL,
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    capacity INTEGER CHECK (capacity IS NULL OR capacity > 0),
    current_utilization INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_warehouses_utilization CHECK (current_utilization >= 0),
    UNIQUE(tenant_id, code)
);

-- 7. CATEGORIES - Categorias de produtos ML
-- Suporta: Products (classificação), Dashboard (métricas por categoria)
CREATE TABLE IF NOT EXISTS categories (
    id VARCHAR(255) PRIMARY KEY, -- ML category ID
    name VARCHAR(255) NOT NULL,
    path_from_root JSONB DEFAULT '[]',
    parent_id VARCHAR(255) REFERENCES categories(id),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- FASE 3: PRODUTOS E ESTOQUE (Core Business)
-- =====================================================

-- 8. PRODUCTS - Produtos sincronizados do ML
-- Suporta: Products, Dashboard, Stock (base de dados principal)
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    ml_account_id UUID NOT NULL REFERENCES ml_accounts(id) ON DELETE CASCADE,
    ml_id VARCHAR(255) NOT NULL,
    sku VARCHAR(255),
    title TEXT NOT NULL,
    description TEXT,
    category_id VARCHAR(255) REFERENCES categories(id),
    category_name VARCHAR(255),
    price DECIMAL(12,2) NOT NULL,
    original_price DECIMAL(12,2),
    available_quantity INTEGER DEFAULT 0,
    sold_quantity INTEGER DEFAULT 0,
    condition VARCHAR(20) DEFAULT 'new',
    status VARCHAR(20) DEFAULT 'active',
    listing_type VARCHAR(50),
    permalink VARCHAR(500),
    thumbnail VARCHAR(500),
    pictures JSONB DEFAULT '[]',
    attributes JSONB DEFAULT '{}',
    variations JSONB DEFAULT '[]',
    shipping JSONB DEFAULT '{}',
    brand VARCHAR(255),
    warranty VARCHAR(255),
    sync_status JSONB DEFAULT '{
        "lastSync": null,
        "isOutOfSync": false,
        "pendingChanges": []
    }',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_products_price_positive CHECK (price >= 0),
    CONSTRAINT chk_products_stock_positive CHECK (available_quantity >= 0),
    CONSTRAINT chk_products_sold_positive CHECK (sold_quantity >= 0),
    CONSTRAINT chk_products_condition_valid CHECK (condition IN ('new', 'used')),
    CONSTRAINT chk_products_status_valid CHECK (status IN ('active', 'paused', 'closed', 'under_review')),
    UNIQUE(tenant_id, ml_id)
);

-- 9. STOCK_CALCULATIONS - Cálculos automáticos de estoque
-- Suporta: Dashboard (gaps), Stock (métricas), Products (indicadores)
CREATE TABLE IF NOT EXISTS stock_calculations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    current_stock INTEGER NOT NULL DEFAULT 0,
    ideal_stock INTEGER NOT NULL DEFAULT 0,
    gap INTEGER NOT NULL DEFAULT 0,
    average_sales DECIMAL(10,2) NOT NULL DEFAULT 0,
    coverage_days INTEGER NOT NULL DEFAULT 0,
    safety_stock INTEGER DEFAULT 0,
    units_in_transit INTEGER DEFAULT 0,
    reorder_point INTEGER DEFAULT 0,
    stock_turnover DECIMAL(8,2),
    profit_margin DECIMAL(5,2),
    priority VARCHAR(20) DEFAULT 'low',
    calculation_params JSONB DEFAULT '{
        "period_days": 30,
        "seasonality_factor": 1.0,
        "growth_factor": 1.0
    }',
    last_calculated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_stock_calc_current_positive CHECK (current_stock >= 0),
    CONSTRAINT chk_stock_calc_ideal_positive CHECK (ideal_stock >= 0),
    CONSTRAINT chk_stock_calc_coverage_valid CHECK (coverage_days >= 0 AND coverage_days <= 365),
    CONSTRAINT chk_stock_calc_priority_valid CHECK (priority IN ('low', 'medium', 'high', 'critical')),
    UNIQUE(product_id)
);

-- 10. STOCK_ALERTS - Alertas automáticos de estoque
-- Suporta: Dashboard (alertas), Stock (notificações)
CREATE TABLE IF NOT EXISTS stock_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    threshold INTEGER,
    current_value INTEGER,
    metadata JSONB DEFAULT '{}',
    is_read BOOLEAN DEFAULT false,
    is_resolved BOOLEAN DEFAULT false,
    resolved_at TIMESTAMP,
    resolved_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_alerts_type_valid CHECK (type IN ('gap_critical', 'stock_low', 'overstock', 'no_sales', 'sync_error')),
    CONSTRAINT chk_alerts_severity_valid CHECK (severity IN ('low', 'medium', 'high', 'critical'))
);

-- 11. STOCK_HISTORY - Histórico de movimentações
-- Suporta: Stock (auditoria), Logs (rastreabilidade)
CREATE TABLE IF NOT EXISTS stock_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    warehouse_id UUID REFERENCES warehouses(id) ON DELETE SET NULL,
    previous_quantity INTEGER NOT NULL,
    new_quantity INTEGER NOT NULL,
    change INTEGER NOT NULL,
    reason TEXT NOT NULL,
    transaction_type VARCHAR(50) DEFAULT 'adjustment',
    reference_id VARCHAR(255), -- ID de referência (venda, compra, etc.)
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_history_quantities_valid CHECK (previous_quantity >= 0 AND new_quantity >= 0),
    CONSTRAINT chk_history_transaction_valid CHECK (transaction_type IN ('adjustment', 'sale', 'purchase', 'transfer', 'loss', 'sync'))
);

-- =====================================================
-- FASE 4: VENDAS E RELATÓRIOS (Analytics)
-- =====================================================

-- 12. SALES - Histórico de vendas
-- Suporta: Dashboard (métricas), Reports (relatórios de vendas)
CREATE TABLE IF NOT EXISTS sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    ml_order_id VARCHAR(255) NOT NULL,
    ml_item_id VARCHAR(255),
    buyer_id VARCHAR(255),
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'BRL',
    sale_date TIMESTAMP NOT NULL,
    status VARCHAR(50) NOT NULL,
    payment_method VARCHAR(100),
    shipping_method VARCHAR(100),
    fees JSONB DEFAULT '{}',
    taxes JSONB DEFAULT '{}',
    profit_margin DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_sales_quantity_positive CHECK (quantity > 0),
    CONSTRAINT chk_sales_prices_positive CHECK (unit_price >= 0 AND total_price >= 0),
    CONSTRAINT chk_sales_currency_valid CHECK (currency IN ('BRL', 'USD', 'EUR', 'ARS'))
);

-- 13. SALES_METRICS - Métricas agregadas de vendas
-- Suporta: Dashboard (gráficos), Reports (performance)
CREATE TABLE IF NOT EXISTS sales_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    category_id VARCHAR(255) REFERENCES categories(id),
    period_type VARCHAR(20) NOT NULL,
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    total_sales INTEGER DEFAULT 0,
    total_revenue DECIMAL(12,2) DEFAULT 0,
    total_units INTEGER DEFAULT 0,
    average_price DECIMAL(12,2) DEFAULT 0,
    unique_buyers INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2),
    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_metrics_period_valid CHECK (period_type IN ('daily', 'weekly', 'monthly', 'yearly')),
    CONSTRAINT chk_metrics_dates_valid CHECK (period_start <= period_end),
    CONSTRAINT chk_metrics_values_positive CHECK (total_sales >= 0 AND total_revenue >= 0 AND total_units >= 0),
    UNIQUE(tenant_id, product_id, period_type, period_start)
);

-- 14. SPREADSHEETS - Planilhas geradas
-- Suporta: Reports (histórico), Envios (arquivos ML Full)
CREATE TABLE IF NOT EXISTS spreadsheets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255),
    file_path VARCHAR(500),
    format VARCHAR(10) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    ml_status VARCHAR(20),
    total_products INTEGER NOT NULL DEFAULT 0,
    warehouse_ids JSONB DEFAULT '[]',
    generated_by UUID NOT NULL REFERENCES users(id) ON DELETE SET NULL,
    generation_params JSONB DEFAULT '{}',
    tags JSONB DEFAULT '[]',
    notes TEXT,
    file_size BIGINT DEFAULT 0,
    download_url VARCHAR(500),
    download_count INTEGER DEFAULT 0,
    expires_at TIMESTAMP,
    ml_uploaded_at TIMESTAMP,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_spreadsheets_format_valid CHECK (format IN ('xlsx', 'csv', 'pdf')),
    CONSTRAINT chk_spreadsheets_status_valid CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'expired')),
    CONSTRAINT chk_spreadsheets_ml_status_valid CHECK (ml_status IS NULL OR ml_status IN ('pending', 'processing', 'completed', 'failed')),
    CONSTRAINT chk_spreadsheets_products_positive CHECK (total_products >= 0),
    CONSTRAINT chk_spreadsheets_size_positive CHECK (file_size >= 0)
);

-- =====================================================
-- FASE 5: ENVIOS E SISTEMA (Operações Avançadas)
-- =====================================================

-- 15. SHIPMENTS - Envios ML Full
-- Suporta: Envios (gestão completa)
CREATE TABLE IF NOT EXISTS shipments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    created_by UUID NOT NULL REFERENCES users(id) ON DELETE SET NULL,
    spreadsheet_id UUID REFERENCES spreadsheets(id) ON DELETE SET NULL,
    inbound_id VARCHAR(255), -- ID do ML Full
    status VARCHAR(20) DEFAULT 'pending',
    total_quantity INTEGER NOT NULL DEFAULT 0,
    total_value DECIMAL(12,2) NOT NULL DEFAULT 0,
    product_count INTEGER NOT NULL DEFAULT 0,
    warehouse_id UUID REFERENCES warehouses(id),
    generated_file JSONB,
    receipt_pdf JSONB,
    tracking_info JSONB DEFAULT '{}',
    preparation_instructions TEXT,
    notes TEXT,
    expected_delivery DATE,
    collection_date DATE,
    delivered_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_shipments_status_valid CHECK (status IN ('pending', 'received', 'processing', 'in_transit', 'delivered', 'rejected', 'cancelled')),
    CONSTRAINT chk_shipments_quantities_positive CHECK (total_quantity >= 0 AND total_value >= 0 AND product_count >= 0),
    CONSTRAINT chk_shipments_dates_valid CHECK (expected_delivery IS NULL OR collection_date IS NULL OR collection_date <= expected_delivery)
);

-- 16. SHIPMENT_PRODUCTS - Produtos por envio (N:M)
-- Suporta: Envios (detalhamento de produtos)
CREATE TABLE IF NOT EXISTS shipment_products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    shipment_id UUID NOT NULL REFERENCES shipments(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(12,2) NOT NULL,
    total_price DECIMAL(12,2) NOT NULL,
    sku VARCHAR(255),
    title VARCHAR(500),
    barcode VARCHAR(100),
    dimensions JSONB DEFAULT '{}', -- height, width, depth, weight
    warehouse_location VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_shipment_products_quantity_positive CHECK (quantity > 0),
    CONSTRAINT chk_shipment_products_prices_positive CHECK (unit_price >= 0 AND total_price >= 0),
    UNIQUE(shipment_id, product_id)
);

-- 17. LOG_ENTRIES - Logs de auditoria
-- Suporta: Logs (auditoria completa), todas as páginas (rastreabilidade)
CREATE TABLE IF NOT EXISTS log_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    user_name VARCHAR(255),
    user_email VARCHAR(255),
    session_id VARCHAR(255),
    action VARCHAR(50) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    resource_id VARCHAR(255),
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    request_id VARCHAR(255),
    severity VARCHAR(20) DEFAULT 'info',
    category VARCHAR(50) DEFAULT 'data',
    duration_ms INTEGER,
    error_code VARCHAR(50),
    error_message TEXT,

    CONSTRAINT chk_logs_action_valid CHECK (action IN ('LOGIN', 'LOGOUT', 'CREATE', 'UPDATE', 'DELETE', 'VIEW', 'EXPORT', 'IMPORT', 'SYNC', 'BACKUP', 'RESTORE', 'CONFIG_CHANGE', 'PASSWORD_CHANGE', 'PERMISSION_CHANGE')),
    CONSTRAINT chk_logs_severity_valid CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    CONSTRAINT chk_logs_category_valid CHECK (category IN ('authentication', 'authorization', 'data', 'system', 'security', 'integration', 'configuration')),
    CONSTRAINT chk_logs_duration_positive CHECK (duration_ms IS NULL OR duration_ms >= 0)
);

-- 18. SESSIONS - Sessões de usuário
-- Suporta: Logs (rastreamento), Settings (segurança)
CREATE TABLE IF NOT EXISTS sessions (
    id VARCHAR(255) PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    ip_address INET,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    location_info JSONB DEFAULT '{}',
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    logout_reason VARCHAR(50),

    CONSTRAINT chk_sessions_dates_valid CHECK (started_at <= last_activity AND last_activity <= expires_at),
    CONSTRAINT chk_sessions_logout_reason_valid CHECK (logout_reason IS NULL OR logout_reason IN ('manual', 'timeout', 'forced', 'expired'))
);

-- 19. FILES - Metadados de arquivos
-- Suporta: Sistema de arquivos (tracking completo)
CREATE TABLE IF NOT EXISTS files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    uploaded_by UUID NOT NULL REFERENCES users(id) ON DELETE SET NULL,
    original_name VARCHAR(255) NOT NULL,
    stored_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    checksum VARCHAR(64),
    is_public BOOLEAN DEFAULT false,
    download_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP,
    expires_at TIMESTAMP,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT chk_files_type_valid CHECK (file_type IN ('avatar', 'document', 'spreadsheet', 'temp')),
    CONSTRAINT chk_files_size_positive CHECK (file_size > 0),
    CONSTRAINT chk_files_download_count_positive CHECK (download_count >= 0),
    CONSTRAINT chk_files_expires_future CHECK (expires_at IS NULL OR expires_at > created_at)
);

-- =====================================================
-- ÍNDICES DE PERFORMANCE
-- =====================================================

-- Índices para consultas frequentes baseados na auditoria das páginas

-- USERS - UserManagement, Logs, Settings
CREATE INDEX IF NOT EXISTS idx_users_tenant_email ON users(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_users_tenant_role ON users(tenant_id, role) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login_at DESC NULLS LAST);
CREATE INDEX IF NOT EXISTS idx_users_department ON users(department_id) WHERE is_active = true;

-- PRODUCTS - Products, Dashboard, Stock
CREATE INDEX IF NOT EXISTS idx_products_tenant_status ON products(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_products_ml_account ON products(ml_account_id);
CREATE INDEX IF NOT EXISTS idx_products_category ON products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_title_search ON products USING gin(to_tsvector('portuguese', title));
CREATE INDEX IF NOT EXISTS idx_products_sku ON products(sku) WHERE sku IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_sync_status ON products USING gin(sync_status);

-- STOCK_CALCULATIONS - Dashboard, Stock
CREATE INDEX IF NOT EXISTS idx_stock_calc_tenant_priority ON stock_calculations(tenant_id, priority, last_calculated DESC);
CREATE INDEX IF NOT EXISTS idx_stock_calc_gap ON stock_calculations(gap DESC) WHERE gap > 0;
CREATE INDEX IF NOT EXISTS idx_stock_calc_coverage ON stock_calculations(coverage_days) WHERE coverage_days < 30;

-- STOCK_ALERTS - Dashboard, Stock
CREATE INDEX IF NOT EXISTS idx_stock_alerts_unread ON stock_alerts(product_id, is_read, severity) WHERE is_resolved = false;
CREATE INDEX IF NOT EXISTS idx_stock_alerts_created ON stock_alerts(created_at DESC);

-- LOG_ENTRIES - Logs
CREATE INDEX IF NOT EXISTS idx_log_entries_tenant_time ON log_entries(tenant_id, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_log_entries_user_action ON log_entries(user_id, action, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_log_entries_resource ON log_entries(resource, resource_id);
CREATE INDEX IF NOT EXISTS idx_log_entries_severity ON log_entries(severity, timestamp DESC) WHERE severity IN ('error', 'critical');
CREATE INDEX IF NOT EXISTS idx_log_entries_session ON log_entries(session_id);

-- SALES - Dashboard, Reports
CREATE INDEX IF NOT EXISTS idx_sales_product_date ON sales(product_id, sale_date DESC);
CREATE INDEX IF NOT EXISTS idx_sales_date_status ON sales(sale_date DESC, status);
CREATE INDEX IF NOT EXISTS idx_sales_ml_order ON sales(ml_order_id);

-- SHIPMENTS - Envios
CREATE INDEX IF NOT EXISTS idx_shipments_tenant_status ON shipments(tenant_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_shipments_created_by ON shipments(created_by, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_shipments_inbound_id ON shipments(inbound_id) WHERE inbound_id IS NOT NULL;

-- SPREADSHEETS - Reports
CREATE INDEX IF NOT EXISTS idx_spreadsheets_tenant_status ON spreadsheets(tenant_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_spreadsheets_generated_by ON spreadsheets(generated_by, created_at DESC);

-- SESSIONS - Logs, Settings
CREATE INDEX IF NOT EXISTS idx_sessions_user_active ON sessions(user_id, is_active, last_activity DESC);
CREATE INDEX IF NOT EXISTS idx_sessions_expires ON sessions(expires_at) WHERE is_active = true;

-- FILES - Sistema de arquivos
CREATE INDEX IF NOT EXISTS idx_files_tenant_type ON files(tenant_id, file_type);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by ON files(uploaded_by, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_files_checksum ON files(checksum) WHERE checksum IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_files_expires ON files(expires_at) WHERE expires_at IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_files_public ON files(is_public, created_at DESC) WHERE is_public = true;
CREATE INDEX IF NOT EXISTS idx_files_size ON files(file_size DESC);
CREATE INDEX IF NOT EXISTS idx_files_accessed ON files(last_accessed_at DESC) WHERE last_accessed_at IS NOT NULL;

-- =====================================================
-- TRIGGERS E FUNÇÕES
-- =====================================================

-- Função para atualizar updated_at automaticamente
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers para updated_at
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ml_accounts_updated_at BEFORE UPDATE ON ml_accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_warehouses_updated_at BEFORE UPDATE ON warehouses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sales_updated_at BEFORE UPDATE ON sales
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_spreadsheets_updated_at BEFORE UPDATE ON spreadsheets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shipments_updated_at BEFORE UPDATE ON shipments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_files_updated_at BEFORE UPDATE ON files
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Função para log automático de mudanças críticas
CREATE OR REPLACE FUNCTION log_critical_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Log para mudanças em produtos
    IF TG_TABLE_NAME = 'products' THEN
        IF OLD.status != NEW.status OR OLD.available_quantity != NEW.available_quantity THEN
            INSERT INTO log_entries (
                tenant_id, action, resource, resource_id,
                details, severity, category
            ) VALUES (
                NEW.tenant_id, 'UPDATE', 'products', NEW.id::text,
                jsonb_build_object(
                    'old_status', OLD.status,
                    'new_status', NEW.status,
                    'old_quantity', OLD.available_quantity,
                    'new_quantity', NEW.available_quantity
                ),
                'info', 'data'
            );
        END IF;
    END IF;

    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para log automático
CREATE TRIGGER log_products_changes AFTER UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION log_critical_changes();

-- =====================================================
-- DADOS DE SEED
-- =====================================================

-- Departamentos padrão
INSERT INTO departments (name, description) VALUES
('Vendas', 'Equipe de vendas e atendimento ao cliente'),
('Estoque', 'Controle e gestão de estoque'),
('TI', 'Tecnologia da informação e sistemas'),
('Financeiro', 'Gestão financeira e contábil'),
('Marketing', 'Marketing digital e publicidade'),
('Operações', 'Operações e logística')
ON CONFLICT (name) DO NOTHING;

-- Categorias principais do Mercado Livre
INSERT INTO categories (id, name, path_from_root) VALUES
('MLB1051', 'Celulares e Telefones', '["Eletrônicos, Áudio e Vídeo", "Celulares e Telefones"]'),
('MLB1648', 'Informática', '["Eletrônicos, Áudio e Vídeo", "Computação"]'),
('MLB1276', 'Esportes e Fitness', '["Esportes e Fitness"]'),
('MLB1430', 'Roupas e Calçados', '["Moda"]'),
('MLB1574', 'Casa, Móveis e Decoração', '["Casa, Móveis e Jardim"]'),
('MLB1132', 'Brinquedos e Hobbies', '["Brinquedos e Hobbies"]'),
('MLB1071', 'Animais', '["Animais"]'),
('MLB1367', 'Beleza e Cuidado Pessoal', '["Beleza e Cuidado Pessoal"]'),
('MLB1384', 'Bebês', '["Bebês"]'),
('MLB1196', 'Música, Filmes e Seriados', '["Música, Filmes e Seriados"]')
ON CONFLICT (id) DO NOTHING;

-- Tenant demo para desenvolvimento
INSERT INTO tenants (id, name, email, cnpj, settings) VALUES
('demo-tenant-001', 'Magnow Demo', '<EMAIL>', '12.345.678/0001-90', '{
    "maxUsers": 100,
    "maxProducts": 50000,
    "features": {
        "mlIntegration": true,
        "advancedReports": true,
        "apiAccess": true,
        "customBranding": false
    },
    "billing": {
        "plan": "professional",
        "billingCycle": "monthly"
    }
}')
ON CONFLICT (id) DO NOTHING;

-- Usuário administrador inicial
INSERT INTO users (id, tenant_id, email, name, password_hash, role) VALUES
('admin-user-001', 'demo-tenant-001', '<EMAIL>', 'Administrador Sistema',
 '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm', 'admin')
ON CONFLICT (tenant_id, email) DO NOTHING;

-- Perfil do usuário admin
INSERT INTO user_profiles (user_id, notifications, preferences) VALUES
('admin-user-001', '{
    "email": true,
    "stockAlerts": true,
    "salesReports": true
}', '{
    "theme": "system",
    "currency": "BRL"
}')
ON CONFLICT (user_id) DO NOTHING;

-- Armazém padrão
INSERT INTO warehouses (tenant_id, name, code, address, city, state, is_default) VALUES
('demo-tenant-001', 'Armazém Principal', 'MAIN',
 'Rua das Empresas, 123', 'São Paulo', 'SP', true),
('demo-tenant-001', 'Armazém Secundário', 'SEC',
 'Av. Logística, 456', 'Guarulhos', 'SP', false)
ON CONFLICT (tenant_id, code) DO NOTHING;

-- =====================================================
-- VIEWS PARA CONSULTAS OTIMIZADAS
-- =====================================================

-- View para produtos com dados de estoque consolidados
-- Suporta: Dashboard, Products, Stock (consultas frequentes)
CREATE OR REPLACE VIEW products_with_stock AS
SELECT
    p.*,
    sc.current_stock,
    sc.ideal_stock,
    sc.gap,
    sc.average_sales,
    sc.coverage_days,
    sc.priority,
    sc.last_calculated,
    COALESCE(alert_counts.total_alerts, 0) as alert_count,
    COALESCE(alert_counts.critical_alerts, 0) as critical_alerts,
    CASE
        WHEN sc.gap > 0 THEN 'gap'
        WHEN sc.current_stock <= sc.safety_stock THEN 'low'
        WHEN sc.current_stock > sc.ideal_stock * 1.5 THEN 'overstock'
        ELSE 'normal'
    END as stock_status
FROM products p
LEFT JOIN stock_calculations sc ON p.id = sc.product_id
LEFT JOIN (
    SELECT
        product_id,
        COUNT(*) as total_alerts,
        COUNT(*) FILTER (WHERE severity = 'critical') as critical_alerts
    FROM stock_alerts
    WHERE is_resolved = false
    GROUP BY product_id
) alert_counts ON p.id = alert_counts.product_id;

-- View para métricas do dashboard
-- Suporta: Dashboard (estatísticas principais)
CREATE OR REPLACE VIEW dashboard_metrics AS
SELECT
    p.tenant_id,
    COUNT(*) as total_products,
    COUNT(*) FILTER (WHERE p.status = 'active') as active_products,
    COUNT(*) FILTER (WHERE sc.gap > 0) as gap_products,
    COUNT(*) FILTER (WHERE sc.priority = 'critical') as critical_products,
    COALESCE(SUM(p.available_quantity), 0) as total_stock,
    COALESCE(AVG(p.available_quantity), 0) as avg_stock,
    COUNT(*) FILTER (WHERE sa.id IS NOT NULL AND sa.is_resolved = false) as active_alerts
FROM products p
LEFT JOIN stock_calculations sc ON p.id = sc.product_id
LEFT JOIN stock_alerts sa ON p.id = sa.product_id AND sa.is_resolved = false
GROUP BY p.tenant_id;

-- View para histórico de logs resumido
-- Suporta: Logs (consultas otimizadas)
CREATE OR REPLACE VIEW log_entries_summary AS
SELECT
    DATE(timestamp) as log_date,
    tenant_id,
    action,
    severity,
    category,
    COUNT(*) as entry_count,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(*) FILTER (WHERE severity IN ('error', 'critical')) as error_count
FROM log_entries
WHERE timestamp >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(timestamp), tenant_id, action, severity, category;

-- =====================================================
-- FUNÇÕES UTILITÁRIAS
-- =====================================================

-- Função para calcular métricas de estoque
CREATE OR REPLACE FUNCTION calculate_stock_metrics(p_product_id UUID, p_period_days INTEGER DEFAULT 30)
RETURNS TABLE (
    current_stock INTEGER,
    average_sales DECIMAL,
    ideal_stock INTEGER,
    gap INTEGER,
    coverage_days INTEGER,
    priority TEXT
) AS $$
DECLARE
    v_current_stock INTEGER;
    v_sales_data RECORD;
    v_avg_sales DECIMAL;
    v_ideal_stock INTEGER;
    v_gap INTEGER;
    v_coverage INTEGER;
    v_priority TEXT;
BEGIN
    -- Buscar estoque atual
    SELECT available_quantity INTO v_current_stock
    FROM products WHERE id = p_product_id;

    -- Calcular vendas médias
    SELECT
        COALESCE(AVG(quantity), 0) as avg_daily_sales,
        COALESCE(SUM(quantity), 0) as total_sales
    INTO v_sales_data
    FROM sales
    WHERE product_id = p_product_id
    AND sale_date >= CURRENT_DATE - INTERVAL '1 day' * p_period_days;

    v_avg_sales := v_sales_data.avg_daily_sales;

    -- Calcular estoque ideal (vendas médias * período de cobertura desejado)
    v_ideal_stock := CEIL(v_avg_sales * 30); -- 30 dias de cobertura

    -- Calcular gap
    v_gap := GREATEST(0, v_ideal_stock - v_current_stock);

    -- Calcular dias de cobertura
    IF v_avg_sales > 0 THEN
        v_coverage := FLOOR(v_current_stock / v_avg_sales);
    ELSE
        v_coverage := 999; -- Estoque infinito se não há vendas
    END IF;

    -- Determinar prioridade
    IF v_coverage <= 7 THEN
        v_priority := 'critical';
    ELSIF v_coverage <= 15 THEN
        v_priority := 'high';
    ELSIF v_coverage <= 30 THEN
        v_priority := 'medium';
    ELSE
        v_priority := 'low';
    END IF;

    RETURN QUERY SELECT v_current_stock, v_avg_sales, v_ideal_stock, v_gap, v_coverage, v_priority;
END;
$$ LANGUAGE plpgsql;

-- Função para limpar dados antigos (manutenção)
CREATE OR REPLACE FUNCTION cleanup_old_data(p_days_to_keep INTEGER DEFAULT 90)
RETURNS TEXT AS $$
DECLARE
    v_deleted_logs INTEGER;
    v_deleted_sessions INTEGER;
    v_deleted_spreadsheets INTEGER;
BEGIN
    -- Limpar logs antigos
    DELETE FROM log_entries
    WHERE timestamp < CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep
    AND severity = 'info';
    GET DIAGNOSTICS v_deleted_logs = ROW_COUNT;

    -- Limpar sessões expiradas
    DELETE FROM sessions
    WHERE expires_at < CURRENT_TIMESTAMP - INTERVAL '7 days';
    GET DIAGNOSTICS v_deleted_sessions = ROW_COUNT;

    -- Limpar planilhas antigas
    DELETE FROM spreadsheets
    WHERE created_at < CURRENT_DATE - INTERVAL '1 day' * p_days_to_keep
    AND status IN ('completed', 'failed');
    GET DIAGNOSTICS v_deleted_spreadsheets = ROW_COUNT;

    RETURN FORMAT('Limpeza concluída: %s logs, %s sessões, %s planilhas removidas',
                  v_deleted_logs, v_deleted_sessions, v_deleted_spreadsheets);
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMENTÁRIOS PARA ROLLBACK (DESCOMENTE SE NECESSÁRIO)
-- =====================================================

/*
-- ROLLBACK COMPLETO - CUIDADO: REMOVE TODOS OS DADOS!
-- Execute apenas se precisar reverter completamente a migração

-- Remover views
DROP VIEW IF EXISTS products_with_stock CASCADE;
DROP VIEW IF EXISTS dashboard_metrics CASCADE;
DROP VIEW IF EXISTS log_entries_summary CASCADE;

-- Remover funções
DROP FUNCTION IF EXISTS calculate_stock_metrics(UUID, INTEGER) CASCADE;
DROP FUNCTION IF EXISTS cleanup_old_data(INTEGER) CASCADE;
DROP FUNCTION IF EXISTS log_critical_changes() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- Remover tabelas (ordem reversa das dependências)
DROP TABLE IF EXISTS log_entries CASCADE;
DROP TABLE IF EXISTS sessions CASCADE;
DROP TABLE IF EXISTS shipment_products CASCADE;
DROP TABLE IF EXISTS shipments CASCADE;
DROP TABLE IF EXISTS spreadsheets CASCADE;
DROP TABLE IF EXISTS sales_metrics CASCADE;
DROP TABLE IF EXISTS sales CASCADE;
DROP TABLE IF EXISTS stock_history CASCADE;
DROP TABLE IF EXISTS stock_alerts CASCADE;
DROP TABLE IF EXISTS stock_calculations CASCADE;
DROP TABLE IF EXISTS products CASCADE;
DROP TABLE IF EXISTS categories CASCADE;
DROP TABLE IF EXISTS warehouses CASCADE;
DROP TABLE IF EXISTS ml_accounts CASCADE;
DROP TABLE IF EXISTS user_profiles CASCADE;
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS departments CASCADE;
DROP TABLE IF EXISTS tenants CASCADE;

-- Remover extensões (se não usadas por outras aplicações)
-- DROP EXTENSION IF EXISTS "pg_trgm";
-- DROP EXTENSION IF EXISTS "uuid-ossp";
*/

-- =====================================================
-- VERIFICAÇÃO FINAL
-- =====================================================

-- Verificar se todas as tabelas foram criadas
DO $$
DECLARE
    table_count INTEGER;
    expected_tables TEXT[] := ARRAY[
        'tenants', 'departments', 'users', 'user_profiles', 'ml_accounts',
        'warehouses', 'categories', 'products', 'stock_calculations',
        'stock_alerts', 'stock_history', 'sales', 'sales_metrics',
        'spreadsheets', 'shipments', 'shipment_products', 'log_entries', 'sessions'
    ];
    missing_tables TEXT[];
    table_name TEXT;
BEGIN
    -- Contar tabelas criadas
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name = ANY(expected_tables);

    -- Verificar tabelas faltantes
    SELECT ARRAY_AGG(t) INTO missing_tables
    FROM UNNEST(expected_tables) AS t
    WHERE NOT EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'public' AND table_name = t
    );

    RAISE NOTICE '=== VERIFICAÇÃO DE MIGRAÇÃO ===';
    RAISE NOTICE 'Tabelas criadas: % de %', table_count, array_length(expected_tables, 1);

    IF missing_tables IS NOT NULL THEN
        RAISE WARNING 'Tabelas faltantes: %', array_to_string(missing_tables, ', ');
    ELSE
        RAISE NOTICE '✅ Todas as tabelas foram criadas com sucesso!';
    END IF;

    RAISE NOTICE 'Migração concluída em: %', CURRENT_TIMESTAMP;
END $$;

-- =====================================================
-- FIM DA MIGRAÇÃO
-- =====================================================
