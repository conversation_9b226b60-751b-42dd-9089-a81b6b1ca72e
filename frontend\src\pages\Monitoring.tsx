import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { CustomL<PERSON><PERSON><PERSON>, CustomArea<PERSON><PERSON>, CustomBarChart } from '../components/ui/Chart';

interface MetricPoint {
  time: string;
  cpu: number;
  memory: number;
  cacheHit: number;
  cacheMiss: number;
}

export default function MonitoringPage() {
  const [data, setData] = useState<MetricPoint[]>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      const now = new Date();
      const newPoint: MetricPoint = {
        time: now.toLocaleTimeString('pt-BR', { minute: '2-digit', second: '2-digit' }),
        cpu: Math.random() * 80 + 10,
        memory: Math.random() * 70 + 20,
        cacheHit: Math.floor(Math.random() * 1000),
        cacheMiss: Math.floor(Math.random() * 200),
      };
      setData((prev) => {
        const arr = [...prev, newPoint];
        if (arr.length > 30) arr.shift();
        return arr;
      });
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-foreground">Monitoramento em Tempo Real</h1>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Uso de CPU (%)</CardTitle>
          </CardHeader>
          <CardContent>
            <CustomLineChart data={data} xKey="time" dataKey="cpu" height={250} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Uso de Memória (%)</CardTitle>
          </CardHeader>
          <CardContent>
            <CustomLineChart data={data} xKey="time" dataKey="memory" height={250} color="#10B981" />
          </CardContent>
        </Card>

        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Cache Hits vs Miss</CardTitle>
          </CardHeader>
          <CardContent>
            <CustomBarChart
              data={data}
              xKey="time"
              height={280}
              bars={[
                { key: 'cacheHit', name: 'Hit', color: '#3B82F6' },
                { key: 'cacheMiss', name: 'Miss', color: '#EF4444' },
              ]}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 