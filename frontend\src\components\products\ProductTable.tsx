import React, { useState } from 'react';
import Table from '../ui/Table';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '../ui/card';
import { ChevronLeftIcon, ChevronRightIcon, ArrowUpIcon, ArrowDownIcon } from '@radix-ui/react-icons';
import { Input } from '../ui/input';

interface Product {
  id: string;
  sku: string;
  title: string;
  price: number;
  availableQuantity: number;
  category: string;
  status: string;
}

interface SortConfig {
  column: keyof Product;
  direction: 'asc' | 'desc';
}

interface ProductTableProps {
  products: Product[];
  onEdit: (product: Product) => void;
  onDelete: (productId: string) => void;
  loading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  sortConfig?: SortConfig;
  onSort: (column: keyof Product) => void;
}

export default function ProductTable({ products, onEdit, onDelete, loading, currentPage, totalPages, onPageChange, sortConfig, onSort }: ProductTableProps) {
  if (loading) {
    return (
      <Card>
        <CardHeader><CardTitle>Carregando Produtos...</CardTitle></CardHeader>
        <CardContent>
          <div className="animate-pulse flex space-x-4">
            <div className="flex-1 space-y-4 py-1">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getSortIcon = (column: keyof Product) => {
    if (!sortConfig || sortConfig.column !== column) {
      return null;
    }
    if (sortConfig.direction === 'asc') {
      return <ArrowUpIcon className="ml-1 h-3 w-3 inline" />;
    } else {
      return <ArrowDownIcon className="ml-1 h-3 w-3 inline" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Lista de Produtos</CardTitle>
      </CardHeader>
      <CardContent>
        {products.length === 0 ? (
          <p className="text-muted-foreground">Nenhum produto encontrado.</p>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('sku')}>SKU{getSortIcon('sku')}</TableHead>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('title')}>Título{getSortIcon('title')}</TableHead>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('price')}>Preço{getSortIcon('price')}</TableHead>
                  <TableHead className="cursor-pointer hover:text-foreground/80" onClick={() => onSort('availableQuantity')}>Qtd. Disp.{getSortIcon('availableQuantity')}</TableHead>
                  <TableHead>Categoria</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Ações</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {products.map((product) => (
                  <TableRow key={product.id}>
                    <TableCell>{product.sku}</TableCell>
                    <TableCell>{product.title}</TableCell>
                    <TableCell>{product.price.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}</TableCell>
                    <TableCell>{product.availableQuantity}</TableCell>
                    <TableCell>{product.category}</TableCell>
                    <TableCell>{product.status}</TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm" onClick={() => onEdit(product)}>Editar</Button>
                      <Button variant="destructive" size="sm" className="ml-2" onClick={() => onDelete(product.id)}>Excluir</Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
      {totalPages > 1 && (
        <CardFooter className="flex justify-between items-center">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeftIcon className="mr-2 h-4 w-4" /> Anterior
          </Button>
          <span className="text-sm text-muted-foreground">
            Página {currentPage} de {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            Próximo <ChevronRightIcon className="ml-2 h-4 w-4" />
          </Button>
        </CardFooter>
      )}
    </Card>
  );
} 