# 🏪 Magnow - Sistema Inteligente de Controle de Estoque

<div align="center">
  <img src="https://img.shields.io/badge/Node.js-18%2B-339933?style=for-the-badge&logo=node.js&logoColor=white" alt="Node.js" />
  <img src="https://img.shields.io/badge/TypeScript-5.3-3178C6?style=for-the-badge&logo=typescript&logoColor=white" alt="TypeScript" />
  <img src="https://img.shields.io/badge/Express-4.18-000000?style=for-the-badge&logo=express&logoColor=white" alt="Express" />
  <img src="https://img.shields.io/badge/PostgreSQL-15%2B-336791?style=for-the-badge&logo=postgresql&logoColor=white" alt="PostgreSQL" />
  <img src="https://img.shields.io/badge/Redis-7%2B-DC382D?style=for-the-badge&logo=redis&logoColor=white" alt="Redis" />
  <img src="https://img.shields.io/badge/Prisma-5.8-2D3748?style=for-the-badge&logo=prisma&logoColor=white" alt="Prisma" />
</div>

<div align="center">
  <img src="https://img.shields.io/badge/Coverage-95%25-brightgreen?style=for-the-badge" alt="Coverage" />
  <img src="https://img.shields.io/badge/License-MIT-blue?style=for-the-badge" alt="License" />
  <img src="https://img.shields.io/badge/Status-Production%20Ready-green?style=for-the-badge" alt="Status" />
</div>

## 📋 Índice

- [Sobre o Projeto](#-sobre-o-projeto)
- [Funcionalidades Principais (MVP)](#-funcionalidades-principais-mvp)
- [Documentação](#-documentação)
- [Começando](#-começando)
- [Desenvolvimento](#-desenvolvimento)
- [Contribuição](#-contribuição)
- [Roadmap](#-roadmap)
- [Licença](#-licença)
- [Suporte](#-suporte)

## 🎯 Sobre o Projeto

O **Magnow** é uma plataforma SaaS robusta e inteligente, projetada para revolucionar o controle de estoque de vendedores do **Mercado Livre Full**. Nossa missão é automatizar e otimizar a gestão de inventário, permitindo que você foque no que realmente importa: **escalar suas vendas**.

A plataforma centraliza o monitoramento de ponta a ponta, desde a sincronização em tempo real com a API do Mercado Livre até a geração de relatórios analíticos e a criação automatizada de remessas de entrada. Com o Magnow, você transforma dados brutos em decisões estratégicas.

### Por que Magnow?

- **🚀 Foco no Mercado Livre Full**: Ferramentas desenhadas especificamente para as dores de quem vende na modalidade Full.
- **💡 Calculadora Inteligente**: Algoritmos que analisam seu histórico de vendas para prever a demanda e sugerir o estoque ideal, evitando rupturas e excessos.
- **⚡ Automação de Remessas**: Geração automática de planilhas de remessa de entrada, eliminando erros manuais e economizando seu tempo.
- **🔗 Integração Nativa**: Conexão direta e segura com a API do Mercado Livre via OAuth 2.0, garantindo dados sempre atualizados.
- **🏢 Arquitetura Multi-Tenant**: Gerencie múltiplas contas de vendedor a partir de um único painel, com total isolamento e segurança dos dados.
- **🛡️ Segurança e Escalabilidade**: Construído sobre uma arquitetura moderna, o Magnow garante a segurança dos seus dados e está pronto para crescer com o seu negócio.

## ✨ Funcionalidades Principais (MVP)

O MVP do Magnow está focado em entregar valor real através de três pilares essenciais:

### 1. 🔗 Integração Segura com o Mercado Livre
- **Autenticação OAuth 2.0**: Conexão segura e simplificada com sua conta de vendedor.
- **Arquitetura Multi-Tenant**: Suporte para gerenciar múltiplas contas do Mercado Livre de forma centralizada e segura.
- **Sincronização de Dados**: Importação e atualização contínua de produtos, vendas e níveis de estoque do Full.

### 2. 💡 Calculadora Inteligente de Estoque Alvo
- **Análise de Velocidade de Vendas (VMD)**: Cálculo automático da média de vendas diárias por produto.
- **Definição de Cobertura de Estoque**: Permite configurar quantos dias de estoque você deseja manter no Full.
- **Cálculo do Estoque Ideal**: Sugestão precisa da quantidade de cada item a ser mantida no centro de distribuição.
- **Identificação de Prioridades**: Destaque para produtos que necessitam de reposição urgente.

### 3. 📄 Geração Automatizada de Arquivos de Remessa
- **Criação de Planilhas de Entrada**: Geração do arquivo de remessa (`shipping`) no formato exato exigido pelo Mercado Livre.
- **Preenchimento Automático**: A planilha é preenchida com os SKUs e as quantidades recomendadas pela calculadora inteligente.
- **Redução de Erros**: Elimina a digitação manual e os erros de formatação que podem atrasar o recebimento dos seus produtos.

## 📚 Documentação

Para uma compreensão completa do projeto, consulte nossa documentação detalhada:

- 📄 **[Requisitos de Negócio (`BUSINESS_REQUIREMENTS.md`)] (./docs/BUSINESS_REQUIREMENTS.md)**: A visão completa do produto, público-alvo, análise competitiva e requisitos funcionais.
- 🏛️ **[Arquitetura do Backend (`ARCHITECTURE_DETAILS.md`)] (./docs/ARCHITECTURE_DETAILS.md)**: Detalhes sobre a arquitetura de microsserviços, tecnologias, fluxos de dados e decisões de design.
- 🎨 **[Arquitetura do Frontend (`FRONTEND_ARCHITECTURE_DETAILS.md`)] (./docs/FRONTEND_ARCHITECTURE_DETAILS.md)**: Especificações sobre a arquitetura do frontend, componentes e estado da aplicação.
- 📖 **[Guia do Usuário (`USER_GUIDE.md`)] (./docs/USER_GUIDE.md)**: Um manual completo sobre como usar a plataforma Magnow.
- 🚀 **[Guia de Instalação (`INSTALLATION.md`)] (./docs/INSTALLATION.md)**: Instruções passo a passo para configurar o ambiente de desenvolvimento.

## 🚀 Começando

Para começar a usar o Magnow, você precisará ter o **Node.js**, **PostgreSQL**, **Redis** e **Docker** (recomendado) instalados em seu ambiente.

Para um guia completo de instalação e configuração, incluindo a configuração de variáveis de ambiente e a inicialização do banco de dados, consulte nosso **[Guia de Instalação (`INSTALLATION.md`)] (./docs/INSTALLATION.md)**.

## 💻 Desenvolvimento

Nosso guia de desenvolvimento cobre tudo o que você precisa para rodar e customizar o Magnow:

- **[Guia de Desenvolvimento (`DEVELOPMENT.md`)] (./docs/DEVELOPMENT.md)**: Instruções sobre como configurar variáveis de ambiente, rodar a aplicação em modo de desenvolvimento e produção, e usar os scripts do projeto.

## 📚 API Documentation

Para detalhes sobre os endpoints, consulte a **[Documentação da API](./docs/API.md)**.

## 📁 Estrutura do Projeto

Para detalhes sobre a organização do código, consulte a **[Documentação de Arquitetura](./docs/ARCHITECTURE.md)**.

## 📜 Scripts Disponíveis

Para instruções sobre os scripts, consulte o **[Guia de Desenvolvimento](./docs/DEVELOPMENT.md)**.

## 🧪 Testes

Para instruções sobre como rodar os testes, consulte o **[Guia de Testes](./docs/TESTING.md)**.

## 🚀 Deployment

Para instruções de deploy, consulte o **[Guia de Deployment](./docs/DEPLOYMENT.md)**.

## 🤝 Contribuição

Contribuições são o que tornam a comunidade de código aberto um lugar incrível para aprender, inspirar e criar. Qualquer contribuição que você fizer será **muito apreciada**.

Para saber como você pode contribuir com o projeto, por favor, leia nosso **[Guia de Contribuição (`CONTRIBUTING.md`)] (./docs/CONTRIBUTING.md)**.

## 🗺️ Roadmap

Nós temos um plano claro para o futuro do Magnow, incluindo novas funcionalidades e melhorias. Para ver o que estamos planejando, confira nosso:

- **[Roadmap do Projeto (`ROADMAP.md`)] (./docs/ROADMAP.md)**

## 📄 Licença

O Magnow é um software de código aberto licenciado sob a **[Licença MIT] (./LICENSE)**.

## 📞 Suporte

### 🆘 Precisa de Ajuda?

- 📧 **Email**: <EMAIL>
- 💬 **Discord**: [Comunidade Magnow](https://discord.gg/magnow)
- 📱 **WhatsApp**: (11) 99999-9999
- 🐛 **Issues**: [GitHub Issues](https://github.com/seu-usuario/magnow/issues)

### 📚 Documentação

- [📖 Guia do Usuário](./docs/USER_GUIDE.md)
- [🏗️ Arquitetura do Sistema](./docs/ARCHITECTURE.md)
- [⚙️ Guia de Instalação](./docs/INSTALLATION.md)
- [🤝 Guia de Contribuição](./docs/CONTRIBUTING.md)

### 🌐 Links Úteis

- **Website**: https://magnow.com
- **Documentação**: https://docs.magnow.com
- **Status**: https://status.magnow.com
- **Blog**: https://blog.magnow.com

---

<div align="center">
  <strong>Feito com ❤️ pela equipe Magnow</strong>
  <br>
  <small>© 2024 Magnow. Todos os direitos reservados.</small>
</div>

