import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  HomeIcon,
  CubeIcon,
  ChartBarIcon,
  DocumentTextIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowRightOnRectangleIcon,
  UsersIcon
} from '@heroicons/react/24/outline';
import { useAuthStore } from '../../store/authStore';
import { useDashboardStore } from '../../store/dashboardStore';
import NotificationDropdown from './NotificationDropdown';
import { ThemeToggle } from '../ThemeToggle';
import { ProfileDropdown } from '../profile';
import { FadeIn, SlideIn, RippleEffect } from '../ui/Animations';

interface LayoutProps {
  children: React.ReactNode;
}

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'Produtos', href: '/products', icon: CubeIcon },
  { name: 'Esto<PERSON>', href: '/stock', icon: ChartBarIcon },
  { name: 'Envios', href: '/envios', icon: DocumentTextIcon },
  { name: 'Relató<PERSON>s', href: '/reports/sales', icon: ChartBarIcon },
  { name: 'Usuários', href: '/users', icon: UsersIcon },
  { name: 'Monitoramento', href: '/monitoring', icon: DocumentTextIcon },
  { name: 'Logs', href: '/logs', icon: DocumentTextIcon },
  { name: 'Configurações', href: '/settings', icon: Cog6ToothIcon },
];

export default function Layout({ children }: LayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const location = useLocation();
  const { user, logout } = useAuthStore();
  const { unreadAlertsCount } = useDashboardStore();

  // Fechar sidebar quando a rota mudar (mobile)
  useEffect(() => {
    setSidebarOpen(false);
  }, [location.pathname]);

  // Remover loading simulado que pode causar recarregamentos
  // useEffect(() => {
  //   setIsLoading(true);
  //   const timer = setTimeout(() => setIsLoading(false), 150);
  //   return () => clearTimeout(timer);
  // }, [location.pathname]);

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logout();
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getPageTitle = () => {
    const path = location.pathname.replace('/', '');
    const navItem = navigation.find(item => item.href === location.pathname);
    return navItem?.name || (path ? path.charAt(0).toUpperCase() + path.slice(1) : 'Dashboard');
  };

  return (
    <div className="h-screen flex overflow-hidden bg-background">
      {/* Overlay para mobile */}
      {sidebarOpen && (
        <FadeIn duration={200}>
          <div 
            className="fixed inset-0 bg-gray-600 bg-opacity-75 z-40 md:hidden" 
            onClick={() => setSidebarOpen(false)}
          />
        </FadeIn>
      )}

      {/* Sidebar mobile */}
      <div className={`fixed inset-y-0 left-0 z-50 md:hidden transition-transform duration-300 ease-in-out ${
        sidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <SlideIn direction="right" duration={300}>
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-card shadow-xl">
            {/* Header do sidebar mobile */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h1 className="text-xl font-bold text-foreground">Magnow</h1>
              <RippleEffect>
                <button
                  type="button"
                  className="p-2 rounded-md text-muted-foreground hover:text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-ring transition-colors"
                  onClick={() => setSidebarOpen(false)}
                  aria-label="Fechar menu"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </RippleEffect>
            </div>
            
            {/* Navegação mobile */}
            <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
              {navigation.map((item, index) => {
                const isActive = location.pathname === item.href;
                return (
                  <FadeIn key={item.name} delay={index * 50}>
                    <RippleEffect>
                      <Link
                        to={item.href}
                        className={`group flex items-center px-3 py-3 text-base font-medium rounded-lg transition-all duration-200 ${
                          isActive
                            ? 'bg-primary text-primary-foreground shadow-sm'
                            : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground hover:shadow-sm'
                        }`}
                      >
                        <item.icon className={`mr-4 h-6 w-6 transition-colors ${
                          isActive ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-accent-foreground'
                        }`} />
                        {item.name}
                      </Link>
                    </RippleEffect>
                  </FadeIn>
                );
              })}
            </nav>

            {/* Footer do sidebar mobile */}
            <div className="p-4 border-t border-border">
              <RippleEffect>
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center px-3 py-2 text-sm text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-lg transition-colors"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5 mr-2" />
                  Sair
                </button>
              </RippleEffect>
            </div>
          </div>
        </SlideIn>
      </div>

      {/* Sidebar desktop */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col h-0 flex-1 bg-card shadow-sm border-r border-border">
            {/* Header do sidebar desktop */}
            <div className="flex items-center flex-shrink-0 px-6 py-4 border-b border-border">
              <h1 className="text-xl font-bold text-foreground">Magnow</h1>
            </div>
            
            {/* Navegação desktop */}
            <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
              {navigation.map((item, index) => {
                const isActive = location.pathname === item.href;
                return (
                  <FadeIn key={item.name} delay={index * 30}>
                    <RippleEffect>
                      <Link
                        to={item.href}
                        className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                          isActive
                            ? 'bg-primary text-primary-foreground shadow-sm'
                            : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground hover:shadow-sm'
                        }`}
                      >
                        <item.icon className={`mr-3 h-5 w-5 transition-colors ${
                          isActive ? 'text-primary-foreground' : 'text-muted-foreground group-hover:text-accent-foreground'
                        }`} />
                        {item.name}
                      </Link>
                    </RippleEffect>
                  </FadeIn>
                );
              })}
            </nav>

            {/* Footer do sidebar desktop */}
            <div className="flex-shrink-0 p-3 border-t border-border">
              <RippleEffect>
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center px-3 py-2 text-sm text-muted-foreground hover:text-destructive hover:bg-destructive/10 rounded-lg transition-colors"
                  disabled={isLoading}
                >
                  <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                  {isLoading ? 'Saindo...' : 'Sair'}
                </button>
              </RippleEffect>
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo principal */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Header */}
        <FadeIn>
          <header className="relative z-10 flex-shrink-0 flex h-16 bg-card shadow-sm border-b border-border">
            {/* Botão do menu mobile */}
            <RippleEffect>
              <button
                type="button"
                className="px-4 border-r border-border text-muted-foreground hover:text-foreground hover:bg-accent focus:outline-none focus:ring-2 focus:ring-inset focus:ring-ring md:hidden transition-colors"
                onClick={() => setSidebarOpen(true)}
                aria-label="Abrir menu"
              >
                <Bars3Icon className="h-6 w-6" />
              </button>
            </RippleEffect>
            
            <div className="flex-1 px-4 flex justify-between items-center">
              {/* Título da página */}
              <div className="flex items-center">
                <h2 className="text-lg font-semibold text-foreground">
                  {getPageTitle()}
                </h2>
                {unreadAlertsCount > 0 && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    {unreadAlertsCount} {unreadAlertsCount === 1 ? 'alerta' : 'alertas'}
                  </span>
                )}
              </div>
              
              {/* Ações do header */}
              <div className="flex items-center space-x-4">
                <ThemeToggle />
                <NotificationDropdown />
                <ProfileDropdown />
              </div>
            </div>
          </header>
        </FadeIn>

        {/* Conteúdo da página */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-2"></div>
                <p className="text-sm text-gray-500">Carregando...</p>
              </div>
            </div>
          ) : (
            <FadeIn duration={200}>
              <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  {children}
                </div>
              </div>
            </FadeIn>
          )}
        </main>
      </div>
    </div>
  );
}