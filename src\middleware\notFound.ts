import { Request, Response, NextFunction } from 'express';
import { NotFoundError } from '@/middleware/errorHandler';

// Middleware para tratar rotas não encontradas
export const notFoundHandler = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const error = new NotFoundError(`Rota ${req.method} ${req.originalUrl} não encontrada`);
  next(error);
};

export default notFoundHandler; 