 
import type { Meta, StoryObj } from '@storybook/react';
import { action } from '@storybook/addon-actions';

import { Button } from './Button';

const meta = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: [
        'primary',
        'secondary',
        'tertiary',
        'danger',
        'success',
        'warning',
        'info',
        'light',
        'dark',
        'link',
        'outline',
        'ghost',
      ],
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
    },
    disabled: { control: 'boolean' },
    loading: { control: 'boolean' },
    className: { control: 'text' },
    children: { control: 'text' },
    onClick: { action: 'clicked' },
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: 'Botão Padrão',
  },
};

export const Primary: Story = {
  args: {
    variant: 'primary',
    children: 'Botão Primário',
  },
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    children: 'Botão Secundário',
  },
};

export const Danger: Story = {
  args: {
    variant: 'danger',
    children: 'Botão Perigo',
  },
};

export const Link: Story = {
  args: {
    variant: 'link',
    children: 'Botão Link',
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: 'Botão Outline',
  },
};

export const Ghost: Story = {
  args: {
    variant: 'ghost',
    children: 'Botão Ghost',
  },
};

export const Small: Story = {
  args: {
    size: 'sm',
    children: 'Botão Pequeno',
  },
};

export const Large: Story = {
  args: {
    size: 'lg',
    children: 'Botão Grande',
  },
};

export const WithLoading: Story = {
  args: {
    loading: true,
    children: 'Carregando...',
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
    children: 'Desabilitado',
  },
}; 
