import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Label } from '../ui/label';
import { 
  Search, 
  Filter, 
  Package, 
  AlertTriangle,
  CheckCircle,
  TrendingDown,
  RotateCcw,
  Grid3X3,
  List,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useMLFullWizardStore } from '../../store/mlFullWizardStore';
import StockIndicators from '../products/StockIndicators';
import { ProductCardSkeleton } from '../ui/loading';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../ui/collapsible';

interface ProductSelectionStepProps {
  onNext: () => void;
  onBack: () => void;
}

export default function ProductSelectionStep({ onNext, onBack }: ProductSelectionStepProps) {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filtersExpanded, setFiltersExpanded] = useState(false);
  
  const {
    filteredProducts,
    selectedProducts,
    filters,
    isLoadingProducts,
    selectAllProducts,
    unselectAllProducts,
    toggleProductSelection,
    setFilters,
    clearFilters,
  } = useMLFullWizardStore();

  const eligibleProducts = filteredProducts.filter(p => p.isEligible);
  const selectedCount = selectedProducts.length;
  const totalEligible = eligibleProducts.length;

  const handleSearchChange = (value: string) => {
    setFilters({ search: value });
  };

  const handleStockStatusChange = (status: string, checked: boolean) => {
    const currentStatus = filters.stockStatus || [];
    let newStatus: string[];
    
    if (checked) {
      newStatus = [...currentStatus, status];
    } else {
      newStatus = currentStatus.filter(s => s !== status);
    }
    
    setFilters({ stockStatus: newStatus as any });
  };

  const getProductStockStatus = (product: any) => {
    if (product.currentStock === 0) return 'out_of_stock';
    if (product.currentStock <= 10) return 'low_stock';
    if (product.stockCalculation?.gap > 0) return 'critical_gap';
    return 'in_stock';
  };

  const getStockStatusBadge = (product: any) => {
    const status = getProductStockStatus(product);
    switch (status) {
      case 'out_of_stock':
        return <Badge variant="destructive" className="text-xs">Sem Estoque</Badge>;
      case 'low_stock':
        return <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">Baixo</Badge>;
      case 'critical_gap':
        return <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">Gap Crítico</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">Em Estoque</Badge>;
    }
  };

  const renderProductCard = (product: any) => {
    const isSelected = selectedProducts.includes(product.id);

    return (
      <Card
        key={product.id}
        className={`transition-all duration-200 cursor-pointer hover:shadow-md h-full ${
          isSelected ? 'ring-2 ring-blue-500 bg-blue-50/50' : ''
        } ${!product.isEligible ? 'opacity-60' : ''}`}
        onClick={() => product.isEligible && toggleProductSelection(product.id)}
      >
        <CardContent className="p-4 h-full flex flex-col">
          {/* Header: Checkbox + Thumbnail */}
          <div className="flex items-start justify-between gap-3 mb-3">
            <input
              type="checkbox"
              checked={isSelected}
              disabled={!product.isEligible}
              onChange={() => product.isEligible && toggleProductSelection(product.id)}
              className="mt-1 flex-shrink-0 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />

            {product.thumbnail && (
              <img
                src={product.thumbnail}
                alt={product.title}
                className="w-16 h-16 object-cover rounded border flex-shrink-0"
              />
            )}
          </div>

          {/* Title - More prominent */}
          <div className="mb-3 flex-grow">
            <h3 className="font-semibold text-base line-clamp-2 mb-2 leading-tight">
              {product.title}
            </h3>

            {/* IDs: SKU + ML ID */}
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">
                <span className="font-medium">SKU:</span> {product.sku}
              </p>
              <p className="text-xs text-muted-foreground">
                <span className="font-medium">ML ID:</span> {product.mlId}
              </p>
            </div>
          </div>

          {/* Stock: Quantity + Status Badge */}
          <div className="mb-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Estoque:</span>
              <div className="flex items-center gap-2">
                <span className="font-semibold">{product.currentStock}</span>
                {getStockStatusBadge(product)}
              </div>
            </div>

            {/* Gap - If exists, show highlighted */}
            {product.stockCalculation?.gap > 0 && (
              <div className="bg-orange-50 border border-orange-200 rounded-md p-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-orange-800">Gap:</span>
                  <span className="text-sm font-bold text-orange-600">
                    +{product.stockCalculation.gap} unidades
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Stock Indicators */}
          <div className="mb-3 flex justify-center">
            <StockIndicators
              product={product}
              showDetailed={false}
              size="sm"
            />
          </div>

          {/* Price - Centered and prominent in footer */}
          <div className="mt-auto pt-3 border-t">
            <div className="text-center">
              <span className="text-xl font-bold text-green-600">
                R$ {product.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
              </span>
            </div>

            {/* Eligibility Message */}
            {!product.isEligible && (
              <div className="text-xs text-red-600 text-center mt-2">
                Produto não elegível para ML Full
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  };

  const renderProductList = (product: any) => {
    const isSelected = selectedProducts.includes(product.id);

    return (
      <Card
        key={product.id}
        className={`transition-all duration-200 cursor-pointer hover:shadow-sm ${
          isSelected ? 'ring-2 ring-blue-500 bg-blue-50/50' : ''
        } ${!product.isEligible ? 'opacity-60' : ''}`}
        onClick={() => product.isEligible && toggleProductSelection(product.id)}
      >
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <input
              type="checkbox"
              checked={isSelected}
              disabled={!product.isEligible}
              onChange={() => product.isEligible && toggleProductSelection(product.id)}
              className="flex-shrink-0 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />

            {product.thumbnail && (
              <img
                src={product.thumbnail}
                alt={product.title}
                className="w-12 h-12 object-cover rounded border flex-shrink-0"
              />
            )}

            <div className="flex-1 grid grid-cols-1 md:grid-cols-6 gap-4 items-center min-w-0">
              {/* Product Info */}
              <div className="md:col-span-2 min-w-0">
                <h3 className="font-medium text-sm line-clamp-1 mb-1">{product.title}</h3>
                <div className="space-y-0.5">
                  <p className="text-xs text-muted-foreground">
                    <span className="font-medium">SKU:</span> {product.sku}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    <span className="font-medium">ML ID:</span> {product.mlId}
                  </p>
                </div>
              </div>

              {/* Stock Quantity */}
              <div className="text-center">
                <div className="font-semibold text-base">{product.currentStock}</div>
                <div className="text-xs text-muted-foreground">unidades</div>
              </div>

              {/* Status Badge */}
              <div className="flex justify-center">
                {getStockStatusBadge(product)}
              </div>

              {/* Gap Info */}
              <div className="text-center">
                {product.stockCalculation?.gap > 0 ? (
                  <div className="text-sm font-medium text-orange-600">
                    +{product.stockCalculation.gap}
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">-</div>
                )}
                <div className="text-xs text-muted-foreground">gap</div>
              </div>

              {/* Price */}
              <div className="text-right">
                <div className="font-bold text-green-600 text-base">
                  R$ {product.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </div>
                {!product.isEligible && (
                  <div className="text-xs text-red-600 mt-1">
                    Não elegível
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold">Seleção de Produtos</h2>
          <p className="text-muted-foreground">
            Escolha os produtos que deseja enviar para o Mercado Livre Full
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {selectedCount} de {totalEligible} selecionados
          </Badge>
          
          <div className="flex items-center border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-base flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Filtros
            </CardTitle>
            <Collapsible open={filtersExpanded} onOpenChange={setFiltersExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm">
                  {filtersExpanded ? (
                    <ChevronUp className="h-4 w-4" />
                  ) : (
                    <ChevronDown className="h-4 w-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Buscar por nome ou SKU..."
              value={filters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          <Collapsible open={filtersExpanded} onOpenChange={setFiltersExpanded}>
            <CollapsibleContent className="space-y-4">
              {/* Stock Status Filter */}
              <div className="space-y-3">
                <Label>Status do Estoque</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="in_stock"
                      checked={filters.stockStatus?.includes('in_stock') || false}
                      onChange={(e) => handleStockStatusChange('in_stock', e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="in_stock" className="flex items-center gap-2 cursor-pointer">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Em Estoque
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="low_stock"
                      checked={filters.stockStatus?.includes('low_stock') || false}
                      onChange={(e) => handleStockStatusChange('low_stock', e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="low_stock" className="flex items-center gap-2 cursor-pointer">
                      <AlertTriangle className="h-4 w-4 text-yellow-500" />
                      Baixo Estoque
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="critical_gap"
                      checked={filters.stockStatus?.includes('critical_gap') || false}
                      onChange={(e) => handleStockStatusChange('critical_gap', e.target.checked)}
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                    <Label htmlFor="critical_gap" className="flex items-center gap-2 cursor-pointer">
                      <TrendingDown className="h-4 w-4 text-orange-500" />
                      Gap Crítico
                    </Label>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-2 border-t">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearFilters}
                  className="flex items-center gap-1"
                >
                  <RotateCcw className="h-4 w-4" />
                  Limpar Filtros
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={selectAllProducts}
                  disabled={eligibleProducts.length === 0}
                  className="flex items-center gap-1"
                >
                  <CheckCircle className="h-4 w-4" />
                  Selecionar Todos
                </Button>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={unselectAllProducts}
                  disabled={selectedCount === 0}
                  className="flex items-center gap-1"
                >
                  Desmarcar Todos
                </Button>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Products List */}
      {isLoadingProducts ? (
        <ProductCardSkeleton count={6} />
      ) : eligibleProducts.length === 0 ? (
        <Card>
          <CardContent className="py-12 text-center">
            <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nenhum produto encontrado</h3>
            <p className="text-muted-foreground">
              Não há produtos elegíveis que correspondam aos filtros aplicados.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className={
          viewMode === 'grid'
            ? 'grid grid-cols-1 lg:grid-cols-2 gap-6'
            : 'space-y-2'
        }>
          {eligibleProducts.map(product => 
            viewMode === 'grid' ? renderProductCard(product) : renderProductList(product)
          )}
        </div>
      )}

      {/* Navigation */}
      <div className="flex justify-end mt-6">
        <Button
          onClick={onNext}
          disabled={selectedProducts.length === 0}
          className="flex items-center gap-2"
        >
          <CheckCircle className="h-4 w-4" />
          Continuar para Configuração
        </Button>
      </div>
    </div>
  );
}
