import React, { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { But<PERSON> } from '../ui/button';
import { Input } from '../ui/input';
import { Checkbox } from '../ui/checkbox';
import StockIndicators from '../products/StockIndicators';
import {
  Edit3,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Package,
  MapPin,
  Eye,
  MoreHorizontal,
  Trash2,
  RefreshCw,
  Calendar,
  Hash
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '../ui/dropdown-menu';
import type { StockItem } from '../../services/stockService';
import type { ProductWithStock } from '../../types/api';

interface StockCardProps {
  stockItem: StockItem;
  onUpdateStock?: (itemId: string, quantity: number) => Promise<void>;
  onViewDetails?: (item: StockItem) => void;
  onDelete?: (itemId: string) => Promise<void>;
  onRefresh?: (itemId: string) => Promise<void>;
  isUpdatingStock?: boolean;
  isSelected?: boolean;
  onSelect?: () => void;
  viewMode?: 'card' | 'list';
}

export default function StockCard({
  stockItem,
  onUpdateStock,
  onViewDetails,
  onDelete,
  onRefresh,
  isUpdatingStock = false,
  isSelected = false,
  onSelect,
  viewMode = 'card'
}: StockCardProps) {
  const [isEditingStock, setIsEditingStock] = useState(false);
  const [stockValue, setStockValue] = useState(stockItem?.currentQuantity?.toString() ?? '0');
  const [isLoading, setIsLoading] = useState(false);

  // Convert StockItem to ProductWithStock for StockIndicators
  const productWithStock: ProductWithStock = {
    id: stockItem.id,
    title: stockItem.productTitle,
    sku: stockItem.productSku,
    availableQuantity: stockItem.currentQuantity,
    currentStock: stockItem.currentQuantity,
    location: stockItem.location,
    lastUpdated: stockItem.lastUpdated,
    // Default values for required fields
    price: 0,
    status: 'active',
    categoryId: '',
    permalink: '',
    thumbnail: '',
    condition: 'new',
    listingTypeId: 'gold_special',
    soldQuantity: 0,
    metrics: {
      reorderPoint: 10,
      averageSales: 1,
      daysOfStock: stockItem.currentQuantity,
      stockTurnover: 0,
      lastSaleDate: stockItem.lastUpdated,
    }
  };

  const getStockStatus = () => {
    const stock = stockItem.currentQuantity;
    if (stock === 0) return { 
      status: 'Sem estoque', 
      color: 'bg-red-100 text-red-800 border-red-200',
      icon: <AlertTriangle className="h-3 w-3" />
    };
    if (stock <= 10) return { 
      status: 'Baixo estoque', 
      color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      icon: <TrendingDown className="h-3 w-3" />
    };
    return { 
      status: 'Em estoque', 
      color: 'bg-green-100 text-green-800 border-green-200',
      icon: <CheckCircle className="h-3 w-3" />
    };
  };

  const handleStockUpdate = async () => {
    if (!onUpdateStock) return;
    
    const newQuantity = parseInt(stockValue);
    if (isNaN(newQuantity) || newQuantity < 0) {
      alert('Por favor, insira uma quantidade válida');
      return;
    }

    setIsLoading(true);
    try {
      await onUpdateStock(stockItem.id, newQuantity);
      setIsEditingStock(false);
    } catch (error) {
      console.error('Erro ao atualizar estoque:', error);
      alert('Erro ao atualizar estoque');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setStockValue(stockItem.currentQuantity.toString());
    setIsEditingStock(false);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const stockStatus = getStockStatus();

  if (viewMode === 'list') {
    return (
      <Card className={`transition-all duration-200 ${isSelected ? 'ring-2 ring-primary' : ''}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            {onSelect && (
              <Checkbox
                checked={isSelected}
                onCheckedChange={onSelect}
              />
            )}
            
            <div className="flex-1 grid grid-cols-1 md:grid-cols-6 gap-4 items-center">
              {/* Product Info */}
              <div className="md:col-span-2">
                <h3 className="font-semibold text-sm line-clamp-1">
                  {stockItem.productTitle}
                </h3>
                <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                  <Hash className="h-3 w-3" />
                  <span>{stockItem.productSku}</span>
                </div>
              </div>

              {/* Location */}
              <div className="flex items-center gap-1 text-sm">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{stockItem.location}</span>
              </div>

              {/* Stock Quantity */}
              <div className="text-center">
                {isEditingStock ? (
                  <div className="flex items-center gap-1">
                    <Input
                      type="number"
                      value={stockValue}
                      onChange={(e) => setStockValue(e.target.value)}
                      className="w-20 h-8 text-center"
                      min="0"
                    />
                    <Button
                      size="sm"
                      onClick={handleStockUpdate}
                      disabled={isLoading}
                      className="h-8 px-2"
                    >
                      <CheckCircle className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelEdit}
                      disabled={isLoading}
                      className="h-8 px-2"
                    >
                      ×
                    </Button>
                  </div>
                ) : (
                  <div className="text-lg font-bold">{stockItem.currentQuantity}</div>
                )}
              </div>

              {/* Status */}
              <div className="flex justify-center">
                <StockIndicators
                  product={productWithStock}
                  showDetailed={false}
                  size="sm"
                />
              </div>

              {/* Actions */}
              <div className="flex items-center justify-end gap-1">
                {onUpdateStock && !isEditingStock && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsEditingStock(true)}
                    disabled={isUpdatingStock}
                  >
                    <Edit3 className="h-4 w-4" />
                  </Button>
                )}
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    {onViewDetails && (
                      <DropdownMenuItem onClick={() => onViewDetails(stockItem)}>
                        <Eye className="h-4 w-4 mr-2" />
                        Ver Detalhes
                      </DropdownMenuItem>
                    )}
                    {onRefresh && (
                      <DropdownMenuItem onClick={() => onRefresh(stockItem.id)}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Atualizar
                      </DropdownMenuItem>
                    )}
                    {onDelete && (
                      <>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => onDelete(stockItem.id)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Excluir
                        </DropdownMenuItem>
                      </>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Card view
  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${isSelected ? 'ring-2 ring-primary' : ''}`}>
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm line-clamp-2 mb-1">
                {stockItem.productTitle}
              </h3>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Hash className="h-3 w-3" />
                <span>{stockItem.productSku}</span>
              </div>
            </div>
            
            {onSelect && (
              <Checkbox
                checked={isSelected}
                onCheckedChange={onSelect}
                className="ml-2"
              />
            )}
          </div>

          {/* Location */}
          <div className="flex items-center gap-2 text-sm">
            <MapPin className="h-4 w-4 text-muted-foreground" />
            <span>{stockItem.location}</span>
          </div>

          {/* Stock Quantity */}
          <div className="text-center py-2">
            {isEditingStock ? (
              <div className="space-y-2">
                <Input
                  type="number"
                  value={stockValue}
                  onChange={(e) => setStockValue(e.target.value)}
                  className="text-center"
                  min="0"
                />
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={handleStockUpdate}
                    disabled={isLoading}
                    className="flex-1"
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Salvar
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelEdit}
                    disabled={isLoading}
                    className="flex-1"
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            ) : (
              <>
                <div className="text-2xl font-bold mb-1">{stockItem.currentQuantity}</div>
                <div className="text-xs text-muted-foreground">unidades</div>
              </>
            )}
          </div>

          {/* Stock Indicators */}
          <div className="flex justify-center">
            <StockIndicators
              product={productWithStock}
              showDetailed={false}
              size="md"
            />
          </div>

          {/* Last Updated */}
          <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            <span>Atualizado em {formatDate(stockItem.lastUpdated)}</span>
          </div>

          {/* Actions */}
          <div className="flex gap-2">
            {onUpdateStock && !isEditingStock && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditingStock(true)}
                disabled={isUpdatingStock}
                className="flex-1"
              >
                <Edit3 className="h-4 w-4 mr-1" />
                Editar
              </Button>
            )}
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" className="px-2">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onViewDetails && (
                  <DropdownMenuItem onClick={() => onViewDetails(stockItem)}>
                    <Eye className="h-4 w-4 mr-2" />
                    Ver Detalhes
                  </DropdownMenuItem>
                )}
                {onRefresh && (
                  <DropdownMenuItem onClick={() => onRefresh(stockItem.id)}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Atualizar
                  </DropdownMenuItem>
                )}
                {onDelete && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => onDelete(stockItem.id)}
                      className="text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Excluir
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
