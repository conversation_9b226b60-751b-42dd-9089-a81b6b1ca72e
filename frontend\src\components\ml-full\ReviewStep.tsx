import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Label } from '../ui/label';
import { useMLFullWizardStore } from '../../store/mlFullWizardStore';
import {
  FileText,
  Download,
  Eye,
  Package,
  Calculator,
  DollarSign,
  CheckCircle,
  AlertTriangle,
  FileSpreadsheet,
  FileDown,
  Loader2,
  ExternalLink,
  Info
} from 'lucide-react';

interface ReviewStepProps {
  onNext: () => void;
  onBack: () => void;
}

export default function ReviewStep({ onNext, onBack }: ReviewStepProps) {
  const {
    selectedProducts,
    productQuantities,
    filteredProducts,
    isGenerating,
    generatedFile,
    generateSpreadsheet,
    downloadFile,
    getTotalQuantity,
    getTotalValue,
  } = useMLFullWizardStore();

  const [showPreview, setShowPreview] = useState(false);

  // Get selected products with their data and quantities
  const selectedProductsData = useMemo(() => {
    return filteredProducts
      .filter(product => selectedProducts.includes(product.id))
      .map(product => ({
        ...product,
        selectedQuantity: productQuantities[product.id]?.quantity || 0,
        totalValue: (productQuantities[product.id]?.quantity || 0) * product.price,
      }));
  }, [filteredProducts, selectedProducts, productQuantities]);

  // Calculate summary totals
  const summary = useMemo(() => {
    const totalProducts = selectedProducts.length;
    const totalQuantity = getTotalQuantity();
    const totalValue = getTotalValue();
    const averagePrice = totalQuantity > 0 ? totalValue / totalQuantity : 0;

    return { totalProducts, totalQuantity, totalValue, averagePrice };
  }, [selectedProducts.length, getTotalQuantity, getTotalValue]);

  const handleGenerateSpreadsheet = async () => {
    try {
      await generateSpreadsheet(); // Always generate Excel format for ML Full
    } catch (error) {
      console.error('Erro ao gerar planilha:', error);
    }
  };

  const handleDownload = () => {
    if (generatedFile) {
      downloadFile();
    }
  };

  const renderProductPreview = () => (
    <div className="space-y-3">
      {selectedProductsData.map((product) => (
        <div key={product.id} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-3">
            {product.thumbnail && (
              <img
                src={product.thumbnail}
                alt={product.title}
                className="w-12 h-12 object-cover rounded border"
              />
            )}
            <div>
              <h4 className="font-medium text-sm line-clamp-1">{product.title}</h4>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>SKU: {product.sku}</span>
                <span>•</span>
                <span>ML ID: {product.mlId}</span>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="font-semibold">{product.selectedQuantity}x</div>
            <div className="text-sm text-muted-foreground">
              R$ {product.totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </div>
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold mb-2">Revisão e Geração</h2>
        <p className="text-muted-foreground">
          Revise sua seleção e gere a planilha para envio ao Mercado Livre Full.
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Produtos</p>
                <p className="text-2xl font-bold">{summary.totalProducts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Calculator className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Quantidade</p>
                <p className="text-2xl font-bold">{summary.totalQuantity}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor Total</p>
                <p className="text-xl font-bold">
                  R$ {summary.totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Calculator className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Preço Médio</p>
                <p className="text-xl font-bold">
                  R$ {summary.averagePrice.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* ML Full Submission Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileSpreadsheet className="h-5 w-5" />
            Planilha para ML Full
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Info className="h-5 w-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-900 mb-2">Sobre a Planilha ML Full</h4>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• Formato Excel (.xlsx) otimizado para o Mercado Livre Full</p>
                  <p>• Contém todos os dados necessários para o envio</p>
                  <p>• Inclui informações de estoque, preços e quantidades</p>
                  <p>• Pronta para upload no portal do ML Full</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Product Preview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Preview dos Produtos
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
            >
              {showPreview ? 'Ocultar' : 'Mostrar'} Preview
            </Button>
          </div>
        </CardHeader>
        {showPreview && (
          <CardContent>
            {renderProductPreview()}
          </CardContent>
        )}
      </Card>

      {/* Generation Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileDown className="h-5 w-5" />
            Gerar Planilha
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {!generatedFile ? (
            <div className="text-center py-6">
              <Button
                onClick={handleGenerateSpreadsheet}
                disabled={isGenerating || selectedProducts.length === 0}
                className="flex items-center gap-2"
                size="lg"
              >
                {isGenerating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Gerando...
                  </>
                ) : (
                  <>
                    <FileSpreadsheet className="h-4 w-4" />
                    Gerar Planilha ML Full
                  </>
                )}
              </Button>
              <p className="text-sm text-muted-foreground mt-2">
                A planilha Excel será gerada com todos os produtos selecionados para envio ao ML Full
              </p>
            </div>
          ) : (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <div>
                    <h4 className="font-medium text-green-800">Planilha Gerada!</h4>
                    <p className="text-sm text-green-600">
                      {generatedFile.filename} • {generatedFile.productCount} produtos • {generatedFile.totalQuantity} unidades
                    </p>
                  </div>
                </div>
                <Button onClick={handleDownload} className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button onClick={onBack} variant="outline">
          Voltar
        </Button>
        <Button
          onClick={onNext}
          disabled={!generatedFile}
          className="flex items-center gap-2"
        >
          <CheckCircle className="h-4 w-4" />
          Continuar para Finalização
        </Button>
      </div>
    </div>
  );
}
