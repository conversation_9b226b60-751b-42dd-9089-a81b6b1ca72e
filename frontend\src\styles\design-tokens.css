/* ===== DESIGN TOKENS MAGNOW ===== */
/* Arquivo de variáveis CSS para design tokens que complementam o Tailwind */

:root {
  /* === CORES SEMÂNTICAS === */
  --foreground: #111827;
  --background: #ffffff;
  --muted: #f9fafb;
  --muted-foreground: #6b7280;
  --border: #e5e7eb;
  --input: #f3f4f6;
  --ring: #3b82f6;

  /* === CORES DE SUPERFÍCIE === */
  --surface-50: #ffffff;
  --surface-100: #f9fafb;
  --surface-200: #f3f4f6;
  --surface-300: #e5e7eb;

  /* === CORES PRIMÁRIAS === */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  --primary-950: #172554;

  /* === COMPONENTES BASE === */
  
  /* Button */
  --button-font-weight: 500;
  --button-border-radius: 0.375rem;
  --button-transition: all 150ms ease-in-out;
  --button-focus-ring: 0 0 0 2px rgba(59, 130, 246, 0.5);
  
  /* Card */
  --card-border-radius: 0.5rem;
  --card-border-color: var(--border);
  --card-background: var(--background);
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  
  /* Input */
  --input-border-radius: 0.375rem;
  --input-border-color: var(--border);
  --input-background: var(--background);
  --input-focus-border: var(--ring);
  --input-focus-ring: 0 0 0 2px rgba(59, 130, 246, 0.2);
  
  /* Modal */
  --modal-backdrop: rgba(0, 0, 0, 0.5);
  --modal-background: var(--background);
  --modal-border-radius: 0.5rem;
  --modal-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Toast */
  --toast-border-radius: 0.5rem;
  --toast-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  
  /* === ANIMAÇÕES === */
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 300ms;
  --animation-duration-slow: 500ms;
  --animation-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* === ESPAÇAMENTOS SEMÂNTICOS === */
  --spacing-component-sm: 0.5rem;    /* 8px */
  --spacing-component-md: 1rem;      /* 16px */
  --spacing-component-lg: 1.5rem;    /* 24px */
  --spacing-component-xl: 2rem;      /* 32px */
  
  --spacing-section-sm: 1.5rem;      /* 24px */
  --spacing-section-md: 3rem;        /* 48px */
  --spacing-section-lg: 4rem;        /* 64px */
  --spacing-section-xl: 6rem;        /* 96px */

  /* === TIPOGRAFIA === */
  --font-family-primary: 'Inter', system-ui, -apple-system, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-loose: 1.75;

  /* === Z-INDEX === */
  --z-dropdown: 1000;
  --z-sticky: 1010;
  --z-fixed: 1020;
  --z-modal-backdrop: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  --z-toast: 1070;
}

/* === TEMA ESCURO === */
.dark {
  --foreground: #f9fafb;
  --background: #111827;
  --muted: #1f2937;
  --muted-foreground: #9ca3af;
  --border: #374151;
  --input: #1f2937;
  --ring: #60a5fa;
  
  --surface-50: #111827;
  --surface-100: #1f2937;
  --surface-200: #374151;
  --surface-300: #4b5563;
  
  --card-background: #1f2937;
  --card-border-color: #374151;
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px -1px rgba(0, 0, 0, 0.3);
  
  --input-background: #1f2937;
  --input-border-color: #374151;
  --input-focus-border: #60a5fa;
  --input-focus-ring: 0 0 0 2px rgba(96, 165, 250, 0.2);
  
  --modal-background: #1f2937;
  --modal-backdrop: rgba(0, 0, 0, 0.8);
  --modal-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  
  --toast-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -4px rgba(0, 0, 0, 0.3);
}

/* === CLASSES UTILITÁRIAS === */
.transition-default {
  transition: var(--animation-duration-normal) var(--animation-ease);
}

.transition-fast {
  transition: var(--animation-duration-fast) var(--animation-ease);
}

.transition-slow {
  transition: var(--animation-duration-slow) var(--animation-ease);
}

.transition-bounce {
  transition: var(--animation-duration-normal) var(--animation-bounce);
}

.elevation-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.elevation-md {
  box-shadow: var(--card-shadow);
}

.elevation-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
}

.elevation-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
}

.surface-base {
  background-color: var(--surface-50);
}

.surface-elevated {
  background-color: var(--surface-100);
}

.surface-elevated-2 {
  background-color: var(--surface-200);
}

/* === DEBUGGING === */
.debug-grid {
  background-image: 
    linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 8px 8px;
}

.debug-component {
  outline: 2px dashed rgba(255, 0, 0, 0.3);
  outline-offset: 2px;
}

/* === ACESSIBILIDADE === */
.focus-visible {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* === MOTION PREFERENCES === */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}