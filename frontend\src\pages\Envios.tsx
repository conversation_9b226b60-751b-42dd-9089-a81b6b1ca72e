import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../components/ui/dropdown-menu';
import { 
  Search,
  Filter,
  Plus,
  Eye,
  Download,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertTriangle,
  XCircle,
  MoreHorizontal,
  Package,
  Calendar,
  DollarSign,
  Truck,
  FileSpreadsheet
} from 'lucide-react';
import { useMLFullWizardStore } from '../store/mlFullWizardStore';
import PDFUploadComponent from '../components/envios/PDFUploadComponent';

interface Shipment {
  id: string;
  totalUnits: number;
  status: 'pending' | 'received' | 'processing' | 'rejected';
  uploadDate: string;
  collectionDate?: string;
  productCount: number;
  totalValue: number;
  generatedFile?: {
    filename: string;
    url: string;
    format: 'excel' | 'csv';
  };
  notes?: string;
  expectedDelivery?: string;
}

export default function Envios() {
  const navigate = useNavigate();
  const { shipments, loadShipments, updateShipmentStatus, deleteShipment } = useMLFullWizardStore();
  
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [shipmentToDelete, setShipmentToDelete] = useState<string | null>(null);

  // Load shipments on component mount
  useEffect(() => {
    loadShipments();
  }, [loadShipments]);

  // Convert ML Full Wizard shipments to our format
  const formattedShipments: Shipment[] = shipments.map(shipment => ({
    id: shipment.id,
    totalUnits: shipment.totalQuantity,
    status: shipment.status as 'pending' | 'received' | 'processing' | 'rejected',
    uploadDate: shipment.createdAt,
    collectionDate: shipment.updatedAt !== shipment.createdAt ? shipment.updatedAt : undefined,
    productCount: shipment.productCount,
    totalValue: shipment.totalValue,
    generatedFile: shipment.generatedFile ? {
      filename: shipment.generatedFile.filename,
      url: shipment.generatedFile.url,
      format: shipment.generatedFile.format
    } : undefined,
    notes: shipment.notes,
    expectedDelivery: shipment.expectedDelivery,
  }));

  // Filter shipments based on search and filters
  const filteredShipments = formattedShipments.filter(shipment => {
    const matchesSearch = shipment.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shipment.notes?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || shipment.status === statusFilter;
    
    const matchesDate = dateFilter === 'all' || (() => {
      const shipmentDate = new Date(shipment.uploadDate);
      const now = new Date();
      const daysDiff = Math.floor((now.getTime() - shipmentDate.getTime()) / (1000 * 60 * 60 * 24));
      
      switch (dateFilter) {
        case '7d': return daysDiff <= 7;
        case '30d': return daysDiff <= 30;
        case '90d': return daysDiff <= 90;
        default: return true;
      }
    })();
    
    return matchesSearch && matchesStatus && matchesDate;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: { variant: 'secondary' as const, color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      received: { variant: 'default' as const, color: 'bg-green-100 text-green-800', icon: CheckCircle },
      processing: { variant: 'outline' as const, color: 'bg-blue-100 text-blue-800', icon: Package },
      rejected: { variant: 'destructive' as const, color: 'bg-red-100 text-red-800', icon: XCircle },
    };

    const config = variants[status as keyof typeof variants] || variants.pending;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className={`${config.color} flex items-center gap-1`}>
        <Icon className="h-3 w-3" />
        {status === 'pending' && 'Pendente'}
        {status === 'received' && 'Recebido'}
        {status === 'processing' && 'Processando'}
        {status === 'rejected' && 'Rejeitado'}
      </Badge>
    );
  };

  const handleStatusUpdate = (shipmentId: string, newStatus: 'pending' | 'received' | 'processing' | 'rejected') => {
    updateShipmentStatus(shipmentId, newStatus);
  };

  const handleDelete = (shipmentId: string) => {
    setShipmentToDelete(shipmentId);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (shipmentToDelete) {
      deleteShipment(shipmentToDelete);
      setShipmentToDelete(null);
      setIsDeleteDialogOpen(false);
    }
  };

  const handleDownload = (shipment: Shipment) => {
    if (shipment.generatedFile?.url) {
      const link = document.createElement('a');
      link.href = shipment.generatedFile.url;
      link.download = shipment.generatedFile.filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleViewDetails = (shipment: Shipment) => {
    setSelectedShipment(shipment);
    setIsDetailModalOpen(true);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Envios</h1>
          <p className="text-muted-foreground">
            Gerencie todos os seus envios para o Mercado Livre Full
          </p>
        </div>
        <Button 
          onClick={() => navigate('/ml-full-wizard')}
          className="flex items-center gap-2"
        >
          <Plus className="h-4 w-4" />
          Novo Envio
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Buscar por ID ou observações..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="all">Todos os status</option>
              <option value="pending">Pendente</option>
              <option value="received">Recebido</option>
              <option value="processing">Processando</option>
              <option value="rejected">Rejeitado</option>
            </select>

            <select
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            >
              <option value="all">Todas as datas</option>
              <option value="7d">Últimos 7 dias</option>
              <option value="30d">Últimos 30 dias</option>
              <option value="90d">Últimos 90 dias</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Package className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total de Envios</p>
                <p className="text-2xl font-bold">{formattedShipments.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Recebidos</p>
                <p className="text-2xl font-bold">
                  {formattedShipments.filter(s => s.status === 'received').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Clock className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Pendentes</p>
                <p className="text-2xl font-bold">
                  {formattedShipments.filter(s => s.status === 'pending').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Valor Total</p>
                <p className="text-xl font-bold">
                  R$ {formattedShipments.reduce((sum, s) => sum + s.totalValue, 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Shipments Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Truck className="h-5 w-5" />
            Lista de Envios
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredShipments.length === 0 ? (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Nenhum envio encontrado</h3>
              <p className="text-muted-foreground mb-4">
                {formattedShipments.length === 0 
                  ? 'Você ainda não criou nenhum envio. Comece criando seu primeiro envio!'
                  : 'Nenhum envio corresponde aos filtros aplicados.'
                }
              </p>
              {formattedShipments.length === 0 && (
                <Button onClick={() => navigate('/ml-full-wizard')}>
                  <Plus className="h-4 w-4 mr-2" />
                  Criar Primeiro Envio
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID do Envio</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Produtos</TableHead>
                    <TableHead>Total Unidades</TableHead>
                    <TableHead>Valor Total</TableHead>
                    <TableHead>Data Upload</TableHead>
                    <TableHead>Data Coleta</TableHead>
                    <TableHead className="text-right">Ações</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredShipments.map((shipment) => (
                    <TableRow key={shipment.id}>
                      <TableCell className="font-medium">
                        <button
                          onClick={() => handleViewDetails(shipment)}
                          className="text-blue-600 hover:text-blue-800 hover:underline"
                        >
                          {shipment.id.slice(0, 8)}...
                        </button>
                      </TableCell>
                      <TableCell>{getStatusBadge(shipment.status)}</TableCell>
                      <TableCell>{shipment.productCount}</TableCell>
                      <TableCell>{shipment.totalUnits}</TableCell>
                      <TableCell>
                        R$ {shipment.totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                      </TableCell>
                      <TableCell>
                        {new Date(shipment.uploadDate).toLocaleDateString('pt-BR')}
                      </TableCell>
                      <TableCell>
                        {shipment.collectionDate 
                          ? new Date(shipment.collectionDate).toLocaleDateString('pt-BR')
                          : '-'
                        }
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(shipment)}>
                              <Eye className="mr-2 h-4 w-4" />
                              Visualizar
                            </DropdownMenuItem>
                            {shipment.status === 'pending' && (
                              <DropdownMenuItem onClick={() => handleStatusUpdate(shipment.id, 'received')}>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Marcar como Recebido
                              </DropdownMenuItem>
                            )}
                            {shipment.generatedFile && (
                              <DropdownMenuItem onClick={() => handleDownload(shipment)}>
                                <Download className="mr-2 h-4 w-4" />
                                Download Planilha
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={() => handleDelete(shipment.id)}>
                              <Trash2 className="mr-2 h-4 w-4" />
                              Excluir
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Detail Modal */}
      <Dialog open={isDetailModalOpen} onOpenChange={setIsDetailModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalhes do Envio</DialogTitle>
            <DialogDescription>
              Informações completas sobre o envio selecionado
            </DialogDescription>
          </DialogHeader>
          {selectedShipment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">ID do Envio</label>
                  <p className="font-mono text-sm">{selectedShipment.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <div className="mt-1">{getStatusBadge(selectedShipment.status)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Produtos</label>
                  <p>{selectedShipment.productCount} produtos</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Total de Unidades</label>
                  <p>{selectedShipment.totalUnits} unidades</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Valor Total</label>
                  <p>R$ {selectedShipment.totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Data de Upload</label>
                  <p>{new Date(selectedShipment.uploadDate).toLocaleString('pt-BR')}</p>
                </div>
              </div>
              
              {selectedShipment.collectionDate && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Data de Coleta</label>
                  <p>{new Date(selectedShipment.collectionDate).toLocaleString('pt-BR')}</p>
                </div>
              )}
              
              {selectedShipment.expectedDelivery && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Entrega Prevista</label>
                  <p>{new Date(selectedShipment.expectedDelivery).toLocaleDateString('pt-BR')}</p>
                </div>
              )}
              
              {selectedShipment.notes && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Observações</label>
                  <p className="text-sm bg-muted p-3 rounded-md">{selectedShipment.notes}</p>
                </div>
              )}
              
              {selectedShipment.generatedFile && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Arquivo Gerado</label>
                  <div className="flex items-center gap-2 mt-1">
                    <FileSpreadsheet className="h-4 w-4" />
                    <span className="text-sm">{selectedShipment.generatedFile.filename}</span>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDownload(selectedShipment)}
                    >
                      <Download className="h-3 w-3 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
              )}

              {/* PDF Upload Component */}
              <div className="mt-6 border-t pt-6">
                <h4 className="font-medium mb-4">Upload de Comprovantes</h4>
                <PDFUploadComponent
                  onFilesUploaded={(files) => {
                    console.log('Arquivos enviados para o envio:', selectedShipment?.id, files);
                    // Aqui você pode integrar com o store ou API para associar os PDFs ao envio
                  }}
                  maxFiles={3}
                  allowMultiple={true}
                  className="w-full"
                />
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir este envio? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={confirmDelete}>
              Excluir
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
