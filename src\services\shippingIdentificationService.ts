import { 
  ShippingProduct, 
  GenerateSpreadsheetParams, 
  StockCalculationResult,
  Warehouse,
  AutoDistributionConfig,
  DistributionRule,
  ProductAdjustment
} from '../types/stock';

/**
 * Serviço para identificação de produtos que precisam ser enviados
 * ao Mercado Envios Full baseado em análise de estoque
 */
class ShippingIdentificationService {
  /**
   * Identifica produtos que precisam ser enviados baseado nos parâmetros
   */
  async identifyProductsForShipping(params: GenerateSpreadsheetParams): Promise<ShippingProduct[]> {
    try {
      let products: ShippingProduct[] = [];

      if (params.useStockCalculation) {
        // Usar cálculo de gap de estoque para identificar produtos
        products = await this.identifyByStockGap(params);
      } else {
        // Usar filtros manuais especificados
        products = await this.identifyByFilters(params);
      }

      // Aplicar ajustes manuais se especificados
      if (params.manualAdjustments && params.manualAdjustments.length > 0) {
        products = this.applyManualAdjustments(products, params.manualAdjustments);
      }

      // Aplicar limite máximo de produtos se especificado
      if (params.maxProducts && products.length > params.maxProducts) {
        products = this.prioritizeProducts(products, params.maxProducts);
      }

      return products;
    } catch (error) {
      console.error('Erro ao identificar produtos para envio:', error);
      throw new Error('Falha na identificação de produtos para envio');
    }
  }

  /**
   * Identifica produtos baseado no gap de estoque calculado
   */
  private async identifyByStockGap(params: GenerateSpreadsheetParams): Promise<ShippingProduct[]> {
    const products: ShippingProduct[] = [];
    const minGap = params.minGapThreshold || 1;

    try {
      // Buscar cálculos de estoque recentes
      const stockCalculations = await this.getStockCalculations(params.tenantId, params.productIds);

      for (const calc of stockCalculations) {
        // Verificar se o produto tem gap positivo (falta de estoque)
        if (calc.stockGap >= minGap) {
          const product = await this.createShippingProduct(calc, params);
          if (product) {
            products.push(product);
          }
        }
      }

      // Distribuir produtos entre armazéns se necessário
      if (params.warehouseIds && params.warehouseIds.length > 1) {
        return await this.distributeProductsToWarehouses(products, params.warehouseIds, params.tenantId);
      }

      return products;
    } catch (error) {
      console.error('Erro ao identificar por gap de estoque:', error);
      throw error;
    }
  }

  /**
   * Identifica produtos baseado em filtros manuais
   */
  private async identifyByFilters(params: GenerateSpreadsheetParams): Promise<ShippingProduct[]> {
    const products: ShippingProduct[] = [];

    try {
      // Buscar produtos baseado nos filtros
      const filteredProducts = await this.getFilteredProducts(params);

      for (const productData of filteredProducts) {
        const product = await this.createShippingProductFromData(productData, params);
        if (product) {
          products.push(product);
        }
      }

      return products;
    } catch (error) {
      console.error('Erro ao identificar por filtros:', error);
      throw error;
    }
  }

  /**
   * Cria um produto de envio baseado no cálculo de estoque
   */
  private async createShippingProduct(
    calc: StockCalculationResult, 
    params: GenerateSpreadsheetParams
  ): Promise<ShippingProduct | null> {
    try {
      // Buscar dados adicionais do produto
      const productData = await this.getProductData(calc.mlItemId, params.tenantId);
      
      if (!productData) {
        return null;
      }

      // Determinar quantidade a enviar (o gap de estoque)
      const quantityToSend = Math.max(1, Math.round(calc.stockGap));

      // Determinar armazém de destino
      const warehouse = await this.determineTargetWarehouse(
        calc.mlItemId, 
        params.warehouseIds, 
        params.tenantId
      );

      if (!warehouse) {
        return null;
      }

      return {
        sku: calc.sku,
        mlItemId: calc.mlItemId,
        title: productData.title,
        description: productData.description,
        barcode: productData.barcode,
        internalCode: productData.internalCode,
        height: productData.height,
        width: productData.width,
        depth: productData.depth,
        weight: productData.weight,
        unitPrice: productData.unitPrice,
        category: productData.category,
        brand: productData.brand,
        currentStock: calc.currentStock,
        quantityToSend: quantityToSend,
        warehouseId: warehouse.id,
        warehouseName: warehouse.name
      };
    } catch (error) {
      console.error(`Erro ao criar produto de envio para ${calc.sku}:`, error);
      return null;
    }
  }

  /**
   * Cria um produto de envio baseado em dados brutos
   */
  private async createShippingProductFromData(
    productData: any, 
    params: GenerateSpreadsheetParams
  ): Promise<ShippingProduct | null> {
    try {
      // Determinar quantidade padrão (pode ser configurável)
      const quantityToSend = productData.suggestedQuantity || 10;

      // Determinar armazém de destino
      const warehouse = await this.determineTargetWarehouse(
        productData.mlItemId, 
        params.warehouseIds, 
        params.tenantId
      );

      if (!warehouse) {
        return null;
      }

      return {
        sku: productData.sku,
        mlItemId: productData.mlItemId,
        title: productData.title,
        description: productData.description,
        barcode: productData.barcode,
        internalCode: productData.internalCode,
        height: productData.height,
        width: productData.width,
        depth: productData.depth,
        weight: productData.weight,
        unitPrice: productData.unitPrice,
        category: productData.category,
        brand: productData.brand,
        currentStock: productData.currentStock || 0,
        quantityToSend: quantityToSend,
        warehouseId: warehouse.id,
        warehouseName: warehouse.name
      };
    } catch (error) {
      console.error(`Erro ao criar produto de envio:`, error);
      return null;
    }
  }

  /**
   * Distribui produtos entre múltiplos armazéns
   */
  private async distributeProductsToWarehouses(
    products: ShippingProduct[], 
    warehouseIds: string[], 
    tenantId: string
  ): Promise<ShippingProduct[]> {
    try {
      const warehouses = await this.getWarehouses(warehouseIds, tenantId);
      const distributionConfig = await this.getDistributionConfig(tenantId);
      
      if (!distributionConfig) {
        // Distribuição simples - round robin
        return this.simpleDistribution(products, warehouses);
      }

      // Distribuição baseada em configuração
      return this.smartDistribution(products, warehouses, distributionConfig);
    } catch (error) {
      console.error('Erro na distribuição de produtos:', error);
      return products; // Retorna produtos sem distribuição em caso de erro
    }
  }

  /**
   * Distribuição simples entre armazéns (round robin)
   */
  private simpleDistribution(products: ShippingProduct[], warehouses: Warehouse[]): ShippingProduct[] {
    if (warehouses.length === 0) {
      return products;
    }
    
    return products.map((product, index) => {
      const warehouse = warehouses[index % warehouses.length];
      if (!warehouse) {
        return product; // Retorna produto sem modificação se não há armazém
      }
      
      return {
        ...product,
        warehouseId: warehouse.id,
        warehouseName: warehouse.name
      };
    });
  }

  /**
   * Distribuição inteligente baseada em regras
   */
  private smartDistribution(
    products: ShippingProduct[], 
    warehouses: Warehouse[], 
    config: AutoDistributionConfig
  ): ShippingProduct[] {
    if (warehouses.length === 0) {
      return products;
    }
    
    const distributedProducts: ShippingProduct[] = [];
    const warehouseLoads = new Map<string, { count: number; value: number }>();

    // Inicializar cargas dos armazéns
    warehouses.forEach(w => {
      warehouseLoads.set(w.id, { count: 0, value: 0 });
    });

    for (const product of products) {
      let targetWarehouse = this.selectWarehouseByRules(product, warehouses, config, warehouseLoads);
      
      if (!targetWarehouse) {
        // Fallback para distribuição por prioridade
        const sortedWarehouses = warehouses.sort((a, b) => b.priority - a.priority);
        targetWarehouse = sortedWarehouses.length > 0 ? sortedWarehouses[0] : null;
      }

      if (!targetWarehouse) {
        // Se ainda não há armazém, pular este produto
        continue;
      }

      // Atualizar carga do armazém
      const load = warehouseLoads.get(targetWarehouse.id);
      if (load) {
        load.count += product.quantityToSend;
        load.value += product.quantityToSend * product.unitPrice;
      }

      distributedProducts.push({
        ...product,
        warehouseId: targetWarehouse.id,
        warehouseName: targetWarehouse.name
      });
    }

    return distributedProducts;
  }

  /**
   * Seleciona armazém baseado em regras de distribuição
   */
  private selectWarehouseByRules(
    product: ShippingProduct,
    warehouses: Warehouse[],
    config: AutoDistributionConfig,
    warehouseLoads: Map<string, { count: number; value: number }>
  ): Warehouse | null {
    // Aplicar regras em ordem de prioridade
    const sortedRules = config.rules
      .filter(rule => rule.isActive)
      .sort((a, b) => b.priority - a.priority);

    for (const rule of sortedRules) {
      if (this.productMatchesRule(product, rule)) {
        // Encontrar armazém preferido disponível
        const preferredWarehouses = rule.action.preferredWarehouses || [];
        const excludedWarehouses = rule.action.excludedWarehouses || [];

        for (const warehouseId of preferredWarehouses) {
          const warehouse = warehouses.find(w => w.id === warehouseId);
          if (warehouse && !excludedWarehouses.includes(warehouseId)) {
            const load = warehouseLoads.get(warehouseId);
            
            // Verificar limites
            if (load && this.warehouseCanAcceptProduct(warehouse, load, rule, config)) {
              return warehouse;
            }
          }
        }
      }
    }

    return null;
  }

  /**
   * Verifica se produto atende às condições da regra
   */
  private productMatchesRule(product: ShippingProduct, rule: DistributionRule): boolean {
    const conditions = rule.conditions;

    if (conditions.categories && conditions.categories.length > 0) {
      if (!product.category || !conditions.categories.includes(product.category)) {
        return false;
      }
    }

    if (conditions.brands && conditions.brands.length > 0) {
      if (!product.brand || !conditions.brands.includes(product.brand)) {
        return false;
      }
    }

    if (conditions.minValue !== undefined) {
      if (product.unitPrice < conditions.minValue) {
        return false;
      }
    }

    if (conditions.maxValue !== undefined) {
      if (product.unitPrice > conditions.maxValue) {
        return false;
      }
    }

    if (conditions.minWeight !== undefined) {
      if (!product.weight || product.weight < conditions.minWeight) {
        return false;
      }
    }

    if (conditions.maxWeight !== undefined) {
      if (!product.weight || product.weight > conditions.maxWeight) {
        return false;
      }
    }

    return true;
  }

  /**
   * Verifica se armazém pode aceitar o produto
   */
  private warehouseCanAcceptProduct(
    warehouse: Warehouse,
    currentLoad: { count: number; value: number },
    rule: DistributionRule,
    config: AutoDistributionConfig
  ): boolean {
    // Verificar capacidade máxima do armazém
    if (warehouse.maxCapacity && currentLoad.count >= warehouse.maxCapacity) {
      return false;
    }

    // Verificar limites da regra
    if (rule.action.maxQuantityPerWarehouse && 
        currentLoad.count >= rule.action.maxQuantityPerWarehouse) {
      return false;
    }

    // Verificar limites globais
    if (config.maxProductsPerWarehouse && 
        currentLoad.count >= config.maxProductsPerWarehouse) {
      return false;
    }

    if (config.maxValuePerWarehouse && 
        currentLoad.value >= config.maxValuePerWarehouse) {
      return false;
    }

    return true;
  }

  /**
   * Aplica ajustes manuais aos produtos identificados
   */
  private applyManualAdjustments(
    products: ShippingProduct[], 
    adjustments: ProductAdjustment[]
  ): ShippingProduct[] {
    const adjustedProducts = [...products];
    
    for (const adjustment of adjustments) {
      const productIndex = adjustedProducts.findIndex(p => p.sku === adjustment.sku);
      
      switch (adjustment.action) {
        case 'exclude':
          if (productIndex >= 0) {
            adjustedProducts.splice(productIndex, 1);
          }
          break;
          
        case 'modify':
          if (productIndex >= 0) {
            if (adjustment.newQuantity !== undefined) {
              adjustedProducts[productIndex].quantityToSend = adjustment.newQuantity;
            }
          }
          break;
          
        case 'include':
          // Adicionar produto se não estiver na lista
          if (productIndex < 0) {
            // Criar produto baseado no ajuste
            // (implementar lógica para criar produto)
          }
          break;
      }
    }
    
    return adjustedProducts;
  }

  /**
   * Prioriza produtos quando há limite máximo
   */
  private prioritizeProducts(products: ShippingProduct[], maxProducts: number): ShippingProduct[] {
    return products
      .sort((a, b) => {
        // Prioridade 1: Quantidade a enviar (descendente)
        if (a.quantityToSend !== b.quantityToSend) {
          return b.quantityToSend - a.quantityToSend;
        }
        
        // Prioridade 2: Valor unitário (descendente)
        if (a.unitPrice !== b.unitPrice) {
          return b.unitPrice - a.unitPrice;
        }
        
        // Prioridade 3: SKU (ascendente)
        return a.sku.localeCompare(b.sku);
      })
      .slice(0, maxProducts);
  }

  /**
   * Busca cálculos de estoque (mock - integrar com serviço real)
   */
  private async getStockCalculations(
    tenantId: string, 
    productIds?: string[]
  ): Promise<StockCalculationResult[]> {
    // Mock - substituir por integração real com stockCalculationService
    return [];
  }

  /**
   * Busca dados do produto (mock - integrar com API do ML)
   */
  private async getProductData(mlItemId: string, tenantId: string): Promise<any> {
    // Mock - substituir por integração real com API do ML
    return {
      title: 'Produto Exemplo',
      description: 'Descrição do produto',
      barcode: '1234567890123',
      height: 10,
      width: 15,
      depth: 20,
      weight: 500,
      unitPrice: 29.90,
      category: 'Eletrônicos',
      brand: 'Marca Exemplo'
    };
  }

  /**
   * Determina armazém de destino para o produto
   */
  private async determineTargetWarehouse(
    mlItemId: string, 
    warehouseIds?: string[], 
    tenantId?: string
  ): Promise<Warehouse | null> {
    // Mock - substituir por lógica real de seleção de armazém
    return {
      id: 'warehouse-1',
      name: 'Armazém Principal',
      code: 'ARM001',
      address: {
        street: 'Rua Exemplo',
        number: '123',
        neighborhood: 'Centro',
        city: 'São Paulo',
        state: 'SP',
        zipCode: '01000-000',
        country: 'BR'
      },
      isActive: true,
      priority: 1,
      createdAt: new Date(),
      updatedAt: new Date()
    };
  }

  /**
   * Busca produtos filtrados (mock)
   */
  private async getFilteredProducts(params: GenerateSpreadsheetParams): Promise<any[]> {
    // Mock - substituir por busca real no banco de dados
    return [];
  }

  /**
   * Busca armazéns (mock)
   */
  private async getWarehouses(warehouseIds: string[], tenantId: string): Promise<Warehouse[]> {
    // Mock - substituir por busca real no banco de dados
    return [];
  }

  /**
   * Busca configuração de distribuição (mock)
   */
  private async getDistributionConfig(tenantId: string): Promise<AutoDistributionConfig | null> {
    return null;
  }
}

export const shippingIdentificationService = new ShippingIdentificationService();
