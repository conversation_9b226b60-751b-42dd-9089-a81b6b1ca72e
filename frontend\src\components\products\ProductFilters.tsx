import React, { useState, useEffect } from 'react';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select-radix';

interface ProductFiltersProps {
  onFilterChange: (filters: { search?: string; category?: string; status?: string }) => void;
}

export default function ProductFilters({ onFilterChange }: ProductFiltersProps) {
  const [search, setSearch] = useState('');
  const [category, setCategory] = useState('');
  const [status, setStatus] = useState('');

  useEffect(() => {
    const handler = setTimeout(() => {
      onFilterChange({ search, category, status });
    }, 300);

    return () => clearTimeout(handler);
  }, [search, category, status, onFilterChange]);

  const categoryOptions = [
    { label: 'Todas as Categorias', value: '' },
    { label: 'Eletrônicos', value: 'Eletrônicos' },
    { label: 'Roupas', value: 'Roupas' },
    { label: 'Casa', value: 'Casa' },
  ];

  const statusOptions = [
    { label: 'Todos os Status', value: '' },
    { label: 'Ativo', value: 'Ativo' },
    { label: 'Inativo', value: 'Inativo' },
  ];

  const handleClearFilters = () => {
    setSearch('');
    setCategory('');
    setStatus('');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Filtros de Produtos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-4 items-end">
          <div className="space-y-2">
            <Label htmlFor="search">Buscar</Label>
            <Input
              id="search"
              type="text"
              placeholder="Pesquisar por SKU ou Título"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="category">Categoria</Label>
            <Select value={category} onValueChange={setCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione uma categoria" />
              </SelectTrigger>
              <SelectContent>
                {categoryOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Selecione um status" />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <Button variant="secondary" onClick={handleClearFilters}>Limpar Filtros</Button>
        </div>
      </CardContent>
    </Card>
  );
}