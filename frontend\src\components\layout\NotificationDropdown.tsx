import React, { useState } from 'react';
import {
  BellIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/react/24/outline';
import { useNotifications } from '../../hooks/useNotifications';

interface NotificationDropdownProps {
  className?: string;
}

export const NotificationDropdown: React.FC<NotificationDropdownProps> = ({
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);
  
  const {
    notifications,
    unreadCount,
    isConnected,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll
  } = useNotifications();

  const getNotificationIcon = (severity: string) => {
    const iconClass = "h-5 w-5";
    
    switch (severity) {
      case 'success':
        return <CheckCircleIcon className={`${iconClass} text-green-500`} />;
      case 'warning':
        return <ExclamationTriangleIcon className={`${iconClass} text-yellow-500`} />;
      case 'error':
        return <ExclamationTriangleIcon className={`${iconClass} text-red-500`} />;
      default:
        return <InformationCircleIcon className={`${iconClass} text-blue-500`} />;
    }
  };

  const formatTime = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h`;
    return `${Math.floor(diffInMinutes / 1440)}d`;
  };

  return (
    <div className={`relative ${className}`}>
      {/* Botão de notificações */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="relative p-2 text-muted-foreground hover:text-foreground hover:bg-accent rounded-full transition-colors"
        aria-label={`Notificações ${unreadCount > 0 ? `(${unreadCount} não lidas)` : ''}`}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <BellIcon className="h-6 w-6" />
        
        {/* Badge de contador */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
        
        {/* Indicador de conexão */}
        <span className={`absolute bottom-0 right-0 h-3 w-3 rounded-full border-2 border-card ${
          isConnected ? 'bg-green-400' : 'bg-muted'
        }`} />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <>
          {/* Overlay para fechar */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Conteúdo do dropdown */}
          <div className="absolute right-0 mt-2 w-96 bg-card rounded-lg shadow-lg border border-border z-20">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h3 className="text-lg font-medium text-foreground">
                Notificações
                {unreadCount > 0 && (
                  <span className="ml-2 text-sm text-muted-foreground">
                    ({unreadCount} não lida{unreadCount !== 1 ? 's' : ''})
                  </span>
                )}
              </h3>
              
              <div className="flex items-center gap-2">
                {/* Status de conexão */}
                <span className={`text-xs px-2 py-1 rounded-full ${
                  isConnected 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' 
                    : 'bg-muted text-muted-foreground'
                }`}>
                  {isConnected ? 'Conectado' : 'Desconectado'}
                </span>
                
                {/* Marcar todas como lidas */}
                {unreadCount > 0 && (
                  <button
                    onClick={markAllAsRead}
                    className="text-sm text-primary hover:text-primary/80"
                  >
                    Marcar todas como lidas
                  </button>
                )}
              </div>
            </div>

            {/* Lista de notificações */}
            <div className="max-h-96 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-8 text-center text-muted-foreground">
                  <BellIcon className="h-8 w-8 mx-auto mb-2 text-muted-foreground/50" />
                  <p>Nenhuma notificação</p>
                </div>
              ) : (
                <div className="divide-y divide-border" role="list" aria-label="Lista de notificações">
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 hover:bg-accent/50 transition-colors ${
                        !notification.isRead ? 'bg-primary/5' : ''
                      }`}
                      role="listitem"
                      aria-label={`Notificação: ${notification.title}`}
                    >
                      <div className="flex items-start gap-3">
                        {/* Ícone da notificação */}
                        <div className="flex-shrink-0 mt-1">
                          {getNotificationIcon(notification.severity)}
                        </div>
                        
                        {/* Conteúdo */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className={`text-sm font-medium ${
                                !(notification as any).read ? 'text-foreground' : 'text-muted-foreground'
                              }`}>
                                {notification.title}
                              </h4>
                              <p className="text-sm text-muted-foreground mt-1">
                                {notification.message}
                              </p>
                              <p className="text-xs text-muted-foreground/70 mt-2">
                                {formatTime(notification.timestamp)}
                              </p>
                            </div>
                            
                            {/* Ações */}
                            <div className="flex items-center gap-1 ml-2">
                              {!(notification as any).read && (
                                <button
                                  onClick={() => markAsRead(notification.id)}
                                  className="p-1 text-muted-foreground hover:text-green-600 rounded"
                                  title="Marcar como lida"
                                >
                                  <CheckIcon className="h-4 w-4" />
                                </button>
                              )}
                              <button
                                onClick={() => removeNotification(notification.id)}
                                className="p-1 text-muted-foreground hover:text-destructive rounded"
                                title="Remover"
                              >
                                <XMarkIcon className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="p-3 border-t border-border bg-muted/30">
                <div className="flex items-center justify-between">
                  <button
                    onClick={clearAll}
                    className="text-sm text-destructive hover:text-destructive/80"
                  >
                    Limpar todas
                  </button>
                  
                  <button
                    onClick={() => setIsOpen(false)}
                    className="text-sm text-muted-foreground hover:text-foreground"
                  >
                    Fechar
                  </button>
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationDropdown;