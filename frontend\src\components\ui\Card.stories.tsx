 
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './Card';
import { Button } from './Button';
import { Stack } from './Stack';
import { Input } from './Input';

const meta = {
  title: 'Components/Card',
  component: Card,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'elevated', 'outline', 'ghost', 'flat', 'primary', 'secondary', 'tertiary'],
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
    },
    hoverEffect: { control: 'boolean' },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <>
        <CardHeader>
          <CardTitle>Título do Cartão</CardTitle>
          <CardDescription>Uma breve descrição do conteúdo do cartão.</CardDescription>
        </CardHeader>
        <CardContent>
          <p>Este é o conteúdo principal do cartão.</p>
          <p>Você pode colocar qualquer elemento aqui.</p>
        </CardContent>
        <CardFooter>
          <Button variant="primary" size="sm">Ação</Button>
        </CardFooter>
      </>
    ),
  },
};

export const Elevated: Story = {
  args: {
    variant: 'elevated',
    children: (
      <>
        <CardHeader>
          <CardTitle>Cartão Elevado</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Este cartão possui uma sombra para destaque.</p>
        </CardContent>
      </>
    ),
  },
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    children: (
      <>
        <CardHeader>
          <CardTitle>Cartão Outline</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Este cartão possui borda e fundo transparente.</p>
        </CardContent>
      </>
    ),
  },
};

export const WithHoverEffect: Story = {
  args: {
    hoverEffect: true,
    children: (
      <>
        <CardHeader>
          <CardTitle>Efeito Hover</CardTitle>
        </CardHeader>
        <CardContent>
          <p>Este cartão muda de aparência ao passar o mouse.</p>
        </CardContent>
      </>
    ),
  },
};

export const LoginFormCard: Story = {
  args: {
    className: 'w-96',
    children: (
      <>
        <CardHeader className="text-center">
          <CardTitle>Login</CardTitle>
          <CardDescription>Entre na sua conta Magnow</CardDescription>
        </CardHeader>
        <CardContent>
          <Stack spacing="md">
            <Input label="Email" type="email" placeholder="<EMAIL>" />
            <Input label="Senha" type="password" placeholder="********" />
            <Button variant="primary" className="w-full">Entrar</Button>
          </Stack>
        </CardContent>
        <CardFooter className="justify-center">
          <Button variant="link">Esqueceu a senha?</Button>
        </CardFooter>
      </>
    ),
  },
}; 
