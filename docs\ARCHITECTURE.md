# Arquitetura do Magnow

Este documento descreve a arquitetura técnica do Magnow, uma plataforma SaaS para gestão de estoque no Mercado Livre Full.

## Visão Geral

O Magnow é construído como uma aplicação web moderna, seguindo uma arquitetura de microsserviços com foco em escalabilidade, manutenibilidade e segurança.

### Stack Tecnológico

#### Backend
- **Node.js** com **TypeScript**
- **Express.js** para API REST
- **Prisma** como ORM
- **PostgreSQL** para banco de dados principal
- **Redis** para cache e gerenciamento de sessões
- **Jest** para testes

#### Frontend
- **React** com **TypeScript**
- **Vite** como bundler
- **TailwindCSS** para estilização
- **Redux Toolkit** para gerenciamento de estado
- **Storybook** para documentação de componentes
- **Vitest** para testes

## Arquitetura do Backend

### Estrutura de Diretórios

```
src/
├── config/           # Configurações (banco de dados, cache, etc.)
├── controllers/      # Controladores da API
├── middleware/       # Middlewares Express
├── routes/           # Rotas da API
├── services/         # Lógica de negócios
├── types/            # Definições de tipos TypeScript
└── utils/            # Utilitários e helpers
```

### Camadas da Aplicação

1. **API Layer** (Controllers & Routes)
   - Gerenciamento de requisições HTTP
   - Validação de input
   - Autenticação e autorização

2. **Service Layer**
   - Lógica de negócios
   - Cálculos de estoque
   - Integração com Mercado Livre
   - Geração de planilhas

3. **Data Layer**
   - Acesso ao banco de dados via Prisma
   - Cache com Redis
   - Persistência de dados

### Serviços Principais

- **stockCalculationService**: Calcula o estoque alvo com base em algoritmos de análise de vendas
- **mercadoLivreApiService**: Gerencia a integração com a API do Mercado Livre
- **spreadsheetGeneratorService**: Gera planilhas de remessa de entrada
- **stockAlertService**: Monitora níveis de estoque e gera alertas

## Arquitetura do Frontend

### Estrutura de Diretórios

```
frontend/src/
├── components/       # Componentes React reutilizáveis
├── pages/            # Páginas da aplicação
├── services/         # Serviços de API
├── store/            # Estado global (Redux)
├── hooks/            # Hooks personalizados
├── styles/           # Estilos globais
└── utils/            # Utilitários
```

### Padrões de Design

- **Atomic Design** para organização de componentes
- **Container/Presenter Pattern** para separação de lógica e apresentação
- **Custom Hooks** para lógica reutilizável
- **Redux Toolkit** para gerenciamento de estado global

## Segurança

### Autenticação e Autorização

- **JWT** para autenticação de API
- **OAuth 2.0** para integração com Mercado Livre
- **Multi-tenancy** com isolamento de dados por tenant

### Proteções

- Rate limiting
- CORS configurado
- Validação de input
- Headers de segurança
- Sanitização de dados

## Cache e Performance

### Estratégia de Cache

- **Redis** para cache distribuído
- Cache de sessão
- Cache de dados frequentemente acessados
- Invalidação seletiva de cache

### Otimizações

- Compressão de resposta
- Lazy loading de componentes
- Minificação de assets
- CDN para assets estáticos

## Monitoramento e Logs

### Sistema de Logs

- Logs estruturados
- Níveis de log (debug, info, warn, error)
- Rotação de logs
- Monitoramento de performance

### Métricas

- Tempo de resposta da API
- Taxa de sucesso/erro
- Uso de recursos
- Performance de cache

## Escalabilidade

### Horizontal

- Arquitetura stateless
- Cache distribuído
- Load balancing
- Containers Docker

### Vertical

- Otimização de queries
- Índices de banco de dados
- Pool de conexões
- Processamento em background

## Integração com Mercado Livre

### OAuth 2.0

- Fluxo de autorização
- Refresh tokens
- Gestão de múltiplas contas
- Rate limiting

### Sincronização

- Webhooks para atualizações em tempo real
- Sincronização periódica
- Filas de processamento
- Tratamento de erros

## Deployment

### Containerização

- Dockerfile otimizado
- Docker Compose para desenvolvimento
- Configuração de produção

### CI/CD

- GitHub Actions
- Testes automatizados
- Build e deploy automático
- Ambientes de staging e produção