/**
 * Serviço de Cálculo de Estoque Ideal
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { SalesAnalysisService } from './salesAnalysisService';
import { 
  StockConfiguration,
  StockCalculationResult,
  StockCalculationParams,
  BatchStockCalculationResult,
  StockCalculationError,
  CurrentStock,
  SalesAnalysis,
  StockStatus,
  StockPriority,
  StockAction,
  StockSystemConfiguration
} from '../types/stock';
import { ValidationError, DatabaseError } from '../middleware/errorHandler';
import { MercadoLivreApiService } from './mercadoLivreApiService';
import CacheService, { CacheNamespaces, CacheConfigs } from './cacheService';

export class StockCalculationService {
  private prisma: PrismaClient;
  private salesAnalysisService: SalesAnalysisService;
  private mercadoLivreApi: MercadoLivreApiService;
  private cache: CacheService;

  constructor(prisma?: PrismaClient, cache?: CacheService) {
    this.prisma = prisma || new PrismaClient();
    this.cache = cache || new CacheService();
    this.salesAnalysisService = new SalesAnalysisService(this.prisma, this.cache);
    this.mercadoLivreApi = new MercadoLivreApiService();
  }

  /**
   * Calcula estoque ideal para um produto específico
   */
  public async calculateIdealStock(
    tenantId: string,
    mlItemId: string,
    configuration?: Partial<StockConfiguration>
  ): Promise<StockCalculationResult> {
    const startTime = Date.now();

    try {
      logger.info('Iniciando cálculo de estoque ideal', { tenantId, mlItemId });

      // Busca ou cria configuração do produto
      const config = await this.getOrCreateConfiguration(tenantId, mlItemId, configuration);

      // Busca estoque atual
      const currentStock = await this.getCurrentStock(tenantId, mlItemId);

      // Analisa vendas históricas
      const salesAnalysis = await this.salesAnalysisService.analyzeSalesHistory(
        tenantId,
        mlItemId,
        config.analysisWindowDays
      );

      // Calcula estoque ideal
      const result = this.performStockCalculation(config, currentStock, salesAnalysis);

      // Salva resultado no histórico
      await this.saveCalculationHistory(tenantId, config, result, startTime);

      logger.info('Cálculo de estoque ideal concluído', { 
        tenantId, 
        mlItemId, 
        idealStock: result.idealStock,
        stockGap: result.stockGap,
        status: result.status,
        calculationTimeMs: Date.now() - startTime
      });

      return result;
    } catch (error) {
      logger.error('Erro no cálculo de estoque ideal', { error, tenantId, mlItemId });
      throw new DatabaseError('Erro ao calcular estoque ideal', { originalError: error });
    }
  }

  /**
   * Calcula estoque ideal para múltiplos produtos
   */
  public async calculateBatchIdealStock(
    tenantId: string,
    params: StockCalculationParams = {}
  ): Promise<BatchStockCalculationResult> {
    const startTime = Date.now();
    const results: StockCalculationResult[] = [];
    const errors: StockCalculationError[] = [];

    try {
      // Busca produtos para calcular
      const mlItemIds = await this.getItemsToCalculate(tenantId, params);

      logger.info('Iniciando cálculo em lote', { 
        tenantId, 
        totalItems: mlItemIds.length,
        forceRecalculation: params.forceRecalculation 
      });

      // Processa cada produto
      for (const mlItemId of mlItemIds) {
        try {
          // Verifica se precisa recalcular
          if (!params.forceRecalculation) {
            const recentCalculation = await this.getRecentCalculation(tenantId, mlItemId);
            if (recentCalculation) {
              results.push(recentCalculation);
              continue;
            }
          }

          const result = await this.calculateIdealStock(tenantId, mlItemId);
          results.push(result);
        } catch (error: any) {
          errors.push({
            mlItemId,
            sku: '',
            error: 'Erro no cálculo de estoque',
            details: error.message,
            timestamp: new Date()
          });
        }
      }

      const executionTimeMs = Date.now() - startTime;

      const batchResult: BatchStockCalculationResult = {
        calculatedAt: new Date(),
        totalItems: mlItemIds.length,
        successfulCalculations: results.length,
        failedCalculations: errors.length,
        results,
        errors,
        executionTimeMs
      };

      logger.info('Cálculo em lote concluído', {
        tenantId,
        totalItems: batchResult.totalItems,
        successful: batchResult.successfulCalculations,
        failed: batchResult.failedCalculations,
        executionTimeMs
      });

      return batchResult;
    } catch (error) {
      logger.error('Erro no cálculo em lote', { error, tenantId });
      throw new DatabaseError('Erro ao calcular estoque em lote', { originalError: error });
    }
  }

  /**
   * Realiza o cálculo principal de estoque
   */
  private performStockCalculation(
    config: StockConfiguration,
    currentStock: CurrentStock,
    salesAnalysis: SalesAnalysis
  ): StockCalculationResult {
    // Ajusta vendas médias com fatores de sazonalidade e tendência
    const adjustedDailySales = this.adjustSalesForFactors(
      salesAnalysis.averageDailySales,
      salesAnalysis,
      config
    );

    // Calcula estoque de segurança
    const safetyStock = this.calculateSafetyStock(
      adjustedDailySales,
      config.safetyStockDays,
      salesAnalysis.salesStandardDeviation
    );

    // Calcula estoque ideal baseado na cobertura de dias
    const idealStock = this.calculateIdealStockLevel(
      adjustedDailySales,
      config.coverageDays,
      safetyStock,
      config
    );

    // Calcula gap de estoque
    const stockGap = this.calculateStockGap(
      idealStock,
      currentStock.currentQuantity,
      currentStock.inTransitQuantity,
      safetyStock
    );

    // Calcula dias de cobertura disponível
    const coverageDaysAvailable = this.calculateCoverageDays(
      currentStock.currentQuantity + currentStock.inTransitQuantity,
      adjustedDailySales
    );

    // Determina status e prioridade
    const status = this.determineStockStatus(stockGap, config);
    const priority = this.determineStockPriority(status, stockGap, adjustedDailySales);

    // Determina ação recomendada
    const { recommendedAction, recommendedQuantity } = this.determineRecommendedAction(
      status,
      stockGap,
      idealStock,
      adjustedDailySales
    );

    // Calcula próxima data de revisão
    const nextReviewDate = this.calculateNextReviewDate(
      status,
      adjustedDailySales,
      coverageDaysAvailable
    );

    return {
      mlItemId: config.mlItemId,
      sku: config.sku,
      calculatedAt: new Date(),
      currentStock: currentStock.currentQuantity,
      inTransitStock: currentStock.inTransitQuantity,
      safetyStock,
      averageDailySales: salesAnalysis.averageDailySales,
      adjustedDailySales,
      idealStock,
      stockGap,
      coverageDaysAvailable,
      status,
      priority,
      recommendedAction,
      recommendedQuantity,
      nextReviewDate
    };
  }

  /**
   * Ajusta vendas médias com fatores de sazonalidade e tendência
   */
  private adjustSalesForFactors(
    averageDailySales: number,
    salesAnalysis: SalesAnalysis,
    config: StockConfiguration
  ): number {
    let adjustedSales = averageDailySales;

    // Aplica fator de sazonalidade
    if (salesAnalysis.seasonalityDetected && salesAnalysis.seasonalityPattern) {
      const seasonalityFactor = salesAnalysis.seasonalityPattern.seasonalityFactor;
      adjustedSales *= Math.min(seasonalityFactor * config.seasonalityFactor, 2.0); // Máximo 2x
    }

    // Aplica fator de tendência
    if (salesAnalysis.trendDirection !== 'stable') {
      const trendMultiplier = 1 + (salesAnalysis.trendStrength * config.trendWeight);
      
      if (salesAnalysis.trendDirection === 'increasing') {
        adjustedSales *= Math.min(trendMultiplier, 1.5); // Máximo 50% de aumento
      } else if (salesAnalysis.trendDirection === 'decreasing') {
        adjustedSales *= Math.max(1 / trendMultiplier, 0.7); // Máximo 30% de redução
      }
    }

    // Garante valor mínimo
    return Math.max(adjustedSales, 0.1);
  }

  /**
   * Calcula estoque de segurança
   */
  private calculateSafetyStock(
    adjustedDailySales: number,
    safetyStockDays: number,
    salesStandardDeviation: number
  ): number {
    // Estoque de segurança baseado em dias + variabilidade
    const baseSafetyStock = adjustedDailySales * safetyStockDays;
    
    // Adiciona buffer baseado na variabilidade das vendas
    const variabilityBuffer = salesStandardDeviation * 1.65; // 95% de confiança
    
    return Math.ceil(baseSafetyStock + variabilityBuffer);
  }

  /**
   * Calcula nível de estoque ideal
   */
  private calculateIdealStockLevel(
    adjustedDailySales: number,
    coverageDays: number,
    safetyStock: number,
    config: StockConfiguration
  ): number {
    // Estoque ideal = (vendas diárias ajustadas × dias de cobertura) + estoque de segurança
    const baseIdealStock = adjustedDailySales * coverageDays;
    const idealStock = Math.ceil(baseIdealStock + safetyStock);

    // Aplica limites configurados
    if (config.maxStockLevel && idealStock > config.maxStockLevel) {
      return config.maxStockLevel;
    }

    if (idealStock < config.minStockLevel) {
      return config.minStockLevel;
    }

    return idealStock;
  }

  /**
   * Calcula gap de estoque
   */
  private calculateStockGap(
    idealStock: number,
    currentStock: number,
    inTransitStock: number,
    safetyStock: number
  ): number {
    // Gap = Estoque Ideal - (Estoque Atual + Em Trânsito)
    const totalAvailableStock = currentStock + inTransitStock;
    return idealStock - totalAvailableStock;
  }

  /**
   * Calcula dias de cobertura disponível
   */
  private calculateCoverageDays(
    totalStock: number,
    adjustedDailySales: number
  ): number {
    if (adjustedDailySales <= 0) return 999; // Sem vendas = cobertura infinita
    return totalStock / adjustedDailySales;
  }

  /**
   * Determina status do estoque
   */
  private determineStockStatus(
    stockGap: number,
    config: StockConfiguration
  ): StockStatus {
    if (stockGap >= config.criticalGapThreshold) {
      return 'critical';
    } else if (stockGap >= config.warningGapThreshold) {
      return 'warning';
    } else if (stockGap <= -config.warningGapThreshold) {
      return 'excess';
    } else {
      return 'optimal';
    }
  }

  /**
   * Determina prioridade do estoque
   */
  private determineStockPriority(
    status: StockStatus,
    stockGap: number,
    adjustedDailySales: number
  ): StockPriority {
    if (status === 'critical') {
      return 'high';
    } else if (status === 'warning') {
      // Prioridade baseada na velocidade de vendas
      const daysUntilStockout = Math.abs(stockGap) / Math.max(adjustedDailySales, 0.1);
      return daysUntilStockout <= 7 ? 'high' : 'medium';
    } else if (status === 'excess') {
      return 'low';
    } else {
      return 'medium';
    }
  }

  /**
   * Determina ação recomendada e quantidade
   */
  private determineRecommendedAction(
    status: StockStatus,
    stockGap: number,
    idealStock: number,
    adjustedDailySales: number
  ): { recommendedAction: StockAction; recommendedQuantity: number } {
    if (status === 'critical') {
      return {
        recommendedAction: 'urgent_restock',
        recommendedQuantity: Math.max(stockGap, Math.ceil(adjustedDailySales * 7)) // Mínimo 7 dias
      };
    } else if (status === 'warning') {
      return {
        recommendedAction: 'plan_restock',
        recommendedQuantity: stockGap
      };
    } else if (status === 'excess') {
      const excessQuantity = Math.abs(stockGap);
      if (excessQuantity > idealStock * 0.5) { // Excesso > 50% do ideal
        return {
          recommendedAction: 'reduce_stock',
          recommendedQuantity: Math.floor(excessQuantity * 0.3) // Reduzir 30% do excesso
        };
      } else {
        return {
          recommendedAction: 'monitor',
          recommendedQuantity: 0
        };
      }
    } else {
      return {
        recommendedAction: 'no_action',
        recommendedQuantity: 0
      };
    }
  }

  /**
   * Calcula próxima data de revisão
   */
  private calculateNextReviewDate(
    status: StockStatus,
    adjustedDailySales: number,
    coverageDaysAvailable: number
  ): Date {
    const nextReview = new Date();
    
    if (status === 'critical') {
      nextReview.setDate(nextReview.getDate() + 1); // Revisar diariamente
    } else if (status === 'warning') {
      const daysToReview = Math.min(Math.floor(coverageDaysAvailable / 2), 7);
      nextReview.setDate(nextReview.getDate() + Math.max(daysToReview, 2));
    } else if (status === 'excess') {
      nextReview.setDate(nextReview.getDate() + 14); // Revisar quinzenalmente
    } else {
      nextReview.setDate(nextReview.getDate() + 7); // Revisar semanalmente
    }

    return nextReview;
  }

  /**
   * Busca ou cria configuração do produto
   */
  private async getOrCreateConfiguration(
    tenantId: string,
    mlItemId: string,
    overrides?: Partial<StockConfiguration>
  ): Promise<StockConfiguration> {
    try {
      // Busca configuração existente
      let config = await this.prisma.stockConfiguration.findFirst({
        where: { tenantId, mlItemId }
      });

      if (!config) {
        // Busca configurações globais do tenant
        const systemConfig = await this.getSystemConfiguration(tenantId);
        
        // Busca informações do produto
        const item = await this.prisma.mercadoLivreItem.findFirst({
          where: { tenantId, mlItemId }
        });

        // Cria configuração padrão
        config = await this.prisma.stockConfiguration.create({
          data: {
            tenantId,
            mlItemId,
            sku: item?.sku || '',
            coverageDays: systemConfig.defaultCoverageDays,
            safetyStockDays: systemConfig.defaultSafetyStockDays,
            minStockLevel: 1,
            analysisWindowDays: systemConfig.defaultAnalysisWindowDays,
            seasonalityFactor: 1.0,
            trendWeight: 0.3,
            criticalGapThreshold: systemConfig.globalCriticalThreshold,
            warningGapThreshold: systemConfig.globalWarningThreshold,
            isActive: true,
            ...overrides
          }
        });
      } else if (overrides) {
        // Atualiza configuração com overrides
        config = await this.prisma.stockConfiguration.update({
          where: { id: config.id },
          data: overrides
        });
      }

      return config as StockConfiguration;
    } catch (error) {
      logger.error('Erro ao buscar/criar configuração', { error, tenantId, mlItemId });
      throw error;
    }
  }

  /**
   * Busca configurações globais do sistema
   */
  private async getSystemConfiguration(tenantId: string): Promise<StockSystemConfiguration> {
    try {
      let systemConfig = await this.prisma.stockSystemConfiguration.findFirst({
        where: { tenantId }
      });

      if (!systemConfig) {
        // Cria configuração padrão
        systemConfig = await this.prisma.stockSystemConfiguration.create({
          data: {
            tenantId,
            defaultCoverageDays: 15,
            defaultSafetyStockDays: 3,
            defaultAnalysisWindowDays: 60,
            globalCriticalThreshold: 10,
            globalWarningThreshold: 5,
            calculationFrequencyHours: 24,
            enableAutomaticCalculation: true,
            enableSeasonalityAdjustment: true,
            enableTrendAdjustment: true,
            enableAlerts: true,
            alertRetentionDays: 30,
            notificationChannels: []
          }
        });
      }

      return systemConfig as StockSystemConfiguration;
    } catch (error) {
      logger.error('Erro ao buscar configuração do sistema', { error, tenantId });
      throw error;
    }
  }

  /**
   * Busca estoque atual do produto
   */
  private async getCurrentStock(tenantId: string, mlItemId: string): Promise<CurrentStock> {
    try {
      // Busca estoque atual do item
      const item = await this.prisma.mercadoLivreItem.findFirst({
        where: { tenantId, mlItemId }
      });

      if (!item) {
        throw new ValidationError(`Item não encontrado: ${mlItemId}`);
      }

      return {
        mlItemId,
        sku: item.sku || '',
        currentQuantity: item.availableQuantity || 0,
        availableQuantity: item.availableQuantity || 0,
        reservedQuantity: 0, // TODO: Implementar lógica de reserva
        inTransitQuantity: 0, // TODO: Implementar lógica de trânsito
        lastUpdated: item.updatedAt
      };
    } catch (error) {
      logger.error('Erro ao buscar estoque atual', { error, tenantId, mlItemId });
      throw error;
    }
  }

  /**
   * Busca produtos para calcular
   */
  private async getItemsToCalculate(
    tenantId: string,
    params: StockCalculationParams
  ): Promise<string[]> {
    try {
      const where: any = { tenantId };

      if (params.mlItemIds && params.mlItemIds.length > 0) {
        where.mlItemId = { in: params.mlItemIds };
      }

      if (!params.includeInactive) {
        where.status = 'active';
      }

      const items = await this.prisma.mercadoLivreItem.findMany({
        where,
        select: { mlItemId: true }
      });

      return items.map(item => item.mlItemId);
    } catch (error) {
      logger.error('Erro ao buscar itens para calcular', { error, tenantId });
      throw error;
    }
  }

  /**
   * Busca cálculo recente (últimas 24h)
   */
  private async getRecentCalculation(
    tenantId: string,
    mlItemId: string
  ): Promise<StockCalculationResult | null> {
    try {
      const twentyFourHoursAgo = new Date();
      twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

      const recent = await this.prisma.stockCalculation.findFirst({
        where: {
          tenantId,
          product: {
            mlId: mlItemId
          },
          createdAt: { gte: twentyFourHoursAgo }
        },
        include: {
          product: true
        },
        orderBy: { createdAt: 'desc' }
      });

      if (recent) {
        return {
          mlItemId: recent.product.mlId,
          sku: recent.product.sku || '',
          tenantId: recent.tenantId,
          currentStock: recent.currentStock,
          averageDailySales: Number(recent.averageSales),
          idealStock: recent.idealStock,
          stockGap: recent.stockGap,
          daysOfCoverage: recent.daysOfCoverage,
          safetyStock: recent.safetyStock,
          unitsInTransit: recent.unitsInTransit,
          calculatedAt: recent.createdAt,
          status: recent.stockGap < 0 ? 'critical' : recent.stockGap === 0 ? 'optimal' : 'excess',
          priority: recent.stockGap < -10 ? 'high' : recent.stockGap < 0 ? 'medium' : 'low',
          confidence: 0.8,
          metadata: {
            calculationVersion: '1.0',
            dataQuality: 'good'
          }
        } as StockCalculationResult;
      }

      return null;
    } catch (error) {
      logger.error('Erro ao buscar cálculo recente', { error, tenantId, mlItemId });
      return null;
    }
  }

  /**
   * Salva resultado no histórico
   */
  private async saveCalculationHistory(
    tenantId: string,
    config: StockConfiguration,
    result: StockCalculationResult,
    startTime: number
  ): Promise<void> {
    try {
      // Busca o produto pelo mlId
      const product = await this.prisma.product.findFirst({
        where: {
          tenantId,
          mlId: config.mlItemId
        }
      });

      if (!product) {
        logger.warn('Produto não encontrado para salvar cálculo', { tenantId, mlItemId: config.mlItemId });
        return;
      }

      await this.prisma.stockCalculation.create({
        data: {
          tenantId,
          productId: product.id,
          currentStock: result.currentStock,
          averageSales: result.averageDailySales,
          idealStock: result.idealStock,
          stockGap: result.stockGap,
          daysOfCoverage: result.daysOfCoverage,
          safetyStock: result.safetyStock,
          unitsInTransit: result.unitsInTransit,
          calculationDate: result.calculatedAt
        }
      });
    } catch (error) {
      logger.error('Erro ao salvar histórico', { error, tenantId, mlItemId: config.mlItemId });
      // Não propaga erro para não interromper fluxo principal
    }
  }

  /**
   * Obtém resultado mais recente de um produto
   */
  public async getLatestCalculation(
    tenantId: string,
    mlItemId: string
  ): Promise<StockCalculationResult | null> {
    try {
      const latest = await this.prisma.stockCalculation.findFirst({
        where: { 
          tenantId, 
          product: {
            mlId: mlItemId
          }
        },
        include: {
          product: true
        },
        orderBy: { createdAt: 'desc' }
      });

      if (latest) {
        return {
          mlItemId: latest.product.mlId,
          sku: latest.product.sku || '',
          tenantId: latest.tenantId,
          currentStock: latest.currentStock,
          averageDailySales: Number(latest.averageSales),
          idealStock: latest.idealStock,
          stockGap: latest.stockGap,
          daysOfCoverage: latest.daysOfCoverage,
          safetyStock: latest.safetyStock,
          unitsInTransit: latest.unitsInTransit,
          calculatedAt: latest.createdAt,
          status: latest.stockGap < 0 ? 'critical' : latest.stockGap === 0 ? 'optimal' : 'excess',
          priority: latest.stockGap < -10 ? 'high' : latest.stockGap < 0 ? 'medium' : 'low',
          confidence: 0.8,
          metadata: {
            calculationVersion: '1.0',
            dataQuality: 'good'
          }
        } as StockCalculationResult;
      }

      return null;
    } catch (error) {
      logger.error('Erro ao buscar cálculo mais recente', { error, tenantId, mlItemId });
      return null;
    }
  }

  /**
   * Calcula estoque ideal com cache
   */
  async calculateIdealStockWithCache(
    tenantId: string,
    mlItemId: string,
    configuration?: any
  ): Promise<StockCalculationResult> {
    // Tentar buscar do cache primeiro
    const cacheKey = `calculation:${mlItemId}`;
    const cached = await this.cache.get(
      CacheNamespaces.STOCK,
      cacheKey,
      tenantId
    );

    if (cached) {
      logger.debug(`Cache hit para cálculo de estoque: ${mlItemId}`);
      return cached;
    }

    // Se não encontrar no cache, calcular
    const result = await this.calculateIdealStock(tenantId, mlItemId, configuration);

    // Cache por 15 minutos
    await this.cache.set(
      CacheNamespaces.STOCK,
      cacheKey,
      result,
      CacheConfigs.STOCK_CALCULATIONS,
      tenantId
    );

    logger.debug(`Resultado do cálculo cacheado para: ${mlItemId}`);
    return result;
  }

  /**
   * Busca dados de vendas com cache
   */
  async getSalesDataWithCache(
    tenantId: string,
    mlItemId: string,
    daysBack: number = 30
  ): Promise<any[]> {
    const cacheKey = `sales:${mlItemId}:${daysBack}`;
    const cached = await this.cache.get(
      CacheNamespaces.ANALYTICS,
      cacheKey,
      tenantId
    );

    if (cached) {
      return cached;
    }

    const salesData = await this.getSalesData(tenantId, mlItemId, daysBack);

    // Cache dados de vendas por 30 minutos
    await this.cache.set(
      CacheNamespaces.ANALYTICS,
      cacheKey,
      salesData,
      CacheConfigs.MEDIUM,
      tenantId
    );

    return salesData;
  }

  /**
   * Invalida cache quando há mudanças
   */
  async invalidateProductCache(tenantId: string, mlItemId: string): Promise<void> {
    await Promise.all([
      this.cache.delete(CacheNamespaces.STOCK, `calculation:${mlItemId}`, tenantId),
      this.cache.deleteByPattern(`${CacheNamespaces.ANALYTICS}:*:${mlItemId}`, tenantId)
    ]);

    logger.debug(`Cache invalidado para produto: ${mlItemId}`);
  }

  private async getSalesData(
    tenantId: string,
    mlItemId: string,
    daysBack: number
  ): Promise<any[]> {
    try {
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - daysBack);

      const salesData = await this.prisma.mercadoLivreOrder.findMany({
        where: {
          tenantId,
          items: {
            some: {
              mlItemId
            }
          },
          createdAt: {
            gte: startDate
          }
        },
        include: {
          items: {
            where: {
              mlItemId
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      return salesData.map((order: any) => ({
        date: order.createdAt,
        quantity: order.items.reduce((sum: number, item: any) => sum + item.quantity, 0),
        value: order.items.reduce((sum: number, item: any) => sum + (item.unitPrice * item.quantity), 0),
        orderId: order.mlOrderId
      }));
    } catch (error) {
      logger.error('Erro ao buscar dados de vendas', { error, tenantId, mlItemId, daysBack });
      return [];
    }
  }
}