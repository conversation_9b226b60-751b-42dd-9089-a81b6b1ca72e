import React, { useState, useEffect } from 'react';
import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardDescription,
} from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select-radix';
import { Checkbox } from '../ui/checkbox';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  CogIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface SpreadsheetWizardProps {
  onComplete: (result: any) => void;
  onCancel: () => void;
}

interface GenerateSpreadsheetParams {
  warehouseIds?: string[];
  productIds?: string[];
  categories?: string[];
  brands?: string[];
  useStockCalculation: boolean;
  minGapThreshold: number;
  maxProducts: number;
  format: 'csv' | 'xlsx';
  splitByWarehouse: boolean;
  notes?: string;
  tags?: string[];
}

interface Warehouse {
  id: string;
  name: string;
  code: string;
  productCount: number;
}

interface Category {
  id: string;
  name: string;
  productCount: number;
}

interface PreviewData {
  totalProducts: number;
  totalQuantity: number;
  totalValue: number;
  warehouseBreakdown: Array<{
    warehouseId: string;
    warehouseName: string;
    productCount: number;
    totalQuantity: number;
  }>;
  sampleProducts: Array<{
    sku: string;
    title: string;
    quantity: number;
    warehouseName: string;
  }>;
  potentialIssues: {
    missingDimensions: number;
    missingWeight: number;
    missingBarcode: number;
    zeroStock: number;
  };
}

const STEPS = [
  { id: 1, name: 'Seleção', icon: CogIcon },
  { id: 2, name: 'Configuração', icon: DocumentTextIcon },
  { id: 3, name: 'Preview', icon: EyeIcon },
  { id: 4, name: 'Geração', icon: ArrowDownTrayIcon }
];

export default function SpreadsheetWizard({ onComplete, onCancel }: SpreadsheetWizardProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [generationResult, setGenerationResult] = useState<any>(null);
  
  // Form data
  const [formData, setFormData] = useState<GenerateSpreadsheetParams>({
    warehouseIds: [],
    productIds: [],
    categories: [],
    brands: [],
    useStockCalculation: true,
    minGapThreshold: 1,
    maxProducts: 1000,
    format: 'xlsx',
    splitByWarehouse: false,
    notes: '',
    tags: []
  });

  // Mock data - em produção viria da API
  const [warehouses] = useState<Warehouse[]>([
    { id: '1', name: 'Armazém São Paulo', code: 'SP01', productCount: 150 },
    { id: '2', name: 'Armazém Rio de Janeiro', code: 'RJ01', productCount: 89 },
    { id: '3', name: 'Armazém Belo Horizonte', code: 'BH01', productCount: 67 }
  ]);

  const [categories] = useState<Category[]>([
    { id: '1', name: 'Eletrônicos', productCount: 45 },
    { id: '2', name: 'Roupas', productCount: 78 },
    { id: '3', name: 'Casa e Jardim', productCount: 32 },
    { id: '4', name: 'Esportes', productCount: 23 }
  ]);

  const formatOptions = [
    { value: 'xlsx', label: 'Excel (.xlsx)' },
    { value: 'csv', label: 'CSV (.csv)' }
  ];

  const handleNext = async () => {
    if (currentStep === 3) {
      // Gerar preview
      setIsLoading(true);
      try {
        // Simular chamada da API
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        const mockPreview: PreviewData = {
          totalProducts: 45,
          totalQuantity: 1250,
          totalValue: 125000,
          warehouseBreakdown: [
            { warehouseId: '1', warehouseName: 'Armazém São Paulo', productCount: 25, totalQuantity: 750 },
            { warehouseId: '2', warehouseName: 'Armazém Rio de Janeiro', productCount: 12, totalQuantity: 300 },
            { warehouseId: '3', warehouseName: 'Armazém Belo Horizonte', productCount: 8, totalQuantity: 200 }
          ],
          sampleProducts: [
            { sku: 'SKU001', title: 'Smartphone Samsung Galaxy', quantity: 15, warehouseName: 'Armazém São Paulo' },
            { sku: 'SKU002', title: 'Notebook Dell Inspiron', quantity: 8, warehouseName: 'Armazém São Paulo' },
            { sku: 'SKU003', title: 'Fone Bluetooth JBL', quantity: 25, warehouseName: 'Armazém Rio de Janeiro' }
          ],
          potentialIssues: {
            missingDimensions: 3,
            missingWeight: 1,
            missingBarcode: 0,
            zeroStock: 2
          }
        };
        
        setPreviewData(mockPreview);
      } catch (error) {
        console.error('Erro ao gerar preview:', error);
      } finally {
        setIsLoading(false);
      }
    } else if (currentStep === 4) {
      // Gerar planilha
      setIsLoading(true);
      try {
        const result = {
          fileName: `planilha_mercado_envios_${new Date().toISOString().split('T')[0]}.${formData.format}`,
          totalProducts: previewData?.totalProducts || 0,
          generatedAt: new Date(),
          formData
        };
        setGenerationResult(result);
        onComplete(result);
      } catch (error) {
        console.error('Erro ao gerar planilha:', error);
      } finally {
        setIsLoading(false);
      }
    }
    
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    setCurrentStep(1);
    setPreviewData(null);
    setGenerationResult(null);
    onCancel();
  };

  const updateFormData = (field: keyof GenerateSpreadsheetParams, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-8">
      {STEPS.map((step, index) => {
        const Icon = step.icon;
        const isActive = currentStep === step.id;
        const isCompleted = currentStep > step.id;
        
        return (
          <div key={step.id} className="flex items-center">
            <div className={`
              flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
              ${isActive ? 'border-blue-500 bg-blue-500 text-white' : 
                isCompleted ? 'border-green-500 bg-green-500 text-white' : 
                'border-gray-300 bg-white text-gray-400'}
            `}>
              {isCompleted ? (
                <CheckCircleIcon className="w-5 h-5" />
              ) : (
                <Icon className="w-5 h-5" />
              )}
            </div>
            <span className={`ml-2 text-sm font-medium ${
              isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
            }`}>
              {step.name}
            </span>
            {index < STEPS.length - 1 && (
              <div className={`mx-4 h-0.5 w-12 ${
                isCompleted ? 'bg-green-500' : 'bg-gray-300'
              }`} />
            )}
          </div>
        );
      })}
    </div>
  );

  const renderStep1 = () => (
    <div className="space-y-6">
      <div>
        <Label className="text-base font-semibold mb-3 block">Armazéns</Label>
        <div className="grid grid-cols-1 gap-3">
          {warehouses.map(warehouse => (
            <div key={warehouse.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
              <Checkbox
                name={`warehouse-${warehouse.id}`}
                checked={formData.warehouseIds?.includes(warehouse.id)}
                onCheckedChange={(checked) => {
                  const newIds = checked 
                    ? [...(formData.warehouseIds || []), warehouse.id]
                    : formData.warehouseIds?.filter(id => id !== warehouse.id) || [];
                  updateFormData('warehouseIds', newIds);
                }}
              />
              <div className="flex-1">
                <div className="font-medium">{warehouse.name}</div>
                <div className="text-sm text-gray-500">{warehouse.code} • {warehouse.productCount} produtos</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div>
        <Label className="text-base font-semibold mb-3 block">Categorias</Label>
        <div className="grid grid-cols-2 gap-3">
          {categories.map(category => (
            <div key={category.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50">
              <Checkbox
                name={`category-${category.id}`}
                checked={formData.categories?.includes(category.id)}
                onCheckedChange={(checked) => {
                  const newIds = checked 
                    ? [...(formData.categories || []), category.id]
                    : formData.categories?.filter(id => id !== category.id) || [];
                  updateFormData('categories', newIds);
                }}
              />
              <div className="flex-1">
                <div className="font-medium">{category.name}</div>
                <div className="text-sm text-gray-500">{category.productCount} produtos</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <div>
          <Label htmlFor="format">Formato da Planilha</Label>
          <Select value={formData.format} onValueChange={(value) => updateFormData('format', value)}>
            <SelectTrigger>
              <SelectValue placeholder="Selecione o formato" />
            </SelectTrigger>
            <SelectContent>
              {formatOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="maxProducts">Máximo de Produtos</Label>
          <Input
            id="maxProducts"
            type="number"
            value={formData.maxProducts}
            onChange={(e) => updateFormData('maxProducts', parseInt(e.target.value))}
            min={1}
            max={10000}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-6">
        <div>
          <Label htmlFor="minGapThreshold">Gap Mínimo (unidades)</Label>
          <Input
            id="minGapThreshold"
            type="number"
            value={formData.minGapThreshold}
            onChange={(e) => updateFormData('minGapThreshold', parseInt(e.target.value))}
            min={0}
          />
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-3">
          <Checkbox
            name="useStockCalculation"
            checked={formData.useStockCalculation}
            onCheckedChange={(checked) => updateFormData('useStockCalculation', checked)}
          />
          <Label>Usar cálculo automático de estoque</Label>
        </div>
        
        <div className="flex items-center space-x-3">
          <Checkbox
            name="splitByWarehouse"
            checked={formData.splitByWarehouse}
            onCheckedChange={(checked) => updateFormData('splitByWarehouse', checked)}
          />
          <Label>Gerar arquivo separado por armazém</Label>
        </div>
      </div>

      <div>
        <Label htmlFor="notes">Observações (opcional)</Label>
        <textarea
          id="notes"
          className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={3}
          value={formData.notes}
          onChange={(e) => updateFormData('notes', e.target.value)}
          placeholder="Adicione observações sobre esta planilha..."
        />
      </div>
    </div>
  );

  const renderStep3 = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Gerando preview da planilha...</p>
        </div>
      );
    }

    if (!previewData) return null;

    return (
      <div className="space-y-6">
        {/* Estatísticas Gerais */}
        <div className="grid grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{previewData.totalProducts}</div>
              <div className="text-sm text-gray-600">Produtos</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{previewData.totalQuantity.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Quantidade Total</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">R$ {previewData.totalValue.toLocaleString()}</div>
              <div className="text-sm text-gray-600">Valor Total</div>
            </CardContent>
          </Card>
        </div>

        {/* Distribuição por Armazém */}
        <Card>
          <CardHeader>
            <CardTitle>Distribuição por Armazém</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {previewData.warehouseBreakdown.map(warehouse => (
                <div key={warehouse.warehouseId} className="flex justify-between items-center p-3 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium">{warehouse.warehouseName}</div>
                    <div className="text-sm text-gray-600">{warehouse.productCount} produtos</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{warehouse.totalQuantity} unidades</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Amostra de Produtos */}
        <Card>
          <CardHeader>
            <CardTitle>Amostra de Produtos</CardTitle>
            <CardDescription>Primeiros produtos que serão incluídos na planilha</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">SKU</th>
                    <th className="text-left p-2">Produto</th>
                    <th className="text-left p-2">Quantidade</th>
                    <th className="text-left p-2">Armazém</th>
                  </tr>
                </thead>
                <tbody>
                  {previewData.sampleProducts.map((product, index) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-mono">{product.sku}</td>
                      <td className="p-2">{product.title}</td>
                      <td className="p-2">{product.quantity}</td>
                      <td className="p-2">{product.warehouseName}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Possíveis Problemas */}
        {Object.values(previewData.potentialIssues).some(count => count > 0) && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-amber-600">
                <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
                Atenção - Possíveis Problemas
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {previewData.potentialIssues.missingDimensions > 0 && (
                  <div className="text-sm text-amber-600">
                    • {previewData.potentialIssues.missingDimensions} produtos sem dimensões
                  </div>
                )}
                {previewData.potentialIssues.missingWeight > 0 && (
                  <div className="text-sm text-amber-600">
                    • {previewData.potentialIssues.missingWeight} produtos sem peso
                  </div>
                )}
                {previewData.potentialIssues.zeroStock > 0 && (
                  <div className="text-sm text-amber-600">
                    • {previewData.potentialIssues.zeroStock} produtos com estoque zero
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderStep4 = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
          <p className="text-gray-600">Gerando planilha...</p>
          <p className="text-sm text-gray-500 mt-2">Isso pode levar alguns segundos</p>
        </div>
      );
    }

    if (generationResult) {
      return (
        <div className="text-center py-12">
          <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 mb-2">Planilha Gerada com Sucesso!</h3>
          <p className="text-gray-600 mb-6">
            {generationResult.totalProducts} produtos foram incluídos na planilha
          </p>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="text-sm text-gray-600 mb-2">Arquivo gerado:</div>
            <div className="font-mono text-sm bg-white p-2 rounded border">
              {generationResult.fileName}
            </div>
          </div>

          <div className="flex justify-center space-x-4">
            <Button onClick={() => {/* Implementar download */}}>
              <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
              Baixar Planilha
            </Button>
            <Button variant="outline" onClick={handleClose}>
              Fechar
            </Button>
          </div>
        </div>
      );
    }

    return null;
  };

  return (
    <div className="w-full">
      {renderStepIndicator()}
      
      <div className="min-h-[400px]">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
        {currentStep === 4 && renderStep4()}
      </div>

      {!generationResult && (
        <div className="pt-6 border-t bg-gray-50 flex justify-between">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1 || isLoading}
          >
            <ChevronLeftIcon className="w-4 h-4 mr-2" />
            Anterior
          </Button>
          
          <div className="flex space-x-2">
            <Button variant="outline" onClick={handleClose}>
              Cancelar
            </Button>
            <Button
              onClick={handleNext}
              disabled={isLoading}
            >
              {currentStep === 4 ? 'Gerar Planilha' : 'Próximo'}
              {currentStep < 4 && <ChevronRightIcon className="w-4 h-4 ml-2" />}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}