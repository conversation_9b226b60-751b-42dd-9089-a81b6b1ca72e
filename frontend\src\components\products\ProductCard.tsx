import React, { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import StockIndicators from './StockIndicators';
import {
  ExternalLink,
  Edit3,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Package,
  Eye,
  MoreHorizontal
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '../ui/dropdown-menu';
import type { ProductWithStock } from '../../types/api';

interface ProductCardProps {
  product: ProductWithStock;
  onUpdateStock?: (productId: string, quantity: number) => Promise<void>;
  onViewDetails?: (product: ProductWithStock) => void;
  onSyncProduct?: (productId: string) => Promise<void>;
  isUpdatingStock?: boolean;
  viewMode?: 'grid' | 'list';
}

export default function ProductCard({
  product,
  onUpdateStock,
  onViewDetails,
  onSyncProduct,
  isUpdatingStock = false,
  viewMode = 'grid'
}: ProductCardProps) {
  const [isEditingStock, setIsEditingStock] = useState(false);
  const [stockValue, setStockValue] = useState(product.availableQuantity.toString());
  const [isLoading, setIsLoading] = useState(false);

  const getStockStatus = () => {
    const stock = product.availableQuantity;
    if (stock === 0) return { 
      status: 'Sem estoque', 
      color: 'destructive', 
      icon: AlertTriangle,
      bgColor: 'bg-red-50 border-red-200'
    };
    if (stock <= 5) return { 
      status: 'Estoque baixo', 
      color: 'warning', 
      icon: AlertTriangle,
      bgColor: 'bg-yellow-50 border-yellow-200'
    };
    if (stock <= 10) return { 
      status: 'Estoque médio', 
      color: 'secondary', 
      icon: Package,
      bgColor: 'bg-blue-50 border-blue-200'
    };
    return { 
      status: 'Estoque OK', 
      color: 'default', 
      icon: CheckCircle,
      bgColor: 'bg-green-50 border-green-200'
    };
  };

  const stockStatus = getStockStatus();

  const handleStockUpdate = async () => {
    if (!onUpdateStock) return;
    
    const newQuantity = parseInt(stockValue);
    if (isNaN(newQuantity) || newQuantity < 0) {
      alert('Quantidade inválida');
      return;
    }

    setIsLoading(true);
    try {
      await onUpdateStock(product.id, newQuantity);
      setIsEditingStock(false);
    } catch (error) {
      console.error('Erro ao atualizar estoque:', error);
      alert('Erro ao atualizar estoque');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancelEdit = () => {
    setStockValue(product.availableQuantity.toString());
    setIsEditingStock(false);
  };

  // List view - compact horizontal layout
  if (viewMode === 'list') {
    return (
      <Card className={`hover:shadow-md transition-all duration-200 ${stockStatus.bgColor}`}>
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            {/* Thumbnail */}
            <div className="flex-shrink-0">
              {product.thumbnail ? (
                <img
                  src={product.thumbnail}
                  alt={product.title}
                  className="w-16 h-16 object-cover rounded border"
                />
              ) : (
                <div className="w-16 h-16 bg-muted rounded flex items-center justify-center">
                  <Package className="h-6 w-6 text-muted-foreground" />
                </div>
              )}
            </div>

            {/* Main Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-4">
                {/* Left: Title and IDs */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-base line-clamp-1 mb-1">
                    {product.title}
                  </h3>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground mb-2">
                    {product.sku && (
                      <span><span className="font-medium">SKU:</span> {product.sku}</span>
                    )}
                    <span><span className="font-medium">ML ID:</span> {product.mlId}</span>
                  </div>

                  {/* Stock Indicators - Compact */}
                  <div className="flex items-center gap-2">
                    <StockIndicators product={product} size="sm" />
                  </div>
                </div>

                {/* Center: Stock Management */}
                <div className="flex items-center gap-4">
                  <div className="text-center">
                    <div className="text-xs text-muted-foreground mb-1">Vendidos</div>
                    <div className="font-medium">{product.soldQuantity}</div>
                  </div>

                  <div className="text-center">
                    <div className="text-xs text-muted-foreground mb-1">Estoque</div>
                    <div className="flex items-center gap-2">
                      {isEditingStock ? (
                        <div className="flex items-center gap-1">
                          <Input
                            type="number"
                            value={stockValue}
                            onChange={(e) => setStockValue(e.target.value)}
                            className="w-16 h-7 text-sm"
                            min="0"
                          />
                          <Button
                            size="sm"
                            onClick={handleStockUpdate}
                            disabled={isLoading}
                            className="h-7 w-7 p-0"
                          >
                            {isLoading ? '...' : '✓'}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={handleCancelEdit}
                            className="h-7 w-7 p-0"
                          >
                            ✕
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <span className="font-semibold">{product.availableQuantity}</span>
                          <Badge variant={stockStatus.color as any} className="text-xs">
                            {stockStatus.status}
                          </Badge>
                          {onUpdateStock && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setIsEditingStock(true)}
                              className="h-6 w-6 p-0"
                              disabled={isUpdatingStock}
                            >
                              <Edit3 className="h-3 w-3" />
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Right: Price and Actions */}
                <div className="flex items-center gap-4">
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">
                      R$ {product.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </div>
                    {product.stockCalculation?.gap && product.stockCalculation.gap > 0 && (
                      <div className="text-xs text-orange-600 font-medium">
                        Gap: +{product.stockCalculation.gap}
                      </div>
                    )}
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onViewDetails?.(product)}>
                        <Eye className="h-4 w-4 mr-2" />
                        Ver detalhes
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => window.open(product.permalink, '_blank')}>
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Abrir no ML
                      </DropdownMenuItem>
                      {onSyncProduct && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem onClick={() => onSyncProduct(product.id)}>
                            <TrendingUp className="h-4 w-4 mr-2" />
                            Sincronizar
                          </DropdownMenuItem>
                        </>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Grid view - original vertical layout
  return (
    <Card className={`hover:shadow-md transition-all duration-200 h-full ${stockStatus.bgColor}`}>
      <CardContent className="p-4 h-full flex flex-col">
        {/* Header: Badges + Actions + Thumbnail */}
        <div className="flex items-start justify-between gap-3 mb-4">
          <div className="flex flex-col gap-2">
            {/* Status Badges */}
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {product.condition === 'new' ? 'Novo' : 'Usado'}
              </Badge>
              {product.stockCalculation?.gap && product.stockCalculation.gap > 0 && (
                <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                  Gap: +{product.stockCalculation.gap}
                </Badge>
              )}
            </div>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0 flex-shrink-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem onClick={() => onViewDetails?.(product)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Ver detalhes
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => window.open(product.permalink, '_blank')}>
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Abrir no ML
                </DropdownMenuItem>
                {onSyncProduct && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={() => onSyncProduct(product.id)}>
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Sincronizar
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Thumbnail */}
          {product.thumbnail ? (
            <img
              src={product.thumbnail}
              alt={product.title}
              className="w-20 h-20 object-cover rounded-lg border shadow-sm flex-shrink-0"
            />
          ) : (
            <div className="w-20 h-20 bg-muted rounded-lg flex items-center justify-center border flex-shrink-0">
              <Package className="h-8 w-8 text-muted-foreground" />
            </div>
          )}
        </div>

        {/* Title - More prominent */}
        <div className="mb-3 flex-grow">
          <h3 className="font-semibold text-base line-clamp-2 mb-2 leading-tight">
            {product.title}
          </h3>

          {/* IDs: SKU + ML ID Badge */}
          <div className="space-y-2">
            {product.sku && (
              <p className="text-xs text-muted-foreground">
                <span className="font-medium">SKU:</span> {product.sku}
              </p>
            )}
            <div>
              <Badge variant="secondary" className="text-xs">
                ML: {product.mlId}
              </Badge>
            </div>
          </div>
        </div>

        {/* Sales and Status Info */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Vendidos:</span>
            <span className="font-medium">{product.soldQuantity}</span>
          </div>

          {/* Stock Indicators */}
          <div className="flex justify-center mb-2">
            <StockIndicators product={product} size="sm" />
          </div>
        </div>

        {/* Stock Management */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Estoque:</span>
            <div className="flex items-center gap-2">
              {isEditingStock ? (
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={stockValue}
                    onChange={(e) => setStockValue(e.target.value)}
                    className="w-20 h-8"
                    min="0"
                  />
                  <Button
                    size="sm"
                    onClick={handleStockUpdate}
                    disabled={isLoading}
                    className="h-8"
                  >
                    {isLoading ? '...' : '✓'}
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleCancelEdit}
                    className="h-8"
                  >
                    ✕
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="font-semibold">{product.availableQuantity}</span>
                  <Badge variant={stockStatus.color as any} className="text-xs">
                    {stockStatus.status}
                  </Badge>
                  {onUpdateStock && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => setIsEditingStock(true)}
                      className="h-6 w-6 p-0"
                      disabled={isUpdatingStock}
                    >
                      <Edit3 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Gap - If exists, show highlighted */}
          {product.stockCalculation?.gap && product.stockCalculation.gap > 0 && (
            <div className="bg-orange-50 border border-orange-200 rounded-md p-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-orange-800">Gap:</span>
                <span className="text-sm font-bold text-orange-600">
                  +{product.stockCalculation.gap} unidades
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Coverage and Metrics */}
        {product.metrics && (
          <div className="mb-3 text-xs text-muted-foreground text-center">
            {product.metrics.stockCoverageDays && (
              <span>Cobertura: {product.metrics.stockCoverageDays.toFixed(1)} dias</span>
            )}
          </div>
        )}

        {/* Price - Centered and prominent in footer */}
        <div className="mt-auto pt-3 border-t">
          <div className="text-center">
            <span className="text-xl font-bold text-green-600">
              R$ {product.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </span>
          </div>

          {/* Alerts and Sync Status */}
          <div className="mt-2 space-y-1">
            {product.alerts && product.alerts.length > 0 && (
              <div className="flex items-center justify-center gap-1 text-xs text-amber-600">
                <AlertTriangle className="h-3 w-3" />
                <span>{product.alerts.length} alerta(s)</span>
              </div>
            )}

            {product.syncStatus?.isOutOfSync && (
              <div className="flex items-center justify-center gap-1 text-xs text-orange-600">
                <TrendingDown className="h-3 w-3" />
                <span>Fora de sincronia</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
