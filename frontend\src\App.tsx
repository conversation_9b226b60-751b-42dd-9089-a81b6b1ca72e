import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastProvider } from './components/ui/Toast';
import { useAuthStore } from './store/authStore';

// Componentes de layout
import Layout from './components/layout/Layout';
import AuthLayout from './components/layout/AuthLayout';

// Páginas
import Dashboard from './pages/Dashboard';
import Products from './pages/Products';
import Stock from './pages/Stock';
import Envios from './pages/Envios';
import Settings from './pages/Settings';
import Login from './pages/Login';
import Register from './pages/Register';
import ResetPassword from './pages/ResetPassword';
import NotFound from './pages/NotFound';
import ReportsSales from './pages/ReportsSales';
import ReportsStock from './pages/ReportsStock';
import LogsPage from './pages/Logs';
import MonitoringPage from './pages/Monitoring';
import ComponentsShowcase from './pages/ComponentsShowcase';
import MercadoLivreCallback from './pages/MercadoLivreCallback';
import MLFullWizardPage from './pages/MLFullWizard';
import UserManagement from './pages/UserManagement';

// Verificar se está em ambiente de desenvolvimento
const isDevelopment = import.meta.env.DEV;

function App() {
  return (
    <Router>
      <ToastProvider>
      <div className="min-h-screen bg-background text-foreground">
        <Routes>
          {/* Rotas de autenticação - apenas em produção */}
          {!isDevelopment && (
            <>
              <Route
                path="/login"
                element={
                  <AuthLayout>
                    <Login />
                  </AuthLayout>
                }
              />
              <Route
                path="/register"
                element={
                  <AuthLayout>
                    <Register />
                  </AuthLayout>
                }
              />
              <Route
                path="/reset-password"
                element={
                  <AuthLayout>
                    <ResetPassword />
                  </AuthLayout>
                }
              />
            </>
          )}

          {/* Showcase do Design System */}
          <Route path="/showcase" element={<ComponentsShowcase />} />

          {/* Rotas principais - sempre acessíveis em desenvolvimento */}
          <Route
            path="/dashboard"
            element={
              <Layout>
                <Dashboard />
              </Layout>
            }
          />
          <Route
            path="/products"
            element={
              <Layout>
                <Products />
              </Layout>
            }
          />
          <Route
            path="/stock"
            element={
              <Layout>
                <Stock />
              </Layout>
            }
          />
          <Route
            path="/envios"
            element={
              <Layout>
                <Envios />
              </Layout>
            }
          />
          <Route
            path="/reports/sales"
            element={
              <Layout>
                <ReportsSales />
              </Layout>
            }
          />
          <Route
            path="/reports/stock"
            element={
              <Layout>
                <ReportsStock />
              </Layout>
            }
          />
          <Route
            path="/settings"
            element={
              <Layout>
                <Settings />
              </Layout>
            }
          />
          <Route
            path="/users"
            element={
              <Layout>
                <UserManagement />
              </Layout>
            }
          />
          <Route
            path="/logs"
            element={
              <Layout>
                <LogsPage />
              </Layout>
            }
          />
          <Route
            path="/monitoring"
            element={
              <Layout>
                <MonitoringPage />
              </Layout>
            }
          />

          {/* ML Full Wizard - Full Page */}
          <Route path="/ml-full-wizard" element={<MLFullWizardPage />} />

          {/* Callback OAuth Mercado Livre */}
          <Route path="/mercadolivre/callback" element={<MercadoLivreCallback />} />

          {/* Redirecionamentos */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/spreadsheets" element={<Navigate to="/envios" replace />} />

          {/* Em desenvolvimento, redirecionar login para dashboard */}
          {isDevelopment && (
            <Route path="/login" element={<Navigate to="/dashboard" replace />} />
          )}

          <Route path="*" element={<NotFound />} />
        </Routes>
      </div>
      </ToastProvider>
    </Router>
  );
}

export default App;
