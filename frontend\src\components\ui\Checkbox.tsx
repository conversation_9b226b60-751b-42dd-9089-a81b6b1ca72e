import React, { InputHTMLAttributes } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { useFormContext } from 'react-hook-form';

// Checkbox variants
const checkboxVariants = cva(
  'peer h-4 w-4 shrink-0 rounded-sm border focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      state: {
        default: 'border-primary text-primary focus:ring-primary',
        error: 'border-danger-500 text-danger-500 focus:ring-danger-500',
        success: 'border-success-500 text-success-500 focus:ring-success-500',
        warning: 'border-warning-500 text-warning-500 focus:ring-warning-500',
      },
      variant: {
        default: 'bg-background',
        filled: 'bg-muted',
      },
    },
    defaultVariants: {
      state: 'default',
      variant: 'default',
    },
  }
);

export interface CheckboxProps
  extends InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof checkboxVariants> {
  name: string;
  label?: string;
  description?: string;
  // For React Hook Form integration
  control?: any; // Use `control` from useForm() in RHF
  register?: any; // Use `register` from useForm() in RHF
  rules?: Record<string, any>;
}

const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
  (
    {
      className,
      id,
      name,
      label,
      description,
      state: propState,
      variant,
      disabled,
      control, // for RHF
      register: registerProp, // for RHF
      rules,
      ...props
    },
    ref
  ) => {
    // Try to get form context, but make it optional
    let register, errors;
    try {
      const formContext = useFormContext();
      register = formContext?.register;
      errors = formContext?.formState?.errors || {};
    } catch {
      // No form context available, use props only
      register = null;
      errors = {};
    }

    const uniqueId = id || `checkbox-${name}`;
    const validationError = errors[name]?.message as string | undefined;
    const hasError = !!validationError;
    const effectiveState = hasError ? 'error' : propState;

    const r = registerProp || register; // Use prop or context register

    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <input
          type="checkbox"
          id={uniqueId}
          name={name}
          className={cn(checkboxVariants({ state: effectiveState, variant }), 'focus:ring-offset-background')}
          disabled={disabled}
          {...(r ? r(name, rules) : {})}
          {...props}
        />
        <div className="grid gap-1.5 leading-none">
          {label && (
            <label
              htmlFor={uniqueId}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {label}
            </label>
          )}
          {description && (
            <p className="text-sm text-muted-foreground">
              {description}
            </p>
          )}
          {hasError && (
            <p className="text-sm text-danger-600" role="alert">
              {validationError}
            </p>
          )}
        </div>
      </div>
    );
  }
);
Checkbox.displayName = 'Checkbox';

export { Checkbox };
