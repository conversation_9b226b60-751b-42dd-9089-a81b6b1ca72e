/**
 * Testes de Segurança e Multi-tenant
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 * 
 * Versão simulada para testar aspectos de segurança e isolamento
 */

import request from 'supertest';
import express from 'express';
import jwt from 'jsonwebtoken';
import { mockPrismaClient } from '../setup';

describe('Security and Multi-tenant Tests (Mock)', () => {
  let app: express.Application;
  const JWT_SECRET = 'test-secret-key';
  
  // Mock de usuários de diferentes tenants
  const tenant1User = {
    id: 'user-tenant1-123',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    role: 'USER'
  };
  
  const tenant2User = {
    id: 'user-tenant2-456', 
    email: '<EMAIL>',
    tenantId: 'tenant-2',
    role: 'USER'
  };
  
  const adminUser = {
    id: 'admin-123',
    email: '<EMAIL>',
    tenantId: 'tenant-1',
    role: 'ADMIN'
  };

  beforeAll(() => {
    // Criar uma aplicação Express para simular aspectos de segurança
    app = express();
    
    // Middlewares básicos
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Middleware de autenticação JWT simulado
    const authenticateToken = (req: any, res: any, next: any) => {
      const authHeader = req.headers['authorization'];
      const token = authHeader && authHeader.split(' ')[1];
      
      if (!token) {
        return res.status(401).json({ error: 'Access token required' });
      }
      
      try {
        const decoded = jwt.verify(token, JWT_SECRET) as any;
        req.user = decoded;
        return next();
      } catch (error) {
        return res.status(403).json({ error: 'Invalid token' });
      }
    };
    
    // Middleware de autorização de tenant (isolamento)
    const requireTenant = (req: any, res: any, next: any) => {
      if (!req.user || !req.user.tenantId) {
        return res.status(403).json({ error: 'Tenant context required' });
      }
      return next();
    };
    
    // Middleware de autorização de admin
    const requireAdmin = (req: any, res: any, next: any) => {
      if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({ error: 'Admin access required' });
      }
      return next();
    };
    
    // ROTAS DE AUTENTICAÇÃO
    app.post('/api/auth/login', (req, res) => {
      const { email, password, tenantDomain } = req.body;
      
      if (!email || !password || !tenantDomain) {
        return res.status(400).json({ error: 'All fields required' });
      }
      
      // Simular autenticação por tenant
      let user;
      if (email === tenant1User.email && tenantDomain === 'tenant1') {
        user = tenant1User;
      } else if (email === tenant2User.email && tenantDomain === 'tenant2') {
        user = tenant2User;
      } else if (email === adminUser.email && tenantDomain === 'tenant1') {
        user = adminUser;
      } else {
        return res.status(401).json({ error: 'Invalid credentials' });
      }
      
      // Simular validação de senha
      if (password !== 'validPassword123') {
        return res.status(401).json({ error: 'Invalid credentials' });
      }
      
      // Gerar token JWT
      const token = jwt.sign({
        userId: user.id,
        email: user.email,
        tenantId: user.tenantId,
        role: user.role
      }, JWT_SECRET, { expiresIn: '1h' });
      
      return res.status(200).json({
        message: 'Login successful',
        token,
        user: {
          id: user.id,
          email: user.email,
          tenantId: user.tenantId,
          role: user.role
        }
      });
    });
    
    // ROTAS PROTEGIDAS - DADOS POR TENANT
    app.get('/api/secure/tenant-data', authenticateToken, requireTenant, (req: any, res) => {
      const { tenantId } = req.user;
      
      // Simular dados específicos por tenant
      const tenantData = {
        'tenant-1': {
          companyName: 'Empresa Tenant 1',
          items: ['item1-t1', 'item2-t1'],
          users: ['<EMAIL>', '<EMAIL>']
        },
        'tenant-2': {
          companyName: 'Empresa Tenant 2', 
          items: ['item1-t2', 'item2-t2'],
          users: ['<EMAIL>']
        }
      };
      
      return res.status(200).json({
        tenantId,
        data: tenantData[tenantId as keyof typeof tenantData] || null
      });
    });
    
    // ROTA ADMIN - APENAS ADMINS
    app.get('/api/secure/admin-only', authenticateToken, requireAdmin, (req: any, res) => {
      return res.status(200).json({
        message: 'Admin access granted',
        adminData: {
          systemStats: { totalUsers: 150, totalTenants: 5 },
          privileges: ['VIEW_ALL', 'MANAGE_USERS', 'SYSTEM_CONFIG']
        }
      });
    });
    
    // ROTA VULNERÁVEL - PARA TESTAR TENTATIVAS DE ACESSO CRUZADO
    app.get('/api/vulnerable/user-data/:userId', authenticateToken, (req: any, res) => {
      const { userId } = req.params;
      const { tenantId } = req.user;
      
      // VULNERABILIDADE: Não verifica se o userId pertence ao tenant
      // Isso é o que DEVEMOS prevenir!
      
      const userData = {
        'user-tenant1-123': { name: 'User Tenant 1', sensitive: 'tenant1-secret' },
        'user-tenant2-456': { name: 'User Tenant 2', sensitive: 'tenant2-secret' }
      };
      
      return res.status(200).json({
        requestedUser: userId,
        requesterTenant: tenantId,
        data: userData[userId as keyof typeof userData] || null
      });
    });
    
    // ROTA SEGURA - COM VERIFICAÇÃO DE TENANT
    app.get('/api/secure/user-data/:userId', authenticateToken, requireTenant, (req: any, res) => {
      const { userId } = req.params;
      const { tenantId } = req.user;
      
      // SEGURANÇA: Verificar se o usuário pertence ao tenant
      const userTenantMapping = {
        'user-tenant1-123': 'tenant-1',
        'user-tenant2-456': 'tenant-2',
        'admin-123': 'tenant-1'
      };
      
      const userTenant = userTenantMapping[userId as keyof typeof userTenantMapping];
      
      if (!userTenant || userTenant !== tenantId) {
        return res.status(403).json({ 
          error: 'Access denied - user does not belong to your tenant' 
        });
      }
      
      const userData = {
        'user-tenant1-123': { name: 'User Tenant 1', sensitive: 'tenant1-secret' },
        'user-tenant2-456': { name: 'User Tenant 2', sensitive: 'tenant2-secret' },
        'admin-123': { name: 'Admin Tenant 1', sensitive: 'admin-secret' }
      };
      
      return res.status(200).json({
        requestedUser: userId,
        requesterTenant: tenantId,
        data: userData[userId as keyof typeof userData] || null
      });
    });
    
    // ROTA PARA TESTAR RATE LIMITING (simulado)
    let rateLimitStore: { [key: string]: { count: number, resetTime: number } } = {};
    
    app.get('/api/rate-limited', (req, res) => {
      const clientIp = req.ip || req.connection.remoteAddress || 'unknown';
      const now = Date.now();
      const windowMs = 60000; // 1 minuto
      const limit = 5; // 5 requisições por minuto
      
      if (!rateLimitStore[clientIp] || now > rateLimitStore[clientIp].resetTime) {
        rateLimitStore[clientIp] = { count: 1, resetTime: now + windowMs };
      } else {
        rateLimitStore[clientIp].count++;
      }
      
      if (rateLimitStore[clientIp].count > limit) {
        return res.status(429).json({ 
          error: 'Too many requests',
          retryAfter: Math.ceil((rateLimitStore[clientIp].resetTime - now) / 1000)
        });
      }
      
      return res.status(200).json({
        message: 'Request allowed',
        remaining: limit - rateLimitStore[clientIp].count
      });
    });
    
    // ROTA PARA TESTAR VALIDAÇÃO DE INPUT
    app.post('/api/validate-input', authenticateToken, (req: any, res) => {
      const { data, type } = req.body;
      
      // Simular validações de segurança
      if (typeof data !== 'string') {
        return res.status(400).json({ error: 'Data must be a string' });
      }
      
      // Verificar por padrões perigosos (simulado)
      const dangerousPatterns = [
        /<script/i,
        /javascript:/i,
        /on\w+=/i,
        /eval\(/i,
        /exec\(/i
      ];
      
      for (const pattern of dangerousPatterns) {
        if (pattern.test(data)) {
          return res.status(400).json({ 
            error: 'Potentially dangerous input detected',
            pattern: pattern.source
          });
        }
      }
      
      return res.status(200).json({
        message: 'Input validation passed',
        sanitizedData: data.trim(),
        type
      });
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('JWT Authentication', () => {
    it('deve fazer login com credenciais válidas', async () => {
      const loginData = {
        email: tenant1User.email,
        password: 'validPassword123',
        tenantDomain: 'tenant1'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.tenantId).toBe('tenant-1');
    });

    it('deve rejeitar credenciais inválidas', async () => {
      const loginData = {
        email: tenant1User.email,
        password: 'wrongPassword123',
        tenantDomain: 'tenant1'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Invalid credentials');
    });

    it('deve rejeitar tenant inválido', async () => {
      const loginData = {
        email: tenant1User.email,
        password: 'validPassword123',
        tenantDomain: 'wrongTenant'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expect(response.status).toBe(401);
    });

    it('deve rejeitar token inválido', async () => {
      const response = await request(app)
        .get('/api/secure/tenant-data')
        .set('Authorization', 'Bearer invalid.token');

      expect(response.status).toBe(403);
      expect(response.body.error).toContain('Invalid token');
    });

    it('deve rejeitar requisições sem token', async () => {
      const response = await request(app)
        .get('/api/secure/tenant-data');

      expect(response.status).toBe(401);
      expect(response.body.error).toContain('Access token required');
    });
  });

  describe('Multi-tenant Isolation', () => {
    let tenant1Token: string;
    let tenant2Token: string;

    beforeAll(async () => {
      // Gerar tokens para os dois tenants
      tenant1Token = jwt.sign(tenant1User, JWT_SECRET, { expiresIn: '1h' });
      tenant2Token = jwt.sign(tenant2User, JWT_SECRET, { expiresIn: '1h' });
    });

    it('deve retornar dados específicos do tenant correto', async () => {
      const response = await request(app)
        .get('/api/secure/tenant-data')
        .set('Authorization', `Bearer ${tenant1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.tenantId).toBe('tenant-1');
      expect(response.body.data.companyName).toBe('Empresa Tenant 1');
      expect(response.body.data.items).toContain('item1-t1');
    });

    it('deve retornar dados diferentes para tenant diferente', async () => {
      const response = await request(app)
        .get('/api/secure/tenant-data')
        .set('Authorization', `Bearer ${tenant2Token}`);

      expect(response.status).toBe(200);
      expect(response.body.tenantId).toBe('tenant-2');
      expect(response.body.data.companyName).toBe('Empresa Tenant 2');
      expect(response.body.data.items).toContain('item1-t2');
    });

    it('deve prevenir acesso cruzado de dados de usuário (rota segura)', async () => {
      // Tenant 1 tentando acessar dados de usuário do Tenant 2
      const response = await request(app)
        .get('/api/secure/user-data/user-tenant2-456')
        .set('Authorization', `Bearer ${tenant1Token}`);

      expect(response.status).toBe(403);
      expect(response.body.error).toContain('does not belong to your tenant');
    });

    it('deve permitir acesso a dados próprios do tenant', async () => {
      // Tenant 1 acessando seus próprios dados
      const response = await request(app)
        .get('/api/secure/user-data/user-tenant1-123')
        .set('Authorization', `Bearer ${tenant1Token}`);

      expect(response.status).toBe(200);
      expect(response.body.data.name).toBe('User Tenant 1');
      expect(response.body.requesterTenant).toBe('tenant-1');
    });

    it('deve demonstrar vulnerabilidade na rota insegura', async () => {
      // Esta é uma demonstração do que NÃO devemos permitir
      const response = await request(app)
        .get('/api/vulnerable/user-data/user-tenant2-456')
        .set('Authorization', `Bearer ${tenant1Token}`);

      expect(response.status).toBe(200);
      // PROBLEMA: Tenant 1 conseguiu acessar dados do Tenant 2!
      expect(response.body.data.name).toBe('User Tenant 2');
    });
  });

  describe('Authorization and Roles', () => {
    let adminToken: string;
    let userToken: string;

    beforeAll(async () => {
      adminToken = jwt.sign(adminUser, JWT_SECRET, { expiresIn: '1h' });
      userToken = jwt.sign(tenant1User, JWT_SECRET, { expiresIn: '1h' });
    });

    it('deve permitir acesso admin para rota restrita', async () => {
      const response = await request(app)
        .get('/api/secure/admin-only')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.message).toContain('Admin access granted');
      expect(response.body.adminData).toHaveProperty('systemStats');
    });

    it('deve negar acesso de usuário comum para rota admin', async () => {
      const response = await request(app)
        .get('/api/secure/admin-only')
        .set('Authorization', `Bearer ${userToken}`);

      expect(response.status).toBe(403);
      expect(response.body.error).toContain('Admin access required');
    });
  });

  describe('Rate Limiting', () => {
    it('deve permitir requisições dentro do limite', async () => {
      const response = await request(app)
        .get('/api/rate-limited');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Request allowed');
      expect(response.body.remaining).toBeGreaterThanOrEqual(0);
    });

    it('deve bloquear após exceder o limite', async () => {
      // Fazer várias requisições para exceder o limite
      for (let i = 0; i < 6; i++) {
        await request(app).get('/api/rate-limited');
      }

      const response = await request(app)
        .get('/api/rate-limited');

      expect(response.status).toBe(429);
      expect(response.body.error).toContain('Too many requests');
      expect(response.body).toHaveProperty('retryAfter');
    });
  });

  describe('Input Validation and XSS Prevention', () => {
    let validToken: string;

    beforeAll(() => {
      validToken = jwt.sign(tenant1User, JWT_SECRET, { expiresIn: '1h' });
    });

    it('deve aceitar input seguro', async () => {
      const inputData = {
        data: 'Este é um texto seguro',
        type: 'text'
      };

      const response = await request(app)
        .post('/api/validate-input')
        .set('Authorization', `Bearer ${validToken}`)
        .send(inputData);

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Input validation passed');
      expect(response.body.sanitizedData).toBe('Este é um texto seguro');
    });

    it('deve detectar script tags maliciosos', async () => {
      const inputData = {
        data: '<script>alert("XSS")</script>',
        type: 'text'
      };

      const response = await request(app)
        .post('/api/validate-input')
        .set('Authorization', `Bearer ${validToken}`)
        .send(inputData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Potentially dangerous input detected');
    });

    it('deve detectar javascript: URLs', async () => {
      const inputData = {
        data: 'javascript:alert("XSS")',
        type: 'url'
      };

      const response = await request(app)
        .post('/api/validate-input')
        .set('Authorization', `Bearer ${validToken}`)
        .send(inputData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Potentially dangerous input detected');
    });

    it('deve detectar event handlers maliciosos', async () => {
      const inputData = {
        data: '<img src="x" onerror="alert(1)">',
        type: 'html'
      };

      const response = await request(app)
        .post('/api/validate-input')
        .set('Authorization', `Bearer ${validToken}`)
        .send(inputData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Potentially dangerous input detected');
    });

    it('deve rejeitar input não-string', async () => {
      const inputData = {
        data: { malicious: 'object' },
        type: 'object'
      };

      const response = await request(app)
        .post('/api/validate-input')
        .set('Authorization', `Bearer ${validToken}`)
        .send(inputData);

      expect(response.status).toBe(400);
      expect(response.body.error).toContain('Data must be a string');
    });
  });

  describe('Security Headers and CORS', () => {
    it('deve incluir headers de segurança apropriados', async () => {
      // Usar uma rota que não seja rate-limited para testar headers
      const validToken = jwt.sign(tenant1User, JWT_SECRET, { expiresIn: '1h' });
      
      const response = await request(app)
        .get('/api/secure/tenant-data')
        .set('Authorization', `Bearer ${validToken}`);

      // Verificar se o express está respondendo (não podemos testar todos os headers no mock)
      expect(response.status).toBe(200);
      expect(response.type).toBe('application/json');
    });
  });
}); 