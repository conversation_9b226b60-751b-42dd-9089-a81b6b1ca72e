import { apiService } from './api';

interface StockItem {
  id: string;
  productId: string;
  productSku: string;
  productTitle: string;
  currentQuantity: number;
  location: string;
  lastUpdated: string; // ISO string ou Date string
}

interface CreateStockItemDto {
  productId: string;
  productSku: string;
  productTitle: string;
  currentQuantity: number;
  location: string;
}

interface UpdateStockItemDto {
  currentQuantity?: number;
  location?: string;
}

const stockService = {
  /**
   * Busca todos os itens de estoque da API.
   * @returns Uma Promise que resolve para um array de itens de estoque.
   */
  getAllStockItems: async (): Promise<StockItem[]> => {
    try {
      const response = await apiService.get<StockItem[]>('/api/stock');
      return response.data;
    } catch (error) {
      console.error("Erro ao buscar itens de estoque:", error);
      throw error;
    }
  },

  /**
   * Busca um item de estoque específico por ID.
   * @param id O ID do item de estoque.
   * @returns Uma Promise que resolve para o item de estoque encontrado ou null.
   */
  getStockItemById: async (id: string): Promise<StockItem | null> => {
    try {
      const response = await apiService.get<StockItem>(`/api/stock/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar item de estoque com ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Cria um novo item de estoque.
   * @param item Os dados do novo item de estoque.
   * @returns Uma Promise que resolve para o item de estoque criado.
   */
  createStockItem: async (item: CreateStockItemDto): Promise<StockItem> => {
    try {
      const response = await apiService.post<StockItem>('/api/stock', item);
      return response.data;
    } catch (error) {
      console.error("Erro ao criar item de estoque:", error);
      throw error;
    }
  },

  /**
   * Atualiza um item de estoque existente.
   * @param id O ID do item de estoque a ser atualizado.
   * @param item Os dados atualizados do item de estoque.
   * @returns Uma Promise que resolve para o item de estoque atualizado.
   */
  updateStockItem: async (id: string, item: UpdateStockItemDto): Promise<StockItem> => {
    try {
      const response = await apiService.put<StockItem>(`/api/stock/${id}`, item);
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar item de estoque com ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Exclui um item de estoque.
   * @param id O ID do item de estoque a ser excluído.
   * @returns Uma Promise que resolve para void.
   */
  deleteStockItem: async (id: string): Promise<void> => {
    try {
      await apiService.delete(`/api/stock/${id}`);
    } catch (error) {
      console.error(`Erro ao excluir item de estoque com ID ${id}:`, error);
      throw error;
    }
  },
};

export default stockService;
export type { StockItem, CreateStockItemDto, UpdateStockItemDto };