import { Router } from 'express';
import { MercadoLivreController } from '../controllers/mercadoLivreController';
import { authenticate, requireTenant } from '../middleware/auth';
import { requestLogger } from '../middleware/logger';
import { PrismaClient } from '@prisma/client';

const router: Router = Router();
const prisma = new PrismaClient();
const mercadoLivreController = new MercadoLivreController(prisma);

// Aplicar middlewares globais para todas as rotas
router.use(requestLogger);

// Rotas públicas
router.get('/health', mercadoLivreController.healthCheck);
router.get('/test', (req, res) => {
  res.json({ message: 'Mercado Livre routes working', status: 'active' });
});

// Rota de teste para OAuth (apenas desenvolvimento)
router.get('/test-oauth', (req, res) => {
  const authUrl = `https://auth.mercadolibre.com.ar/authorization?response_type=code&client_id=${process.env.MERCADO_LIVRE_CLIENT_ID}&redirect_uri=${encodeURIComponent(process.env.MERCADO_LIVRE_REDIRECT_URI || '')}&state=test-state-123`;

  res.json({
    message: 'OAuth test endpoint',
    authUrl,
    clientId: process.env.MERCADO_LIVRE_CLIENT_ID,
    redirectUri: process.env.MERCADO_LIVRE_REDIRECT_URI,
    instructions: 'Acesse a authUrl para iniciar o fluxo OAuth'
  });
});

// Webhooks do Mercado Livre (públicos - não requerem autenticação)
router.post('/webhooks/notifications', mercadoLivreController.handleWebhook);

// Rotas protegidas - requerem autenticação
router.use(authenticate());
router.use(requireTenant);

// Iniciar fluxo OAuth
router.get('/auth/initiate', mercadoLivreController.initiateAuth);
router.get('/auth/callback', mercadoLivreController.handleCallback);

// Endpoints de contas
router.get('/accounts', mercadoLivreController.getConnectedAccounts);
router.delete('/accounts/:accountId', mercadoLivreController.disconnectAccount);

// Endpoints de leitura de anúncios
router.get('/accounts/:accountId/items', mercadoLivreController.getUserItems);
router.get('/accounts/:accountId/items/:itemId', mercadoLivreController.getProductDetails);
router.get('/accounts/:accountId/search', mercadoLivreController.searchProducts);

// Endpoints de leitura de vendas
router.get('/accounts/:accountId/orders', mercadoLivreController.getOrders);
router.get('/accounts/:accountId/orders/:orderId', mercadoLivreController.getOrderDetails);

// Endpoints de gestão de estoque
router.put('/accounts/:accountId/items/:itemId/stock', mercadoLivreController.updateStock);

// Endpoints de sincronização
router.post('/accounts/:accountId/sync/products', mercadoLivreController.syncProducts);

// Endpoints de estatísticas
router.get('/accounts/:accountId/stats', mercadoLivreController.getAccountStats);

// Endpoints de token
router.post('/accounts/:accountId/refresh-token', mercadoLivreController.refreshToken);

export default router;