import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import ErrorMonitoringService, { ErrorMetrics } from '../services/errorMonitoringService';
import { logger } from '../utils/logger';

// Instância global do serviço de monitoramento
let errorMonitoringService: ErrorMonitoringService;

// Inicializar o serviço (chamado no app.ts)
export const initializeErrorMonitoring = (prisma: PrismaClient) => {
  errorMonitoringService = new ErrorMonitoringService(prisma);
  
  // Configurar limpeza automática a cada hora
  setInterval(() => {
    errorMonitoringService.cleanupOldErrors();
  }, 60 * 60 * 1000); // 1 hora
};

// Classes de erro customizadas
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string | undefined;

  constructor(message: string, statusCode: number = 500, code?: string | undefined) {
    super(message);
    this.name = 'AppError';
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string, code?: string) {
    super(message, 400, code);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed', code?: string) {
    super(message, 401, code);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied', code?: string) {
    super(message, 403, code);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found', code?: string) {
    super(message, 404, code);
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict', code?: string) {
    super(message, 409, code);
    this.name = 'ConflictError';
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string, statusCode: number = 502, code?: string) {
    super(message, statusCode, code);
    this.name = 'ExternalServiceError';
  }
}

export class DatabaseError extends AppError {
  constructor(message: string, code?: string) {
    super(message, 500, code);
    this.name = 'DatabaseError';
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = 'Rate limit exceeded', code?: string) {
    super(message, 429, code);
    this.name = 'RateLimitError';
  }
}

// Middleware para capturar erros assíncronos
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Middleware global de tratamento de erros
export const globalErrorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  try {
    // Capturar erro no sistema de monitoramento
    if (errorMonitoringService) {
      errorMonitoringService.captureError(error, req, {
        userAgent: req.get('User-Agent'),
        referer: req.get('Referer'),
        body: req.method !== 'GET' ? req.body : undefined,
        query: req.query,
        params: req.params
      });
    }

    // Determinar código de status
    let statusCode = 500;
    let message = 'Internal Server Error';
    let code: string | undefined;

    if (error instanceof AppError) {
      statusCode = error.statusCode;
      message = error.message;
      code = error.code;
    } else if (error.name === 'ValidationError') {
      statusCode = 400;
      message = error.message;
    } else if (error.name === 'UnauthorizedError' || error.name === 'JsonWebTokenError') {
      statusCode = 401;
      message = 'Authentication failed';
    } else if (error.name === 'ForbiddenError') {
      statusCode = 403;
      message = 'Access denied';
    } else if (error.name === 'NotFoundError') {
      statusCode = 404;
      message = 'Resource not found';
    } else if (error.name === 'PrismaClientKnownRequestError') {
      const prismaError = error as any;
      statusCode = 400;
      
      switch (prismaError.code) {
        case 'P2002':
          message = 'Unique constraint violation';
          break;
        case 'P2025':
          message = 'Record not found';
          statusCode = 404;
          break;
        case 'P2003':
          message = 'Foreign key constraint violation';
          break;
        default:
          message = 'Database error';
          statusCode = 500;
      }
      code = prismaError.code;
    }

    // Preparar resposta de erro
    const errorResponse: any = {
      success: false,
      error: {
        message,
        code,
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    };

    // Adicionar stack trace apenas em desenvolvimento
    if (process.env.NODE_ENV === 'development') {
      errorResponse.error.stack = error.stack;
      errorResponse.error.details = {
        name: error.name,
        originalMessage: error.message
      };
    }

    // Log do erro tratado
    logger.error(`Error handled: ${statusCode} - ${message}`, {
      error: error.name,
      message: error.message,
      statusCode,
      code,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: (req as any).user?.id,
      tenantId: (req as any).user?.tenantId,
      requestId: req.headers['x-request-id'],
      stack: error.stack
    });

    // Enviar resposta
    res.status(statusCode).json(errorResponse);

  } catch (handlerError) {
    // Fallback caso o próprio handler tenha erro
    logger.error('Error in error handler', {
      originalError: error.message,
      handlerError: handlerError instanceof Error ? handlerError.message : 'Unknown',
      timestamp: new Date().toISOString()
    });

    res.status(500).json({
      success: false,
      error: {
        message: 'Internal Server Error',
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] || 'unknown'
      }
    });
  }
};

// Middleware para capturar 404s
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.method} ${req.originalUrl} not found`);
  next(error);
};

// Middleware para capturar exceções não tratadas
export const unhandledErrorHandler = (): void => {
  // Capturar exceções não tratadas
  process.on('uncaughtException', (error: Error) => {
    logger.error('Uncaught Exception', {
      error: error.name,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString()
    });

    if (errorMonitoringService) {
      errorMonitoringService.captureError(error, undefined, {
        type: 'uncaughtException',
        fatal: true
      });
    }

    // Graceful shutdown
    process.exit(1);
  });

  // Capturar promises rejeitadas não tratadas
  process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    
    logger.error('Unhandled Rejection', {
      error: error.name,
      message: error.message,
      stack: error.stack,
      promise: promise.toString(),
      timestamp: new Date().toISOString()
    });

    if (errorMonitoringService) {
      errorMonitoringService.captureError(error, undefined, {
        type: 'unhandledRejection',
        reason: String(reason)
      });
    }
  });

  // Capturar sinais de sistema
  process.on('SIGTERM', () => {
    logger.info('SIGTERM received, shutting down gracefully', {
      timestamp: new Date().toISOString()
    });
    process.exit(0);
  });

  process.on('SIGINT', () => {
    logger.info('SIGINT received, shutting down gracefully', {
      timestamp: new Date().toISOString()
    });
    process.exit(0);
  });
};

// Função para obter métricas de erro (para dashboard)
export const getErrorMetrics = async (
  tenantId?: string,
  startDate?: Date,
  endDate?: Date
) => {
  if (!errorMonitoringService) {
    throw new Error('Error monitoring service not initialized');
  }
  
  return await errorMonitoringService.generateErrorMetrics(tenantId, startDate, endDate);
};

// Função para capturar erro manualmente
export const captureError = async (
  error: Error,
  request?: Request,
  context?: Record<string, any>
) => {
  if (errorMonitoringService) {
    await errorMonitoringService.captureError(error, request, context);
  }
};

export { errorMonitoringService };