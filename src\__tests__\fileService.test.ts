/**
 * File Service Tests
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import fs from 'fs';
import path from 'path';
import { FileService } from '../services/fileService';

// Mock do logger
jest.mock('@/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock do fs
jest.mock('fs');
const mockedFs = fs as jest.Mocked<typeof fs>;

// Mock do crypto
jest.mock('crypto', () => ({
  randomUUID: jest.fn(() => 'test-uuid-123'),
  createHash: jest.fn(() => ({
    update: jest.fn().mockReturnThis(),
    digest: jest.fn(() => 'test-checksum-abc123')
  }))
}));

describe('FileService', () => {
  let fileService: FileService;
  const testTenantId = 'test-tenant-001';
  const testUserId = 'test-user-001';

  beforeEach(() => {
    jest.clearAllMocks();
    fileService = new FileService();
    
    // Mock fs.existsSync para retornar true por padrão
    mockedFs.existsSync.mockReturnValue(true);
    mockedFs.mkdirSync.mockImplementation();
    mockedFs.readFileSync.mockReturnValue(Buffer.from('test file content'));
  });

  describe('Constructor', () => {
    it('should initialize with default uploads directory', () => {
      expect(mockedFs.existsSync).toHaveBeenCalled();
    });

    it('should create uploads directory if it does not exist', () => {
      mockedFs.existsSync.mockReturnValue(false);
      new FileService();
      expect(mockedFs.mkdirSync).toHaveBeenCalled();
    });
  });

  describe('saveFile', () => {
    const mockFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: 'test-avatar.jpg',
      encoding: '7bit',
      mimetype: 'image/jpeg',
      size: 1024,
      destination: '/tmp',
      filename: 'avatar_test-user-001_1234567890_abcd1234.jpg',
      path: '/tmp/avatar_test-user-001_1234567890_abcd1234.jpg',
      buffer: Buffer.from('test'),
      stream: {} as any
    };

    it('should save file successfully', async () => {
      const result = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      expect(result.success).toBe(true);
      expect(result.file).toBeDefined();
      expect(result.file?.originalName).toBe('test-avatar.jpg');
      expect(result.file?.fileType).toBe('avatar');
      expect(result.file?.tenantId).toBe(testTenantId);
      expect(result.file?.uploadedBy).toBe(testUserId);
      expect(result.url).toContain('/api/files/download/');
    });

    it('should handle file save error', async () => {
      mockedFs.readFileSync.mockImplementation(() => {
        throw new Error('File read error');
      });

      const result = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should detect duplicate files by checksum', async () => {
      // Primeiro upload
      const result1 = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      // Segundo upload com mesmo checksum
      const result2 = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      expect(result2.file?.id).toBe(result1.file?.id); // Mesmo arquivo
    });
  });

  describe('getFileById', () => {
    it('should return file when found', async () => {
      // Primeiro salvar um arquivo
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/tmp',
        filename: 'test.jpg',
        path: '/tmp/test.jpg',
        buffer: Buffer.from('test'),
        stream: {} as any
      };

      const saveResult = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      // Depois buscar
      const file = await fileService.getFileById(
        saveResult.file!.id,
        testTenantId
      );

      expect(file).toBeDefined();
      expect(file?.id).toBe(saveResult.file!.id);
      expect(file?.tenantId).toBe(testTenantId);
    });

    it('should return null when file not found', async () => {
      const file = await fileService.getFileById(
        'non-existent-id',
        testTenantId
      );

      expect(file).toBeNull();
    });
  });

  describe('listFiles', () => {
    beforeEach(async () => {
      // Limpar store antes de cada teste
      (fileService as any).memoryStore.clear();

      // Adicionar arquivos diretamente no memory store para garantir que sejam criados
      const testFiles = [
        {
          id: 'test-file-1',
          tenantId: testTenantId,
          uploadedBy: testUserId,
          originalName: 'avatar1.jpg',
          storedName: 'avatar_test-user-001_1234567890_abcd1234.jpg',
          filePath: '/tmp/avatar1.jpg',
          mimeType: 'image/jpeg',
          fileSize: 1024,
          fileType: 'avatar' as const,
          checksum: 'checksum1',
          isPublic: false,
          createdAt: new Date()
        },
        {
          id: 'test-file-2',
          tenantId: testTenantId,
          uploadedBy: testUserId,
          originalName: 'document1.pdf',
          storedName: 'document_test-user-001_1234567890_abcd1235.pdf',
          filePath: '/tmp/document1.pdf',
          mimeType: 'application/pdf',
          fileSize: 2048,
          fileType: 'document' as const,
          checksum: 'checksum2',
          isPublic: false,
          createdAt: new Date()
        },
        {
          id: 'test-file-3',
          tenantId: testTenantId,
          uploadedBy: testUserId,
          originalName: 'spreadsheet1.xlsx',
          storedName: 'spreadsheet_test-user-001_1234567890_abcd1236.xlsx',
          filePath: '/tmp/spreadsheet1.xlsx',
          mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          fileSize: 3072,
          fileType: 'spreadsheet' as const,
          checksum: 'checksum3',
          isPublic: false,
          createdAt: new Date()
        }
      ];

      // Adicionar diretamente no memory store
      testFiles.forEach(file => {
        (fileService as any).storeFileInMemory(file);
      });
    });

    it('should list all files for tenant', async () => {
      const files = await fileService.listFiles({
        tenantId: testTenantId
      });

      expect(files).toHaveLength(3);
      expect(files.every(f => f.tenantId === testTenantId)).toBe(true);
    });

    it('should filter files by type', async () => {
      const avatarFiles = await fileService.listFiles({
        tenantId: testTenantId,
        fileType: 'avatar'
      });

      expect(avatarFiles).toHaveLength(1);
      expect(avatarFiles[0]?.fileType).toBe('avatar');
    });

    it('should filter files by uploader', async () => {
      const userFiles = await fileService.listFiles({
        tenantId: testTenantId,
        uploadedBy: testUserId
      });

      expect(userFiles).toHaveLength(3);
      expect(userFiles.every(f => f?.uploadedBy === testUserId)).toBe(true);
    });

    it('should respect limit and offset', async () => {
      // Primeiro verificar quantos arquivos temos no total
      const allFiles = await fileService.listFiles({
        tenantId: testTenantId
      });

      expect(allFiles.length).toBeGreaterThanOrEqual(3);

      // Agora testar limit e offset
      const files = await fileService.listFiles({
        tenantId: testTenantId,
        limit: 2,
        offset: 1
      });

      expect(files).toHaveLength(2);
    });
  });

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      // Primeiro salvar um arquivo
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test-delete.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/tmp',
        filename: 'test-delete.jpg',
        path: '/tmp/test-delete.jpg',
        buffer: Buffer.from('test'),
        stream: {} as any
      };

      const saveResult = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      // Mock fs.unlinkSync
      mockedFs.unlinkSync.mockImplementation();

      // Deletar arquivo
      const success = await fileService.deleteFile(
        saveResult.file!.id,
        testTenantId,
        testUserId
      );

      expect(success).toBe(true);
      expect(mockedFs.unlinkSync).toHaveBeenCalledWith(mockFile.path);

      // Verificar se arquivo foi removido da memória
      const file = await fileService.getFileById(
        saveResult.file!.id,
        testTenantId
      );
      expect(file).toBeNull();
    });

    it('should fail to delete file from different user', async () => {
      // Salvar arquivo com um usuário
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test-unauthorized.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/tmp',
        filename: 'test-unauthorized.jpg',
        path: '/tmp/test-unauthorized.jpg',
        buffer: Buffer.from('test'),
        stream: {} as any
      };

      const saveResult = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      // Tentar deletar com usuário diferente
      const success = await fileService.deleteFile(
        saveResult.file!.id,
        testTenantId,
        'different-user-id'
      );

      expect(success).toBe(false);
    });
  });

  describe('hasFileAccess', () => {
    it('should allow access to own files', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test-access.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/tmp',
        filename: 'test-access.jpg',
        path: '/tmp/test-access.jpg',
        buffer: Buffer.from('test'),
        stream: {} as any
      };

      const saveResult = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false
      );

      const hasAccess = await fileService.hasFileAccess(
        saveResult.file!.id,
        testTenantId,
        testUserId
      );

      expect(hasAccess).toBe(true);
    });

    it('should allow access to public files', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test-public.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/tmp',
        filename: 'test-public.jpg',
        path: '/tmp/test-public.jpg',
        buffer: Buffer.from('test'),
        stream: {} as any
      };

      const saveResult = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        true // público
      );

      const hasAccess = await fileService.hasFileAccess(
        saveResult.file!.id,
        testTenantId,
        'different-user-id'
      );

      expect(hasAccess).toBe(true);
    });

    it('should deny access to private files from different users', async () => {
      const mockFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: 'test-private.jpg',
        encoding: '7bit',
        mimetype: 'image/jpeg',
        size: 1024,
        destination: '/tmp',
        filename: 'test-private.jpg',
        path: '/tmp/test-private.jpg',
        buffer: Buffer.from('test'),
        stream: {} as any
      };

      const saveResult = await fileService.saveFile(
        mockFile,
        testTenantId,
        testUserId,
        'avatar',
        false // privado
      );

      const hasAccess = await fileService.hasFileAccess(
        saveResult.file!.id,
        testTenantId,
        'different-user-id'
      );

      expect(hasAccess).toBe(false);
    });
  });

  describe('generateFileUrl', () => {
    it('should generate correct file URL', () => {
      const fileId = 'test-file-123';
      const url = fileService.generateFileUrl(fileId);

      expect(url).toBe(`http://localhost:3000/api/files/download/${fileId}`);
    });

    it('should use custom base URL from environment', () => {
      const originalBaseUrl = process.env.API_BASE_URL;
      process.env.API_BASE_URL = 'https://api.magnow.com';

      const fileId = 'test-file-123';
      const url = fileService.generateFileUrl(fileId);

      expect(url).toBe(`https://api.magnow.com/api/files/download/${fileId}`);

      // Restaurar valor original
      process.env.API_BASE_URL = originalBaseUrl;
    });
  });
});
