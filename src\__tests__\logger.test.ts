import { 
  logger, 
  logRequest, 
  logUserActivity, 
  logMercadoLivreActivity, 
  logError,
  logPerformance,
  logSecurityEvent
} from '../utils/logger';

// Mock do winston para testes
jest.mock('winston', () => {
  const mLogger = {
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    http: jest.fn(),
    debug: jest.fn(),
    log: jest.fn(),
  };

  return {
    createLogger: jest.fn(() => mLogger),
    format: {
      combine: jest.fn(),
      timestamp: jest.fn(),
      colorize: jest.fn(),
      printf: jest.fn(),
      errors: jest.fn(),
      json: jest.fn(),
      prettyPrint: jest.fn(),
    },
    transports: {
      Console: jest.fn(),
      File: jest.fn(),
    },
    addColors: jest.fn(),
  };
});

// Mock do DailyRotateFile
jest.mock('winston-daily-rotate-file', () => {
  return jest.fn().mockImplementation(() => ({
    on: jest.fn(),
  }));
});

describe('Sistema de Logging', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('logRequest', () => {
    it('deve logar requisições HTTP com sucesso', () => {
      const requestData = {
        method: 'GET',
        url: '/api/stock',
        statusCode: 200,
        responseTime: 150,
        ip: '127.0.0.1',
        userAgent: 'Test Agent',
        userId: 'user123',
        tenantId: 'tenant456',
      };

      logRequest(requestData);

      expect(logger.http).toHaveBeenCalledWith(
        'GET /api/stock - 200 - 150ms - 127.0.0.1',
        expect.objectContaining({
          method: 'GET',
          url: '/api/stock',
          statusCode: 200,
          responseTime: 150,
          ip: '127.0.0.1',
          userId: 'user123',
          tenantId: 'tenant456',
        })
      );
    });

    it('deve logar erros HTTP corretamente', () => {
      const requestData = {
        method: 'POST',
        url: '/api/auth/login',
        statusCode: 401,
        responseTime: 50,
        ip: '***********',
      };

      logRequest(requestData);

      expect(logger.warn).toHaveBeenCalledWith(
        'POST /api/auth/login - 401 - 50ms - ***********',
        expect.objectContaining({
          statusCode: 401,
        })
      );
    });

    it('deve lidar com IP undefined', () => {
      const requestData = {
        method: 'GET',
        url: '/api/test',
        statusCode: 200,
        responseTime: 100,
        ip: undefined,
      };

      logRequest(requestData);

      expect(logger.http).toHaveBeenCalledWith(
        'GET /api/test - 200 - 100ms - unknown',
        expect.objectContaining({
          ip: 'unknown',
        })
      );
    });
  });

  describe('logUserActivity', () => {
    it('deve logar atividades do usuário para auditoria', () => {
      const activityData = {
        userId: 'user123',
        action: 'CREATE_PRODUCT',
        resource: 'products',
        details: {
          productId: 'prod456',
          name: 'Produto Teste',
        },
        tenantId: 'tenant789',
        ip: '127.0.0.1',
        userAgent: 'Mozilla/5.0',
      };

      logUserActivity(activityData);

      expect(logger.log).toHaveBeenCalledWith(
        'audit',
        'User user123 performed CREATE_PRODUCT',
        expect.objectContaining({
          userId: 'user123',
          action: 'CREATE_PRODUCT',
          resource: 'products',
          tenantId: 'tenant789',
          ip: '127.0.0.1',
        })
      );
    });
  });

  describe('logMercadoLivreActivity', () => {
    it('deve logar atividades da API do Mercado Livre', () => {
      const apiData = {
        action: 'GET_PRODUCTS',
        endpoint: '/users/me/items',
        method: 'GET',
        statusCode: 200,
        responseTime: 800,
        userId: 'user123',
        tenantId: 'tenant456',
      };

      logMercadoLivreActivity(apiData);

      expect(logger.info).toHaveBeenCalledWith(
        'ML API GET /users/me/items - 200 - 800ms',
        expect.objectContaining({
          action: 'GET_PRODUCTS',
          endpoint: '/users/me/items',
          statusCode: 200,
          responseTime: 800,
        })
      );
    });

    it('deve logar erros da API do Mercado Livre', () => {
      const apiData = {
        action: 'UPDATE_STOCK',
        endpoint: '/items/MLB123/stock',
        method: 'PUT',
        statusCode: 429,
        responseTime: 1200,
        errorDetails: { message: 'Rate limit exceeded' },
      };

      logMercadoLivreActivity(apiData);

      expect(logger.error).toHaveBeenCalledWith(
        'ML API PUT /items/MLB123/stock - 429 - 1200ms',
        expect.objectContaining({
          statusCode: 429,
          errorDetails: { message: 'Rate limit exceeded' },
        })
      );
    });
  });

  describe('logError', () => {
    it('deve logar erros com contexto completo', () => {
      const error = new Error('Erro de teste');
      const errorData = {
        error,
        context: 'STOCK_CALCULATION',
        userId: 'user123',
        tenantId: 'tenant456',
        requestId: 'req789',
        additionalData: { productId: 'prod123' },
      };

      logError(errorData);

      expect(logger.error).toHaveBeenCalledWith(
        '[STOCK_CALCULATION] Erro de teste',
        expect.objectContaining({
          error: {
            name: 'Error',
            message: 'Erro de teste',
            stack: expect.any(String),
          },
          context: 'STOCK_CALCULATION',
          userId: 'user123',
          tenantId: 'tenant456',
          requestId: 'req789',
        })
      );
    });
  });

  describe('logPerformance', () => {
    it('deve logar operações lentas como warning', () => {
      logPerformance('Database Query', 6000, { query: 'SELECT * FROM products' });

      expect(logger.warn).toHaveBeenCalledWith(
        'Performance: Database Query took 6000ms',
        expect.objectContaining({
          operation: 'Database Query',
          duration: 6000,
          details: { query: 'SELECT * FROM products' },
        })
      );
    });

    it('deve logar operações moderadas como info', () => {
      logPerformance('API Call', 3000);

      expect(logger.info).toHaveBeenCalledWith(
        'Performance: API Call took 3000ms',
        expect.objectContaining({
          operation: 'API Call',
          duration: 3000,
        })
      );
    });

    it('deve logar operações rápidas como debug', () => {
      logPerformance('Cache Get', 50);

      expect(logger.debug).toHaveBeenCalledWith(
        'Performance: Cache Get took 50ms',
        expect.objectContaining({
          operation: 'Cache Get',
          duration: 50,
        })
      );
    });
  });

  describe('logSecurityEvent', () => {
    it('deve logar eventos de segurança', () => {
      logSecurityEvent(
        'FAILED_LOGIN_ATTEMPT',
        'warn',
        { email: '<EMAIL>', attempts: 3 },
        undefined,
        undefined,
        '***********00'
      );

      expect(logger.log).toHaveBeenCalledWith(
        'warn',
        'Security Event: FAILED_LOGIN_ATTEMPT',
        expect.objectContaining({
          event: 'FAILED_LOGIN_ATTEMPT',
          details: { email: '<EMAIL>', attempts: 3 },
          ip: '***********00',
        })
      );
    });
  });
}); 