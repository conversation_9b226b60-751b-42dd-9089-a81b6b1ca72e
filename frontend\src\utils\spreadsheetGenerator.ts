import * as XLSX from 'xlsx';
import type { ProductWithStock } from '../types/api';
import type { ProductQuantity } from '../store/mlFullWizardStore';

export interface SpreadsheetData {
  products: ProductWithStock[];
  quantities: Record<string, ProductQuantity>;
  // Always generates Excel format for ML Full
}

export interface SpreadsheetResult {
  filename: string;
  blob: Blob;
  url: string;
}

// Estrutura da planilha para ML Full
interface MLFullSpreadsheetRow {
  'SKU': string;
  'ML ID': string;
  'Título do Produto': string;
  'Quantidade': number;
  'Preço Unitário': string;
  'Valor Total': string;
  'Estoque Atual': number;
  'Status do Estoque': string;
  'Gap de Estoque': number | string;
  'Vendas (30 dias)': number;
  'Média Diária': string;
  'Cobertura (dias)': string;
  'Categoria': string;
  'Observações': string;
}

export class SpreadsheetGenerator {
  static async generateMLFullSpreadsheet(data: SpreadsheetData): Promise<SpreadsheetResult> {
    const { products, quantities } = data;
    
    // Preparar dados para a planilha
    const rows: MLFullSpreadsheetRow[] = products.map(product => {
      const quantity = quantities[product.id]?.quantity || 0;
      const totalValue = quantity * product.price;
      
      // Determinar status do estoque
      let stockStatus = 'Normal';
      if (product.availableQuantity === 0) {
        stockStatus = 'Sem Estoque';
      } else if (product.availableQuantity <= 5) {
        stockStatus = 'Estoque Baixo';
      } else if (product.availableQuantity <= 10) {
        stockStatus = 'Estoque Médio';
      }

      // Gap de estoque
      const gap = product.stockCalculation?.gap || 0;
      
      // Métricas de vendas
      const salesLast30Days = product.soldQuantity || 0;
      const averageDailySales = product.metrics?.averageDailySales || 0;
      const stockCoverageDays = product.metrics?.stockCoverageDays || 0;

      // Observações baseadas em alertas
      let observations = '';
      if (product.alerts && product.alerts.length > 0) {
        observations = product.alerts.map(alert => alert.message).join('; ');
      }
      if (gap > 0) {
        observations += (observations ? '; ' : '') + `Gap de ${gap} unidades`;
      }

      return {
        'SKU': product.sku || '',
        'ML ID': product.mlId,
        'Título do Produto': product.title,
        'Quantidade': quantity,
        'Preço Unitário': `R$ ${product.price.toFixed(2)}`,
        'Valor Total': `R$ ${totalValue.toFixed(2)}`,
        'Estoque Atual': product.availableQuantity,
        'Status do Estoque': stockStatus,
        'Gap de Estoque': gap > 0 ? gap : '-',
        'Vendas (30 dias)': salesLast30Days,
        'Média Diária': averageDailySales > 0 ? averageDailySales.toFixed(1) : '-',
        'Cobertura (dias)': stockCoverageDays > 0 ? stockCoverageDays.toFixed(1) : '-',
        'Categoria': product.category || 'Não categorizado',
        'Observações': observations || '-',
      };
    });

    // Adicionar linha de totais
    const totalQuantity = rows.reduce((sum, row) => sum + row['Quantidade'], 0);
    const totalValue = rows.reduce((sum, row) => {
      const value = parseFloat(row['Valor Total'].replace('R$ ', ''));
      return sum + value;
    }, 0);

    const totalsRow: MLFullSpreadsheetRow = {
      'SKU': 'TOTAL',
      'ML ID': '',
      'Título do Produto': `${rows.length} produtos selecionados`,
      'Quantidade': totalQuantity,
      'Preço Unitário': '',
      'Valor Total': `R$ ${totalValue.toFixed(2)}`,
      'Estoque Atual': 0,
      'Status do Estoque': '',
      'Gap de Estoque': '',
      'Vendas (30 dias)': 0,
      'Média Diária': '',
      'Cobertura (dias)': '',
      'Categoria': '',
      'Observações': 'Linha de totais',
    };

    const allRows = [...rows, totalsRow];

    // Always generate Excel format for ML Full
    return this.generateExcel(allRows);
  }

  private static generateExcel(rows: MLFullSpreadsheetRow[]): SpreadsheetResult {
    // Criar workbook
    const wb = XLSX.utils.book_new();
    
    // Criar worksheet
    const ws = XLSX.utils.json_to_sheet(rows);

    // Configurar larguras das colunas
    const colWidths = [
      { wch: 15 }, // SKU
      { wch: 15 }, // ML ID
      { wch: 40 }, // Título
      { wch: 10 }, // Quantidade
      { wch: 12 }, // Preço Unitário
      { wch: 12 }, // Valor Total
      { wch: 12 }, // Estoque Atual
      { wch: 15 }, // Status
      { wch: 12 }, // Gap
      { wch: 12 }, // Vendas 30d
      { wch: 10 }, // Média Diária
      { wch: 12 }, // Cobertura
      { wch: 15 }, // Categoria
      { wch: 30 }, // Observações
    ];
    ws['!cols'] = colWidths;

    // Adicionar formatação para a linha de totais
    const lastRowIndex = rows.length;
    const range = XLSX.utils.decode_range(ws['!ref'] || 'A1');
    
    // Destacar linha de totais
    for (let col = range.s.c; col <= range.e.c; col++) {
      const cellAddress = XLSX.utils.encode_cell({ r: lastRowIndex, c: col });
      if (!ws[cellAddress]) continue;
      
      ws[cellAddress].s = {
        font: { bold: true },
        fill: { fgColor: { rgb: "FFFFCC" } },
        border: {
          top: { style: "thick" },
          bottom: { style: "thick" },
          left: { style: "thin" },
          right: { style: "thin" }
        }
      };
    }

    // Adicionar worksheet ao workbook
    XLSX.utils.book_append_sheet(wb, ws, 'ML Full - Produtos');

    // Adicionar metadados
    wb.Props = {
      Title: 'ML Full - Lista de Produtos',
      Subject: 'Planilha gerada para envio ao Mercado Livre Full',
      Author: 'Sistema de Estoque',
      CreatedDate: new Date(),
    };

    // Gerar arquivo
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const filename = `ml-full-produtos-${timestamp}.xlsx`;
    const url = URL.createObjectURL(blob);

    return { filename, blob, url };
  }



  static downloadFile(result: SpreadsheetResult): void {
    const link = document.createElement('a');
    link.href = result.url;
    link.download = result.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Limpar URL após download
    setTimeout(() => {
      URL.revokeObjectURL(result.url);
    }, 100);
  }
}

// Função utilitária para uso direto
export const generateAndDownloadSpreadsheet = async (data: SpreadsheetData): Promise<SpreadsheetResult> => {
  const result = await SpreadsheetGenerator.generateMLFullSpreadsheet(data);
  SpreadsheetGenerator.downloadFile(result);
  return result;
};
