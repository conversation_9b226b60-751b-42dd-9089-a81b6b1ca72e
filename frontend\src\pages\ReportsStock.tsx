import { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Label } from '../components/ui/label';
import { LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';

interface StockRecord {
  date: string;
  quantity: number;
}

export default function ReportsStock() {
  const [data, setData] = useState<StockRecord[]>([]);
  const [from, setFrom] = useState('');
  const [to, setTo] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  async function loadData() {
    // TODO: substituir com chamada real ao backend
    const fake = [
      { date: '2024-05', quantity: 500 },
      { date: '2024-06', quantity: 450 },
      { date: '2024-07', quantity: 620 },
    ];
    setData(fake);
  }

  const handleFilter = (e: React.FormEvent) => {
    e.preventDefault();
    loadData();
  };

  const exportCSV = () => {
    const csv = 'Data,Quantidade\n' + data.map(d => `${d.date},${d.quantity}`).join('\n');
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'relatorio_estoque.csv';
    link.click();
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Relatório de Estoque</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={handleFilter} className="flex items-end gap-4">
            <div className="space-y-2">
              <Label>De</Label>
              <Input type="date" value={from} onChange={(e) => setFrom(e.target.value)} />
            </div>
            <div className="space-y-2">
              <Label>Até</Label>
              <Input type="date" value={to} onChange={(e) => setTo(e.target.value)} />
            </div>
            <Button type="submit">Filtrar</Button>
            <Button type="button" variant="secondary" onClick={exportCSV}>Exportar CSV</Button>
          </form>

          <div className="h-72 w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={data}>
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="quantity" stroke="#3b82f6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 