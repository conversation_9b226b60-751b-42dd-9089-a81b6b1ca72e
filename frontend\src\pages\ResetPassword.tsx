import { useState } from 'react';
import { Link } from 'react-router-dom';
import toast from 'react-hot-toast';
import apiService from '../services/api';

export default function ResetPassword() {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      await apiService.forgotPassword(email);
      toast.success('Se existir uma conta, enviaremos um e-mail com instruções.');
      setEmail('');
    } catch (error: any) {
      toast.error(error.message || 'Erro ao solicitar recuperação de senha');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 bg-white p-6 rounded shadow">
        <h2 className="text-center text-2xl font-bold text-gray-900">Recuperar senha</h2>
        <p className="text-center text-sm text-gray-600">Informe o e-mail cadastrado para receber instruções</p>

        <form className="mt-6 space-y-4" onSubmit={handleSubmit}>
          <input
            type="email"
            placeholder="Seu e-mail"
            className="input w-full"
            required
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
          <button type="submit" className="btn-primary w-full" disabled={isLoading}>
            {isLoading ? 'Enviando...' : 'Enviar link de recuperação'}
          </button>
        </form>

        <p className="text-center text-sm text-gray-600">
          Lembrou a senha?{' '}
          <Link to="/login" className="text-primary-600 hover:underline">Voltar ao login</Link>
        </p>
      </div>
    </div>
  );
}
