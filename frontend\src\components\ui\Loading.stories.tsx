 
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Loading, Spinner, Skeleton, LoadingButton } from './Loading';
import { Card, CardContent } from './Card';
import { Stack } from './Stack';

const meta = {
  title: 'Components/Loading',
  component: Loading,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    type: {
      control: 'select',
      options: ['spinner', 'skeleton', 'content'],
    },
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'tertiary', 'danger', 'success', 'warning', 'info', 'light', 'dark'],
    },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl'],
    },
    children: { control: 'text' },
    isLoading: { control: 'boolean' },
  },
} satisfies Meta<typeof Loading>;

export default meta;
type Story = StoryObj<typeof meta>;

export const DefaultLoading: Story = {
  args: {
    isLoading: true,
    children: 'Carregando conteúdo...',
  },
};

export const LoadingWithSpinner: Story = {
  args: {
    type: 'spinner',
    isLoading: true,
    children: 'Processando...',
  },
};

export const LoadingWithSkeleton: Story = {
  args: {
    type: 'skeleton',
    isLoading: true,
    children: (
      <Card className="w-64">
        <CardContent>
          <Skeleton className="h-4 w-full mb-2" />
          <Skeleton className="h-4 w-3/4" />
        </CardContent>
      </Card>
    ),
  },
};

export const SpinnerOnly: Story = {
  args: {
    children: <Spinner size="lg" variant="primary" />,
  },
};

export const SkeletonCard: Story = {
  args: {
    children: (
      <Card className="w-80">
        <CardContent>
          <Stack spacing="md">
            <Skeleton.Card />
            <Skeleton.Stat />
            <Skeleton.Table />
            <Skeleton.Chart />
            <Skeleton.List />
          </Stack>
        </CardContent>
      </Card>
    ),
  },
};

export const LoadingButtonStory: Story = {
  args: {
    children: <LoadingButton isLoading={true}>Aguarde</LoadingButton>,
  },
}; 
