/**
 * Controller de Planilhas
 * Sistema Magnow - Gerador de Planilhas para Mercado Envios Full
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { spreadsheetGeneratorService } from '../services/spreadsheetGeneratorService';
import { spreadsheetHistoryService } from '../services/spreadsheetHistoryService';
import { warehouseService } from '../services/warehouseService';
import { adjustmentService } from '../services/adjustmentService';
import { 
  GenerateSpreadsheetParams,
  ProductAdjustment,
  SpreadsheetConfig,
  AutoDistributionConfig
} from '../types/stock';
import { ValidationError, NotFoundError } from '../middleware/errorHandler';
import path from 'path';
import fs from 'fs';

export class SpreadsheetController {
  /**
   * POST /api/spreadsheets/generate
   * Gera nova planilha para Mercado Envios Full
   */
  public generateSpreadsheet = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        warehouseIds,
        productIds,
        categories,
        brands,
        useStockCalculation = true,
        minGapThreshold = 1,
        maxProducts = 1000,
        format = 'xlsx',
        splitByWarehouse = false,
        manualAdjustments = [],
        notes,
        tags = []
      } = req.body;

      logger.info('Gerando nova planilha', { 
        tenantId, 
        userId,
        format,
        maxProducts,
        warehouseIds: warehouseIds?.length || 'todos'
      });

      const params: GenerateSpreadsheetParams = {
        tenantId,
        warehouseIds,
        productIds,
        categories,
        brands,
        useStockCalculation,
        minGapThreshold,
        maxProducts,
        format,
        splitByWarehouse,
        manualAdjustments,
        generatedBy: userId,
        notes,
        tags
      };

      // Gerar planilha
      const result = await spreadsheetGeneratorService.generateSpreadsheet(params);

      // Registrar no histórico
      const historyRecord = await spreadsheetHistoryService.recordSpreadsheetGeneration(
        result,
        tenantId
      );

      logger.info('Planilha gerada com sucesso', {
        tenantId,
        spreadsheetId: historyRecord.id,
        totalProducts: result.totalProducts,
        fileName: result.fileName
      });

      res.status(201).json({
        success: true,
        data: {
          spreadsheet: result,
          historyId: historyRecord.id
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/spreadsheets/history
   * Lista histórico de planilhas geradas
   */
  public getSpreadsheetHistory = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        startDate,
        endDate,
        generatedBy,
        status,
        mlStatus,
        tags,
        hasErrors,
        limit = 50,
        offset = 0
      } = req.query;

      logger.info('Buscando histórico de planilhas', { 
        tenantId, 
        userId,
        filters: { status, mlStatus, limit, offset }
      });

      const filters = {
        tenantId,
        startDate: startDate ? new Date(startDate as string) : undefined,
        endDate: endDate ? new Date(endDate as string) : undefined,
        generatedBy: generatedBy as string,
        status: status as any,
        mlStatus: mlStatus as any,
        tags: tags ? (Array.isArray(tags) ? tags as string[] : [tags as string]) : undefined,
        hasErrors: hasErrors === 'true' ? true : hasErrors === 'false' ? false : undefined,
        limit: Math.min(parseInt(limit as string) || 50, 100),
        offset: Math.max(parseInt(offset as string) || 0, 0)
      };

      const result = await spreadsheetHistoryService.getSpreadsheetHistory(filters);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/spreadsheets/:id
   * Busca detalhes de uma planilha específica
   */
  public getSpreadsheet = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('ID da planilha é obrigatório');
      }

      logger.info('Buscando planilha', { tenantId, userId, spreadsheetId: id });

      const spreadsheet = await spreadsheetHistoryService.getSpreadsheetById(tenantId, id);

      if (!spreadsheet) {
        throw new NotFoundError('Planilha não encontrada');
      }

      res.json({
        success: true,
        data: spreadsheet
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/spreadsheets/:id/download
   * Download do arquivo da planilha
   */
  public downloadSpreadsheet = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { id } = req.params;

      if (!id) {
        throw new ValidationError('ID da planilha é obrigatório');
      }

      logger.info('Download de planilha', { tenantId, userId, spreadsheetId: id });

      const spreadsheet = await spreadsheetHistoryService.getSpreadsheetById(tenantId, id);

      if (!spreadsheet) {
        throw new NotFoundError('Planilha não encontrada');
      }

      const filePath = spreadsheet.generationResult.filePath;

      // Verificar se arquivo existe
      if (!fs.existsSync(filePath)) {
        throw new NotFoundError('Arquivo da planilha não encontrado');
      }

      // Marcar como baixado
      await spreadsheetHistoryService.markAsDownloaded(tenantId, id, userId);

      // Configurar headers para download
      const fileName = spreadsheet.generationResult.fileName;
      const fileExtension = path.extname(fileName);
      const contentType = fileExtension === '.xlsx' 
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv';

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
      res.setHeader('Content-Length', spreadsheet.generationResult.fileSize);

      // Enviar arquivo
      res.sendFile(path.resolve(filePath));

      logger.info('Download concluído', { tenantId, spreadsheetId: id, fileName });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/spreadsheets/statistics
   * Estatísticas do histórico de planilhas
   */
  public getStatistics = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { days = 30 } = req.query;

      logger.info('Buscando estatísticas de planilhas', { tenantId, userId, days });

      const statistics = await spreadsheetHistoryService.getHistoryStatistics(
        tenantId, 
        parseInt(days as string) || 30
      );

      res.json({
        success: true,
        data: statistics
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/spreadsheets/warehouses
   * Lista armazéns disponíveis
   */
  public getWarehouses = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;

      logger.info('Buscando armazéns', { tenantId, userId });

      const warehouses = await warehouseService.getWarehouses(tenantId);

      res.json({
        success: true,
        data: warehouses
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * POST /api/spreadsheets/preview
   * Gera preview da planilha sem criar arquivo
   */
  public previewSpreadsheet = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        warehouseIds,
        productIds,
        categories,
        brands,
        useStockCalculation = true,
        minGapThreshold = 1,
        maxProducts = 100, // Limite menor para preview
        manualAdjustments = []
      } = req.body;

      logger.info('Gerando preview de planilha', { 
        tenantId, 
        userId,
        maxProducts,
        warehouseIds: warehouseIds?.length || 'todos'
      });

      // Validar ajustes manuais se fornecidos
      if (manualAdjustments.length > 0) {
        const validation = adjustmentService.validateAdjustments(manualAdjustments);
        if (validation.invalid.length > 0) {
          throw new ValidationError(
            'Ajustes inválidos encontrados',
            validation.invalid.map(inv => ({
              field: inv.adjustment.sku,
              errors: inv.errors
            }))
          );
        }
      }

      // Gerar dados da planilha (sem criar arquivo)
      const result = await spreadsheetGeneratorService.generateSpreadsheet({
        tenantId,
        warehouseIds,
        productIds,
        categories,
        brands,
        useStockCalculation,
        minGapThreshold,
        maxProducts,
        manualAdjustments
      });

      // Retornar apenas os dados para preview
      res.json({
        success: true,
        data: {
          products: result.products.slice(0, 10), // Mostrar apenas os primeiros 10 produtos
          totalProducts: result.products.length,
          summary: {
            totalQuantity: result.products.reduce((sum, p) => sum + p.quantity, 0),
            totalValue: result.products.reduce((sum, p) => sum + (p.quantity * p.price), 0),
            warehousesCount: [...new Set(result.products.map(p => p.warehouseId))].length,
            categoriesCount: [...new Set(result.products.map(p => p.category))].length
          },
          appliedAdjustments: result.appliedAdjustments?.length || 0,
          warnings: result.warnings || []
        }
      });
    } catch (error) {
       next(error);
     }
   };

   /**
    * DELETE /api/spreadsheets/:id
    * Remove planilha do histórico
    */
   public deleteSpreadsheet = async (req: Request, res: Response, next: NextFunction) => {
     try {
       const { tenantId, userId } = req.user!;
       const { id } = req.params;

       logger.info('Removendo planilha do histórico', { 
         tenantId, 
         userId,
         spreadsheetId: id
       });

       // Verificar se a planilha existe e pertence ao tenant
       const spreadsheet = await spreadsheetHistoryService.getSpreadsheetById(tenantId, id);
       if (!spreadsheet) {
         throw new NotFoundError('Planilha não encontrada');
       }

       // Remover arquivo físico se existir
       if (spreadsheet.generationResult.filePath && fs.existsSync(spreadsheet.generationResult.filePath)) {
         fs.unlinkSync(spreadsheet.generationResult.filePath);
         logger.info('Arquivo físico removido', { filePath: spreadsheet.generationResult.filePath });
       }

       // Remover do histórico
       await spreadsheetHistoryService.deleteSpreadsheet(tenantId, id);

       res.json({
         success: true,
         message: 'Planilha removida com sucesso'
       });
     } catch (error) {
        next(error);
      }
    };

    /**
     * GET /api/spreadsheets/:id/download
     * Download de planilha gerada
     */
    public downloadSpreadsheet = async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = req.user!;
        const { id } = req.params;

        const spreadsheet = await spreadsheetHistoryService.getSpreadsheetById(tenantId, id);
        if (!spreadsheet) {
          throw new NotFoundError('Planilha não encontrada');
        }

        if (!spreadsheet.generationResult.filePath || !fs.existsSync(spreadsheet.generationResult.filePath)) {
          throw new NotFoundError('Arquivo da planilha não encontrado');
        }

        const fileName = path.basename(spreadsheet.generationResult.filePath);
        res.download(spreadsheet.generationResult.filePath, fileName);
      } catch (error) {
        next(error);
      }
    };

    /**
     * PUT /api/spreadsheets/:id/tags
     * Adiciona tags à planilha
     */
    public addTags = async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = req.user!;
        const { id } = req.params;
        const { tags } = req.body;

        if (!Array.isArray(tags)) {
          throw new ValidationError('Tags deve ser um array');
        }

        await spreadsheetHistoryService.addTags(tenantId, id, tags);

        res.json({
          success: true,
          message: 'Tags adicionadas com sucesso'
        });
      } catch (error) {
        next(error);
      }
    };

    /**
     * DELETE /api/spreadsheets/:id/tags
     * Remove tags da planilha
     */
    public removeTags = async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = req.user!;
        const { id } = req.params;
        const { tags } = req.body;

        if (!Array.isArray(tags)) {
          throw new ValidationError('Tags deve ser um array');
        }

        await spreadsheetHistoryService.removeTags(tenantId, id, tags);

        res.json({
          success: true,
          message: 'Tags removidas com sucesso'
        });
      } catch (error) {
        next(error);
      }
    };

    /**
     * PUT /api/spreadsheets/:id/notes
     * Adiciona notas à planilha
     */
    public addNotes = async (req: Request, res: Response, next: NextFunction) => {
      try {
        const { tenantId } = req.user!;
        const { id } = req.params;
        const { notes } = req.body;

        if (typeof notes !== 'string') {
          throw new ValidationError('Notes deve ser uma string');
        }

        await spreadsheetHistoryService.addNotes(tenantId, id, notes);

        res.json({
          success: true,
          message: 'Notas adicionadas com sucesso'
        });
      } catch (error) {
         next(error);
       }
     };

     /**
      * POST /api/spreadsheets/warehouses
      * Cria novo armazém
      */
     public createWarehouse = async (req: Request, res: Response, next: NextFunction) => {
       try {
         const { tenantId, userId } = req.user!;
         const { name, address, capacity } = req.body;

         const warehouse = await warehouseService.createWarehouse(tenantId, {
           name,
           address,
           capacity,
           createdBy: userId
         });

         res.status(201).json({
           success: true,
           data: warehouse
         });
       } catch (error) {
         next(error);
       }
     };

     /**
      * PUT /api/spreadsheets/warehouses/:id
      * Atualiza armazém
      */
     public updateWarehouse = async (req: Request, res: Response, next: NextFunction) => {
       try {
         const { tenantId } = req.user!;
         const { id } = req.params;
         const updateData = req.body;

         const warehouse = await warehouseService.updateWarehouse(tenantId, id, updateData);

         res.json({
           success: true,
           data: warehouse
         });
       } catch (error) {
         next(error);
       }
     };

     /**
      * GET /api/spreadsheets/distribution/preview
      * Preview de distribuição
      */
     public previewDistribution = async (req: Request, res: Response, next: NextFunction) => {
       try {
         const { tenantId } = req.user!;
         const { warehouseIds, productIds } = req.query;

         // Simular preview de distribuição
         const preview = {
           totalProducts: 0,
           warehouseDistribution: [],
           estimatedShipping: 0
         };

         res.json({
           success: true,
           data: preview
         });
       } catch (error) {
         next(error);
       }
     };

     /**
      * POST /api/spreadsheets/adjustments/apply
      * Aplica ajustes manuais
      */
     public applyAdjustments = async (req: Request, res: Response, next: NextFunction) => {
       try {
         const { tenantId, userId } = req.user!;
         const { adjustments, productIds } = req.body;

         // Simular aplicação de ajustes
         const result = {
           appliedCount: adjustments?.length || 0,
           skippedCount: 0,
           errors: []
         };

         res.json({
           success: true,
           data: result
         });
       } catch (error) {
         next(error);
       }
     };

     /**
      * GET /api/spreadsheets/adjustments/suggestions
      * Sugestões de ajustes
      */
     public getAdjustmentSuggestions = async (req: Request, res: Response, next: NextFunction) => {
       try {
         const { tenantId } = req.user!;
         const { productIds } = req.query;

         // Simular sugestões de ajustes
         const suggestions = [];

         res.json({
           success: true,
           data: suggestions
         });
       } catch (error) {
         next(error);
       }
     };

     /**
      * PUT /api/spreadsheets/:id/ml-status
      * Atualiza status ML
      */
     public updateMLStatus = async (req: Request, res: Response, next: NextFunction) => {
       try {
         const { tenantId } = req.user!;
         const { id } = req.params;
         const { status, mlData } = req.body;

         await spreadsheetHistoryService.updateMLStatus(tenantId, id, status, mlData);

         res.json({
           success: true,
           message: 'Status ML atualizado com sucesso'
         });
       } catch (error) {
         next(error);
       }
     };

     /**
      * GET /api/spreadsheets/export/history
      * Exporta histórico para CSV
      */
     public exportHistory = async (req: Request, res: Response, next: NextFunction) => {
       try {
         const { tenantId } = req.user!;
         const { startDate, endDate, format = 'csv' } = req.query;

         const history = await spreadsheetHistoryService.getHistory(tenantId, {
           startDate: startDate as string,
           endDate: endDate as string,
           limit: 1000
         });

         if (format === 'csv') {
           const csvData = this.convertToCSV(history);
           res.setHeader('Content-Type', 'text/csv');
           res.setHeader('Content-Disposition', 'attachment; filename=historico-planilhas.csv');
           res.send(csvData);
         } else {
           res.json({
             success: true,
             data: history
           });
         }
       } catch (error) {
         next(error);
       }
     };

     /**
      * Converte dados para CSV
      */
     private convertToCSV(data: any[]): string {
       if (!data.length) return '';

       const headers = Object.keys(data[0]).join(',');
       const rows = data.map(row => 
         Object.values(row).map(value => 
           typeof value === 'string' ? `"${value}"` : value
         ).join(',')
       );

       return [headers, ...rows].join('\n');
     }
   }

   export const spreadsheetController = new SpreadsheetController();
   export default spreadsheetController;
