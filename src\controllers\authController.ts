import { Request, Response, NextFunction } from 'express';
import { authService } from '@/services/authService';
import { logger } from '@/utils/logger';
import { ValidationError } from '@/middleware/errorHandler';
import { LoginRequest, RegisterRequest, RefreshTokenRequest } from '@/types/auth';

class AuthController {
  // Registro de usuário
  async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userData: RegisterRequest = req.body;

      // Validação básica dos campos obrigatórios
      if (!userData.email || !userData.password || !userData.firstName || 
          !userData.lastName || !userData.tenantName || !userData.tenantDomain) {
        throw new ValidationError('Todos os campos são obrigatórios');
      }

      const result = await authService.register(userData);

      logger.info(`Usuário registrado com sucesso: ${userData.email}`);

      res.status(201).json({
        success: true,
        message: 'Usuário registrado com sucesso',
        data: result
      });

    } catch (error) {
      logger.error('Erro no registro:', error);
      next(error);
    }
  }

  // Login de usuário
  async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const loginData: LoginRequest = req.body;

      // Validação básica
      if (!loginData.email || !loginData.password) {
        throw new ValidationError('Email e senha são obrigatórios');
      }

      const result = await authService.login(loginData);

      logger.info(`Login realizado com sucesso: ${loginData.email}`);

      res.status(200).json({
        success: true,
        message: 'Login realizado com sucesso',
        data: result
      });

    } catch (error) {
      logger.error('Erro no login:', error);
      next(error);
    }
  }

  // Refresh token
  async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken }: RefreshTokenRequest = req.body;

      if (!refreshToken) {
        throw new ValidationError('Refresh token é obrigatório');
      }

      const result = await authService.refreshToken(refreshToken);

      res.status(200).json({
        success: true,
        message: 'Token renovado com sucesso',
        data: result
      });

    } catch (error) {
      logger.error('Erro no refresh token:', error);
      next(error);
    }
  }

  // Logout (invalidar token - implementação futura com blacklist)
  async logout(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Por enquanto, apenas retorna sucesso
      // Em uma implementação completa, adicionaríamos o token a uma blacklist
      
      logger.info(`Logout realizado: ${req.user?.email || 'Usuário desconhecido'}`);

      res.status(200).json({
        success: true,
        message: 'Logout realizado com sucesso'
      });

    } catch (error) {
      logger.error('Erro no logout:', error);
      next(error);
    }
  }

  // Verificar token atual
  async me(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user || !req.tenant) {
        throw new ValidationError('Usuário não autenticado');
      }

      res.status(200).json({
        success: true,
        message: 'Dados do usuário atual',
        data: {
          user: req.user,
          tenant: req.tenant
        }
      });

    } catch (error) {
      logger.error('Erro ao obter dados do usuário:', error);
      next(error);
    }
  }

  // Verificar saúde da autenticação
  async healthCheck(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      res.status(200).json({
        success: true,
        message: 'Serviço de autenticação funcionando',
        timestamp: new Date().toISOString(),
        endpoints: [
          'POST /api/auth/register - Registro de usuário',
          'POST /api/auth/login - Login de usuário',
          'POST /api/auth/refresh - Renovar token',
          'POST /api/auth/logout - Logout',
          'GET /api/auth/me - Dados do usuário atual',
          'GET /api/auth/health - Health check'
        ]
      });

    } catch (error) {
      logger.error('Erro no health check de auth:', error);
      next(error);
    }
  }
}

export const authController = new AuthController();
export default authController; 