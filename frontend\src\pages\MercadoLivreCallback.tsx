import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useMercadoLivreStore } from '../store/mercadoLivreStore';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { CheckCircle, XCircle, RefreshCw } from 'lucide-react';

export default function MercadoLivreCallback() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { handleCallback } = useMercadoLivreStore();
  
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processCallback = async () => {
      const code = searchParams.get('code');
      const state = searchParams.get('state');
      const errorParam = searchParams.get('error');

      if (errorParam) {
        setStatus('error');
        setError(`Erro na autorização: ${errorParam}`);
        return;
      }

      if (!code) {
        setStatus('error');
        setError('Código de autorização não encontrado');
        return;
      }

      try {
        await handleCallback(code, state || undefined);
        setStatus('success');
        
        // Notify parent window if opened in popup
        if (window.opener) {
          window.opener.postMessage({ type: 'MERCADOLIVRE_AUTH_SUCCESS' }, '*');
          window.close();
        } else {
          // Redirect to settings after 2 seconds
          setTimeout(() => {
            navigate('/settings');
          }, 2000);
        }
      } catch (err) {
        setStatus('error');
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      }
    };

    processCallback();
  }, [searchParams, handleCallback, navigate]);

  const handleRetry = () => {
    navigate('/settings');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            {status === 'loading' && <RefreshCw className="h-5 w-5 animate-spin" />}
            {status === 'success' && <CheckCircle className="h-5 w-5 text-green-500" />}
            {status === 'error' && <XCircle className="h-5 w-5 text-red-500" />}
            
            {status === 'loading' && 'Processando...'}
            {status === 'success' && 'Sucesso!'}
            {status === 'error' && 'Erro'}
          </CardTitle>
        </CardHeader>
        
        <CardContent className="text-center space-y-4">
          {status === 'loading' && (
            <p className="text-muted-foreground">
              Conectando sua conta do Mercado Livre...
            </p>
          )}
          
          {status === 'success' && (
            <div className="space-y-2">
              <p className="text-green-600">
                Conta conectada com sucesso!
              </p>
              <p className="text-sm text-muted-foreground">
                {window.opener ? 'Esta janela será fechada automaticamente.' : 'Redirecionando para configurações...'}
              </p>
            </div>
          )}
          
          {status === 'error' && (
            <div className="space-y-4">
              <p className="text-red-600">
                {error}
              </p>
              <Button onClick={handleRetry} className="w-full">
                Tentar Novamente
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
