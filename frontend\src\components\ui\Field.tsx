import React from 'react';
import { cn } from '../../lib/utils';
import { useFormContext } from 'react-hook-form';
import type { FieldError } from 'react-hook-form';

export interface FieldProps extends React.HTMLAttributes<HTMLDivElement> {
  name: string;
  label?: string;
  htmlFor?: string;
  helpText?: string;
  children: React.ReactNode;
  className?: string;
}

const Field = React.forwardRef<HTMLDivElement, FieldProps>(
  (
    {
      name,
      label,
      htmlFor,
      helpText,
      children,
      className,
      ...props
    },
    ref
  ) => {
    const { formState: { errors } } = useFormContext();
    const error = errors[name] as FieldError | undefined; // Voltando para o tipo FieldError

    const id = htmlFor || name;

    return (
      <div ref={ref} className={cn('space-y-1', className)} {...props}>
        {label && (
          <label htmlFor={id} className="block text-sm font-medium text-foreground">
            {label}
          </label>
        )}
        {children}
        {error ? (
          <p className="text-sm text-danger-600" role="alert">
            {error.message}
          </p>
        ) : helpText ? (
          <p className="text-sm text-muted-foreground">
            {helpText}
          </p>
        ) : null}
      </div>
    );
  }
);
Field.displayName = 'Field';

export { Field }; 
