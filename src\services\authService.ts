import * as bcrypt from 'bcryptjs';
import * as jwt from 'jsonwebtoken';
import * as crypto from 'crypto';
import { getDatabase } from '../config/database';
import { logger } from '../utils/logger';
import { 
  LoginRequest, 
  RegisterRequest, 
  LoginResponse, 
  JwtPayload, 
  UserSafe, 
  TenantSafe,
  PasswordValidation,
  EmailValidation,
  EncryptionResult,
  DecryptionParams
} from '../types/auth';
import { AuthenticationError, ValidationError, NotFoundError } from '../middleware/errorHandler';

class AuthService {
  private readonly jwtSecret: string;
  private readonly jwtRefreshSecret: string;
  private readonly jwtExpiresIn: string;
  private readonly jwtRefreshExpiresIn: string;
  private readonly encryptionKey: string;
  private readonly saltRounds: number = 12;

  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'default_secret_change_in_production';
    this.jwtRefreshSecret = process.env.JWT_REFRESH_SECRET || 'default_refresh_secret_change_in_production';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '7d';
    this.jwtRefreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '30d';
    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default_encryption_key_32_chars!';

    // Removido debug temporário

    if (this.encryptionKey.length !== 32) {
      throw new Error('ENCRYPTION_KEY deve ter exatamente 32 caracteres');
    }
  }

  // Registro de usuário com tenant
  async register(data: RegisterRequest): Promise<LoginResponse> {
    const prisma = getDatabase();

    try {
      // Validar dados
      await this.validateRegistrationData(data);

      // Verificar se o domínio do tenant já existe
      const existingTenant = await prisma.tenant.findUnique({
        where: { domain: data.tenantDomain }
      });

      if (existingTenant) {
        throw new ValidationError('Domínio do tenant já está em uso');
      }

      // Verificar se o email já existe
      const existingUser = await prisma.user.findUnique({
        where: { email: data.email }
      });

      if (existingUser) {
        throw new ValidationError('Email já está em uso');
      }

      // Hash da senha
      const hashedPassword = await this.hashPassword(data.password);

      // Criar tenant e usuário em transação
      const result = await prisma.$transaction(async (tx) => {
        // Criar tenant
        const tenant = await tx.tenant.create({
          data: {
            name: data.tenantName,
            domain: data.tenantDomain,
            isActive: true,
          }
        });

        // Criar usuário admin do tenant
        const user = await tx.user.create({
          data: {
            email: data.email,
            password: hashedPassword,
            firstName: data.firstName,
            lastName: data.lastName,
            role: 'ADMIN',
            isActive: true,
            emailVerified: false,
            tenantId: tenant.id,
          }
        });

        return { user, tenant };
      });

      // Gerar tokens
      const { accessToken, refreshToken } = await this.generateTokens(result.user, result.tenant);

      // Atualizar último login
      await prisma.user.update({
        where: { id: result.user.id },
        data: { lastLoginAt: new Date() }
      });

      logger.info(`Novo usuário registrado: ${result.user.email} (Tenant: ${result.tenant.domain})`);

      return {
        user: this.sanitizeUser(result.user),
        tenant: this.sanitizeTenant(result.tenant),
        accessToken,
        refreshToken,
        expiresIn: this.getTokenExpirationTime()
      };

    } catch (error) {
      logger.error('Erro no registro:', error);
      throw error;
    }
  }

  // Login de usuário
  async login(data: LoginRequest): Promise<LoginResponse> {
    const prisma = getDatabase();

    try {
      // Buscar usuário com tenant
      const user = await prisma.user.findUnique({
        where: { email: data.email },
        include: { tenant: true }
      });

      if (!user) {
        throw new AuthenticationError('Credenciais inválidas');
      }

      // Verificar se o tenant está ativo
      if (!user.tenant.isActive) {
        throw new AuthenticationError('Tenant inativo');
      }

      // Verificar se o usuário está ativo
      if (!user.isActive) {
        throw new AuthenticationError('Usuário inativo');
      }

      // Verificar senha
      const isPasswordValid = await this.verifyPassword(data.password, user.password);
      if (!isPasswordValid) {
        throw new AuthenticationError('Credenciais inválidas');
      }

      // Verificar domínio do tenant se fornecido
      if (data.tenantDomain && user.tenant.domain !== data.tenantDomain) {
        throw new AuthenticationError('Tenant não encontrado');
      }

      // Gerar tokens
      const { accessToken, refreshToken } = await this.generateTokens(user, user.tenant);

      // Atualizar último login
      await prisma.user.update({
        where: { id: user.id },
        data: { lastLoginAt: new Date() }
      });

      logger.info(`Login realizado: ${user.email} (Tenant: ${user.tenant.domain})`);

      return {
        user: this.sanitizeUser(user),
        tenant: this.sanitizeTenant(user.tenant),
        accessToken,
        refreshToken,
        expiresIn: this.getTokenExpirationTime()
      };

    } catch (error) {
      logger.error('Erro no login:', error);
      throw error;
    }
  }

  // Refresh token
  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string; expiresIn: number }> {
    try {
      // Verificar refresh token
      const decoded = jwt.verify(refreshToken, this.jwtRefreshSecret) as JwtPayload;

      const prisma = getDatabase();
      
      // Buscar usuário atual
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        include: { tenant: true }
      });

      if (!user || !user.tenant.isActive || !user.isActive) {
        throw new AuthenticationError('Token inválido');
      }

      // Gerar novos tokens
      const tokens = await this.generateTokens(user, user.tenant);

      return {
        ...tokens,
        expiresIn: this.getTokenExpirationTime()
      };

    } catch (error) {
      logger.error('Erro no refresh token:', error);
      throw new AuthenticationError('Token inválido');
    }
  }

  // Verificar token de acesso
  async verifyAccessToken(token: string): Promise<JwtPayload> {
    try {
      const decoded = jwt.verify(token, this.jwtSecret) as JwtPayload;
      return decoded;
    } catch (error) {
      throw new AuthenticationError('Token inválido');
    }
  }

  // Validação de dados de registro
  private async validateRegistrationData(data: RegisterRequest): Promise<void> {
    const errors: string[] = [];

    // Validar email
    const emailValidation = this.validateEmail(data.email);
    if (!emailValidation.isValid) {
      errors.push(emailValidation.error!);
    }

    // Validar senha
    const passwordValidation = this.validatePassword(data.password);
    if (!passwordValidation.isValid) {
      errors.push(...passwordValidation.errors);
    }

    // Validar nome
    if (!data.firstName || data.firstName.trim().length < 2) {
      errors.push('Nome deve ter pelo menos 2 caracteres');
    }

    if (!data.lastName || data.lastName.trim().length < 2) {
      errors.push('Sobrenome deve ter pelo menos 2 caracteres');
    }

    // Validar nome do tenant
    if (!data.tenantName || data.tenantName.trim().length < 2) {
      errors.push('Nome da empresa deve ter pelo menos 2 caracteres');
    }

    // Validar domínio do tenant
    if (!data.tenantDomain || !/^[a-z0-9-]+$/.test(data.tenantDomain)) {
      errors.push('Domínio deve conter apenas letras minúsculas, números e hífens');
    }

    if (errors.length > 0) {
      throw new ValidationError(`Dados inválidos: ${errors.join(', ')}`);
    }
  }

  // Validação de email
  private validateEmail(email: string): EmailValidation {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (!email) {
      return { isValid: false, error: 'Email é obrigatório' };
    }

    if (!emailRegex.test(email)) {
      return { isValid: false, error: 'Email inválido' };
    }

    return { isValid: true };
  }

  // Validação de senha
  private validatePassword(password: string): PasswordValidation {
    const errors: string[] = [];

    if (!password) {
      errors.push('Senha é obrigatória');
      return { isValid: false, errors };
    }

    if (password.length < 8) {
      errors.push('Senha deve ter pelo menos 8 caracteres');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Senha deve conter pelo menos uma letra maiúscula');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Senha deve conter pelo menos uma letra minúscula');
    }

    if (!/\d/.test(password)) {
      errors.push('Senha deve conter pelo menos um número');
    }

    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('Senha deve conter pelo menos um caractere especial');
    }

    return { isValid: errors.length === 0, errors };
  }

  // Hash da senha
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.saltRounds);
  }

  // Verificar senha
  async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  // Verificar senha (método privado)
  private async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  // Gerar tokens JWT
  private async generateTokens(user: any, tenant: any): Promise<{ accessToken: string; refreshToken: string }> {
    const payload: JwtPayload = {
      userId: user.id,
      tenantId: tenant.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.jwtExpiresIn,
    } as jwt.SignOptions);

    const refreshToken = jwt.sign(payload, this.jwtRefreshSecret, {
      expiresIn: this.jwtRefreshExpiresIn,
    } as jwt.SignOptions);

    return { accessToken, refreshToken };
  }

  // Obter tempo de expiração do token
  private getTokenExpirationTime(): number {
    // Converter string de tempo para segundos
    const timeStr = this.jwtExpiresIn;
    const timeValue = parseInt(timeStr);
    const unit = timeStr.slice(-1);

    switch (unit) {
      case 's': return timeValue;
      case 'm': return timeValue * 60;
      case 'h': return timeValue * 3600;
      case 'd': return timeValue * 86400;
      default: return 604800; // 7 dias por padrão
    }
  }

  // Sanitizar dados do usuário
  private sanitizeUser(user: any): UserSafe {
    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isActive: user.isActive,
      lastLoginAt: user.lastLoginAt,
      emailVerified: user.emailVerified,
      tenantId: user.tenantId,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };
  }

  // Sanitizar dados do tenant
  private sanitizeTenant(tenant: any): TenantSafe {
    return {
      id: tenant.id,
      name: tenant.name,
      domain: tenant.domain,
      subdomain: tenant.subdomain,
      isActive: tenant.isActive,
      createdAt: tenant.createdAt,
      updatedAt: tenant.updatedAt,
    };
  }

  // Criptografar dados sensíveis
  encrypt(text: string): EncryptionResult {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher('aes-256-cbc', this.encryptionKey);
    cipher.setAutoPadding(true);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    return {
      encrypted,
      iv: iv.toString('hex')
    };
  }

  // Descriptografar dados sensíveis
  decrypt(params: DecryptionParams): string {
    const decipher = crypto.createDecipher('aes-256-cbc', this.encryptionKey);
    decipher.setAutoPadding(true);
    
    let decrypted = decipher.update(params.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }

  // Gerar token JWT (método público para testes)
  generateToken(user: { id: string; email: string; tenantId: string; role: string }): string {
    const payload: JwtPayload = {
      userId: user.id,
      tenantId: user.tenantId,
      email: user.email,
      role: user.role as any,
    };

    return jwt.sign(payload, this.jwtSecret, {
      expiresIn: this.jwtExpiresIn,
    } as jwt.SignOptions);
  }

  // Verificar token JWT (método público para testes)
  verifyToken(token: string): JwtPayload {
    return jwt.verify(token, this.jwtSecret) as JwtPayload;
  }

  // Validar email (método público)
  isValidEmail(email: string): boolean {
    const emailValidation = this.validateEmail(email);
    return emailValidation.isValid;
  }

  // Validar senha (método público)
  isValidPassword(password: string): boolean {
    const passwordValidation = this.validatePassword(password);
    return passwordValidation.isValid;
  }

  // Sanitizar email
  sanitizeEmail(email: string): string {
    if (!email) return '';
    return email.trim().toLowerCase();
  }

  // Gerar código de verificação
  generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  // Verificar rate limiting
  isRateLimited(attempts: number, maxAttempts: number): boolean {
    return attempts >= maxAttempts;
  }

  // Gerenciamento de sessão simples (para testes)
  private sessions = new Map<string, any>();

  // Criar sessão
  createSession(sessionData: any): string {
    const sessionId = crypto.randomBytes(32).toString('hex');
    this.sessions.set(sessionId, sessionData);
    return sessionId;
  }

  // Validar sessão
  isValidSession(sessionId: string): boolean {
    return this.sessions.has(sessionId);
  }

  // Destruir sessão
  destroySession(sessionId: string): void {
    this.sessions.delete(sessionId);
  }
}

export const authService = new AuthService();
export default authService;