import axios, { AxiosResponse, AxiosError } from 'axios';
import { PrismaClient } from '@prisma/client';
import { 
  logger, 
  logMLApiPerformance, 
  logMLRateLimit, 
  logMLError, 
  logMLRetry, 
  logMLSync 
} from '../utils/logger';
import {
  MercadoLivreItem,
  MercadoLivreOrder,
  MercadoLivreSearchParams,
  MercadoLivreSearchResponse,
  MercadoLivreStockUpdate,
  MercadoLivreStockResponse,
  MercadoLivreApiResponse,
  MERCADO_LIVRE_ENDPOINTS,
  MERCADO_LIVRE_COUNTRIES,
  CountryConfig
} from '../types/mercadolivre';

// Classes de erro personalizadas simplificadas
class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

class ExternalServiceError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ExternalServiceError';
  }
}

class NotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class MercadoLivreApiService {
  private prisma: PrismaClient;
  private baseUrl: string;
  private rateLimitInfo: Map<string, { remaining: number; reset: number; limit: number }>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.baseUrl = process.env.MERCADO_LIVRE_API_URL || 'https://api.mercadolibre.com';
    this.rateLimitInfo = new Map();
  }

  /**
   * Faz uma requisição autenticada para a API do Mercado Livre
   */
  private async makeAuthenticatedRequest<T>(
    endpoint: string,
    accessToken: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any,
    params?: Record<string, any>,
    userId?: string,
    tenantId?: string
  ): Promise<MercadoLivreApiResponse<T>> {
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(2, 15);
    let retryCount = 0;
    const maxRetries = 3;

    try {
      const config = {
        method,
        url: `${this.baseUrl}${endpoint}`,
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          'User-Agent': 'Magnow/1.0',
          'X-Request-ID': requestId
        },
        params,
        data,
        timeout: 30000
      };

      logger.debug(`ML API Request: ${method} ${endpoint}`, {
        requestId,
        userId,
        tenantId,
        endpoint,
        method,
        hasData: !!data,
        paramsCount: params ? Object.keys(params).length : 0
      });

      const response: AxiosResponse<T> = await axios(config);
      const responseTime = Date.now() - startTime;

      // Atualiza informações de rate limit
      this.updateRateLimitInfo(response.headers as any, endpoint, userId, tenantId);

      // Log de performance da API
      logMLApiPerformance({
        endpoint,
        method,
        responseTime,
        retryCount,
        cacheHit: false,
        dataSize: JSON.stringify(response.data).length,
        userId: userId || undefined,
        tenantId: tenantId || undefined,
        requestId
      });

      logger.debug(`ML API Response: ${method} ${endpoint} - ${response.status}`, {
        requestId,
        statusCode: response.status,
        responseTime,
        dataSize: JSON.stringify(response.data).length
      });

      return {
        data: response.data,
        status: response.status,
        headers: response.headers as Record<string, string>
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError;
        const statusCode = axiosError.response?.status || 0;
        
        // Atualiza rate limit mesmo em caso de erro
        if (axiosError.response?.headers) {
          this.updateRateLimitInfo(axiosError.response.headers as any, endpoint, userId, tenantId);
        }

        // Log detalhado do erro
        logMLError(
          endpoint,
          method,
          statusCode,
          axiosError,
          retryCount,
          userId || undefined,
          tenantId || undefined
        );

        // Log de performance mesmo com erro
        logMLApiPerformance({
          endpoint,
          method,
          responseTime,
          retryCount,
          cacheHit: false,
          userId: userId || undefined,
          tenantId: tenantId || undefined,
          requestId
        });

        // Trata diferentes tipos de erro
        if (statusCode === 404) {
          throw new NotFoundError('Recurso não encontrado na API do Mercado Livre');
        }

        if (statusCode === 429) {
          // Log específico de rate limit
          logMLRetry(
            endpoint,
            method,
            retryCount + 1,
            maxRetries,
            5000,
            'Rate limit exceeded',
            userId || undefined,
            tenantId || undefined
          );
          throw new ExternalServiceError('Rate limit excedido na API do Mercado Livre');
        }

        if (statusCode === 401) {
          throw new ExternalServiceError('Token de acesso inválido ou expirado');
        }

        throw new ExternalServiceError(
          `Erro na API do Mercado Livre: ${statusCode || 'Unknown'}`
        );
      }

      // Log para erro não relacionado ao axios
      logMLError(
        endpoint,
        method,
        0,
        error,
        retryCount,
        userId || undefined,
        tenantId || undefined
      );

      throw new ExternalServiceError('Erro interno ao comunicar com API do Mercado Livre');
    }
  }

  /**
   * Atualiza informações de rate limit com base nos headers da resposta
   */
  private updateRateLimitInfo(
    headers: Record<string, any>, 
    endpoint?: string, 
    userId?: string, 
    tenantId?: string
  ): void {
    const remaining = headers['x-ratelimit-remaining'];
    const reset = headers['x-ratelimit-reset'];
    const limit = headers['x-ratelimit-limit'];

    if (remaining && reset && limit) {
      const rateLimitData = {
        remaining: parseInt(remaining),
        reset: parseInt(reset),
        limit: parseInt(limit)
      };

      this.rateLimitInfo.set('global', rateLimitData);

      // Log específico de rate limit
      logMLRateLimit({
        endpoint: endpoint || 'unknown',
        remaining: rateLimitData.remaining,
        limit: rateLimitData.limit,
        reset: rateLimitData.reset,
        userId: userId || undefined,
        tenantId: tenantId || undefined
      });
    }
  }

  /**
   * Busca anúncios do usuário
   */
  public async getUserItems(
    accessToken: string,
    userId: string,
    searchParams: MercadoLivreSearchParams = {},
    requestUserId?: string,
    tenantId?: string
  ): Promise<MercadoLivreApiResponse<MercadoLivreSearchResponse>> {
    const endpoint = `/users/${userId}/items/search`;
    
    const params = {
      limit: searchParams.limit || 50,
      offset: searchParams.offset || 0,
      status: searchParams.status,
      sort: searchParams.sort || 'relevance'
    };

    // Remove parâmetros undefined
    Object.keys(params).forEach(key => 
      params[key as keyof typeof params] === undefined && delete params[key as keyof typeof params]
    );

    return this.makeAuthenticatedRequest<MercadoLivreSearchResponse>(
      endpoint,
      accessToken,
      'GET',
      undefined,
      params,
      requestUserId,
      tenantId
    );
  }

  /**
   * Busca detalhes de um anúncio específico
   */
  public async getItemDetails(
    accessToken: string,
    itemId: string,
    userId?: string,
    tenantId?: string
  ): Promise<MercadoLivreApiResponse<MercadoLivreItem>> {
    const endpoint = `/items/${itemId}`;
    
    return this.makeAuthenticatedRequest<MercadoLivreItem>(
      endpoint,
      accessToken,
      'GET',
      undefined,
      undefined,
      userId,
      tenantId
    );
  }

  /**
   * Busca múltiplos anúncios por IDs
   */
  public async getMultipleItems(
    accessToken: string,
    itemIds: string[],
    userId?: string,
    tenantId?: string
  ): Promise<MercadoLivreApiResponse<MercadoLivreItem[]>> {
    if (itemIds.length === 0) {
      throw new ValidationError('Lista de IDs de itens não pode estar vazia');
    }

    if (itemIds.length > 20) {
      throw new ValidationError('Máximo de 20 itens por requisição');
    }

    const idsParam = itemIds.join(',');
    
    return this.makeAuthenticatedRequest<MercadoLivreItem[]>(
      `/items?ids=${idsParam}`,
      accessToken,
      'GET',
      undefined,
      undefined,
      userId,
      tenantId
    );
  }

  /**
   * Busca vendas/pedidos do usuário
   */
  public async getUserOrders(
    accessToken: string,
    searchParams: {
      seller_id?: string;
      buyer_id?: string;
      order_status?: string;
      date_from?: string;
      date_to?: string;
      limit?: number;
      offset?: number;
      sort?: 'date_asc' | 'date_desc';
    } = {}
  ): Promise<MercadoLivreApiResponse<{ results: MercadoLivreOrder[]; paging: any }>> {
    const params = {
      limit: searchParams.limit || 50,
      offset: searchParams.offset || 0,
      seller: searchParams.seller_id,
      buyer: searchParams.buyer_id,
      'order.status': searchParams.order_status,
      'order.date_created.from': searchParams.date_from,
      'order.date_created.to': searchParams.date_to,
      sort: searchParams.sort || 'date_desc'
    };

    // Remove parâmetros undefined
    Object.keys(params).forEach(key => 
      params[key as keyof typeof params] === undefined && delete params[key as keyof typeof params]
    );

    return this.makeAuthenticatedRequest<{ results: MercadoLivreOrder[]; paging: any }>(
      '/orders/search',
      accessToken,
      'GET',
      undefined,
      params
    );
  }

  /**
   * Busca detalhes de um pedido específico
   */
  public async getOrderDetails(
    accessToken: string,
    orderId: string
  ): Promise<MercadoLivreApiResponse<MercadoLivreOrder>> {
    const endpoint = `/orders/${orderId}`;
    
    return this.makeAuthenticatedRequest<MercadoLivreOrder>(
      endpoint,
      accessToken,
      'GET'
    );
  }

  /**
   * Atualiza estoque de um anúncio
   */
  public async updateItemStock(
    accessToken: string,
    itemId: string,
    stockUpdate: MercadoLivreStockUpdate
  ): Promise<MercadoLivreApiResponse<MercadoLivreItem>> {
    const endpoint = `/items/${itemId}`;
    
    const updateData = {
      available_quantity: stockUpdate.available_quantity
    };

    return this.makeAuthenticatedRequest<MercadoLivreItem>(
      endpoint,
      accessToken,
      'PUT',
      updateData
    );
  }

  /**
   * Busca itens no Mercado Livre
   */
  public async searchItems(
    accessToken: string,
    searchParams: MercadoLivreSearchParams
  ): Promise<MercadoLivreApiResponse<MercadoLivreSearchResponse>> {
    const endpoint = '/sites/MLB/search';
    
    const params = {
      q: searchParams.q,
      limit: searchParams.limit || 50,
      offset: searchParams.offset || 0
    };

    return this.makeAuthenticatedRequest<MercadoLivreSearchResponse>(
      endpoint,
      accessToken,
      'GET',
      undefined,
      params
    );
  }

  /**
   * Atualiza estoque de um item (alias para updateItemStock)
   */
  public async updateStock(
    accessToken: string,
    itemId: string,
    availableQuantity: number
  ): Promise<MercadoLivreApiResponse<MercadoLivreItem>> {
    return this.updateItemStock(accessToken, itemId, { available_quantity: availableQuantity });
  }

  /**
   * Obtém pedidos do usuário (alias para getUserOrders)
   */
  public async getOrders(
    accessToken: string,
    userId: string,
    searchParams: {
      limit?: number;
      offset?: number;
      sort?: string;
    } = {}
  ): Promise<MercadoLivreApiResponse<{ results: MercadoLivreOrder[]; paging: any }>> {
    return this.getUserOrders(accessToken, {
      seller_id: userId,
      limit: searchParams.limit,
      offset: searchParams.offset,
      sort: searchParams.sort as 'date_asc' | 'date_desc'
    });
  }

  /**
   * Sincroniza dados de anúncios com o banco de dados local
   */
  public async syncItemsToDatabase(
    tenantId: string,
    accountId: string,
    items: MercadoLivreItem[]
  ): Promise<{ synced: number; errors: number }> {
    const startTime = Date.now();
    let synced = 0;
    let errors = 0;
    const syncErrors: any[] = [];

    for (const item of items) {
      try {
        await this.prisma.product.upsert({
          where: {
            tenantId_mlId: {
              mlId: item.id,
              tenantId
            }
          },
          update: {
            title: item.title,
            price: item.price,
            availableQuantity: item.available_quantity,
            soldQuantity: item.sold_quantity,
            status: item.status,
            thumbnail: item.secure_thumbnail,
            permalink: item.permalink,
            updatedAt: new Date()
          },
          create: {
            tenantId,
            mlAccountId: accountId,
            mlId: item.id,
            title: item.title,
            category: item.category_id || 'Unknown',
            price: item.price,
            availableQuantity: item.available_quantity,
            soldQuantity: item.sold_quantity,
            status: item.status,
            thumbnail: item.secure_thumbnail,
            permalink: item.permalink
          }
        });
        synced++;
      } catch (error) {
        logger.error('Erro ao sincronizar item com banco de dados', {
          itemId: item.id,
          tenantId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        syncErrors.push({ itemId: item.id, error });
        errors++;
      }
    }

    // Log da sincronização
    logMLSync({
      operation: 'SYNC_ITEMS',
      totalItems: items.length,
      syncedItems: synced,
      failedItems: errors,
      duration: Date.now() - startTime,
      errors: syncErrors.length > 0 ? syncErrors : undefined,
      tenantId
    });

    return { synced, errors };
  }

  /**
   * Sincroniza dados de vendas com o banco de dados local
   */
  public async syncSalesToDatabase(
    tenantId: string,
    orders: MercadoLivreOrder[]
  ): Promise<{ synced: number; errors: number }> {
    const startTime = Date.now();
    let synced = 0;
    let errors = 0;
    const syncErrors: any[] = [];

    for (const order of orders) {
      try {
        // Para cada item no pedido, criar uma venda
        for (const orderItem of order.order_items || []) {
          // Encontrar o produto correspondente
          const product = await this.prisma.product.findFirst({
            where: {
              tenantId,
              mlId: orderItem.item?.id
            }
          });

          if (product) {
            await this.prisma.sale.create({
              data: {
                mlOrderId: order.id.toString(),
                quantity: orderItem.quantity,
                unitPrice: orderItem.unit_price,
                totalPrice: orderItem.quantity * orderItem.unit_price,
                saleDate: new Date(order.date_created),
                status: order.status,
                productId: product.id
              }
            });
          }
        }
        synced++;
      } catch (error) {
        logger.error('Erro ao sincronizar venda com banco de dados', {
          orderId: order.id,
          tenantId,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        syncErrors.push({ orderId: order.id, error });
        errors++;
      }
    }

    // Log da sincronização
    logMLSync({
      operation: 'SYNC_ORDERS',
      totalItems: orders.length,
      syncedItems: synced,
      failedItems: errors,
      duration: Date.now() - startTime,
      errors: syncErrors.length > 0 ? syncErrors : undefined,
      tenantId
    });

    return { synced, errors };
  }

  /**
   * Verifica o status da API do Mercado Livre
   */
  public async healthCheck(): Promise<{ status: 'ok' | 'error'; details: any }> {
    try {
      const response = await axios.get(`${this.baseUrl}/sites`, {
        timeout: 5000
      });

      return {
        status: 'ok',
        details: {
          apiUrl: this.baseUrl,
          responseTime: response.headers['x-response-time'] || 'unknown',
          rateLimitInfo: this.rateLimitInfo.get('global') || null,
          sitesCount: response.data?.length || 0
        }
      };
    } catch (error) {
      return {
        status: 'error',
        details: {
          error: error instanceof Error ? error.message : 'Unknown error',
          apiUrl: this.baseUrl
        }
      };
    }
  }
}
