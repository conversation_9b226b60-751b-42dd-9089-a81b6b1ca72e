import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { Package, FileSpreadsheet, Upload } from 'lucide-react';
import { Card, CardContent } from './card';

// Spinner component with variants
const spinnerVariants = cva(
  'animate-spin rounded-full border-2 border-border-secondary',
  {
    variants: {
      variant: {
        default: 'border-t-primary',
        primary: 'border-t-primary',
        success: 'border-t-success',
        warning: 'border-t-warning',
        danger: 'border-t-danger',
        info: 'border-t-info',
        muted: 'border-t-muted-foreground',
      },
      size: {
        xs: 'h-3 w-3',
        sm: 'h-4 w-4',
        default: 'h-6 w-6',
        lg: 'h-8 w-8',
        xl: 'h-10 w-10',
        '2xl': 'h-12 w-12',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface SpinnerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof spinnerVariants> {}

const Spinner = React.forwardRef<HTMLDivElement, SpinnerProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(spinnerVariants({ variant, size, className }))}
        role="status"
        aria-label="Carregando..."
        {...props}
      />
    );
  }
);
Spinner.displayName = 'Spinner';

// Loading container variants
const loadingVariants = cva(
  'flex items-center justify-center',
  {
    variants: {
      variant: {
        overlay: 'fixed inset-0 bg-surface/80 backdrop-blur-sm z-50',
        inline: 'py-8',
        compact: 'py-4',
        fullscreen: 'min-h-screen bg-surface',
        card: 'bg-surface rounded-lg border border-border shadow-sm p-6',
      },
      alignment: {
        center: 'text-center',
        start: 'text-left',
        end: 'text-right',
      },
    },
    defaultVariants: {
      variant: 'inline',
      alignment: 'center',
    },
  }
);

export interface LoadingProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof loadingVariants> {
  message?: string;
  spinner?: VariantProps<typeof spinnerVariants>;
  showMessage?: boolean;
}

export const Loading = React.forwardRef<HTMLDivElement, LoadingProps>(
  ({ 
    className, 
    variant, 
    alignment, 
    message = 'Carregando...', 
    spinner,
    showMessage = true,
    ...props 
  }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(loadingVariants({ variant, alignment, className }))}
        role="status"
        aria-live="polite"
        {...props}
      >
        <div className={cn(alignment === 'center' ? 'text-center' : '')}>
          <Spinner 
            variant={spinner?.variant} 
            size={spinner?.size} 
            className={cn(alignment === 'center' ? 'mx-auto' : '', 'mb-2')}
          />
          {showMessage && (
            <p className="text-sm text-muted-foreground font-medium">
              {message}
            </p>
          )}
        </div>
      </div>
    );
  }
);
Loading.displayName = 'Loading';

// Skeleton variants
const skeletonVariants = cva(
  'animate-pulse bg-muted rounded',
  {
    variants: {
      variant: {
        default: 'bg-muted',
        text: 'bg-muted h-4',
        avatar: 'bg-muted rounded-full',
        button: 'bg-muted h-10 rounded-md',
        card: 'bg-muted rounded-lg',
        image: 'bg-muted aspect-video rounded-lg',
      },
      size: {
        xs: 'h-2',
        sm: 'h-3',
        default: 'h-4',
        lg: 'h-6',
        xl: 'h-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface SkeletonProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof skeletonVariants> {}

export const Skeleton = React.forwardRef<HTMLDivElement, SkeletonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(skeletonVariants({ variant, size, className }))}
        {...props}
      />
    );
  }
);
Skeleton.displayName = 'Skeleton';

// Specialized skeleton components
export interface CardSkeletonProps {
  className?: string;
  showAvatar?: boolean;
  lines?: number;
}

export const CardSkeleton: React.FC<CardSkeletonProps> = ({ 
  className, 
  showAvatar = false, 
  lines = 3 
}) => {
  return (
    <div className={cn('animate-pulse bg-surface rounded-lg border border-border p-6', className)}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {showAvatar && <Skeleton variant="avatar" className="h-8 w-8" />}
          <Skeleton variant="text" className="h-5 w-32" />
        </div>
        <Skeleton variant="text" className="h-4 w-16" />
      </div>
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, i) => (
          <Skeleton 
            key={i} 
            variant="text" 
            className={cn(
              'h-4',
              i === lines - 1 ? 'w-2/3' : 'w-full'
            )}
          />
        ))}
      </div>
    </div>
  );
};

export interface StatSkeletonProps {
  className?: string;
  showIcon?: boolean;
}

export const StatSkeleton: React.FC<StatSkeletonProps> = ({ 
  className, 
  showIcon = true 
}) => {
  return (
    <div className={cn('animate-pulse bg-surface rounded-lg border border-border p-6', className)}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <Skeleton variant="text" className="h-4 w-24 mb-2" />
          <Skeleton variant="text" className="h-8 w-20 mb-2" />
          <Skeleton variant="text" className="h-3 w-16" />
        </div>
        {showIcon && (
          <Skeleton variant="avatar" className="h-10 w-10" />
        )}
      </div>
    </div>
  );
};

export interface TableSkeletonProps {
  className?: string;
  rows?: number;
  columns?: number;
}

export const TableSkeleton: React.FC<TableSkeletonProps> = ({ 
  className, 
  rows = 5, 
  columns = 4 
}) => {
  return (
    <div className={cn('animate-pulse space-y-4', className)}>
      {/* Header */}
      <div className="border-b border-border pb-4">
        <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} variant="text" className="h-4" />
          ))}
        </div>
      </div>
      
      {/* Rows */}
      <div className="space-y-3">
        {Array.from({ length: rows }).map((_, i) => (
          <div key={i} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
            {Array.from({ length: columns }).map((_, j) => (
              <Skeleton key={j} variant="text" className="h-4" />
            ))}
          </div>
        ))}
      </div>
    </div>
  );
};

export interface ChartSkeletonProps {
  className?: string;
  height?: string;
  showLegend?: boolean;
}

export const ChartSkeleton: React.FC<ChartSkeletonProps> = ({ 
  className, 
  height = 'h-64', 
  showLegend = true 
}) => {
  return (
    <div className={cn('animate-pulse bg-surface rounded-lg border border-border p-6', className)}>
      <div className="flex items-center justify-between mb-6">
        <Skeleton variant="text" className="h-5 w-32" />
        <Skeleton variant="text" className="h-4 w-20" />
      </div>
      <Skeleton variant="card" className={cn(height, 'mb-4')} />
      {showLegend && (
        <div className="flex justify-center space-x-4">
          <Skeleton variant="text" className="h-3 w-16" />
          <Skeleton variant="text" className="h-3 w-16" />
          <Skeleton variant="text" className="h-3 w-16" />
        </div>
      )}
    </div>
  );
};

export interface ListSkeletonProps {
  className?: string;
  items?: number;
  showAvatar?: boolean;
  showActions?: boolean;
}

export const ListSkeleton: React.FC<ListSkeletonProps> = ({ 
  className, 
  items = 3, 
  showAvatar = true,
  showActions = true 
}) => {
  return (
    <div className={cn('animate-pulse space-y-4', className)}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-4 border border-border rounded-lg">
          {showAvatar && <Skeleton variant="avatar" className="h-10 w-10" />}
          <div className="flex-1 space-y-2">
            <Skeleton variant="text" className="h-4 w-3/4" />
            <Skeleton variant="text" className="h-3 w-1/2" />
          </div>
          {showActions && (
            <Skeleton variant="button" className="h-6 w-16" />
          )}
        </div>
      ))}
    </div>
  );
};

// Legacy exports for backward compatibility
export const LoadingOverlay: React.FC<{ message?: string }> = ({ message }) => (
  <Loading variant="overlay" message={message} />
);

export const InlineLoading: React.FC<{ message?: string; className?: string }> = ({ 
  message, 
  className 
}) => (
  <Loading variant="inline" message={message} className={className} />
);

export const LoadingButton: React.FC<{
  loading?: boolean;
  children: React.ReactNode;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
}> = ({ 
  loading = false, 
  children, 
  disabled = false, 
  className = '', 
  onClick,
  variant = 'default'
}) => {
  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={cn(
        'relative inline-flex items-center justify-center transition-all duration-200',
        (disabled || loading) && 'opacity-50 cursor-not-allowed',
        className
      )}
    >
      {loading && (
        <Spinner 
          variant={variant} 
          size="sm" 
          className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2" 
        />
      )}
      <span className={cn('transition-opacity duration-200', loading && 'opacity-0')}>
        {children}
      </span>
    </button>
  );
};

// ML Full Wizard specific loading components
interface ProductCardSkeletonProps {
  count?: number;
}

const ProductCardSkeleton: React.FC<ProductCardSkeletonProps> = ({ count = 6 }) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {Array.from({ length: count }).map((_, index) => (
        <Card key={index} className="h-full">
          <CardContent className="p-4 h-full flex flex-col">
            {/* Header skeleton */}
            <div className="flex items-start justify-between gap-3 mb-3">
              <div className="w-4 h-4 bg-gray-200 rounded animate-pulse" />
              <div className="w-16 h-16 bg-gray-200 rounded animate-pulse" />
            </div>

            {/* Title skeleton */}
            <div className="mb-3 flex-grow">
              <div className="h-4 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-3 bg-gray-200 rounded animate-pulse w-3/4 mb-1" />
              <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
            </div>

            {/* Metrics skeleton */}
            <div className="mb-3">
              <div className="flex justify-between mb-2">
                <div className="h-3 bg-gray-200 rounded animate-pulse w-1/3" />
                <div className="h-3 bg-gray-200 rounded animate-pulse w-1/4" />
              </div>
            </div>

            {/* Price skeleton */}
            <div className="mt-auto pt-3 border-t">
              <div className="text-center">
                <div className="h-6 bg-gray-200 rounded animate-pulse w-1/2 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

interface WizardLoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  progress?: number;
  icon?: 'spinner' | 'package' | 'spreadsheet' | 'upload';
}

const WizardLoadingOverlay: React.FC<WizardLoadingOverlayProps> = ({
  isVisible,
  message = 'Carregando...',
  progress,
  icon = 'spinner'
}) => {
  if (!isVisible) return null;

  const iconComponents = {
    spinner: <Spinner size="lg" className="text-blue-600" />,
    package: <Package className="h-8 w-8 text-blue-600 animate-pulse" />,
    spreadsheet: <FileSpreadsheet className="h-8 w-8 text-green-600 animate-pulse" />,
    upload: <Upload className="h-8 w-8 text-purple-600 animate-pulse" />
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4 text-center">
        <div className="mb-4 flex justify-center">
          {iconComponents[icon]}
        </div>
        <p className="text-gray-700 mb-4">{message}</p>
        {progress !== undefined && (
          <div className="w-full">
            <div className="flex justify-between items-center mb-2">
              <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export {
  Spinner,
  spinnerVariants,
  loadingVariants,
  skeletonVariants,
  ProductCardSkeleton,
  WizardLoadingOverlay,
};
