import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import toast from 'react-hot-toast';
import type { StockAlert } from '../types/api';

// MOCK DATA FOR DEVELOPMENT - Aligned with api.ts types
interface Notification {
  id: string;
  type: 'stock_alert' | 'new_sale' | 'price_change' | 'system' | 'error';
  title: string;
  message: string;
  severity: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  productId?: string;
  autoClose?: boolean;
  duration?: number;
  isRead: boolean;
}

interface NotificationThrottleData {
  productId: string;
  type: string;
  lastNotificationTime: string;
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  lastNotificationByProduct: Record<string, string>; // key: productId-type, value: timestamp
  isConnected: boolean;
  loading: boolean;
  error: string | null;
  pollingInterval: number;
  enableToasts: boolean;
  maxNotifications: number;
}

interface NotificationActions {
  // Core notification management
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'isRead'>) => string | null;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  removeNotification: (id: string) => void;
  clearAll: () => void;
  
  // Throttling system
  checkThrottling: (productId: string, type: string) => boolean;
  updateThrottleData: (productId: string, type: string) => void;
  
  // Persistence
  loadFromStorage: () => void;
  saveToStorage: () => void;
  
  // Configuration
  setPollingInterval: (interval: number) => void;
  setEnableToasts: (enabled: boolean) => void;
  setMaxNotifications: (max: number) => void;
  
  // Integration with stock alerts
  processStockAlerts: (alerts: StockAlert[]) => void;
  
  // Connection status
  setConnectionStatus: (connected: boolean) => void;
  setError: (error: string | null) => void;
}

type NotificationStore = NotificationState & NotificationActions;

// Constants
const THROTTLE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const STORAGE_KEY = 'magnow_notifications';
const THROTTLE_STORAGE_KEY = 'magnow_notification_throttle';
const DEFAULT_POLLING_INTERVAL = 60000; // 1 minute (reduced from 30s)

// Helper functions
const generateNotificationId = (): string => {
  return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

const getThrottleKey = (productId: string, type: string): string => {
  return `${productId}-${type}`;
};

const showToast = (notification: Notification) => {
  const options = {
    duration: notification.duration || (notification.autoClose !== false ? 4000 : Infinity),
    position: 'top-right' as const,
  };

  switch (notification.severity) {
    case 'success':
      toast.success(`${notification.title}\n${notification.message}`, options);
      break;
    case 'warning':
      toast(`${notification.title}\n${notification.message}`, {
        ...options,
        icon: '⚠️',
      });
      break;
    case 'error':
      toast.error(`${notification.title}\n${notification.message}`, options);
      break;
    default:
      toast(`${notification.title}\n${notification.message}`, {
        ...options,
        icon: 'ℹ️',
      });
  }
};

export const useNotificationStore = create<NotificationStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      notifications: [],
      unreadCount: 0,
      lastNotificationByProduct: {},
      isConnected: true,
      loading: false,
      error: null,
      pollingInterval: DEFAULT_POLLING_INTERVAL,
      enableToasts: true,
      maxNotifications: 50,

      // Core notification management
      addNotification: (notification) => {
        const state = get();
        
        // Check throttling for stock alerts
        if (notification.type === 'stock_alert' && notification.productId) {
          if (!state.checkThrottling(notification.productId, notification.type)) {
            console.log(`Notification throttled for product ${notification.productId}`);
            return null; // Block notification
          }
        }

        const newNotification: Notification = {
          ...notification,
          id: generateNotificationId(),
          timestamp: new Date().toISOString(),
          isRead: false
        };

        // Update throttle data for stock alerts
        if (notification.type === 'stock_alert' && notification.productId) {
          state.updateThrottleData(notification.productId, notification.type);
        }

        set((state) => ({
          notifications: [newNotification, ...state.notifications].slice(0, state.maxNotifications),
          unreadCount: state.unreadCount + 1
        }));

        // Show toast if enabled
        if (state.enableToasts) {
          showToast(newNotification);
        }

        // Save to storage
        state.saveToStorage();

        return newNotification.id;
      },

      markAsRead: (id) => {
        set((state) => {
          const updatedNotifications = state.notifications.map(notif =>
            notif.id === id ? { ...notif, isRead: true } : notif
          );
          
          const unreadCount = updatedNotifications.filter(notif => !notif.isRead).length;
          
          return {
            notifications: updatedNotifications,
            unreadCount
          };
        });
        
        get().saveToStorage();
      },

      markAllAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map(notif => ({ ...notif, isRead: true })),
          unreadCount: 0
        }));
        
        get().saveToStorage();
      },

      removeNotification: (id) => {
        set((state) => {
          const updatedNotifications = state.notifications.filter(notif => notif.id !== id);
          const unreadCount = updatedNotifications.filter(notif => !notif.isRead).length;
          
          return {
            notifications: updatedNotifications,
            unreadCount
          };
        });
        
        get().saveToStorage();
      },

      clearAll: () => {
        set({
          notifications: [],
          unreadCount: 0
        });
        
        get().saveToStorage();
      },

      // Throttling system
      checkThrottling: (productId, type) => {
        const state = get();
        const key = getThrottleKey(productId, type);
        const lastNotificationTime = state.lastNotificationByProduct[key];
        
        if (!lastNotificationTime) {
          return true; // First notification for this product-type combination
        }
        
        const timeSinceLastNotification = Date.now() - new Date(lastNotificationTime).getTime();
        return timeSinceLastNotification >= THROTTLE_DURATION;
      },

      updateThrottleData: (productId, type) => {
        const key = getThrottleKey(productId, type);
        const timestamp = new Date().toISOString();
        
        set((state) => ({
          lastNotificationByProduct: {
            ...state.lastNotificationByProduct,
            [key]: timestamp
          }
        }));
        
        // Save throttle data to separate storage
        try {
          const throttleData = get().lastNotificationByProduct;
          localStorage.setItem(THROTTLE_STORAGE_KEY, JSON.stringify(throttleData));
        } catch (error) {
          console.warn('Failed to save throttle data to localStorage:', error);
        }
      },

      // Persistence
      loadFromStorage: () => {
        try {
          // Load notifications
          const stored = localStorage.getItem(STORAGE_KEY);
          if (stored) {
            const data = JSON.parse(stored);
            const unreadCount = data.notifications?.filter((notif: Notification) => !notif.isRead).length || 0;
            
            set({
              notifications: data.notifications || [],
              unreadCount
            });
          }
          
          // Load throttle data
          const throttleStored = localStorage.getItem(THROTTLE_STORAGE_KEY);
          if (throttleStored) {
            const throttleData = JSON.parse(throttleStored);
            set({
              lastNotificationByProduct: throttleData
            });
          }
        } catch (error) {
          console.warn('Failed to load notifications from localStorage:', error);
          set({ error: 'Erro ao carregar notificações salvas' });
        }
      },

      saveToStorage: () => {
        try {
          const state = get();
          const dataToSave = {
            notifications: state.notifications,
            timestamp: new Date().toISOString()
          };
          
          localStorage.setItem(STORAGE_KEY, JSON.stringify(dataToSave));
        } catch (error) {
          console.warn('Failed to save notifications to localStorage:', error);
        }
      },

      // Configuration
      setPollingInterval: (interval) => {
        set({ pollingInterval: Math.max(interval, 30000) }); // Minimum 30 seconds
      },

      setEnableToasts: (enabled) => {
        set({ enableToasts: enabled });
      },

      setMaxNotifications: (max) => {
        set({ maxNotifications: Math.max(max, 10) }); // Minimum 10 notifications
      },

      // Integration with stock alerts
      processStockAlerts: (alerts) => {
        const state = get();
        
        alerts.forEach(alert => {
          if (!alert.isRead && alert.productId) {
            // Convert StockAlert to Notification
            const notification = {
              type: 'stock_alert' as const,
              title: getAlertTitle(alert.type),
              message: alert.message,
              severity: mapAlertSeverityToNotification(alert.severity),
              productId: alert.productId,
              autoClose: alert.severity === 'critical' ? false : true,
              duration: alert.severity === 'critical' ? undefined : 6000
            };
            
            state.addNotification(notification);
          }
        });
      },

      // Connection status
      setConnectionStatus: (connected) => {
        set({ isConnected: connected });
      },

      setError: (error) => {
        set({ error });
      }
    }),
    {
      name: 'notification-store'
    }
  )
);

// Helper functions for alert processing
const getAlertTitle = (type: StockAlert['type']): string => {
  switch (type) {
    case 'gap_critical':
      return 'Gap Crítico de Estoque';
    case 'stock_low':
      return 'Estoque Baixo';
    case 'overstock':
      return 'Excesso de Estoque';
    case 'no_sales':
      return 'Sem Vendas';
    default:
      return 'Alerta de Estoque';
  }
};

const mapAlertSeverityToNotification = (severity: StockAlert['severity']): Notification['severity'] => {
  switch (severity) {
    case 'critical':
      return 'error';
    case 'high':
      return 'warning';
    case 'medium':
      return 'warning';
    case 'low':
      return 'info';
    default:
      return 'info';
  }
};

export default useNotificationStore;
