import type { User, LogEntry } from '../types/api';

// MOCK DATA FOR DEVELOPMENT - Remove for production

export interface ActivityEntry {
  id: string;
  timestamp: string;
  action: string;
  description: string;
  ipAddress?: string;
  userAgent?: string;
  severity: 'info' | 'warning' | 'error';
}

export interface SessionInfo {
  currentSession: {
    id: string;
    startedAt: string;
    ipAddress: string;
    userAgent: string;
    location?: string;
  };
  recentSessions: Array<{
    id: string;
    startedAt: string;
    endedAt?: string;
    ipAddress: string;
    location?: string;
    duration?: string;
  }>;
}

// Mock de atividades recentes do usuário
export const mockUserActivities: ActivityEntry[] = [
  {
    id: 'activity-001',
    timestamp: '2024-07-31T09:15:00Z',
    action: 'LOGIN',
    description: 'Login realizado com sucesso',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-002',
    timestamp: '2024-07-31T08:45:00Z',
    action: 'UPDATE',
    description: 'Perfil atualizado - alteração de telefone',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-003',
    timestamp: '2024-07-30T16:30:00Z',
    action: 'EXPORT',
    description: 'Planilha de estoque exportada (150 produtos)',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-004',
    timestamp: '2024-07-30T14:20:00Z',
    action: 'SYNC',
    description: 'Sincronização com Mercado Livre concluída',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-005',
    timestamp: '2024-07-30T11:15:00Z',
    action: 'CREATE',
    description: 'Novo usuário criado: Maria Santos',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-006',
    timestamp: '2024-07-29T17:45:00Z',
    action: 'CONFIG_CHANGE',
    description: 'Configurações de notificação alteradas',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-007',
    timestamp: '2024-07-29T15:30:00Z',
    action: 'PASSWORD_CHANGE',
    description: 'Senha alterada com sucesso',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-008',
    timestamp: '2024-07-29T09:00:00Z',
    action: 'LOGIN',
    description: 'Login realizado com sucesso',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-009',
    timestamp: '2024-07-28T18:20:00Z',
    action: 'LOGOUT',
    description: 'Logout realizado',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
  {
    id: 'activity-010',
    timestamp: '2024-07-28T16:45:00Z',
    action: 'UPDATE',
    description: 'Produto atualizado: Smartphone Galaxy S24',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    severity: 'info',
  },
];

// Mock de informações de sessão
export const mockSessionInfo: SessionInfo = {
  currentSession: {
    id: 'session-current-001',
    startedAt: '2024-07-31T09:15:00Z',
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    location: 'São Paulo, SP - Brasil',
  },
  recentSessions: [
    {
      id: 'session-002',
      startedAt: '2024-07-30T08:30:00Z',
      endedAt: '2024-07-30T18:45:00Z',
      ipAddress: '*************',
      location: 'São Paulo, SP - Brasil',
      duration: '10h 15m',
    },
    {
      id: 'session-003',
      startedAt: '2024-07-29T09:00:00Z',
      endedAt: '2024-07-29T17:30:00Z',
      ipAddress: '*************',
      location: 'São Paulo, SP - Brasil',
      duration: '8h 30m',
    },
    {
      id: 'session-004',
      startedAt: '2024-07-28T14:20:00Z',
      endedAt: '2024-07-28T18:20:00Z',
      ipAddress: '*************',
      location: 'São Paulo, SP - Brasil',
      duration: '4h 00m',
    },
  ],
};

// Função para simular upload de avatar
export const mockUploadAvatar = async (file: File): Promise<string> => {
  // Simular delay de upload
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Simular URL do avatar (usando uma imagem placeholder)
  const avatarUrls = [
    'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
  ];
  
  return avatarUrls[Math.floor(Math.random() * avatarUrls.length)];
};

// Função para obter atividades do usuário
export const getUserActivities = async (userId: string, limit: number = 10): Promise<ActivityEntry[]> => {
  // Simular delay da API
  await new Promise(resolve => setTimeout(resolve, 300));
  
  return mockUserActivities.slice(0, limit);
};

// Função para obter informações de sessão
export const getSessionInfo = async (userId: string): Promise<SessionInfo> => {
  // Simular delay da API
  await new Promise(resolve => setTimeout(resolve, 200));
  
  return mockSessionInfo;
};

// Função para formatar data de último login
export const formatLastLogin = (lastLoginAt?: string): string => {
  if (!lastLoginAt) return 'Nunca';
  
  const now = new Date();
  const loginDate = new Date(lastLoginAt);
  const diffInMinutes = Math.floor((now.getTime() - loginDate.getTime()) / (1000 * 60));
  
  if (diffInMinutes < 1) return 'Agora mesmo';
  if (diffInMinutes < 60) return `${diffInMinutes} min atrás`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h atrás`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) return `${diffInDays} dias atrás`;
  
  return loginDate.toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

// Função para obter iniciais do nome
export const getInitials = (name: string): string => {
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

// Função para obter cor do avatar baseada no nome
export const getAvatarColor = (name: string): string => {
  const colors = [
    'bg-blue-500',
    'bg-green-500',
    'bg-purple-500',
    'bg-pink-500',
    'bg-indigo-500',
    'bg-yellow-500',
    'bg-red-500',
    'bg-teal-500',
  ];
  
  const index = name.charCodeAt(0) % colors.length;
  return colors[index];
};
