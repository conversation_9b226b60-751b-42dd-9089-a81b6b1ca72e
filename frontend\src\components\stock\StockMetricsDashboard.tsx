import React, { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  Package,
  Clock,
  Target,
  BarChart3,
  DollarSign
} from 'lucide-react';
import { useStockStore } from '../../store/stockStore';
import type { StockCalculation } from '../../types/api';

interface StockMetricsDashboardProps {
  className?: string;
}

interface MetricsSummary {
  totalProducts: number;
  totalCurrentStock: number;
  totalIdealStock: number;
  totalGap: number;
  averageCoverage: number;
  criticalProducts: number;
  highPriorityProducts: number;
  productsWithGaps: number;
  productsWithExcess: number;
  averageSales: number;
  totalSafetyStock: number;
  unitsInTransit: number;
}

export const StockMetricsDashboard: React.FC<StockMetricsDashboardProps> = ({ className }) => {
  const { stockCalculations, productsWithStock, calculationsLoading } = useStockStore();

  const metrics = useMemo((): MetricsSummary => {
    const calculations = Object.values(stockCalculations);
    
    if (calculations.length === 0) {
      return {
        totalProducts: 0,
        totalCurrentStock: 0,
        totalIdealStock: 0,
        totalGap: 0,
        averageCoverage: 0,
        criticalProducts: 0,
        highPriorityProducts: 0,
        productsWithGaps: 0,
        productsWithExcess: 0,
        averageSales: 0,
        totalSafetyStock: 0,
        unitsInTransit: 0,
      };
    }

    const totalProducts = calculations.length;
    const totalCurrentStock = calculations.reduce((sum, calc) => sum + calc.currentStock, 0);
    const totalIdealStock = calculations.reduce((sum, calc) => sum + calc.idealStock, 0);
    const totalGap = calculations.reduce((sum, calc) => sum + calc.gap, 0);
    const averageCoverage = calculations.reduce((sum, calc) => sum + calc.coverageDays, 0) / totalProducts;
    const criticalProducts = calculations.filter(calc => calc.priority === 'critical').length;
    const highPriorityProducts = calculations.filter(calc => calc.priority === 'high').length;
    const productsWithGaps = calculations.filter(calc => calc.gap > 0).length;
    const productsWithExcess = calculations.filter(calc => calc.gap < 0).length;
    const averageSales = calculations.reduce((sum, calc) => sum + calc.averageSales, 0) / totalProducts;
    const totalSafetyStock = calculations.reduce((sum, calc) => sum + calc.safetyStock, 0);
    const unitsInTransit = calculations.reduce((sum, calc) => sum + calc.unitsInTransit, 0);

    return {
      totalProducts,
      totalCurrentStock,
      totalIdealStock,
      totalGap,
      averageCoverage,
      criticalProducts,
      highPriorityProducts,
      productsWithGaps,
      productsWithExcess,
      averageSales,
      totalSafetyStock,
      unitsInTransit,
    };
  }, [stockCalculations]);

  const getGapStatusColor = (gap: number) => {
    if (gap > 0) return 'text-red-600';
    if (gap < 0) return 'text-blue-600';
    return 'text-green-600';
  };

  const getCoverageColor = (coverage: number) => {
    if (coverage < 7) return 'text-red-600';
    if (coverage < 15) return 'text-yellow-600';
    if (coverage < 30) return 'text-green-600';
    return 'text-blue-600';
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('pt-BR').format(Math.round(num));
  };

  if (calculationsLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50 animate-pulse" />
            <p>Carregando métricas...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (metrics.totalProducts === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Nenhum cálculo de estoque disponível</p>
            <p className="text-sm">Execute cálculos para ver as métricas</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total de Produtos</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              Com cálculos de estoque
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Gap Total</CardTitle>
            {metrics.totalGap > 0 ? (
              <TrendingDown className="h-4 w-4 text-red-500" />
            ) : metrics.totalGap < 0 ? (
              <TrendingUp className="h-4 w-4 text-blue-500" />
            ) : (
              <Target className="h-4 w-4 text-green-500" />
            )}
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getGapStatusColor(metrics.totalGap)}`}>
              {metrics.totalGap > 0 ? '+' : ''}{formatNumber(metrics.totalGap)}
            </div>
            <p className="text-xs text-muted-foreground">
              {metrics.totalGap > 0 ? 'Déficit' : metrics.totalGap < 0 ? 'Excesso' : 'Equilibrado'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cobertura Média</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getCoverageColor(metrics.averageCoverage)}`}>
              {formatNumber(metrics.averageCoverage)}
            </div>
            <p className="text-xs text-muted-foreground">
              dias de estoque
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Produtos Críticos</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{metrics.criticalProducts}</div>
            <p className="text-xs text-muted-foreground">
              requerem atenção imediata
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Stock Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Análise de Estoque
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-muted-foreground">Estoque Atual</div>
                <div className="text-xl font-bold">{formatNumber(metrics.totalCurrentStock)}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Estoque Ideal</div>
                <div className="text-xl font-bold">{formatNumber(metrics.totalIdealStock)}</div>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Eficiência do Estoque</span>
                <span>
                  {metrics.totalIdealStock > 0 
                    ? Math.round((metrics.totalCurrentStock / metrics.totalIdealStock) * 100)
                    : 0}%
                </span>
              </div>
              <Progress 
                value={metrics.totalIdealStock > 0 
                  ? Math.min((metrics.totalCurrentStock / metrics.totalIdealStock) * 100, 100)
                  : 0
                } 
                className="h-2" 
              />
            </div>

            <div className="grid grid-cols-2 gap-4 pt-2 border-t">
              <div>
                <div className="text-sm text-muted-foreground">Com Déficit</div>
                <div className="font-semibold text-red-600">{metrics.productsWithGaps}</div>
              </div>
              <div>
                <div className="text-sm text-muted-foreground">Com Excesso</div>
                <div className="font-semibold text-blue-600">{metrics.productsWithExcess}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Priority Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Análise de Prioridade
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="destructive" className="w-3 h-3 p-0"></Badge>
                  <span className="text-sm">Crítico</span>
                </div>
                <span className="font-bold text-red-600">{metrics.criticalProducts}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className="w-3 h-3 p-0 bg-orange-500"></Badge>
                  <span className="text-sm">Alto</span>
                </div>
                <span className="font-bold text-orange-600">{metrics.highPriorityProducts}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge className="w-3 h-3 p-0 bg-yellow-500"></Badge>
                  <span className="text-sm">Médio</span>
                </div>
                <span className="font-bold text-yellow-600">
                  {metrics.totalProducts - metrics.criticalProducts - metrics.highPriorityProducts}
                </span>
              </div>
            </div>

            <div className="pt-4 border-t space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Vendas Médias</span>
                <span className="font-semibold">{formatNumber(metrics.averageSales)}/dia</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Estoque Segurança</span>
                <span className="font-semibold">{formatNumber(metrics.totalSafetyStock)}</span>
              </div>
              
              {metrics.unitsInTransit > 0 && (
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Em Trânsito</span>
                  <span className="font-semibold text-blue-600">{formatNumber(metrics.unitsInTransit)}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StockMetricsDashboard;
