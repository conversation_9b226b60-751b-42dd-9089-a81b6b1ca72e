/**
 * Serviço Aprimorado de Geração de Planilhas
 * Sistema Magnow - Fase 1 de Melhorias
 */

import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { createObjectCsvWriter } from 'csv-writer';
import {
  ShippingProduct,
  SpreadsheetGenerationResult,
  SpreadsheetConfig,
  ShippingSpreadsheetRow,
  SpreadsheetError,
  SpreadsheetWarning,
  GenerateSpreadsheetParams,
  SpreadsheetTemplate,
  TemplateColumn
} from '../types/stock';
import { templateService } from './templateService';
import CacheService, { CacheNamespaces, CacheConfigs } from './cacheService';
import { logger } from '../utils/logger';

interface EnhancedGenerationOptions {
  useCache?: boolean;
  validateData?: boolean;
  applyFormatting?: boolean;
  generatePreview?: boolean;
  tenantId: string;
}

interface GenerationStats {
  totalProducts: number;
  validProducts: number;
  invalidProducts: number;
  warnings: number;
  errors: number;
  processingTime: number;
  cacheHit: boolean;
}

class EnhancedSpreadsheetService {
  private readonly outputDir: string;
  private readonly previewDir: string;
  private cache: CacheService;

  constructor() {
    this.outputDir = path.join(process.cwd(), 'uploads', 'spreadsheets');
    this.previewDir = path.join(process.cwd(), 'uploads', 'previews');
    this.cache = new CacheService();
    this.ensureDirectories();
  }

  /**
   * Gera planilha com sistema aprimorado
   */
  async generateSpreadsheet(
    products: ShippingProduct[],
    params: GenerateSpreadsheetParams,
    config: SpreadsheetConfig,
    options: EnhancedGenerationOptions
  ): Promise<SpreadsheetGenerationResult> {
    const startTime = Date.now();
    
    try {
      logger.info('Iniciando geração de planilha aprimorada', {
        productCount: products.length,
        format: params.format,
        templateId: config.templateId,
        tenantId: options.tenantId
      });

      // Verificar cache se habilitado
      if (options.useCache) {
        const cachedResult = await this.getCachedResult(config, options.tenantId);
        if (cachedResult) {
          logger.info('Resultado obtido do cache');
          return {
            ...cachedResult,
            stats: {
              ...cachedResult.stats,
              cacheHit: true,
              processingTime: Date.now() - startTime
            }
          };
        }
      }

      // Obter template
      const template = await this.getTemplate(config, options.tenantId);
      
      // Validar dados se habilitado
      let validationResult = { isValid: true, errors: [] };
      if (options.validateData && template) {
        validationResult = await templateService.validateData(products, template);
      }

      // Processar produtos
      const { validProducts, errors, warnings } = this.processProducts(
        products,
        config,
        template,
        validationResult
      );

      // Converter para formato de planilha
      const rows = this.convertProductsToRows(validProducts, config, template);

      // Gerar arquivo
      const fileResult = await this.generateFile(
        rows,
        params,
        config,
        template,
        options
      );

      // Gerar preview se solicitado
      let previewPath: string | undefined;
      if (options.generatePreview) {
        previewPath = await this.generatePreview(rows, config, template);
      }

      // Calcular estatísticas
      const stats: GenerationStats = {
        totalProducts: products.length,
        validProducts: validProducts.length,
        invalidProducts: products.length - validProducts.length,
        warnings: warnings.length,
        errors: errors.length,
        processingTime: Date.now() - startTime,
        cacheHit: false
      };

      const result: SpreadsheetGenerationResult = {
        id: `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        tenantId: options.tenantId,
        generatedAt: new Date(),
        generatedBy: params.generatedBy,
        fileName: fileResult.fileName,
        filePath: fileResult.filePath,
        fileSize: 0, // Será calculado após a geração
        format: params.format,
        totalProducts: products.length,
        totalQuantity: rows.reduce((sum, row) => sum + row.quantity, 0),
        totalValue: rows.reduce((sum, row) => sum + (row.quantity * row.unitPrice), 0),
        warehousesCount: new Set(rows.map(row => row.warehouseCode)).size,
        products: validProducts,
        hasErrors: errors.length > 0,
        errors,
        warnings,
        status: 'generated',
        config
      };

      // Armazenar no cache se habilitado
      if (options.useCache) {
        await this.cacheResult(config, options.tenantId, result);
      }

      logger.info('Planilha gerada com sucesso', {
        fileName: fileResult.fileName,
        totalRows: rows.length,
        processingTime: stats.processingTime
      });

      return result;

    } catch (error) {
      logger.error('Erro na geração de planilha:', error);
      
      return {
        id: `gen_error_${Date.now()}`,
        tenantId: options.tenantId,
        generatedAt: new Date(),
        generatedBy: params.generatedBy,
        fileName: '',
        filePath: '',
        fileSize: 0,
        format: params.format,
        totalProducts: products.length,
        totalQuantity: 0,
        totalValue: 0,
        warehousesCount: 0,
        products: [],
        hasErrors: true,
        errors: [{
          type: 'system',
          severity: 'error',
          code: 'GENERATION_ERROR',
          message: 'Erro interno na geração da planilha',
          details: error instanceof Error ? error.message : 'Erro desconhecido'
        }],
        warnings: [],
        status: 'generated',
        config
      };
    }
  }

  /**
   * Obtém template para a configuração
   */
  private async getTemplate(
    config: SpreadsheetConfig,
    tenantId: string
  ): Promise<SpreadsheetTemplate | null> {
    try {
      // Usar template da configuração se disponível
      if (config.template) {
        return config.template;
      }
      
      // Usar template padrão
      const templates = await templateService.getTemplatesByTenant(tenantId);
      return templates.find(t => t.isDefault) || null;
    } catch (error) {
      logger.error('Erro ao obter template:', error);
      return null;
    }
  }

  /**
   * Processa produtos aplicando validações e filtros
   */
  private processProducts(
    products: ShippingProduct[],
    config: SpreadsheetConfig,
    template: SpreadsheetTemplate | null,
    validationResult: { isValid: boolean; errors: any[] }
  ): {
    validProducts: ShippingProduct[];
    errors: SpreadsheetError[];
    warnings: SpreadsheetWarning[];
  } {
    const validProducts: ShippingProduct[] = [];
    const errors: SpreadsheetError[] = [];
    const warnings: SpreadsheetWarning[] = [];

    // Converter erros de validação
    validationResult.errors.forEach(error => {
      if (error.severity === 'error') {
        errors.push({
          type: 'validation',
          severity: 'error',
          code: 'VALIDATION_ERROR',
          message: error.message,
          details: `Linha ${error.row}, Campo: ${error.field}`
        });
      } else {
        warnings.push({
          type: 'missing_data',
          message: error.message,
          affectedSku: error.sku || '',
          affectedField: error.field || ''
        });
      }
    });

    // Processar cada produto
    products.forEach((product, index) => {
      try {
        // Validações básicas
        if (!product.sku || product.sku.trim() === '') {
          errors.push({
            type: 'validation',
            severity: 'error',
            code: 'MISSING_SKU',
            message: `Produto na linha ${index + 1} não possui SKU`,
            details: `Produto: ${product.title || 'Sem título'}`
          });
          return;
        }

        if (!product.quantityToSend || product.quantityToSend <= 0) {
          warnings.push({
            type: 'missing_data',
            message: `Produto ${product.sku} possui quantidade inválida`,
            affectedSku: product.sku,
            affectedField: 'quantityToSend',
            assumedValue: 0
          });
        }

        // Aplicar filtros básicos (removido config.filters que não existe)
        // Filtros podem ser implementados através de automationRules se necessário

        validProducts.push(product);
      } catch (error) {
        errors.push({
          type: 'system',
          severity: 'error',
          code: 'PROCESSING_ERROR',
          message: `Erro ao processar produto na linha ${index + 1}`,
          details: error instanceof Error ? error.message : 'Erro desconhecido'
        });
      }
    });

    return { validProducts, errors, warnings };
  }

  /**
   * Converte produtos para linhas de planilha usando template
   */
  private convertProductsToRows(
    products: ShippingProduct[],
    config: SpreadsheetConfig,
    template: SpreadsheetTemplate | null
  ): ShippingSpreadsheetRow[] {
    const rows: ShippingSpreadsheetRow[] = [];

    products.forEach(product => {
      const row: ShippingSpreadsheetRow = {
        sku: product.sku,
        title: product.title,
        quantity: product.quantityToSend,
        barcode: product.barcode || '',
        height: product.height || 0,
        width: product.width || 0,
        depth: product.depth || 0,
        weight: product.weight,
        unitPrice: product.unitPrice,
        category: product.category || '',
        brand: product.brand || '',
        warehouseCode: product.warehouseId
      };

      // Aplicar cálculos condicionais do template
      if (template?.conditionalLogic) {
        template.conditionalLogic.forEach(rule => {
          if (rule.action === 'calculate') {
            try {
              // Implementação simplificada de cálculos
              if (rule.target === 'volume') {
                (row as any).volume = (product.height || 0) * (product.width || 0) * (product.depth || 0);
              } else if (rule.target === 'totalValue') {
                (row as any).totalValue = product.quantityToSend * product.unitPrice;
              }
            } catch (error) {
              logger.warn('Erro ao aplicar regra condicional:', error);
            }
          }
        });
      }

      rows.push(row);
    });

    // Aplicar ordenação padrão por SKU
    rows.sort((a, b) => a.sku.localeCompare(b.sku));

    return rows;
  }

  /**
   * Gera arquivo baseado no formato
   */
  private async generateFile(
    rows: ShippingSpreadsheetRow[],
    params: GenerateSpreadsheetParams,
    config: SpreadsheetConfig,
    template: SpreadsheetTemplate | null,
    options: EnhancedGenerationOptions
  ): Promise<{ filePath: string; fileName: string }> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `planilha_${timestamp}.${params.format}`;
    const filePath = path.join(this.outputDir, fileName);

    if (params.format === 'xlsx') {
      await this.generateEnhancedExcel(rows, filePath, config, template, options, params.splitByWarehouse);
    } else {
      await this.generateEnhancedCSV(rows, filePath, config, template);
    }

    return { filePath, fileName };
  }

  /**
   * Gera arquivo Excel aprimorado com formatação
   */
  private async generateEnhancedExcel(
    rows: ShippingSpreadsheetRow[],
    filePath: string,
    config: SpreadsheetConfig,
    template: SpreadsheetTemplate | null,
    options: EnhancedGenerationOptions,
    splitByWarehouse: boolean = false
  ): Promise<void> {
    const workbook = XLSX.utils.book_new();

    // Verificar se deve dividir por armazém através dos parâmetros
    const shouldSplitByWarehouse = splitByWarehouse;
    
    if (shouldSplitByWarehouse) {
      // Agrupar por armazém
      const groupedRows = this.groupRowsByWarehouse(rows);
      
      Object.entries(groupedRows).forEach(([warehouseCode, warehouseRows]) => {
        const worksheet = this.createEnhancedWorksheet(warehouseRows, template, options);
        XLSX.utils.book_append_sheet(workbook, worksheet, warehouseCode);
      });
    } else {
      const worksheet = this.createEnhancedWorksheet(rows, template, options);
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Produtos');
    }

    XLSX.writeFile(workbook, filePath);
  }

  /**
   * Cria worksheet aprimorada com formatação
   */
  private createEnhancedWorksheet(
    rows: ShippingSpreadsheetRow[],
    template: SpreadsheetTemplate | null,
    options: EnhancedGenerationOptions
  ): XLSX.WorkSheet {
    // Determinar colunas baseadas no template
    const columns = template?.columns.sort((a, b) => a.order - b.order) || this.getDefaultColumns();
    
    // Criar cabeçalhos
    const headers = columns.map(col => col.displayName);
    
    // Criar dados
    const data = rows.map(row => {
      return columns.map(col => {
        const value = (row as any)[col.name];
        
        // Aplicar formatação baseada no tipo
        if (col.dataType === 'number' && typeof value === 'number') {
          return col.formatting?.numberFormat ? value : value;
        }
        
        return value || '';
      });
    });

    // Criar worksheet
    const worksheet = XLSX.utils.aoa_to_sheet([headers, ...data]);

    // Aplicar formatação se habilitada
    if (options.applyFormatting && template) {
      this.applyExcelFormatting(worksheet, template, rows.length + 1);
    }

    return worksheet;
  }

  /**
   * Aplica formatação Excel baseada no template
   */
  private applyExcelFormatting(
    worksheet: XLSX.WorkSheet,
    template: SpreadsheetTemplate,
    totalRows: number
  ): void {
    // Implementação básica de formatação
    // Em uma implementação completa, usaria bibliotecas como exceljs
    
    const range = XLSX.utils.decode_range(worksheet['!ref'] || 'A1');
    
    // Aplicar larguras de coluna
    const colWidths: XLSX.ColInfo[] = [];
    template.columns.forEach((col, index) => {
      colWidths[index] = { width: col.name.length + 5 };
    });
    worksheet['!cols'] = colWidths;

    // Aplicar formatação condicional (simplificada)
    template.formatting?.forEach(rule => {
      // Implementação seria mais complexa em produção
      logger.debug('Aplicando regra de formatação:', rule.name);
    });
  }

  /**
   * Gera arquivo CSV aprimorado
   */
  private async generateEnhancedCSV(
    rows: ShippingSpreadsheetRow[],
    filePath: string,
    config: SpreadsheetConfig,
    template: SpreadsheetTemplate | null
  ): Promise<void> {
    const columns = template?.columns.sort((a, b) => a.order - b.order) || this.getDefaultColumns();
    
    const csvWriter = createObjectCsvWriter({
      path: filePath,
      header: columns.map(col => ({
        id: col.name,
        title: col.displayName
      })),
      encoding: 'utf8'
    });

    await csvWriter.writeRecords(rows);
  }

  /**
   * Gera preview da planilha
   */
  private async generatePreview(
    rows: ShippingSpreadsheetRow[],
    config: SpreadsheetConfig,
    template: SpreadsheetTemplate | null
  ): Promise<string> {
    const previewRows = rows.slice(0, 10); // Primeiras 10 linhas
    const timestamp = Date.now();
    const previewPath = path.join(this.previewDir, `preview_${timestamp}.json`);
    
    const previewData = {
      columns: template?.columns || this.getDefaultColumns(),
      rows: previewRows,
      totalRows: rows.length,
      config,
      generatedAt: new Date().toISOString()
    };

    await fs.promises.writeFile(previewPath, JSON.stringify(previewData, null, 2));
    return previewPath;
  }

  /**
   * Agrupa linhas por armazém
   */
  private groupRowsByWarehouse(rows: ShippingSpreadsheetRow[]): Record<string, ShippingSpreadsheetRow[]> {
    return rows.reduce((groups, row) => {
      const warehouse = row.warehouseCode || 'SEM_ARMAZEM';
      if (!groups[warehouse]) {
        groups[warehouse] = [];
      }
      groups[warehouse].push(row);
      return groups;
    }, {} as Record<string, ShippingSpreadsheetRow[]>);
  }

  /**
   * Obtém colunas padrão
   */
  private getDefaultColumns(): TemplateColumn[] {
    return [
      { id: 'sku', name: 'sku', displayName: 'SKU', dataType: 'string', isRequired: true, order: 1 },
      { id: 'title', name: 'title', displayName: 'Título', dataType: 'string', isRequired: true, order: 2 },
      { id: 'quantity', name: 'quantity', displayName: 'Quantidade', dataType: 'number', isRequired: true, order: 3 },
      { id: 'barcode', name: 'barcode', displayName: 'Código de Barras', dataType: 'string', isRequired: false, order: 4 },
      { id: 'height', name: 'height', displayName: 'Altura', dataType: 'number', isRequired: true, order: 5 },
      { id: 'width', name: 'width', displayName: 'Largura', dataType: 'number', isRequired: true, order: 6 },
      { id: 'depth', name: 'depth', displayName: 'Profundidade', dataType: 'number', isRequired: true, order: 7 },
      { id: 'weight', name: 'weight', displayName: 'Peso', dataType: 'number', isRequired: true, order: 8 },
      { id: 'unitPrice', name: 'unitPrice', displayName: 'Preço Unitário', dataType: 'number', isRequired: true, order: 9 },
      { id: 'category', name: 'category', displayName: 'Categoria', dataType: 'string', isRequired: false, order: 10 },
      { id: 'brand', name: 'brand', displayName: 'Marca', dataType: 'string', isRequired: false, order: 11 },
      { id: 'warehouseCode', name: 'warehouseCode', displayName: 'Armazém', dataType: 'string', isRequired: true, order: 12 }
    ];
  }

  /**
   * Obtém resultado do cache
   */
  private async getCachedResult(
    config: SpreadsheetConfig,
    tenantId: string
  ): Promise<SpreadsheetGenerationResult | null> {
    try {
      const cacheKey = this.generateCacheKey(config, tenantId);
      return await this.cache.get<SpreadsheetGenerationResult>(
        CacheNamespaces.SPREADSHEETS,
        cacheKey,
        tenantId
      );
    } catch (error) {
      logger.warn('Erro ao obter resultado do cache:', error);
      return null;
    }
  }

  /**
   * Armazena resultado no cache
   */
  private async cacheResult(
    config: SpreadsheetConfig,
    tenantId: string,
    result: SpreadsheetGenerationResult
  ): Promise<void> {
    try {
      const cacheKey = this.generateCacheKey(config, tenantId);
      await this.cache.set(
        CacheNamespaces.SPREADSHEETS,
        cacheKey,
        result,
        CacheConfigs.STOCK_CALCULATIONS,
        tenantId
      );
    } catch (error) {
      logger.warn('Erro ao armazenar resultado no cache:', error);
    }
  }

  /**
   * Gera chave de cache
   */
  private generateCacheKey(config: SpreadsheetConfig, tenantId: string): string {
    const configHash = JSON.stringify({
      format: config.format,
      templateId: config.template.id,
      encoding: config.encoding,
      delimiter: config.delimiter,
      requiredColumns: config.requiredColumns
    });
    
    return `config_${Buffer.from(configHash).toString('base64').slice(0, 16)}`;
  }

  /**
   * Garante que os diretórios existem
   */
  private ensureDirectories(): void {
    [this.outputDir, this.previewDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  /**
   * Invalida cache relacionado a planilhas
   */
  async invalidateCache(tenantId: string): Promise<void> {
    try {
      await this.cache.deleteByPattern('spreadsheet*', tenantId);
      logger.info(`Cache de planilhas invalidado para tenant: ${tenantId}`);
    } catch (error) {
      logger.error('Erro ao invalidar cache:', error);
    }
  }

  /**
   * Obtém estatísticas do serviço
   */
  async getServiceStats(): Promise<{
    cacheStats: any;
    templatesCount: number;
    outputDirSize: number;
  }> {
    try {
      const cacheStats = this.cache.getMetrics();
      
      // Contar arquivos no diretório de saída
      const files = await fs.promises.readdir(this.outputDir);
      const outputDirSize = files.length;
      
      return {
        cacheStats,
        templatesCount: 0, // Seria obtido do templateService
        outputDirSize
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas:', error);
      return {
        cacheStats: {},
        templatesCount: 0,
        outputDirSize: 0
      };
    }
  }
}

export const enhancedSpreadsheetService = new EnhancedSpreadsheetService();
export default enhancedSpreadsheetService;