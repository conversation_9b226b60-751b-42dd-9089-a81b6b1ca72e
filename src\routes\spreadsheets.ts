/**
 * <PERSON><PERSON><PERSON> de Planilhas
 * Sistema Magnow - Gerador de Planilhas para Mercado Envios Full
 */

import { Router } from 'express';
import { SpreadsheetController } from '../controllers/spreadsheetController';
import { authenticate } from '../middleware/auth';
import { validateTenant } from '../middleware/validateTenant';
import { rateLimiter } from '../middleware/rateLimiter';

const router: Router = Router();
const spreadsheetController = new SpreadsheetController();

// Middleware aplicado a todas as rotas
router.use(authenticate());
router.use(validateTenant);

/**
 * Rotas de Geração de Planilhas
 */

// POST /api/spreadsheets/generate - Gera nova planilha
router.post('/generate',
  rateLimiter({ windowMs: 60 * 1000, max: 5 }), // 5 requests por minuto (operação pesada)
  spreadsheetController.generateSpreadsheet
);

// POST /api/spreadsheets/preview - Preview da planilha sem criar arquivo
router.post('/preview',
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests por minuto
  spreadsheetController.previewSpreadsheet
);

/**
 * Rotas de Histórico e Gerenciamento
 */

// GET /api/spreadsheets/history - Lista histórico de planilhas
router.get('/history',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  spreadsheetController.getSpreadsheetHistory
);

// GET /api/spreadsheets/statistics - Estatísticas do histórico
router.get('/statistics',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  spreadsheetController.getStatistics
);

// GET /api/spreadsheets/:id - Busca planilha específica
router.get('/:id',
  rateLimiter({ windowMs: 60 * 1000, max: 60 }), // 60 requests por minuto
  spreadsheetController.getSpreadsheet
);

// GET /api/spreadsheets/:id/download - Download da planilha
router.get('/:id/download',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  spreadsheetController.downloadSpreadsheet
);

// DELETE /api/spreadsheets/:id - Remove planilha do histórico
router.delete('/:id',
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests por minuto
  spreadsheetController.deleteSpreadsheet
);

/**
 * Rotas de Tags e Notas
 */

// PUT /api/spreadsheets/:id/tags - Adiciona tags
router.put('/:id/tags',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  spreadsheetController.addTags
);

// DELETE /api/spreadsheets/:id/tags - Remove tags
router.delete('/:id/tags',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  spreadsheetController.removeTags
);

// PUT /api/spreadsheets/:id/notes - Adiciona notas
router.put('/:id/notes',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  spreadsheetController.addNotes
);

/**
 * Rotas de Armazéns
 */

// GET /api/spreadsheets/warehouses - Lista armazéns
router.get('/warehouses',
  rateLimiter({ windowMs: 60 * 1000, max: 30 }), // 30 requests por minuto
  spreadsheetController.getWarehouses
);

// POST /api/spreadsheets/warehouses - Cria armazém
router.post('/warehouses',
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests por minuto
  spreadsheetController.createWarehouse
);

// PUT /api/spreadsheets/warehouses/:id - Atualiza armazém
router.put('/warehouses/:id',
  rateLimiter({ windowMs: 60 * 1000, max: 15 }), // 15 requests por minuto
  spreadsheetController.updateWarehouse
);

/**
 * Rotas de Distribuição e Ajustes
 */

// GET /api/spreadsheets/distribution/preview - Preview de distribuição
router.get('/distribution/preview',
  rateLimiter({ windowMs: 60 * 1000, max: 15 }), // 15 requests por minuto
  spreadsheetController.previewDistribution
);

// POST /api/spreadsheets/adjustments/apply - Aplica ajustes manuais
router.post('/adjustments/apply',
  rateLimiter({ windowMs: 60 * 1000, max: 10 }), // 10 requests por minuto
  spreadsheetController.applyAdjustments
);

// GET /api/spreadsheets/adjustments/suggestions - Sugestões de ajustes
router.get('/adjustments/suggestions',
  rateLimiter({ windowMs: 60 * 1000, max: 15 }), // 15 requests por minuto
  spreadsheetController.getAdjustmentSuggestions
);

/**
 * Rotas de Integração ML
 */

// PUT /api/spreadsheets/:id/ml-status - Atualiza status ML
router.put('/:id/ml-status',
  rateLimiter({ windowMs: 60 * 1000, max: 20 }), // 20 requests por minuto
  spreadsheetController.updateMLStatus
);

/**
 * Rotas de Exportação
 */

// GET /api/spreadsheets/export/history - Exporta histórico para CSV
router.get('/export/history',
  rateLimiter({ windowMs: 60 * 1000, max: 5 }), // 5 requests por minuto (operação pesada)
  spreadsheetController.exportHistory
);

export default router;
