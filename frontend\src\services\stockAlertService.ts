import api from './api';

export interface StockAlertConfiguration {
  id?: string;
  tenantId: string;
  enabled: boolean;
  checkFrequencyMinutes: number;
  lowStockThreshold: number;
  criticalStockThreshold: number;
  notificationChannels: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface StockAlertStatistics {
  totalAlerts: number;
  alertsBySeverity: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  resolvedAlerts: number;
  activeAlerts: number;
  averageResolutionTime: number;
  topProductsWithAlerts: Array<{
    productId: string;
    productName: string;
    alertCount: number;
  }>;
}

export interface AlertJobExecution {
  id: string;
  tenantId: string;
  startTime: string;
  endTime?: string;
  status: 'running' | 'completed' | 'failed';
  alertsGenerated: number;
  notificationsSent: number;
  errorMessage?: string;
  executionTimeMs?: number;
}

export interface AlertJobMetrics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  averageExecutionTime: number;
  lastExecution?: AlertJobExecution;
  nextScheduledExecution?: string;
}

export interface FrequencyPreset {
  label: string;
  value: number;
  description: string;
}

class StockAlertService {
  async getAlertConfiguration(): Promise<StockAlertConfiguration> {
    const response = await api.get('/stock-alerts/config');
    return response.data;
  }

  async updateAlertConfiguration(config: Partial<StockAlertConfiguration>): Promise<StockAlertConfiguration> {
    const response = await api.put('/stock-alerts/config', config);
    return response.data;
  }

  async executeManualCheck(): Promise<{ message: string; alertsGenerated: number }> {
    const response = await api.post('/stock-alerts/execute');
    return response.data;
  }

  async getExecutionHistory(limit?: number): Promise<AlertJobExecution[]> {
    const params = limit ? { limit } : {};
    const response = await api.get('/stock-alerts/executions', { params });
    return response.data;
  }

  async getAlertStatistics(): Promise<StockAlertStatistics> {
    const response = await api.get('/stock-alerts/statistics');
    return response.data;
  }

  async getJobMetrics(): Promise<AlertJobMetrics> {
    const response = await api.get('/stock-alerts/metrics');
    return response.data;
  }

  async toggleAlertSystem(enabled: boolean): Promise<{ message: string; enabled: boolean }> {
    const response = await api.post('/stock-alerts/toggle', { enabled });
    return response.data;
  }

  async getFrequencyPresets(): Promise<FrequencyPreset[]> {
    const response = await api.get('/stock-alerts/frequency-presets');
    return response.data;
  }
}

export default new StockAlertService();