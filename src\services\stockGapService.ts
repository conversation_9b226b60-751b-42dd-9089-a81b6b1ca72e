/**
 * Serviço de Análise de Gap de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { StockCalculationService } from './stockCalculationService';
import { 
  StockCalculationResult,
  StockStatus,
  StockPriority,
  BatchStockCalculationResult,
  StockSystemMetrics
} from '../types/stock';
import { DatabaseError } from '../middleware/errorHandler';

/**
 * Interface para relatório de gap de estoque
 */
export interface StockGapReport {
  tenantId: string;
  generatedAt: Date;
  totalProducts: number;
  
  // Resumo por status
  criticalProducts: StockGapProduct[];
  warningProducts: StockGapProduct[];
  optimalProducts: StockGapProduct[];
  excessProducts: StockGapProduct[];
  
  // Métricas agregadas
  totalGapValue: number; // Valor total em gap (positivo = falta, negativo = sobra)
  averageGap: number;
  medianGap: number;
  
  // Prioridades
  highPriorityCount: number;
  mediumPriorityCount: number;
  lowPriorityCount: number;
  
  // Recomendações
  urgentRestockNeeded: number;
  planRestockNeeded: number;
  excessReductionNeeded: number;
  
  // Valor financeiro
  potentialLostSalesValue: number;
  excessStockValue: number;
  recommendedInvestment: number;
}

/**
 * Interface para produto no relatório de gap
 */
export interface StockGapProduct {
  mlItemId: string;
  sku: string;
  title?: string;
  currentStock: number;
  idealStock: number;
  stockGap: number;
  gapValue: number; // Gap × preço médio
  status: StockStatus;
  priority: StockPriority;
  averageDailySales: number;
  coverageDaysAvailable: number;
  recommendedQuantity: number;
  lastCalculated: Date;
}

/**
 * Parâmetros para análise de gap
 */
export interface StockGapAnalysisParams {
  mlItemIds?: string[];
  status?: StockStatus[];
  priority?: StockPriority[];
  minGap?: number;
  maxGap?: number;
  includeInactive?: boolean;
  sortBy?: 'gap' | 'value' | 'priority' | 'coverage';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
}

export class StockGapService {
  private prisma: PrismaClient;
  private stockCalculationService: StockCalculationService;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    this.stockCalculationService = new StockCalculationService(this.prisma);
  }

  /**
   * Gera relatório completo de gap de estoque
   */
  public async generateGapReport(
    tenantId: string,
    params: StockGapAnalysisParams = {}
  ): Promise<StockGapReport> {
    try {
      logger.info('Gerando relatório de gap de estoque', { tenantId });

      // Busca ou calcula dados de estoque mais recentes
      const stockData = await this.getStockCalculations(tenantId, params);

      // Busca informações de produtos
      const productsInfo = await this.getProductsInfo(tenantId, stockData.map(s => s.mlItemId));

      // Combina dados e calcula métricas
      const gapProducts = await this.buildGapProducts(stockData, productsInfo);

      // Categoriza produtos por status
      const categorizedProducts = this.categorizeProductsByStatus(gapProducts);

      // Calcula métricas agregadas
      const metrics = await this.calculateAggregatedMetrics(gapProducts);

      const report: StockGapReport = {
        tenantId,
        generatedAt: new Date(),
        totalProducts: gapProducts.length,
        ...categorizedProducts,
        ...metrics
      };

      logger.info('Relatório de gap gerado', { 
        tenantId, 
        totalProducts: report.totalProducts,
        criticalCount: report.criticalProducts.length,
        totalGapValue: report.totalGapValue
      });

      return report;
    } catch (error) {
      logger.error('Erro ao gerar relatório de gap', { error, tenantId });
      throw new DatabaseError('Erro ao gerar relatório de gap de estoque', { originalError: error });
    }
  }

  /**
   * Analisa gap de produtos específicos
   */
  public async analyzeProductsGap(
    tenantId: string,
    mlItemIds: string[],
    forceRecalculation: boolean = false
  ): Promise<StockGapProduct[]> {
    try {
      logger.info('Analisando gap de produtos específicos', { tenantId, itemCount: mlItemIds.length });

      const results: StockCalculationResult[] = [];

      for (const mlItemId of mlItemIds) {
        try {
          let calculation: StockCalculationResult | null = null;

          if (!forceRecalculation) {
            calculation = await this.stockCalculationService.getLatestCalculation(tenantId, mlItemId);
          }

          if (!calculation || forceRecalculation) {
            calculation = await this.stockCalculationService.calculateIdealStock(tenantId, mlItemId);
          }

          if (calculation) {
            results.push(calculation);
          }
        } catch (error) {
          logger.warn('Erro ao analisar produto específico', { error, mlItemId });
        }
      }

      const productsInfo = await this.getProductsInfo(tenantId, mlItemIds);
      return await this.buildGapProducts(results, productsInfo);
    } catch (error) {
      logger.error('Erro ao analisar gap de produtos', { error, tenantId });
      throw new DatabaseError('Erro ao analisar gap de produtos', { originalError: error });
    }
  }

  /**
   * Identifica produtos com gap crítico
   */
  public async getCriticalGapProducts(
    tenantId: string,
    limit: number = 50
  ): Promise<StockGapProduct[]> {
    try {
      const params: StockGapAnalysisParams = {
        status: ['critical'],
        sortBy: 'gap',
        sortOrder: 'desc',
        limit
      };

      const report = await this.generateGapReport(tenantId, params);
      return report.criticalProducts;
    } catch (error) {
      logger.error('Erro ao buscar produtos com gap crítico', { error, tenantId });
      throw error;
    }
  }

  /**
   * Calcula impacto financeiro do gap
   */
  public async calculateFinancialImpact(
    tenantId: string,
    mlItemIds?: string[]
  ): Promise<{
    potentialLostSales: number;
    excessStockValue: number;
    recommendedInvestment: number;
    opportunityCost: number;
  }> {
    try {
      const params: StockGapAnalysisParams = mlItemIds ? { mlItemIds } : {};
      const report = await this.generateGapReport(tenantId, params);

      // Calcula custo de oportunidade (vendas perdidas por produtos em falta)
      let opportunityCost = 0;
      for (const product of report.criticalProducts) {
        const price = await this.getAveragePrice(product.mlItemId);
        const dailyLostRevenue = product.averageDailySales * price;
        const daysUntilRestock = Math.max(product.stockGap / Math.max(product.averageDailySales, 0.1), 1);
        opportunityCost += dailyLostRevenue * daysUntilRestock;
      }

      return {
        potentialLostSales: report.potentialLostSalesValue,
        excessStockValue: report.excessStockValue,
        recommendedInvestment: report.recommendedInvestment,
        opportunityCost
      };
    } catch (error) {
      logger.error('Erro ao calcular impacto financeiro', { error, tenantId });
      throw error;
    }
  }

  /**
   * Busca cálculos de estoque existentes
   */
  private async getStockCalculations(
    tenantId: string,
    params: StockGapAnalysisParams
  ): Promise<StockCalculationResult[]> {
    try {
      // Busca cálculos mais recentes (últimas 48h)
      const twoDaysAgo = new Date();
      twoDaysAgo.setHours(twoDaysAgo.getHours() - 48);

      const where: any = {
        tenantId,
        calculatedAt: { gte: twoDaysAgo }
      };

      if (params.mlItemIds && params.mlItemIds.length > 0) {
        where.mlItemId = { in: params.mlItemIds };
      }

      const calculations = await this.prisma.stockCalculation.findMany({
        where: {
          tenantId,
          createdAt: { gte: twoDaysAgo },
          ...(params.mlItemIds && params.mlItemIds.length > 0 && {
            product: {
              mlId: { in: params.mlItemIds }
            }
          })
        },
        include: {
          product: true
        },
        orderBy: { createdAt: 'desc' }
      });

      // Converte para StockCalculationResult
      const results: StockCalculationResult[] = [];
      const seenProducts = new Set<string>();
      
      for (const calc of calculations) {
        try {
          // Pega apenas o mais recente de cada produto
          if (seenProducts.has(calc.product.mlId)) continue;
          seenProducts.add(calc.product.mlId);

          const result: StockCalculationResult = {
            mlItemId: calc.product.mlId,
            sku: calc.product.sku || '',
            tenantId: calc.tenantId,
            currentStock: calc.currentStock,
            averageDailySales: Number(calc.averageSales),
            idealStock: calc.idealStock,
            stockGap: calc.stockGap,
            daysOfCoverage: calc.daysOfCoverage,
            safetyStock: calc.safetyStock,
            unitsInTransit: calc.unitsInTransit,
            calculatedAt: calc.createdAt,
            status: calc.stockGap < 0 ? 'critical' : calc.stockGap === 0 ? 'optimal' : 'excess',
            priority: calc.stockGap < -10 ? 'high' : calc.stockGap < 0 ? 'medium' : 'low',
            confidence: 0.8, // Valor padrão
            metadata: {
              calculationVersion: '1.0',
              dataQuality: 'good'
            }
          };
          
          // Aplica filtros
          if (params.status && !params.status.includes(result.status)) continue;
          if (params.priority && !params.priority.includes(result.priority)) continue;
          if (params.minGap !== undefined && result.stockGap < params.minGap) continue;
          if (params.maxGap !== undefined && result.stockGap > params.maxGap) continue;

          results.push(result);
        } catch (error) {
          logger.warn('Erro ao parsear resultado de cálculo', { error, calcId: calc.id });
        }
      }

      // Ordena resultados
      if (params.sortBy) {
        results.sort((a, b) => {
          let valueA: number, valueB: number;

          switch (params.sortBy) {
            case 'gap':
              valueA = Math.abs(a.stockGap);
              valueB = Math.abs(b.stockGap);
              break;
            case 'priority':
              const priorityOrder = { high: 3, medium: 2, low: 1 };
              valueA = priorityOrder[a.priority];
              valueB = priorityOrder[b.priority];
              break;
            case 'coverage':
              valueA = a.coverageDaysAvailable;
              valueB = b.coverageDaysAvailable;
              break;
            default:
              valueA = a.stockGap;
              valueB = b.stockGap;
          }

          return params.sortOrder === 'desc' ? valueB - valueA : valueA - valueB;
        });
      }

      // Aplica limite
      if (params.limit) {
        return results.slice(0, params.limit);
      }

      return results;
    } catch (error) {
      logger.error('Erro ao buscar cálculos de estoque', { error, tenantId });
      throw error;
    }
  }

  /**
   * Busca informações dos produtos
   */
  private async getProductsInfo(
    tenantId: string,
    mlItemIds: string[]
  ): Promise<Map<string, any>> {
    try {
      const products = await this.prisma.product.findMany({
        where: {
          tenantId,
          mlId: { in: mlItemIds }
        },
        select: {
          mlId: true,
          title: true,
          sku: true,
          price: true,
          status: true
        }
      });

      const productMap = new Map();
      for (const product of products) {
        productMap.set(product.mlId, product);
      }

      return productMap;
    } catch (error) {
      logger.error('Erro ao buscar informações dos produtos', { error, tenantId });
      throw error;
    }
  }

  /**
   * Constrói lista de produtos com gap
   */
  private async buildGapProducts(
    stockData: StockCalculationResult[],
    productsInfo: Map<string, any>
  ): Promise<StockGapProduct[]> {
    const gapProducts: StockGapProduct[] = [];

    for (const stock of stockData) {
      const productInfo = productsInfo.get(stock.mlItemId);
      const averagePrice = productInfo?.price ? parseFloat(productInfo.price.toString()) : 0;

      gapProducts.push({
        mlItemId: stock.mlItemId,
        sku: stock.sku,
        title: productInfo?.title || '',
        currentStock: stock.currentStock,
        idealStock: stock.idealStock,
        stockGap: stock.stockGap,
        gapValue: stock.stockGap * averagePrice,
        status: stock.status,
        priority: stock.priority,
        averageDailySales: stock.averageDailySales,
        coverageDaysAvailable: stock.coverageDaysAvailable,
        recommendedQuantity: stock.recommendedQuantity,
        lastCalculated: stock.calculatedAt
      });
    }

    return gapProducts;
  }

  /**
   * Categoriza produtos por status
   */
  private categorizeProductsByStatus(gapProducts: StockGapProduct[]) {
    return {
      criticalProducts: gapProducts.filter(p => p.status === 'critical'),
      warningProducts: gapProducts.filter(p => p.status === 'warning'),
      optimalProducts: gapProducts.filter(p => p.status === 'optimal'),
      excessProducts: gapProducts.filter(p => p.status === 'excess')
    };
  }

  /**
   * Calcula métricas agregadas
   */
  private async calculateAggregatedMetrics(gapProducts: StockGapProduct[]) {
    if (gapProducts.length === 0) {
      return {
        totalGapValue: 0,
        averageGap: 0,
        medianGap: 0,
        highPriorityCount: 0,
        mediumPriorityCount: 0,
        lowPriorityCount: 0,
        urgentRestockNeeded: 0,
        planRestockNeeded: 0,
        excessReductionNeeded: 0,
        potentialLostSalesValue: 0,
        excessStockValue: 0,
        recommendedInvestment: 0
      };
    }

    const gaps = gapProducts.map(p => p.stockGap);
    const gapValues = gapProducts.map(p => p.gapValue);

    // Métricas de gap
    const totalGapValue = gapValues.reduce((sum, val) => sum + val, 0);
    const averageGap = gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length;
    const sortedGaps = [...gaps].sort((a, b) => a - b);
    const medianGap = sortedGaps[Math.floor(sortedGaps.length / 2)];

    // Contadores por prioridade
    const highPriorityCount = gapProducts.filter(p => p.priority === 'high').length;
    const mediumPriorityCount = gapProducts.filter(p => p.priority === 'medium').length;
    const lowPriorityCount = gapProducts.filter(p => p.priority === 'low').length;

    // Recomendações
    const urgentRestockNeeded = gapProducts.filter(p => p.status === 'critical').length;
    const planRestockNeeded = gapProducts.filter(p => p.status === 'warning').length;
    const excessReductionNeeded = gapProducts.filter(p => p.status === 'excess').length;

    // Valores financeiros
    const potentialLostSalesValue = gapProducts
      .filter(p => p.stockGap > 0)
      .reduce((sum, p) => sum + Math.abs(p.gapValue), 0);

    const excessStockValue = gapProducts
      .filter(p => p.stockGap < 0)
      .reduce((sum, p) => sum + Math.abs(p.gapValue), 0);

    // Calcula investimento recomendado com preços reais
    const productsWithRecommendation = gapProducts.filter(p => p.recommendedQuantity > 0);
    let recommendedInvestment = 0;
    
    for (const product of productsWithRecommendation) {
      const price = await this.getAveragePrice(product.mlItemId);
      recommendedInvestment += product.recommendedQuantity * price;
    }

    return {
      totalGapValue,
      averageGap,
      medianGap,
      highPriorityCount,
      mediumPriorityCount,
      lowPriorityCount,
      urgentRestockNeeded,
      planRestockNeeded,
      excessReductionNeeded,
      potentialLostSalesValue,
      excessStockValue,
      recommendedInvestment
    };
  }

  /**
   * Obtém preço médio de um produto
   */
  private async getAveragePrice(mlItemId: string): Promise<number> {
    try {
      const product = await this.prisma.product.findFirst({
        where: { mlId: mlItemId },
        select: { price: true }
      });

      if (product && product.price) {
        return parseFloat(product.price.toString());
      }

      // Valor padrão se não encontrar o produto
      return 100;
    } catch (error) {
      logger.warn('Erro ao buscar preço do produto', { error, mlItemId });
      return 100; // Valor padrão em caso de erro
    }
  }

  /**
   * Exporta relatório de gap para diferentes formatos
   */
  public async exportGapReport(
    tenantId: string,
    format: 'json' | 'csv' | 'xlsx' = 'json',
    params: StockGapAnalysisParams = {}
  ): Promise<string | Buffer> {
    try {
      const report = await this.generateGapReport(tenantId, params);

      switch (format) {
        case 'json':
          return JSON.stringify(report, null, 2);
        
        case 'csv':
          return this.generateCSVReport(report);
        
        case 'xlsx':
          // TODO: Implementar exportação Excel
          throw new Error('Exportação XLSX não implementada ainda');
        
        default:
          throw new Error(`Formato não suportado: ${format}`);
      }
    } catch (error) {
      logger.error('Erro ao exportar relatório', { error, tenantId, format });
      throw error;
    }
  }

  /**
   * Gera relatório em formato CSV
   */
  private generateCSVReport(report: StockGapReport): string {
    const allProducts = [
      ...report.criticalProducts,
      ...report.warningProducts,
      ...report.optimalProducts,
      ...report.excessProducts
    ];

    const headers = [
      'SKU',
      'Título',
      'Estoque Atual',
      'Estoque Ideal',
      'Gap',
      'Status',
      'Prioridade',
      'Vendas Diárias',
      'Cobertura (dias)',
      'Quantidade Recomendada'
    ];

    const rows = allProducts.map(product => [
      product.sku,
      product.title || '',
      product.currentStock,
      product.idealStock,
      product.stockGap,
      product.status,
      product.priority,
      product.averageDailySales.toFixed(2),
      product.coverageDaysAvailable.toFixed(1),
      product.recommendedQuantity
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  }
}