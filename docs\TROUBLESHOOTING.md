# 🛠 Troubleshooting e FAQ - Magnow

## Índice

1. [<PERSON><PERSON>](#problemas-comuns)
2. [Logs e Diagnóstico](#logs-e-diagnóstico)
3. [Problemas de Conexão](#problemas-de-conexão)
4. [Problemas de Autenticação](#problemas-de-autenticação)
5. [Problemas de Integração ML](#problemas-de-integração-ml)
6. [Problemas de Performance](#problemas-de-performance)
7. [FAQ](#faq)
8. [Suporte Técnico](#suporte-técnico)

## 🚨 Problemas Comuns

### Erro: "Cannot connect to database"

**Sintomas:**
- Aplicação não inicia
- Erro: `Error: P1001: Can't reach database server`
- Timeout nas consultas

**Soluções:**

1. **Verificar se PostgreSQL está rodando:**
   ```bash
   # Windows
   services.msc (buscar PostgreSQL)
   
   # Linux/macOS
   sudo systemctl status postgresql
   ```

2. **Verificar configurações de conexão:**
   ```bash
   # Verificar variáveis de ambiente
   echo $DATABASE_URL
   
   # Testar conexão manual
   psql $DATABASE_URL
   ```

3. **Verificar firewall e rede:**
   ```bash
   # Testar conectividade
   telnet localhost 5432
   
   # Verificar se porta está em uso
   netstat -an | grep 5432
   ```

4. **Resetar conexões:**
   ```bash
   # Reiniciar PostgreSQL
   sudo systemctl restart postgresql
   
   # Limpar pool de conexões
   npm run prisma:reset
   ```

### Erro: "Redis connection failed"

**Sintomas:**
- Cache não funciona
- Sessões não persistem
- Erro: `Error: ECONNREFUSED 127.0.0.1:6379`

**Soluções:**

1. **Verificar se Redis está rodando:**
   ```bash
   # Windows
   redis-cli ping
   
   # Linux/macOS
   sudo systemctl status redis
   ```

2. **Iniciar Redis:**
   ```bash
   # Windows
   redis-server
   
   # Linux/macOS
   sudo systemctl start redis
   ```

3. **Verificar configuração:**
   ```bash
   # Testar conexão
   redis-cli -h localhost -p 6379 ping
   ```

### Erro: "Port 3000 already in use"

**Sintomas:**
- Aplicação não inicia
- Erro: `EADDRINUSE: address already in use :::3000`

**Soluções:**

1. **Encontrar processo usando a porta:**
   ```bash
   # Windows
   netstat -ano | findstr :3000
   
   # Linux/macOS
   lsof -i :3000
   ```

2. **Matar processo:**
   ```bash
   # Windows
   taskkill /PID <PID> /F
   
   # Linux/macOS
   kill -9 <PID>
   ```

3. **Usar porta diferente:**
   ```bash
   PORT=3001 npm run dev
   ```

### Erro: "JWT token invalid"

**Sintomas:**
- Logout automático
- Erro 401 em requisições
- Token expirado

**Soluções:**

1. **Verificar configuração JWT:**
   ```bash
   # Verificar variáveis
   echo $JWT_SECRET
   echo $JWT_EXPIRES_IN
   ```

2. **Gerar novo token:**
   - Fazer logout e login novamente
   - Verificar se token está sendo enviado corretamente

3. **Verificar sincronização de tempo:**
   ```bash
   # Verificar hora do sistema
   date
   ```

## 📋 Logs e Diagnóstico

### Configurando Logs de Debug

#### 1. Habilitar logs detalhados

```bash
# Desenvolvimento
LOG_LEVEL=debug npm run dev

# Ou via environment
export LOG_LEVEL=debug
npm run dev
```

#### 2. Logs por módulo

```bash
# Logs apenas de autenticação
DEBUG=auth* npm run dev

# Logs de integração ML
DEBUG=mercadolivre* npm run dev

# Todos os logs
DEBUG=* npm run dev
```

### Localizando Arquivos de Log

```bash
# Logs da aplicação
ls -la logs/

# Estrutura típica:
logs/
├── combined.log      # Todos os logs
├── error.log        # Apenas erros
├── access.log       # Logs de acesso HTTP
└── debug.log        # Logs de debug
```

### Comandos Úteis para Logs

```bash
# Ver logs em tempo real
tail -f logs/combined.log

# Filtrar por nível
grep "ERROR" logs/combined.log

# Logs das últimas 24h
find logs/ -name "*.log" -mtime -1

# Buscar erro específico
grep -r "P1001" logs/
```

### Verificação de Sistema

#### Health Check Completo

```bash
# Executar diagnóstico
npm run health-check

# Ou via curl
curl http://localhost:3000/api/health

# Resposta esperada:
{
  "status": "healthy",
  "database": "connected",
  "redis": "connected",
  "mercadolivre": "connected",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

#### Verificação Manual dos Serviços

```bash
# Testar banco de dados
npm run prisma:studio

# Testar Redis
redis-cli ping

# Testar integração ML
curl -H "Authorization: Bearer TOKEN" \
     "https://api.mercadolibre.com/users/me"
```

## 🌐 Problemas de Conexão

### Timeout de Requisições

**Sintomas:**
- Requisições demoram muito
- Timeout errors
- Interface lenta

**Diagnóstico:**

```bash
# Verificar latência de rede
ping api.mercadolibre.com

# Testar DNS
nslookup api.mercadolibre.com

# Verificar conectividade HTTPS
curl -I https://api.mercadolibre.com
```

**Soluções:**

1. **Aumentar timeout:**
   ```typescript
   // config/axios.ts
   const axiosConfig = {
     timeout: 30000, // 30 segundos
     retries: 3
   };
   ```

2. **Verificar proxy/VPN:**
   ```bash
   # Desabilitar proxy temporariamente
   unset http_proxy https_proxy
   ```

3. **Configurar retry logic:**
   ```typescript
   // utils/httpClient.ts
   const retryConfig = {
     retries: 3,
     retryDelay: 1000,
     retryCondition: (error) => {
       return error.code === 'ECONNRESET';
     }
   };
   ```

### Problemas de CORS

**Sintomas:**
- Erro: `Access to XMLHttpRequest blocked by CORS policy`
- Frontend não consegue acessar API

**Soluções:**

1. **Verificar configuração CORS:**
   ```typescript
   // index.ts
   app.use(cors({
     origin: process.env.FRONTEND_URL || 'http://localhost:3000',
     credentials: true
   }));
   ```

2. **Configurar origins múltiplas:**
   ```typescript
   const corsOptions = {
     origin: [
       'http://localhost:3000',
       'https://magnow.com',
       'https://app.magnow.com'
     ]
   };
   ```

## 🔐 Problemas de Autenticação

### Token JWT Expirado

**Sintomas:**
- Logout automático frequente
- Erro 401 após algum tempo

**Soluções:**

1. **Implementar refresh token:**
   ```typescript
   // Verificar se refresh está configurado
   const refreshToken = localStorage.getItem('refreshToken');
   if (refreshToken) {
     // Renovar token automaticamente
   }
   ```

2. **Aumentar tempo de expiração (desenvolvimento):**
   ```bash
   JWT_EXPIRES_IN=24h
   ```

### Problemas de Permissão

**Sintomas:**
- Erro 403 Forbidden
- Usuário não consegue acessar recursos

**Diagnóstico:**

```bash
# Verificar token
jwt-decode "SEU_TOKEN_AQUI"

# Verificar roles no banco
psql -c "SELECT id, email, role FROM users WHERE email='<EMAIL>';"
```

**Soluções:**

1. **Verificar roles do usuário:**
   ```sql
   UPDATE users 
   SET role = 'admin' 
   WHERE email = '<EMAIL>';
   ```

2. **Verificar middleware de autorização:**
   ```typescript
   // middleware/auth.ts
   if (!user.role || !allowedRoles.includes(user.role)) {
     return res.status(403).json({ error: 'Insufficient permissions' });
   }
   ```

## 🛒 Problemas de Integração ML

### Token de Acesso Inválido

**Sintomas:**
- Erro: `invalid_token`
- Produtos não sincronizam
- Falha na importação

**Soluções:**

1. **Renovar token via refresh:**
   ```bash
   # Via API
   curl -X POST "https://api.mercadolibre.com/oauth/token" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "grant_type=refresh_token&client_id=CLIENT_ID&client_secret=CLIENT_SECRET&refresh_token=REFRESH_TOKEN"
   ```

2. **Reautorizar aplicação:**
   - Acessar: `https://auth.mercadolibre.com.br/authorization?response_type=code&client_id=CLIENT_ID&redirect_uri=REDIRECT_URI`
   - Reautorizar aplicação
   - Atualizar tokens

### Rate Limiting do ML

**Sintomas:**
- Erro: `rate_limit_exceeded`
- HTTP 429 Too Many Requests

**Soluções:**

1. **Implementar backoff exponencial:**
   ```typescript
   // services/mercadoLivreService.ts
   async function makeRequestWithRetry(url: string, retries = 3) {
     try {
       return await axios.get(url);
     } catch (error) {
       if (error.response?.status === 429 && retries > 0) {
         const delay = Math.pow(2, 4 - retries) * 1000; // 2s, 4s, 8s
         await new Promise(resolve => setTimeout(resolve, delay));
         return makeRequestWithRetry(url, retries - 1);
       }
       throw error;
     }
   }
   ```

2. **Configurar rate limiting interno:**
   ```typescript
   // Queue para requisições ML
   const mlQueue = new Queue('ml-requests', {
     defaultJobOptions: {
       attempts: 3,
       backoff: 'exponential'
     }
   });
   ```

### Webhook não recebido

**Sintomas:**
- Alterações no ML não refletem no sistema
- Webhooks não chegam

**Diagnóstico:**

```bash
# Verificar URL do webhook
curl "https://api.mercadolibre.com/applications/APP_ID"

# Testar endpoint local
curl -X POST "http://localhost:3000/webhook/mercadolivre" \
     -H "Content-Type: application/json" \
     -d '{"test": true}'
```

**Soluções:**

1. **Configurar túnel para desenvolvimento:**
   ```bash
   # Usar ngrok
   ngrok http 3000
   
   # Atualizar webhook URL
   curl -X PUT "https://api.mercadolibre.com/applications/APP_ID" \
        -H "Authorization: Bearer TOKEN" \
        -d '{"notification_url": "https://abc123.ngrok.io/webhook/mercadolivre"}'
   ```

2. **Verificar processamento de webhooks:**
   ```typescript
   // routes/webhook.ts
   app.post('/webhook/mercadolivre', (req, res) => {
     console.log('Webhook received:', req.body);
     // Processar webhook
     res.status(200).send('OK');
   });
   ```

## ⚡ Problemas de Performance

### Aplicação Lenta

**Sintomas:**
- Tempo de resposta alto
- Interface travando
- Consultas demoradas

**Diagnóstico:**

```bash
# Monitorar recursos
top
htop

# Verificar logs de performance
grep "duration" logs/combined.log | tail -20

# Analisar queries SQL
tail -f /var/log/postgresql/postgresql.log
```

**Soluções:**

1. **Otimizar queries:**
   ```typescript
   // Usar select específico
   const users = await prisma.user.findMany({
     select: {
       id: true,
       email: true,
       name: true
     }
   });
   
   // Usar include ao invés de queries separadas
   const userWithOrders = await prisma.user.findFirst({
     include: {
       orders: {
         take: 10,
         orderBy: { createdAt: 'desc' }
       }
     }
   });
   ```

2. **Implementar paginação:**
   ```typescript
   // Paginação cursor-based
   const products = await prisma.product.findMany({
     take: 20,
     skip: page * 20,
     orderBy: { createdAt: 'desc' }
   });
   ```

3. **Usar cache:**
   ```typescript
   // Cache de consultas frequentes
   const cacheKey = `products:${category}:${page}`;
   let products = await redis.get(cacheKey);
   
   if (!products) {
     products = await getProductsFromDB();
     await redis.setex(cacheKey, 300, JSON.stringify(products));
   }
   ```

### Alto Uso de Memória

**Sintomas:**
- Aplicação consome muita RAM
- OOM (Out of Memory) errors
- Sistema lento

**Diagnóstico:**

```bash
# Monitorar uso de memória
node --inspect index.js
# Abrir Chrome DevTools > Memory

# Usar clinic.js
npx clinic doctor -- node index.js
```

**Soluções:**

1. **Streaming de dados grandes:**
   ```typescript
   // Para arquivos grandes
   const stream = fs.createReadStream('large-file.json');
   stream.pipe(parser).pipe(processor);
   ```

2. **Cleanup de recursos:**
   ```typescript
   // Fechar conexões
   process.on('SIGTERM', async () => {
     await prisma.$disconnect();
     await redis.quit();
     server.close();
   });
   ```

## ❓ FAQ

### Perguntas Gerais

**Q: Como resetar minha senha?**

A: Use o endpoint de reset:
```bash
curl -X POST "http://localhost:3000/api/auth/forgot-password" \
     -H "Content-Type: application/json" \
     -d '{"email": "<EMAIL>"}'
```

**Q: Posso usar o sistema sem integração com Mercado Livre?**

A: Sim, a integração é opcional. Configure `ML_INTEGRATION_ENABLED=false` no .env.

**Q: Como fazer backup dos dados?**

A: 
```bash
# Backup do banco
pg_dump magnow_production > backup_$(date +%Y%m%d).sql

# Backup completo com Docker
docker exec postgres pg_dumpall -U postgres > full_backup.sql
```

**Q: Como migrar para produção?**

A: Siga o guia em `docs/INSTALLATION.md`, seção "Configuração para Produção".

### Perguntas Técnicas

**Q: Como adicionar novo campo no banco?**

A:
```bash
# 1. Editar schema.prisma
# 2. Gerar migração
npx prisma migrate dev --name add_new_field

# 3. Aplicar em produção
npx prisma migrate deploy
```

**Q: Como debugar queries SQL?**

A:
```typescript
// Habilitar log de queries
const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
});
```

**Q: Como configurar múltiplos ambientes?**

A: Crie arquivos `.env.development`, `.env.staging`, `.env.production` e use:
```bash
NODE_ENV=production npm start
```

**Q: Como monitorar performance em produção?**

A: Use ferramentas como:
- New Relic
- DataDog
- Sentry
- PM2 Monitor

### Erros Específicos

**Q: Erro "P2002: Unique constraint failed"**

A: Dados duplicados. Verifique:
```sql
SELECT email, COUNT(*) 
FROM users 
GROUP BY email 
HAVING COUNT(*) > 1;
```

**Q: Erro "ENOTFOUND" ao conectar com ML**

A: Problema de DNS/conectividade:
```bash
# Verificar DNS
nslookup api.mercadolibre.com

# Usar DNS público
echo "nameserver *******" >> /etc/resolv.conf
```

**Q: Sessão expira muito rápido**

A: Ajustar configuração:
```bash
# Aumentar tempo de sessão
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_EXPIRES_IN=7d
```

## 📞 Suporte Técnico

### Informações para Suporte

Ao reportar problemas, inclua:

1. **Versão do sistema:**
   ```bash
   npm run version
   node --version
   npm --version
   ```

2. **Logs relevantes:**
   ```bash
   # Últimos 50 erros
   tail -50 logs/error.log
   ```

3. **Configuração (sem dados sensíveis):**
   ```bash
   # Variáveis (mascaradas)
   env | grep -E '^(NODE_ENV|PORT|LOG_LEVEL)' 
   ```

4. **Passos para reproduzir**
5. **Comportamento esperado vs atual**

### Logs de Debug Avançado

```bash
# Debug completo
DEBUG=* LOG_LEVEL=debug npm run dev > debug.log 2>&1

# Profiling de performance
node --prof index.js
node --prof-process isolate-*.log > profiling.txt
```

### Canais de Suporte

- **GitHub Issues**: Para bugs e features
- **Discord**: Para discussões em tempo real
- **Email**: <EMAIL>
- **Documentação**: https://docs.magnow.com

### Horário de Suporte

- **Segunda a Sexta**: 9h às 18h (BRT)
- **Emergências**: 24/7 (apenas clientes enterprise)
- **Tempo de resposta**: 
  - Crítico: 1 hora
  - Alto: 4 horas
  - Médio: 24 horas
  - Baixo: 72 horas

---

## 🔧 Ferramentas de Diagnóstico

### Scripts Utilitários

```bash
# Verificação completa do sistema
npm run system-check

# Limpeza de cache
npm run cache:clear

# Reinicialização completa
npm run reset:all

# Backup de desenvolvimento
npm run backup:dev
```

### Monitoramento

```bash
# Status em tempo real
npm run monitor

# Métricas de performance
npm run metrics

# Health check detalhado
npm run health:detailed
```

---

**📝 Nota:** Esta documentação é atualizada constantemente. Para problemas não cobertos aqui, consulte os logs detalhados ou entre em contato com o suporte técnico.

**⚡ Dica:** Mantenha sempre a versão mais recente do sistema para ter acesso às últimas correções e melhorias.

---

**Feito com ❤️ pela equipe Magnow** 