import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { MercadoLivreOAuthService } from '../services/mercadoLivreOAuthService';
import { MercadoLivreApiService } from '../services/mercadoLivreApiService';
import { logger } from '../utils/logger';

// Classes de erro locais
class ValidationError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'ValidationError';
    }
}

class AuthenticationError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AuthenticationError';
    }
}

class ExternalServiceError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'ExternalServiceError';
    }
}

class NotFoundError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'NotFoundError';
    }
}

export class MercadoLivreController {
    private prisma: PrismaClient;
    private oauthService: MercadoLivreOAuthService;
    private apiService: MercadoLivreApiService;

    constructor(prisma: PrismaClient) {
        this.prisma = prisma;
        this.oauthService = new MercadoLivreOAuthService(prisma);
        this.apiService = new MercadoLivreApiService(prisma);
    }

    /**
     * Health check da integração com Mercado Livre
     */
    healthCheck = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const healthStatus = await this.apiService.healthCheck();

            res.json({
                success: true,
                data: healthStatus,
                message: 'Health check realizado com sucesso'
            });
        } catch (error) {
            logger.error('Erro no health check:', error);
            next(error);
        }
    };

    /**
     * Inicia o processo de autenticação OAuth
     */
    initiateAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { tenantId } = req.body;
            const userId = req.user?.id;

            if (!tenantId || !userId) {
                throw new ValidationError('Tenant ID e User ID são obrigatórios');
            }

            const authUrl = await this.oauthService.getAuthorizationUrl(tenantId, userId);
            
            res.json({
                success: true,
                authUrl,
                message: 'URL de autorização gerada com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao iniciar autenticação OAuth:', error);
            next(error);
        }
    };

    /**
     * Processa o callback do OAuth
     */
    handleCallback = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { code, state, error } = req.query;

            if (error) {
                throw new AuthenticationError(`Erro na autorização: ${error}`);
            }

            if (!code || !state) {
                throw new ValidationError('Código de autorização e state são obrigatórios');
            }

            const result = await this.oauthService.handleCallback(
                code as string,
                state as string
            );

            res.json({
                success: true,
                data: result,
                message: 'Autenticação realizada com sucesso'
            });
        } catch (error) {
            logger.error('Erro no callback OAuth:', error);
            next(error);
        }
    };

    /**
     * Lista contas conectadas do tenant
     */
    getConnectedAccounts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const tenantId = req.user?.tenantId;

            if (!tenantId) {
                throw new ValidationError('Tenant ID é obrigatório');
            }

            const accounts = await this.prisma.mercadoLivreAccount.findMany({
                where: { tenantId },
                select: {
                    id: true,
                    userId: true,
                    nickname: true,
                    email: true,
                    countryId: true,
                    siteId: true,
                    isActive: true,
                    createdAt: true,
                    updatedAt: true
                }
            });

            res.json({
                success: true,
                data: accounts,
                message: 'Contas conectadas recuperadas com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao buscar contas conectadas:', error);
            next(error);
        }
    };

    /**
     * Remove uma conta conectada
     */
    disconnectAccount = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId } = req.params;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId) {
                throw new ValidationError('Tenant ID e Account ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada');
            }

            await this.prisma.mercadoLivreAccount.delete({
                where: { id: accountId }
            });

            res.json({
                success: true,
                message: 'Conta desconectada com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao desconectar conta:', error);
            next(error);
        }
    };

    /**
     * Busca produtos do Mercado Livre
     */
    searchProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId } = req.params;
            const { q, limit = 50, offset = 0 } = req.query;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId) {
                throw new ValidationError('Tenant ID e Account ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            const searchParams = {
                q: q as string,
                limit: parseInt(limit as string),
                offset: parseInt(offset as string)
            };

            const products = await this.apiService.searchItems(account.accessToken, searchParams);

            res.json({
                success: true,
                data: products,
                message: 'Produtos encontrados com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao buscar produtos:', error);
            next(error);
        }
    };

    /**
     * Obtém detalhes de um produto específico
     */
    getProductDetails = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId, itemId } = req.params;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId || !itemId) {
                throw new ValidationError('Tenant ID, Account ID e Item ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            const product = await this.apiService.getItemDetails(account.accessToken, itemId);

            res.json({
                success: true,
                data: product,
                message: 'Detalhes do produto obtidos com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao obter detalhes do produto:', error);
            next(error);
        }
    };

    /**
     * Lista produtos do usuário
     */
    getUserItems = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId } = req.params;
            const { limit = 50, offset = 0 } = req.query;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId) {
                throw new ValidationError('Tenant ID e Account ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            const items = await this.apiService.getUserItems(
                account.accessToken,
                account.userId,
                {
                    limit: parseInt(limit as string),
                    offset: parseInt(offset as string)
                }
            );

            res.json({
                success: true,
                data: items,
                message: 'Itens do usuário obtidos com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao obter itens do usuário:', error);
            next(error);
        }
    };

    /**
     * Atualiza estoque de um produto
     */
    updateStock = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId, itemId } = req.params;
            const { availableQuantity } = req.body;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId || !itemId) {
                throw new ValidationError('Tenant ID, Account ID e Item ID são obrigatórios');
            }

            if (typeof availableQuantity !== 'number' || availableQuantity < 0) {
                throw new ValidationError('Quantidade disponível deve ser um número não negativo');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            const result = await this.apiService.updateStock(
                account.accessToken,
                itemId,
                availableQuantity
            );

            res.json({
                success: true,
                data: result,
                message: 'Estoque atualizado com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao atualizar estoque:', error);
            next(error);
        }
    };

    /**
     * Sincroniza produtos com o banco de dados
     */
    syncProducts = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId } = req.params;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId) {
                throw new ValidationError('Tenant ID e Account ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            // Buscar todos os produtos do usuário
            const items = await this.apiService.getUserItems(
                account.accessToken,
                account.userId,
                { limit: 200 }
            );

            // Sincronizar com o banco de dados
            const syncResult = await this.apiService.syncItemsToDatabase(
                tenantId,
                accountId,
                items.data.results
            );

            res.json({
                success: true,
                data: syncResult,
                message: 'Sincronização realizada com sucesso'
            });
        } catch (error) {
            logger.error('Erro na sincronização:', error);
            next(error);
        }
    };

    /**
     * Obtém pedidos do Mercado Livre
     */
    getOrders = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId } = req.params;
            const { limit = 50, offset = 0, sort = 'date_desc' } = req.query;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId) {
                throw new ValidationError('Tenant ID e Account ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            const orders = await this.apiService.getOrders(
                account.accessToken,
                account.userId,
                {
                    limit: parseInt(limit as string),
                    offset: parseInt(offset as string),
                    sort: sort as string
                }
            );

            res.json({
                success: true,
                data: orders,
                message: 'Pedidos obtidos com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao obter pedidos:', error);
            next(error);
        }
    };

    /**
     * Obtém detalhes de um pedido específico
     */
    getOrderDetails = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId, orderId } = req.params;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId || !orderId) {
                throw new ValidationError('Tenant ID, Account ID e Order ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            const order = await this.apiService.getOrderDetails(account.accessToken, orderId);

            res.json({
                success: true,
                data: order,
                message: 'Detalhes do pedido obtidos com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao obter detalhes do pedido:', error);
            next(error);
        }
    };

    /**
     * Obtém estatísticas da conta
     */
    getAccountStats = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId } = req.params;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId) {
                throw new ValidationError('Tenant ID e Account ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId,
                    isActive: true
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada ou inativa');
            }

            // Buscar estatísticas básicas
            const [itemsCount, ordersCount] = await Promise.all([
                this.apiService.getUserItems(account.accessToken, account.userId, { limit: 1 }),
                this.apiService.getOrders(account.accessToken, account.userId, { limit: 1 })
            ]);

            const stats = {
                totalItems: itemsCount.data.paging.total,
                totalOrders: ordersCount.data.paging.total,
                accountInfo: {
                    nickname: account.nickname,
                    email: account.email,
                    countryId: account.countryId,
                    siteId: account.siteId
                }
            };

            res.json({
                success: true,
                data: stats,
                message: 'Estatísticas da conta obtidas com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao obter estatísticas da conta:', error);
            next(error);
        }
    };

    /**
     * Atualiza token de acesso
     */
    refreshToken = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { accountId } = req.params;
            const tenantId = req.user?.tenantId;

            if (!tenantId || !accountId) {
                throw new ValidationError('Tenant ID e Account ID são obrigatórios');
            }

            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    id: accountId,
                    tenantId
                }
            });

            if (!account) {
                throw new NotFoundError('Conta não encontrada');
            }

            const newTokens = await this.oauthService.refreshAccessToken(account.refreshToken);

            await this.prisma.mercadoLivreAccount.update({
                where: { id: accountId },
                data: {
                    accessToken: newTokens.access_token,
                    refreshToken: newTokens.refresh_token,
                    expiresAt: new Date(Date.now() + newTokens.expires_in * 1000)
                }
            });

            res.json({
                success: true,
                message: 'Token atualizado com sucesso'
            });
        } catch (error) {
            logger.error('Erro ao atualizar token:', error);
            next(error);
        }
    };

    /**
     * Processa webhooks do Mercado Livre
     */
    handleWebhook = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
        try {
            const { topic, resource, user_id, application_id } = req.body;

            logger.info('Webhook recebido do Mercado Livre', {
                topic,
                resource,
                user_id,
                application_id,
                body: req.body
            });

            // Responder imediatamente para o ML
            res.status(200).json({ status: 'received' });

            // Processar webhook de forma assíncrona
            this.processWebhookAsync(topic, resource, user_id, application_id);

        } catch (error) {
            logger.error('Erro ao processar webhook:', error);
            res.status(200).json({ status: 'error' }); // Sempre retornar 200 para o ML
        }
    };

    /**
     * Processa webhook de forma assíncrona
     */
    private async processWebhookAsync(topic: string, resource: string, userId: string, applicationId: string): Promise<void> {
        try {
            // Encontrar conta do usuário
            const account = await this.prisma.mercadoLivreAccount.findFirst({
                where: {
                    userId: userId,
                    isActive: true
                }
            });

            if (!account) {
                logger.warn('Conta não encontrada para webhook', { userId, topic, resource });
                return;
            }

            switch (topic) {
                case 'orders_v2':
                    await this.processOrderWebhook(account, resource);
                    break;

                case 'items':
                    await this.processItemWebhook(account, resource);
                    break;

                case 'stock_fulfillment':
                    await this.processStockWebhook(account, resource);
                    break;

                default:
                    logger.info('Tópico de webhook não processado', { topic, resource });
            }

        } catch (error) {
            logger.error('Erro no processamento assíncrono do webhook', {
                topic,
                resource,
                userId,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Processa webhook de pedidos
     */
    private async processOrderWebhook(account: any, resource: string): Promise<void> {
        try {
            const orderId = resource.split('/').pop();
            if (!orderId) return;

            // Buscar detalhes do pedido
            const orderDetails = await this.apiService.getOrderDetails(account.accessToken, orderId);

            if (orderDetails.success) {
                // Sincronizar pedido com banco de dados
                await this.apiService.syncOrdersToDatabase(
                    account.tenantId,
                    account.id,
                    [orderDetails.data]
                );

                logger.info('Pedido sincronizado via webhook', {
                    orderId,
                    accountId: account.id,
                    tenantId: account.tenantId
                });
            }

        } catch (error) {
            logger.error('Erro ao processar webhook de pedido', {
                resource,
                accountId: account.id,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Processa webhook de itens
     */
    private async processItemWebhook(account: any, resource: string): Promise<void> {
        try {
            const itemId = resource.split('/').pop();
            if (!itemId) return;

            // Buscar detalhes do item
            const itemDetails = await this.apiService.getItemDetails(account.accessToken, itemId);

            if (itemDetails.success) {
                // Sincronizar item com banco de dados
                await this.apiService.syncItemsToDatabase(
                    account.tenantId,
                    account.id,
                    [itemDetails.data]
                );

                logger.info('Item sincronizado via webhook', {
                    itemId,
                    accountId: account.id,
                    tenantId: account.tenantId
                });
            }

        } catch (error) {
            logger.error('Erro ao processar webhook de item', {
                resource,
                accountId: account.id,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Processa webhook de estoque
     */
    private async processStockWebhook(account: any, resource: string): Promise<void> {
        try {
            logger.info('Processando webhook de estoque', {
                resource,
                accountId: account.id,
                tenantId: account.tenantId
            });

            // Para webhooks de estoque, podemos precisar sincronizar todos os produtos
            // ou processar informações específicas do recurso
            // Implementação específica depende da estrutura do recurso

        } catch (error) {
            logger.error('Erro ao processar webhook de estoque', {
                resource,
                accountId: account.id,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
}