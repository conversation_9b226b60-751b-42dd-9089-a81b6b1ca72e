/**
 * Dashboard de Analytics de Planilhas
 * Sistema Magnow - Interface Aprimorada
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select-radix';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Progress } from '../ui/progress';
import { ScrollArea } from '../ui/scroll-area';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  FileSpreadsheet, 
  Download, 
  Clock, 
  Users, 
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Calendar,
  Filter,
  Eye,
  Share2,
  Target,
  Zap,
  Database
} from 'lucide-react';

interface AnalyticsData {
  overview: OverviewMetrics;
  usage: UsageMetrics;
  performance: PerformanceMetrics;
  templates: TemplateMetrics[];
  trends: TrendData[];
  errors: ErrorMetrics;
  predictions: PredictionData;
}

interface OverviewMetrics {
  totalSpreadsheets: number;
  totalDownloads: number;
  totalProducts: number;
  totalUsers: number;
  growthRate: number;
  avgGenerationTime: number;
  successRate: number;
  cacheHitRate: number;
}

interface UsageMetrics {
  dailyGenerations: number;
  weeklyGenerations: number;
  monthlyGenerations: number;
  peakHours: Array<{ hour: number; count: number }>;
  topTemplates: Array<{ name: string; usage: number; percentage: number }>;
  userActivity: Array<{ date: string; count: number }>;
}

interface PerformanceMetrics {
  avgResponseTime: number;
  p95ResponseTime: number;
  errorRate: number;
  throughput: number;
  cachePerformance: {
    hitRate: number;
    missRate: number;
    avgRetrievalTime: number;
  };
  systemLoad: {
    cpu: number;
    memory: number;
    disk: number;
  };
}

interface TemplateMetrics {
  id: string;
  name: string;
  usageCount: number;
  successRate: number;
  avgGenerationTime: number;
  errorCount: number;
  lastUsed: string;
  trend: 'up' | 'down' | 'stable';
}

interface TrendData {
  date: string;
  generations: number;
  downloads: number;
  errors: number;
  avgTime: number;
}

interface ErrorMetrics {
  totalErrors: number;
  errorRate: number;
  topErrors: Array<{
    type: string;
    count: number;
    percentage: number;
    lastOccurrence: string;
  }>;
  errorsByTemplate: Array<{
    templateName: string;
    errorCount: number;
    errorRate: number;
  }>;
}

interface PredictionData {
  nextWeekGenerations: number;
  peakLoadPrediction: {
    date: string;
    expectedLoad: number;
    confidence: number;
  };
  resourceRecommendations: Array<{
    type: 'cpu' | 'memory' | 'storage';
    recommendation: string;
    priority: 'low' | 'medium' | 'high';
  }>;
}

interface SpreadsheetAnalyticsProps {
  analyticsData: AnalyticsData | null;
  isLoading: boolean;
  onRefresh: () => void;
  dateRange: string;
  onDateRangeChange: (range: string) => void;
}

const DATE_RANGES = [
  { value: '24h', label: 'Últimas 24 horas' },
  { value: '7d', label: 'Últimos 7 dias' },
  { value: '30d', label: 'Últimos 30 dias' },
  { value: '90d', label: 'Últimos 90 dias' },
  { value: 'custom', label: 'Período personalizado' }
];

const SpreadsheetAnalytics: React.FC<SpreadsheetAnalyticsProps> = ({
  analyticsData,
  isLoading,
  onRefresh,
  dateRange,
  onDateRangeChange
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await onRefresh();
    setTimeout(() => setRefreshing(false), 1000);
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
          <BarChart3 className="w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-blue-500" />
        </div>
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Carregando Analytics</h3>
          <p className="text-gray-600">Coletando dados de performance e uso...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Dados não disponíveis</h3>
        <p className="text-gray-600 mb-6">Não foi possível carregar os dados de analytics.</p>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Tentar Novamente
        </Button>
      </div>
    );
  }

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Métricas Principais */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Planilhas</p>
                <p className="text-2xl font-bold text-blue-600">
                  {analyticsData.overview.totalSpreadsheets.toLocaleString()}
                </p>
              </div>
              <FileSpreadsheet className="w-8 h-8 text-blue-500" />
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
              <span className="text-sm text-green-600">+{analyticsData.overview.growthRate}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Downloads</p>
                <p className="text-2xl font-bold text-green-600">
                  {analyticsData.overview.totalDownloads.toLocaleString()}
                </p>
              </div>
              <Download className="w-8 h-8 text-green-500" />
            </div>
            <div className="flex items-center mt-2">
              <span className="text-sm text-gray-600">Taxa de sucesso: {analyticsData.overview.successRate}%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Produtos Processados</p>
                <p className="text-2xl font-bold text-purple-600">
                  {analyticsData.overview.totalProducts.toLocaleString()}
                </p>
              </div>
              <Database className="w-8 h-8 text-purple-500" />
            </div>
            <div className="flex items-center mt-2">
              <span className="text-sm text-gray-600">Tempo médio: {analyticsData.overview.avgGenerationTime}ms</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Usuários Ativos</p>
                <p className="text-2xl font-bold text-orange-600">
                  {analyticsData.overview.totalUsers.toLocaleString()}
                </p>
              </div>
              <Users className="w-8 h-8 text-orange-500" />
            </div>
            <div className="flex items-center mt-2">
              <span className="text-sm text-gray-600">Cache hit: {analyticsData.overview.cacheHitRate}%</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance em Tempo Real */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              Performance do Sistema
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>CPU</span>
                <span>{analyticsData.performance.systemLoad.cpu}%</span>
              </div>
              <Progress value={analyticsData.performance.systemLoad.cpu} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Memória</span>
                <span>{analyticsData.performance.systemLoad.memory}%</span>
              </div>
              <Progress value={analyticsData.performance.systemLoad.memory} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Disco</span>
                <span>{analyticsData.performance.systemLoad.disk}%</span>
              </div>
              <Progress value={analyticsData.performance.systemLoad.disk} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Métricas de Cache
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {analyticsData.performance.cachePerformance.hitRate}%
                </div>
                <div className="text-sm text-gray-600">Hit Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analyticsData.performance.cachePerformance.avgRetrievalTime}ms
                </div>
                <div className="text-sm text-gray-600">Tempo Médio</div>
              </div>
            </div>
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Cache Hit Rate</span>
                <span>{analyticsData.performance.cachePerformance.hitRate}%</span>
              </div>
              <Progress value={analyticsData.performance.cachePerformance.hitRate} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderUsageTab = () => (
    <div className="space-y-6">
      {/* Estatísticas de Uso */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-blue-600">
              {analyticsData.usage.dailyGenerations}
            </div>
            <div className="text-sm text-gray-600">Gerações Hoje</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-green-600">
              {analyticsData.usage.weeklyGenerations}
            </div>
            <div className="text-sm text-gray-600">Esta Semana</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-purple-600">
              {analyticsData.usage.monthlyGenerations}
            </div>
            <div className="text-sm text-gray-600">Este Mês</div>
          </CardContent>
        </Card>
      </div>

      {/* Templates Mais Usados */}
      <Card>
        <CardHeader>
          <CardTitle>Templates Mais Utilizados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analyticsData.usage.topTemplates.map((template, index) => (
              <div key={template.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                  </div>
                  <div>
                    <div className="font-medium">{template.name}</div>
                    <div className="text-sm text-gray-600">{template.usage} usos</div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24">
                    <Progress value={template.percentage} className="h-2" />
                  </div>
                  <span className="text-sm font-medium">{template.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Horários de Pico */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Horários de Maior Uso
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-6 gap-2">
            {analyticsData.usage.peakHours.map((hour) => (
              <div key={hour.hour} className="text-center">
                <div className="text-xs text-gray-600 mb-1">{hour.hour}h</div>
                <div className="h-16 bg-gray-100 rounded relative overflow-hidden">
                  <div 
                    className="absolute bottom-0 left-0 right-0 bg-blue-500 transition-all"
                    style={{ height: `${(hour.count / Math.max(...analyticsData.usage.peakHours.map(h => h.count))) * 100}%` }}
                  />
                </div>
                <div className="text-xs font-medium mt-1">{hour.count}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPerformanceTab = () => (
    <div className="space-y-6">
      {/* Métricas de Performance */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {analyticsData.performance.avgResponseTime}ms
            </div>
            <div className="text-sm text-gray-600">Tempo Médio</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">
              {analyticsData.performance.p95ResponseTime}ms
            </div>
            <div className="text-sm text-gray-600">P95 Response</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {analyticsData.performance.errorRate}%
            </div>
            <div className="text-sm text-gray-600">Taxa de Erro</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">
              {analyticsData.performance.throughput}
            </div>
            <div className="text-sm text-gray-600">Req/min</div>
          </CardContent>
        </Card>
      </div>

      {/* Performance por Template */}
      <Card>
        <CardHeader>
          <CardTitle>Performance por Template</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-3">
              {analyticsData.templates.map((template) => (
                <div key={template.id} className="flex items-center justify-between p-3 border rounded-md">
                  <div className="flex items-center space-x-3">
                    <div>
                      <div className="font-medium">{template.name}</div>
                      <div className="text-sm text-gray-600">
                        {template.usageCount} usos • {template.avgGenerationTime}ms médio
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant={template.successRate > 95 ? "default" : template.successRate > 90 ? "secondary" : "destructive"}
                      className="text-xs"
                    >
                      {template.successRate}% sucesso
                    </Badge>
                    {template.trend === 'up' && <TrendingUp className="w-4 h-4 text-green-500" />}
                    {template.trend === 'down' && <TrendingDown className="w-4 h-4 text-red-500" />}
                    {template.errorCount > 0 && (
                      <Badge variant="destructive" className="text-xs">
                        {template.errorCount} erros
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );

  const renderErrorsTab = () => (
    <div className="space-y-6">
      {/* Resumo de Erros */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-red-600">
              {analyticsData.errors.totalErrors}
            </div>
            <div className="text-sm text-gray-600">Total de Erros</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-orange-600">
              {analyticsData.errors.errorRate}%
            </div>
            <div className="text-sm text-gray-600">Taxa de Erro</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-3xl font-bold text-blue-600">
              {analyticsData.errors.topErrors.length}
            </div>
            <div className="text-sm text-gray-600">Tipos de Erro</div>
          </CardContent>
        </Card>
      </div>

      {/* Top Erros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Erros Mais Frequentes
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analyticsData.errors.topErrors.map((error, index) => (
              <div key={error.type} className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-red-600">{index + 1}</span>
                  </div>
                  <div>
                    <div className="font-medium text-red-800">{error.type}</div>
                    <div className="text-sm text-red-600">
                      Última ocorrência: {new Date(error.lastOccurrence).toLocaleString()}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="destructive" className="text-xs">
                    {error.count} ocorrências
                  </Badge>
                  <span className="text-sm font-medium text-red-600">{error.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Erros por Template */}
      <Card>
        <CardHeader>
          <CardTitle>Erros por Template</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analyticsData.errors.errorsByTemplate.map((template) => (
              <div key={template.templateName} className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <div className="font-medium">{template.templateName}</div>
                  <div className="text-sm text-gray-600">{template.errorCount} erros</div>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-24">
                    <Progress value={template.errorRate} className="h-2" />
                  </div>
                  <Badge 
                    variant={template.errorRate < 5 ? "default" : template.errorRate < 10 ? "secondary" : "destructive"}
                    className="text-xs"
                  >
                    {template.errorRate}%
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPredictionsTab = () => (
    <div className="space-y-6">
      {/* Previsões */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Previsões de Uso
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
              <div className="text-2xl font-bold text-blue-600">
                {analyticsData.predictions.nextWeekGenerations}
              </div>
              <div className="text-sm text-blue-700">Gerações previstas para próxima semana</div>
            </div>
            <div className="p-4 bg-orange-50 border border-orange-200 rounded-md">
              <div className="text-lg font-bold text-orange-600">
                {analyticsData.predictions.peakLoadPrediction.date}
              </div>
              <div className="text-sm text-orange-700">
                Pico de carga previsto ({analyticsData.predictions.peakLoadPrediction.confidence}% confiança)
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recomendações */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="w-5 h-5 mr-2" />
            Recomendações de Recursos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analyticsData.predictions.resourceRecommendations.map((rec, index) => (
              <div key={index} className={`p-3 border rounded-md ${
                rec.priority === 'high' ? 'bg-red-50 border-red-200' :
                rec.priority === 'medium' ? 'bg-yellow-50 border-yellow-200' :
                'bg-green-50 border-green-200'
              }`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Badge 
                      variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'secondary' : 'default'}
                      className="text-xs"
                    >
                      {rec.type.toUpperCase()}
                    </Badge>
                    <span className="font-medium">{rec.recommendation}</span>
                  </div>
                  <Badge 
                    variant={rec.priority === 'high' ? 'destructive' : rec.priority === 'medium' ? 'secondary' : 'default'}
                    className="text-xs"
                  >
                    {rec.priority}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Analytics de Planilhas</h2>
          <p className="text-gray-600">Monitore performance, uso e tendências do sistema</p>
        </div>
        <div className="flex items-center space-x-3">
          <Select value={dateRange} onValueChange={onDateRangeChange}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {DATE_RANGES.map(range => (
                <SelectItem key={range.value} value={range.value}>
                  {range.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button 
            variant="outline" 
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="usage">Uso</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="errors" className={analyticsData.errors.totalErrors > 0 ? "text-red-600" : ""}>
            Erros
            {analyticsData.errors.totalErrors > 0 && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {analyticsData.errors.totalErrors}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="predictions">Previsões</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">{renderOverviewTab()}</TabsContent>
        <TabsContent value="usage">{renderUsageTab()}</TabsContent>
        <TabsContent value="performance">{renderPerformanceTab()}</TabsContent>
        <TabsContent value="errors">{renderErrorsTab()}</TabsContent>
        <TabsContent value="predictions">{renderPredictionsTab()}</TabsContent>
      </Tabs>
    </div>
  );
};

export default SpreadsheetAnalytics;