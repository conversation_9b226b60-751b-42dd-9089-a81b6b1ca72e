/**
 * Rotas de Alertas de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Router } from 'express';
// import {
//   getAlertConfiguration,
//   configureAlerts,
//   executeManualCheck,
//   getJobExecutions,
//   getAlertStatistics,
//   toggleAlertSystem,
//   getFrequencyPresets
// } from '../controllers/stockAlertController'; // Temporariamente desabilitado
import { authenticate } from '../middleware/auth';
import { rateLimitMiddleware } from '../middleware/rateLimiter';

const router = Router();

// Middleware de autenticação para todas as rotas
router.use(authenticate());

// Rotas temporariamente desabilitadas devido a problemas de importação
// /**
//  * @route GET /api/stock-alerts/config
//  * @desc Obtém configuração atual de alertas
//  * @access Private
//  */
// router.get('/config', getAlertConfiguration);

// /**
//  * @route PUT /api/stock-alerts/config
//  * @desc Configura alertas de estoque
//  * @access Private
//  */
// router.put('/config', configureAlerts);

// /**
//  * @route POST /api/stock-alerts/execute
//  * @desc Executa verificação manual de alertas
//  * @access Private
//  */
// router.post('/execute', rateLimitMiddleware({
//   windowMs: 60 * 1000,
//   max: 5,
//   message: 'Muitas execuções manuais. Tente novamente em 1 minuto.'
// }), executeManualCheck);

// /**
//  * @route GET /api/stock-alerts/executions
//  * @desc Lista histórico de execuções de jobs
//  * @access Private
//  */
// router.get('/executions', getJobExecutions);

// /**
//  * @route GET /api/stock-alerts/statistics
//  * @desc Obtém estatísticas de alertas
//  * @access Private
//  */
// router.get('/statistics', getAlertStatistics);

// /**
//  * @route POST /api/stock-alerts/toggle
//  * @desc Ativa/desativa sistema de alertas
//  * @access Private
//  */
// router.post('/toggle', toggleAlertSystem);

// /**
//  * @route GET /api/stock-alerts/frequency-presets
//  * @desc Obtém presets de frequência comuns
//  * @access Private
//  */
// router.get('/frequency-presets', getFrequencyPresets);

// Rota temporária para teste
router.get('/test', (req, res) => {
  res.json({ message: 'Stock alerts routes temporarily disabled' });
});

export default router;