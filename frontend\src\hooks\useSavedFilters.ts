import { useState, useEffect, useCallback } from 'react';

interface SavedFilter {
  id: string;
  name: string;
  description?: string;
  filters: {
    search: string;
    filters: Record<string, any>;
  };
  createdAt: string;
  lastUsed?: string;
}

const STORAGE_KEY = 'magnow_saved_filters';

export const useSavedFilters = () => {
  const [savedFilters, setSavedFilters] = useState<SavedFilter[]>([]);

  // Carregar filtros salvos do localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        setSavedFilters(JSON.parse(stored));
      }
    } catch (error) {
      console.warn('Erro ao carregar filtros salvos:', error);
    }
  }, []);

  // Salvar filtros no localStorage
  const saveToStorage = useCallback((filters: SavedFilter[]) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(filters));
      setSavedFilters(filters);
    } catch (error) {
      console.error('Erro ao salvar filtros:', error);
    }
  }, []);

  // Salvar um novo filtro
  const saveFilter = useCallback((
    name: string,
    filterState: { search: string; filters: Record<string, any> },
    description?: string
  ) => {
    const newFilter: SavedFilter = {
      id: Date.now().toString(),
      name,
      description,
      filters: filterState,
      createdAt: new Date().toISOString()
    };

    const updatedFilters = [...savedFilters, newFilter];
    saveToStorage(updatedFilters);
    
    return newFilter.id;
  }, [savedFilters, saveToStorage]);

  // Atualizar um filtro existente
  const updateFilter = useCallback((
    id: string,
    updates: Partial<Pick<SavedFilter, 'name' | 'description' | 'filters'>>
  ) => {
    const updatedFilters = savedFilters.map(filter =>
      filter.id === id
        ? { ...filter, ...updates }
        : filter
    );
    saveToStorage(updatedFilters);
  }, [savedFilters, saveToStorage]);

  // Deletar um filtro
  const deleteFilter = useCallback((id: string) => {
    const updatedFilters = savedFilters.filter(filter => filter.id !== id);
    saveToStorage(updatedFilters);
  }, [savedFilters, saveToStorage]);

  // Marcar filtro como usado
  const markAsUsed = useCallback((id: string) => {
    const updatedFilters = savedFilters.map(filter =>
      filter.id === id
        ? { ...filter, lastUsed: new Date().toISOString() }
        : filter
    );
    saveToStorage(updatedFilters);
  }, [savedFilters, saveToStorage]);

  // Obter filtros ordenados por uso recente
  const getRecentFilters = useCallback((limit: number = 5) => {
    return [...savedFilters]
      .sort((a, b) => {
        const aDate = new Date(a.lastUsed || a.createdAt);
        const bDate = new Date(b.lastUsed || b.createdAt);
        return bDate.getTime() - aDate.getTime();
      })
      .slice(0, limit);
  }, [savedFilters]);

  return {
    savedFilters,
    saveFilter,
    updateFilter,
    deleteFilter,
    markAsUsed,
    getRecentFilters
  };
}; 