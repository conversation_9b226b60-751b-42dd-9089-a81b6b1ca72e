import React from 'react';
import { cn } from '../../lib/utils';

export interface ResponsiveGridProps {
  children: React.ReactNode;
  columns?: {
    default?: 1 | 2 | 3 | 4 | 5 | 6;
    sm?: 1 | 2 | 3 | 4 | 5 | 6;
    md?: 1 | 2 | 3 | 4 | 5 | 6;
    lg?: 1 | 2 | 3 | 4 | 5 | 6;
    xl?: 1 | 2 | 3 | 4 | 5 | 6;
  };
  gap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { default: 1, lg: 2 },
  gap = 'md',
  className
}) => {
  const getGridClasses = () => {
    const classes = ['grid'];
    
    // Gap classes
    const gapClasses = {
      none: 'gap-0',
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8'
    };
    classes.push(gapClasses[gap]);
    
    // Column classes
    const colClasses = {
      1: 'grid-cols-1',
      2: 'grid-cols-2',
      3: 'grid-cols-3',
      4: 'grid-cols-4',
      5: 'grid-cols-5',
      6: 'grid-cols-6'
    };
    
    // Default columns
    if (columns.default) {
      classes.push(colClasses[columns.default]);
    }
    
    // Responsive columns
    if (columns.sm) {
      classes.push(`sm:${colClasses[columns.sm]}`);
    }
    if (columns.md) {
      classes.push(`md:${colClasses[columns.md]}`);
    }
    if (columns.lg) {
      classes.push(`lg:${colClasses[columns.lg]}`);
    }
    if (columns.xl) {
      classes.push(`xl:${colClasses[columns.xl]}`);
    }
    
    return classes.join(' ');
  };

  return (
    <div className={cn(getGridClasses(), className)}>
      {children}
    </div>
  );
};

// Preset comum para layout 2 colunas
export const TwoColumnGrid: React.FC<{
  children: React.ReactNode;
  gap?: ResponsiveGridProps['gap'];
  className?: string;
}> = ({ children, gap = 'lg', className }) => (
  <ResponsiveGrid
    columns={{ default: 1, lg: 2 }}
    gap={gap}
    className={className}
  >
    {children}
  </ResponsiveGrid>
);

// Preset comum para cards de estatísticas
export const StatsGrid: React.FC<{
  children: React.ReactNode;
  gap?: ResponsiveGridProps['gap'];
  className?: string;
}> = ({ children, gap = 'md', className }) => (
  <ResponsiveGrid
    columns={{ default: 1, md: 2, lg: 4 }}
    gap={gap}
    className={className}
  >
    {children}
  </ResponsiveGrid>
);

// Preset comum para produtos
export const ProductGrid: React.FC<{
  children: React.ReactNode;
  gap?: ResponsiveGridProps['gap'];
  className?: string;
}> = ({ children, gap = 'lg', className }) => (
  <ResponsiveGrid
    columns={{ default: 1, lg: 2 }}
    gap={gap}
    className={className}
  >
    {children}
  </ResponsiveGrid>
);

export default ResponsiveGrid;
