/**
 * Serviço de Alertas de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { StockGapService } from './stockGapService';
import { 
  StockAlert,
  StockAlertConfiguration,
  StockStatus,
  StockPriority,
  AlertSeverity,
  AlertChannel,
  AlertRule,
  StockCalculationResult
} from '../types/stock';
import { DatabaseError } from '../middleware/errorHandler';

/**
 * Interface para configuração de notificação
 */
export interface NotificationConfig {
  enabled: boolean;
  channels: AlertChannel[];
  emailAddresses?: string[];
  webhookUrl?: string;
  slackWebhook?: string;
  discordWebhook?: string;
  telegramBotToken?: string;
  telegramChatId?: string;
}

/**
 * Interface para resultado de envio de alerta
 */
export interface AlertDeliveryResult {
  alertId: string;
  channel: AlertChannel;
  success: boolean;
  error?: string;
  deliveredAt?: Date;
}

/**
 * Interface para estatísticas de alertas
 */
export interface AlertStatistics {
  tenantId: string;
  period: {
    start: Date;
    end: Date;
  };
  totalAlerts: number;
  alertsBySeverity: Record<AlertSeverity, number>;
  alertsByStatus: Record<StockStatus, number>;
  resolvedAlerts: number;
  activeAlerts: number;
  averageResolutionTimeHours: number;
  topAlertedProducts: Array<{
    mlItemId: string;
    sku: string;
    alertCount: number;
  }>;
}

export class StockAlertService {
  private prisma: PrismaClient;
  private stockGapService: StockGapService;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    this.stockGapService = new StockGapService(this.prisma);
  }

  /**
   * Processa alertas para um tenant
   */
  public async processAlerts(tenantId: string): Promise<{
    alertsGenerated: number;
    alertsResolved: number;
    notificationsSent: number;
  }> {
    try {
      logger.info('Processando alertas de estoque', { tenantId });

      // Busca configuração de alertas
      const alertConfig = await this.getAlertConfiguration(tenantId);
      
      if (!alertConfig.isEnabled) {
        logger.info('Alertas desabilitados para tenant', { tenantId });
        return { alertsGenerated: 0, alertsResolved: 0, notificationsSent: 0 };
      }

      // Busca produtos com gap crítico
      const criticalProducts = await this.stockGapService.getCriticalGapProducts(tenantId);

      // Gera novos alertas
      const newAlerts = await this.generateAlerts(tenantId, criticalProducts, alertConfig);

      // Resolve alertas que não são mais relevantes
      const resolvedAlerts = await this.resolveObsoleteAlerts(tenantId, criticalProducts);

      // Envia notificações para alertas ativos
      const notificationsSent = await this.sendNotifications(tenantId, newAlerts);

      logger.info('Processamento de alertas concluído', {
        tenantId,
        alertsGenerated: newAlerts.length,
        alertsResolved: resolvedAlerts,
        notificationsSent
      });

      return {
        alertsGenerated: newAlerts.length,
        alertsResolved: resolvedAlerts,
        notificationsSent
      };
    } catch (error) {
      logger.error('Erro ao processar alertas', { error, tenantId });
      throw new DatabaseError('Erro ao processar alertas de estoque', { originalError: error });
    }
  }

  /**
   * Cria alerta manual para produto específico
   */
  public async createManualAlert(
    tenantId: string,
    mlItemId: string,
    severity: AlertSeverity,
    message: string,
    userId?: string
  ): Promise<StockAlert> {
    try {
      // Busca informações do produto
      const product = await this.prisma.mercadoLivreItem.findFirst({
        where: { tenantId, mlItemId }
      });

      if (!product) {
        throw new Error(`Produto não encontrado: ${mlItemId}`);
      }

      // Cria alerta
      const alert = await this.prisma.stockAlert.create({
        data: {
          tenantId,
          mlItemId,
          sku: product.sku || '',
          severity,
          message,
          isActive: true,
          isManual: true,
          createdBy: userId,
          createdAt: new Date(),
          metadata: JSON.stringify({
            manualAlert: true,
            createdByUser: userId
          })
        }
      });

      // Envia notificação
      await this.sendAlertNotification(tenantId, alert as StockAlert);

      logger.info('Alerta manual criado', { tenantId, mlItemId, alertId: alert.id });

      return alert as StockAlert;
    } catch (error) {
      logger.error('Erro ao criar alerta manual', { error, tenantId, mlItemId });
      throw error;
    }
  }

  /**
   * Resolve alerta específico
   */
  public async resolveAlert(
    tenantId: string,
    alertId: string,
    resolution: string,
    userId?: string
  ): Promise<void> {
    try {
      await this.prisma.stockAlert.update({
        where: { id: alertId, tenantId },
        data: {
          isActive: false,
          resolvedAt: new Date(),
          resolvedBy: userId,
          resolution,
          metadata: {
            // Preserva metadata existente e adiciona resolução
          }
        }
      });

      logger.info('Alerta resolvido', { tenantId, alertId, resolvedBy: userId });
    } catch (error) {
      logger.error('Erro ao resolver alerta', { error, tenantId, alertId });
      throw error;
    }
  }

  /**
   * Busca alertas ativos para um tenant
   */
  public async getActiveAlerts(
    tenantId: string,
    filters: {
      severity?: AlertSeverity[];
      mlItemIds?: string[];
      limit?: number;
    } = {}
  ): Promise<StockAlert[]> {
    try {
      const where: any = {
        tenantId,
        isActive: true
      };

      if (filters.severity && filters.severity.length > 0) {
        where.severity = { in: filters.severity };
      }

      if (filters.mlItemIds && filters.mlItemIds.length > 0) {
        where.mlItemId = { in: filters.mlItemIds };
      }

      const alerts = await this.prisma.stockAlert.findMany({
        where,
        orderBy: [
          { severity: 'desc' },
          { createdAt: 'desc' }
        ],
        take: filters.limit || 100
      });

      return alerts as StockAlert[];
    } catch (error) {
      logger.error('Erro ao buscar alertas ativos', { error, tenantId });
      throw error;
    }
  }

  /**
   * Configura alertas para um tenant
   */
  public async configureAlerts(
    tenantId: string,
    configuration: Partial<StockAlertConfiguration>
  ): Promise<StockAlertConfiguration> {
    try {
      const config = await this.prisma.stockAlertConfiguration.upsert({
        where: { tenantId },
        update: configuration,
        create: {
          tenantId,
          isEnabled: true,
          criticalThreshold: 5,
          warningThreshold: 10,
          checkFrequencyMinutes: 60,
          maxAlertsPerProduct: 3,
          alertRetentionDays: 30,
          notificationChannels: [],
          ...configuration
        }
      });

      logger.info('Configuração de alertas atualizada', { tenantId });

      return config as StockAlertConfiguration;
    } catch (error) {
      logger.error('Erro ao configurar alertas', { error, tenantId });
      throw error;
    }
  }

  /**
   * Gera estatísticas de alertas
   */
  public async getAlertStatistics(
    tenantId: string,
    startDate: Date,
    endDate: Date
  ): Promise<AlertStatistics> {
    try {
      // Busca alertas do período
      const alerts = await this.prisma.stockAlert.findMany({
        where: {
          tenantId,
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        include: {
          item: {
            select: {
              sku: true
            }
          }
        }
      });

      // Calcula estatísticas
      const totalAlerts = alerts.length;
      
      const alertsBySeverity = alerts.reduce((acc, alert) => {
        acc[alert.severity as AlertSeverity] = (acc[alert.severity as AlertSeverity] || 0) + 1;
        return acc;
      }, {} as Record<AlertSeverity, number>);

      const resolvedAlerts = alerts.filter(a => !a.isActive).length;
      const activeAlerts = alerts.filter(a => a.isActive).length;

      // Calcula tempo médio de resolução
      const resolvedAlertsWithTime = alerts.filter(a => a.resolvedAt && a.createdAt);
      const averageResolutionTimeHours = resolvedAlertsWithTime.length > 0
        ? resolvedAlertsWithTime.reduce((sum, alert) => {
            const resolutionTime = (alert.resolvedAt!.getTime() - alert.createdAt.getTime()) / (1000 * 60 * 60);
            return sum + resolutionTime;
          }, 0) / resolvedAlertsWithTime.length
        : 0;

      // Produtos com mais alertas
      const productAlertCounts = alerts.reduce((acc, alert) => {
        const key = alert.mlItemId;
        acc[key] = (acc[key] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const topAlertedProducts = Object.entries(productAlertCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([mlItemId, count]) => {
          const alert = alerts.find(a => a.mlItemId === mlItemId);
          return {
            mlItemId,
            sku: alert?.item?.sku || '',
            alertCount: count
          };
        });

      return {
        tenantId,
        period: { start: startDate, end: endDate },
        totalAlerts,
        alertsBySeverity,
        alertsByStatus: {} as Record<StockStatus, number>, // TODO: Implementar
        resolvedAlerts,
        activeAlerts,
        averageResolutionTimeHours,
        topAlertedProducts
      };
    } catch (error) {
      logger.error('Erro ao gerar estatísticas de alertas', { error, tenantId });
      throw error;
    }
  }

  /**
   * Gera alertas baseados em produtos críticos
   */
  private async generateAlerts(
    tenantId: string,
    criticalProducts: any[],
    config: StockAlertConfiguration
  ): Promise<StockAlert[]> {
    const newAlerts: StockAlert[] = [];

    for (const product of criticalProducts) {
      try {
        // Verifica se já existe alerta ativo para este produto
        const existingAlert = await this.prisma.stockAlert.findFirst({
          where: {
            tenantId,
            mlItemId: product.mlItemId,
            isActive: true
          }
        });

        if (existingAlert) {
          // Atualiza alerta existente se necessário
          await this.updateExistingAlert(existingAlert, product);
          continue;
        }

        // Verifica limite de alertas por produto
        const recentAlertsCount = await this.prisma.stockAlert.count({
          where: {
            tenantId,
            mlItemId: product.mlItemId,
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Últimas 24h
            }
          }
        });

        if (recentAlertsCount >= config.maxAlertsPerProduct) {
          logger.warn('Limite de alertas por produto atingido', {
            tenantId,
            mlItemId: product.mlItemId,
            count: recentAlertsCount
          });
          continue;
        }

        // Determina severidade do alerta
        const severity = this.determineSeverity(product, config);

        // Gera mensagem do alerta
        const message = this.generateAlertMessage(product, severity);

        // Cria novo alerta
        const alert = await this.prisma.stockAlert.create({
          data: {
            tenantId,
            mlItemId: product.mlItemId,
            sku: product.sku,
            severity,
            message,
            isActive: true,
            isManual: false,
            createdAt: new Date(),
            metadata: JSON.stringify({
              stockGap: product.stockGap,
              currentStock: product.currentStock,
              idealStock: product.idealStock,
              coverageDays: product.coverageDaysAvailable,
              priority: product.priority
            })
          }
        });

        newAlerts.push(alert as StockAlert);

        logger.info('Novo alerta gerado', {
          tenantId,
          mlItemId: product.mlItemId,
          severity,
          alertId: alert.id
        });
      } catch (error) {
        logger.error('Erro ao gerar alerta para produto', {
          error,
          tenantId,
          mlItemId: product.mlItemId
        });
      }
    }

    return newAlerts;
  }

  /**
   * Resolve alertas que não são mais relevantes
   */
  private async resolveObsoleteAlerts(
    tenantId: string,
    currentCriticalProducts: any[]
  ): Promise<number> {
    try {
      const criticalMlItemIds = new Set(currentCriticalProducts.map(p => p.mlItemId));

      // Busca alertas ativos que não estão mais na lista crítica
      const obsoleteAlerts = await this.prisma.stockAlert.findMany({
        where: {
          tenantId,
          isActive: true,
          mlItemId: {
            notIn: Array.from(criticalMlItemIds)
          }
        }
      });

      // Resolve alertas obsoletos
      if (obsoleteAlerts.length > 0) {
        await this.prisma.stockAlert.updateMany({
          where: {
            id: { in: obsoleteAlerts.map(a => a.id) }
          },
          data: {
            isActive: false,
            resolvedAt: new Date(),
            resolution: 'Produto não está mais em estado crítico'
          }
        });

        logger.info('Alertas obsoletos resolvidos', {
          tenantId,
          count: obsoleteAlerts.length
        });
      }

      return obsoleteAlerts.length;
    } catch (error) {
      logger.error('Erro ao resolver alertas obsoletos', { error, tenantId });
      return 0;
    }
  }

  /**
   * Envia notificações para alertas
   */
  private async sendNotifications(
    tenantId: string,
    alerts: StockAlert[]
  ): Promise<number> {
    let notificationsSent = 0;

    for (const alert of alerts) {
      try {
        await this.sendAlertNotification(tenantId, alert);
        notificationsSent++;
      } catch (error) {
        logger.error('Erro ao enviar notificação', {
          error,
          tenantId,
          alertId: alert.id
        });
      }
    }

    return notificationsSent;
  }

  /**
   * Envia notificação para um alerta específico
   */
  private async sendAlertNotification(
    tenantId: string,
    alert: StockAlert
  ): Promise<AlertDeliveryResult[]> {
    try {
      const config = await this.getAlertConfiguration(tenantId);
      const notificationConfig = await this.getNotificationConfiguration(tenantId);

      if (!notificationConfig.enabled || notificationConfig.channels.length === 0) {
        return [];
      }

      const results: AlertDeliveryResult[] = [];

      for (const channel of notificationConfig.channels) {
        try {
          const result = await this.sendNotificationToChannel(
            channel,
            alert,
            notificationConfig
          );
          results.push(result);
        } catch (error) {
          results.push({
            alertId: alert.id,
            channel,
            success: false,
            error: error instanceof Error ? error.message : 'Erro desconhecido'
          });
        }
      }

      // Salva histórico de entrega
      await this.saveDeliveryHistory(alert.id, results);

      return results;
    } catch (error) {
      logger.error('Erro ao enviar notificação de alerta', {
        error,
        tenantId,
        alertId: alert.id
      });
      throw error;
    }
  }

  /**
   * Envia notificação para canal específico
   */
  private async sendNotificationToChannel(
    channel: AlertChannel,
    alert: StockAlert,
    config: NotificationConfig
  ): Promise<AlertDeliveryResult> {
    const deliveryResult: AlertDeliveryResult = {
      alertId: alert.id,
      channel,
      success: false
    };

    try {
      switch (channel) {
        case 'email':
          if (config.emailAddresses && config.emailAddresses.length > 0) {
            await this.sendEmailNotification(alert, config.emailAddresses);
            deliveryResult.success = true;
          }
          break;

        case 'webhook':
          if (config.webhookUrl) {
            await this.sendWebhookNotification(alert, config.webhookUrl);
            deliveryResult.success = true;
          }
          break;

        case 'slack':
          if (config.slackWebhook) {
            await this.sendSlackNotification(alert, config.slackWebhook);
            deliveryResult.success = true;
          }
          break;

        case 'discord':
          if (config.discordWebhook) {
            await this.sendDiscordNotification(alert, config.discordWebhook);
            deliveryResult.success = true;
          }
          break;

        case 'telegram':
          if (config.telegramBotToken && config.telegramChatId) {
            await this.sendTelegramNotification(alert, config.telegramBotToken, config.telegramChatId);
            deliveryResult.success = true;
          }
          break;

        default:
          throw new Error(`Canal de notificação não suportado: ${channel}`);
      }

      if (deliveryResult.success) {
        deliveryResult.deliveredAt = new Date();
      }
    } catch (error) {
      deliveryResult.error = error instanceof Error ? error.message : 'Erro desconhecido';
    }

    return deliveryResult;
  }

  /**
   * Busca configuração de alertas
   */
  public async getAlertConfiguration(tenantId: string): Promise<StockAlertConfiguration> {
    let config = await this.prisma.stockAlertConfiguration.findFirst({
      where: { tenantId }
    });

    if (!config) {
      // Cria configuração padrão
      config = await this.prisma.stockAlertConfiguration.create({
        data: {
          tenantId,
          isEnabled: true,
          criticalThreshold: 5,
          warningThreshold: 10,
          checkFrequencyMinutes: 360,
          maxAlertsPerProduct: 3,
          alertRetentionDays: 30,
          notificationChannels: ['email']
        }
      });
    }

    return config as StockAlertConfiguration;
  }

  /**
   * Busca configuração de notificações
   */
  private async getNotificationConfiguration(tenantId: string): Promise<NotificationConfig> {
    // TODO: Implementar busca de configuração de notificações
    // Por enquanto, retorna configuração padrão
    return {
      enabled: true,
      channels: ['email'],
      emailAddresses: ['<EMAIL>'] // TODO: Buscar emails reais do tenant
    };
  }

  /**
   * Determina severidade do alerta
   */
  private determineSeverity(product: any, config: StockAlertConfiguration): AlertSeverity {
    if (product.stockGap >= config.criticalThreshold) {
      return 'critical';
    } else if (product.stockGap >= config.warningThreshold) {
      return 'warning';
    } else {
      return 'info';
    }
  }

  /**
   * Gera mensagem do alerta
   */
  private generateAlertMessage(product: any, severity: AlertSeverity): string {
    const gapText = product.stockGap > 0 ? 'faltam' : 'sobram';
    const quantityText = Math.abs(product.stockGap);
    const coverageText = product.coverageDaysAvailable.toFixed(1);

    switch (severity) {
      case 'critical':
        return `🚨 CRÍTICO: ${gapText} ${quantityText} unidades do produto ${product.sku}. Cobertura atual: ${coverageText} dias.`;
      
      case 'warning':
        return `⚠️ ATENÇÃO: ${gapText} ${quantityText} unidades do produto ${product.sku}. Cobertura atual: ${coverageText} dias.`;
      
      default:
        return `ℹ️ INFO: ${gapText} ${quantityText} unidades do produto ${product.sku}. Cobertura atual: ${coverageText} dias.`;
    }
  }

  /**
   * Atualiza alerta existente
   */
  private async updateExistingAlert(existingAlert: any, product: any): Promise<void> {
    // TODO: Implementar lógica de atualização de alerta existente
    logger.debug('Alerta existente encontrado, não criando duplicata', {
      alertId: existingAlert.id,
      mlItemId: product.mlItemId
    });
  }

  /**
   * Salva histórico de entrega
   */
  private async saveDeliveryHistory(alertId: string, results: AlertDeliveryResult[]): Promise<void> {
    try {
      for (const result of results) {
        await this.prisma.alertDeliveryHistory.create({
          data: {
            alertId,
            channel: result.channel,
            success: result.success,
            error: result.error,
            deliveredAt: result.deliveredAt || new Date()
          }
        });
      }
    } catch (error) {
      logger.error('Erro ao salvar histórico de entrega', { error, alertId });
    }
  }

  // Métodos de notificação (implementações básicas)
  private async sendEmailNotification(alert: StockAlert, emails: string[]): Promise<void> {
    // TODO: Implementar envio de email real
    logger.info('Email enviado (simulado)', { alertId: alert.id, emails });
  }

  private async sendWebhookNotification(alert: StockAlert, webhookUrl: string): Promise<void> {
    // TODO: Implementar webhook real
    logger.info('Webhook enviado (simulado)', { alertId: alert.id, webhookUrl });
  }

  private async sendSlackNotification(alert: StockAlert, webhookUrl: string): Promise<void> {
    // TODO: Implementar Slack real
    logger.info('Slack enviado (simulado)', { alertId: alert.id });
  }

  private async sendDiscordNotification(alert: StockAlert, webhookUrl: string): Promise<void> {
    // TODO: Implementar Discord real
    logger.info('Discord enviado (simulado)', { alertId: alert.id });
  }

  private async sendTelegramNotification(alert: StockAlert, botToken: string, chatId: string): Promise<void> {
    // TODO: Implementar Telegram real
    logger.info('Telegram enviado (simulado)', { alertId: alert.id });
  }
}