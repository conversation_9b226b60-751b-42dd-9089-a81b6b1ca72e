import React from 'react';
import { Card, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { 
  Package, 
  Bell, 
  TrendingUp, 
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Target,
  Clock,
  Zap
} from 'lucide-react';
import type { StockStatistics } from '../../store/stockStore';

interface StockStatisticsCardsProps {
  statistics: StockStatistics;
  isLoading?: boolean;
  className?: string;
}

export default function StockStatisticsCards({ 
  statistics, 
  isLoading = false,
  className = ""
}: StockStatisticsCardsProps) {
  
  const getStockHealthPercentage = () => {
    if (statistics.totalItems === 0) return 0;
    const healthyItems = statistics.totalItems - statistics.lowStockItems - statistics.outOfStockItems;
    return Math.round((healthyItems / statistics.totalItems) * 100);
  };

  const getStockDistribution = () => {
    const total = statistics.totalItems;
    if (total === 0) return { healthy: 0, low: 0, out: 0 };
    
    return {
      healthy: Math.round(((total - statistics.lowStockItems - statistics.outOfStockItems) / total) * 100),
      low: Math.round((statistics.lowStockItems / total) * 100),
      out: Math.round((statistics.outOfStockItems / total) * 100)
    };
  };

  const stockHealth = getStockHealthPercentage();
  const distribution = getStockDistribution();

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
        {Array.from({ length: 4 }, (_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div className="h-4 bg-muted rounded w-24"></div>
              <div className="h-4 w-4 bg-muted rounded"></div>
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-muted rounded w-16 mb-1"></div>
              <div className="h-3 bg-muted rounded w-20"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {/* Total Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total de Itens</CardTitle>
          <Package className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalItems}</div>
          <p className="text-xs text-muted-foreground">
            {statistics.locations.length} {statistics.locations.length === 1 ? 'localização' : 'localizações'}
          </p>
        </CardContent>
      </Card>

      {/* Total Quantity */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Quantidade Total</CardTitle>
          <BarChart3 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{statistics.totalQuantity.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            unidades em estoque
          </p>
        </CardContent>
      </Card>

      {/* Stock Health */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Saúde do Estoque</CardTitle>
          {stockHealth >= 80 ? (
            <CheckCircle className="h-4 w-4 text-green-500" />
          ) : stockHealth >= 60 ? (
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          ) : (
            <AlertTriangle className="h-4 w-4 text-red-500" />
          )}
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${
            stockHealth >= 80 ? 'text-green-600' : 
            stockHealth >= 60 ? 'text-yellow-600' : 
            'text-red-600'
          }`}>
            {stockHealth}%
          </div>
          <div className="space-y-1">
            <Progress 
              value={stockHealth} 
              className="h-2"
            />
            <p className="text-xs text-muted-foreground">
              {stockHealth >= 80 ? 'Excelente' : 
               stockHealth >= 60 ? 'Atenção' : 
               'Crítico'}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Critical Items */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Itens Críticos</CardTitle>
          <Bell className="h-4 w-4 text-red-500" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {statistics.lowStockItems + statistics.outOfStockItems}
          </div>
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-xs">
              <Badge variant="destructive" className="px-1 py-0 text-xs">
                {statistics.outOfStockItems}
              </Badge>
              <span className="text-muted-foreground">sem estoque</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <Badge variant="secondary" className="px-1 py-0 text-xs bg-yellow-100 text-yellow-800">
                {statistics.lowStockItems}
              </Badge>
              <span className="text-muted-foreground">baixo estoque</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stock Distribution Chart */}
      <Card className="md:col-span-2 lg:col-span-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Distribuição do Estoque
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Visual Distribution */}
            <div className="flex h-4 rounded-full overflow-hidden bg-muted">
              {distribution.healthy > 0 && (
                <div 
                  className="bg-green-500 flex items-center justify-center text-xs text-white font-medium"
                  style={{ width: `${distribution.healthy}%` }}
                >
                  {distribution.healthy > 15 && `${distribution.healthy}%`}
                </div>
              )}
              {distribution.low > 0 && (
                <div 
                  className="bg-yellow-500 flex items-center justify-center text-xs text-white font-medium"
                  style={{ width: `${distribution.low}%` }}
                >
                  {distribution.low > 15 && `${distribution.low}%`}
                </div>
              )}
              {distribution.out > 0 && (
                <div 
                  className="bg-red-500 flex items-center justify-center text-xs text-white font-medium"
                  style={{ width: `${distribution.out}%` }}
                >
                  {distribution.out > 15 && `${distribution.out}%`}
                </div>
              )}
            </div>

            {/* Legend */}
            <div className="grid grid-cols-3 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded"></div>
                <span className="text-muted-foreground">Em Estoque</span>
                <span className="font-medium">
                  {statistics.totalItems - statistics.lowStockItems - statistics.outOfStockItems}
                </span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                <span className="text-muted-foreground">Baixo Estoque</span>
                <span className="font-medium">{statistics.lowStockItems}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-red-500 rounded"></div>
                <span className="text-muted-foreground">Sem Estoque</span>
                <span className="font-medium">{statistics.outOfStockItems}</span>
              </div>
            </div>

            {/* Locations */}
            {statistics.locations.length > 0 && (
              <div className="pt-2 border-t">
                <div className="text-sm font-medium mb-2">Localizações:</div>
                <div className="flex flex-wrap gap-1">
                  {statistics.locations.map((location, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {location}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Last Updated */}
            <div className="flex items-center gap-2 text-xs text-muted-foreground pt-2 border-t">
              <Clock className="h-3 w-3" />
              <span>
                Última atualização: {new Date(statistics.lastUpdated).toLocaleString('pt-BR')}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
