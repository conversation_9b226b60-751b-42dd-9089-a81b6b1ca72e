# Guia de Deployment

Este documento fornece um guia para fazer o deploy da aplicação Magnow em produção.

## Opções de Deployment

Existem duas maneiras principais de fazer o deploy da aplicação: usando **Docker** (recomendado) ou configurando o ambiente manualmente com **PM2**.

### 1. <PERSON><PERSON> (Recomendado)

Usar Docker é a maneira mais fácil e confiável de fazer o deploy, pois encapsula a aplicação e todas as suas dependências em um contêiner isolado.

**Pré-requisitos:**

*   Docker e Docker Compose instalados no servidor de produção.

**Passos:**

1.  **Clone o repositório** no seu servidor:

    ```bash
    git clone https://github.com/seu-usuario/magnow.git
    cd magnow
    ```

2.  **Crie o arquivo de variáveis de ambiente** `.env` a partir do exemplo. Preencha com os valores de produção (banco de dados, chaves de API, etc.).

    ```bash
    cp .env.example .env
    nano .env
    ```

3.  **Construa e inicie os contêineres** em modo detached:

    ```bash
    docker-compose -f docker-compose.prod.yml up --build -d
    ```

    Este comando irá:
    *   Construir a imagem Docker de produção.
    *   Iniciar os contêineres da aplicação, banco de dados (PostgreSQL) e cache (Redis).
    *   Aplicar as migrações do banco de dados automaticamente.

4.  **Verifique se a aplicação está rodando**:

    ```bash
    docker-compose -f docker-compose.prod.yml logs -f app
    ```

### 2. Manualmente com PM2

Se você prefere não usar Docker, pode configurar a aplicação para rodar como um serviço usando o `PM2`, um gerenciador de processos para Node.js.

**Pré-requisitos:**

*   Node.js (versão 18 ou superior)
*   NPM ou Yarn
*   PM2 instalado globalmente (`npm install -g pm2`)
*   Um banco de dados PostgreSQL e um servidor Redis acessíveis.

**Passos:**

1.  **Clone o repositório** e instale as dependências:

    ```bash
    git clone https://github.com/seu-usuario/magnow.git
    cd magnow
    npm install --production
    ```

2.  **Crie e configure o arquivo `.env`** com as variáveis de ambiente de produção.

3.  **Compile o código TypeScript** para JavaScript:

    ```bash
    npm run build
    ```

4.  **Execute as migrações** do banco de dados:

    ```bash
    npm run db:migrate:deploy
    ```

5.  **Inicie a aplicação com PM2** usando o arquivo de configuração do ecossistema:

    ```bash
    pm2 start ecosystem.config.js --env production
    ```

6.  **Salve a configuração do PM2** para que ele reinicie a aplicação automaticamente após o boot do servidor:

    ```bash
    pm2 save
    ```

## Provedores de Nuvem

A aplicação pode ser hospedada em qualquer provedor de nuvem que suporte Node.js ou Docker. Algumas opções populares incluem:

*   **AWS**: Usando EC2 para as instâncias, RDS para o PostgreSQL e ElastiCache para o Redis.
*   **Google Cloud**: Usando Compute Engine, Cloud SQL e Memorystore.
*   **Azure**: Usando Virtual Machines, Azure Database for PostgreSQL e Azure Cache for Redis.
*   **DigitalOcean**: Usando Droplets e bancos de dados gerenciados.
*   **Heroku**: Usando Dynos e add-ons para PostgreSQL e Redis.
*   **Render**: Oferece uma ótima experiência para deploy de aplicações Node.js com bancos de dados e Redis como serviços.

Para a maioria desses provedores, a abordagem com Docker é a mais portável e recomendada.