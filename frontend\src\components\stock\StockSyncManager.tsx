import React, { useState } from 'react';
import { Button } from '../ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Progress } from '../ui/progress';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  RefreshCw, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle,
  Zap,
  BarChart3
} from 'lucide-react';
import { useStockSync } from '../../hooks/useStockSync';
import { useStockStore } from '../../store/stockStore';

interface StockSyncManagerProps {
  className?: string;
}

export const StockSyncManager: React.FC<StockSyncManagerProps> = ({ className }) => {
  const [showDetails, setShowDetails] = useState(false);
  
  const {
    syncStatuses,
    isSyncing,
    syncProduct,
    syncAllProducts,
    syncProductsWithGaps,
    clearAllSyncStatuses,
    getFailedSyncs,
    getSuccessfulSyncs,
  } = useStockSync();

  const {
    productsWithStock,
    stockCalculations,
    productsLoading,
  } = useStockStore();

  // Calculate sync statistics
  const totalProducts = productsWithStock.length;
  const syncedProducts = Object.values(syncStatuses).filter(s => s.status === 'success').length;
  const failedSyncs = getFailedSyncs();
  const successfulSyncs = getSuccessfulSyncs();
  const productsWithGaps = productsWithStock.filter(product => {
    const calculation = stockCalculations[product.id];
    return calculation && calculation.gap > 0;
  }).length;

  const syncProgress = totalProducts > 0 ? (syncedProducts / totalProducts) * 100 : 0;

  const handleSyncAll = async () => {
    try {
      await syncAllProducts();
    } catch (error) {
      console.error('Erro na sincronização em lote:', error);
    }
  };

  const handleSyncGaps = async () => {
    try {
      await syncProductsWithGaps();
    } catch (error) {
      console.error('Erro na sincronização de gaps:', error);
    }
  };

  const getSyncStatusIcon = (status: string) => {
    switch (status) {
      case 'syncing':
        return <RefreshCw className="h-4 w-4 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getSyncStatusBadge = (status: string) => {
    switch (status) {
      case 'syncing':
        return <Badge variant="secondary">Sincronizando</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Sucesso</Badge>;
      case 'error':
        return <Badge variant="destructive">Erro</Badge>;
      default:
        return <Badge variant="outline">Pendente</Badge>;
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <RefreshCw className="h-5 w-5" />
          Sincronização de Estoque
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{totalProducts}</div>
            <div className="text-sm text-muted-foreground">Total Produtos</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{syncedProducts}</div>
            <div className="text-sm text-muted-foreground">Sincronizados</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{failedSyncs.length}</div>
            <div className="text-sm text-muted-foreground">Falhas</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{productsWithGaps}</div>
            <div className="text-sm text-muted-foreground">Com Gaps</div>
          </div>
        </div>

        {/* Progress Bar */}
        {totalProducts > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progresso da Sincronização</span>
              <span>{Math.round(syncProgress)}%</span>
            </div>
            <Progress value={syncProgress} className="h-2" />
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            onClick={handleSyncAll}
            disabled={isSyncing || productsLoading || totalProducts === 0}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
            Sincronizar Todos
          </Button>
          
          <Button
            variant="outline"
            onClick={handleSyncGaps}
            disabled={isSyncing || productsLoading || productsWithGaps === 0}
            className="flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            Sincronizar Gaps ({productsWithGaps})
          </Button>
          
          <Button
            variant="ghost"
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            {showDetails ? 'Ocultar' : 'Ver'} Detalhes
          </Button>
          
          {Object.keys(syncStatuses).length > 0 && (
            <Button
              variant="ghost"
              onClick={clearAllSyncStatuses}
              disabled={isSyncing}
              className="text-muted-foreground"
            >
              Limpar Status
            </Button>
          )}
        </div>

        {/* Alerts */}
        {failedSyncs.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {failedSyncs.length} produto(s) falharam na sincronização. 
              Verifique os detalhes abaixo.
            </AlertDescription>
          </Alert>
        )}

        {successfulSyncs.length > 0 && !isSyncing && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              {successfulSyncs.length} produto(s) sincronizados com sucesso.
            </AlertDescription>
          </Alert>
        )}

        {/* Detailed Status */}
        {showDetails && Object.keys(syncStatuses).length > 0 && (
          <div className="space-y-2">
            <h4 className="font-medium">Status Detalhado:</h4>
            <div className="max-h-60 overflow-y-auto space-y-2">
              {Object.values(syncStatuses).map((status) => {
                const product = productsWithStock.find(p => p.id === status.productId);
                return (
                  <div
                    key={status.productId}
                    className="flex items-center justify-between p-2 border rounded-md"
                  >
                    <div className="flex items-center gap-2">
                      {getSyncStatusIcon(status.status)}
                      <div>
                        <div className="font-medium text-sm">
                          {product?.title || status.productId}
                        </div>
                        {status.message && (
                          <div className="text-xs text-muted-foreground">
                            {status.message}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getSyncStatusBadge(status.status)}
                      {status.lastSyncAt && (
                        <div className="text-xs text-muted-foreground">
                          {new Date(status.lastSyncAt).toLocaleTimeString()}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Empty State */}
        {totalProducts === 0 && !productsLoading && (
          <div className="text-center py-8 text-muted-foreground">
            <RefreshCw className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum produto encontrado para sincronização.</p>
            <p className="text-sm">Carregue produtos do Mercado Livre primeiro.</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default StockSyncManager;
