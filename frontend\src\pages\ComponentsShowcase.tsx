import React, { useState } from 'react';

import { But<PERSON> } from '../components/ui/button';
import { LoadingButton } from '../components/ui/Loading';
import { Input } from '../components/ui/input';
import { 
  Card, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter 
} from '../components/ui/Card';
import { 
  Loading, 
  Spinner, 
  Skeleton,
  CardSkeleton,
  StatSkeleton,
  TableSkeleton,
  ChartSkeleton,
  ListSkeleton,
} from '../components/ui/Loading';
import { 
  Modal, 
  ConfirmModal, 
  useModal 
} from '../components/ui/Modal';
import { 
  Toast, 
  ToastProvider, 
  useToast, 
  createToast 
} from '../components/ui/Toast';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../components/ui/Form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Field
} from '../components/ui/Field';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select-radix';
import {
  Checkbox
} from '../components/ui/Checkbox';
import {
  Radio
} from '../components/ui/Radio';
import {
  Switch
} from '../components/ui/Switch';
import { Container } from '../components/ui/Container';
import { Stack } from '../components/ui/Stack';
import { Grid } from '../components/ui/Grid';
import { Divider } from '../components/ui/Divider';
import { Spacer } from '../components/ui/Spacer';

import { 
  CalendarIcon, 
  UserIcon, 
  CogIcon,
  HeartIcon,
  StarIcon,
  ShoppingCartIcon,
  ChartBarIcon,
  PresentationChartLineIcon,
  EnvelopeIcon,
  LockClosedIcon,
  PhoneIcon,
  MapPinIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline';

// Zod schema for form validation
const formSchema = z.object({
  username: z.string().min(3, { message: 'Nome de usuário deve ter pelo menos 3 caracteres.' }).max(50, { message: 'Nome de usuário muito longo.' }),
  email: z.string().email({ message: 'E-mail inválido.' }),
  password: z.string().min(8, { message: 'Senha deve ter pelo menos 8 caracteres.' }),
  country: z.string().min(1, { message: 'Selecione um país.' }),
  newsletter: z.boolean().default(false),
  terms: z.boolean().refine(val => val === true, { message: 'Você deve aceitar os termos e condições.' }),
  gender: z.enum(["male", "female", "other"], { message: "Selecione um gênero." }),
  notifications: z.boolean().default(true),
});

type FormData = z.infer<typeof formSchema>;

export default function ComponentsShowcase() {
  const [inputValue, setInputValue] = useState('');
  const [inputState, setInputState] = useState<'default' | 'error' | 'success' | 'warning'>('default');
  const [isLoading, setIsLoading] = useState(false);
  const [loadingVariant, setLoadingVariant] = useState<'overlay' | 'inline' | 'compact' | 'card'>('inline');
  
  // Modal states
  const basicModal = useModal();
  const confirmModal = useModal();
  const fullscreenModal = useModal();
  
  // Toast
  const { addToast } = useToast();

  // Form with React Hook Form
  const formMethods = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      username: '',
      email: '',
      password: '',
      country: '',
      newsletter: false,
      terms: false,
      gender: undefined, // undefined for initial state of enum
      notifications: true,
    },
  });

  const onSubmit = (data: FormData) => {
    console.log('Form submitted:', data);
    addToast(createToast.success('Formulário Enviado!', 'Dados validados e enviados com sucesso.'));
  };

  const countryOptions = [
    { label: 'Selecione um país', value: '', disabled: true },
    { label: 'Brasil', value: 'BR' },
    { label: 'Estados Unidos', value: 'US' },
    { label: 'Canadá', value: 'CA' },
    { label: 'Alemanha', value: 'DE' },
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInputValue(value);
    
    if (value.length === 0) {
      setInputState('default');
    } else if (value.length < 3) {
      setInputState('error');
    } else if (value.includes('@')) {
      setInputState('success');
    } else if (value.length > 10) {
      setInputState('warning');
    } else {
      setInputState('default');
    }
  };

  const showToastExample = (variant: 'success' | 'error' | 'warning' | 'info' | 'default') => {
    const toasts = {
      success: createToast.success('Sucesso!', 'Operação realizada com sucesso.'),
      error: createToast.error('Erro!', 'Algo deu errado. Tente novamente.'),
      warning: createToast.warning('Atenção!', 'Verifique os dados informados.'),
      info: createToast.info('Informação', 'Nova atualização disponível.'),
      default: createToast.default('Notificação', 'Esta é uma mensagem padrão.'),
    };
    
    addToast(toasts[variant]);
  };

  const handleLoadingTest = () => {
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 3000);
  };

  return (
    <div className="min-h-screen bg-background py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Design System Magnow - Fase 3
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Demonstração completa dos componentes de formulário refatorados com CVA e design tokens.
          </p>
        </div>

        <div className="space-y-16">

          {/* Cards and other Phase 2 components */}
          <section>
            <h2 className="text-3xl font-semibold mb-8">Cards Refatorados</h2>
            
            <div className="space-y-8">
              {/* Card Variants */}
              <div>
                <h3 className="text-xl font-medium mb-4">Variants</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Card variant="default">
                    <CardHeader>
                      <CardTitle>Card Padrão</CardTitle>
                      <CardDescription>
                        Card com estilo padrão usando design tokens
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Este é um card padrão com shadow suave e hover effect.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button size="sm">Ação</Button>
                    </CardFooter>
                  </Card>

                  <Card variant="elevated">
                    <CardHeader>
                      <CardTitle>Card Elevado</CardTitle>
                      <CardDescription>
                        Card com maior elevação e destaque
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Perfeito para destacar conteúdo importante.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="primary" size="sm">Destaque</Button>
                    </CardFooter>
                  </Card>

                  <Card variant="success">
                    <CardHeader>
                      <CardTitle>Card Sucesso</CardTitle>
                      <CardDescription>
                        Card com tema de sucesso
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-success-700">
                        Ideal para mostrar estados positivos.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="success" size="sm">Confirmar</Button>
                    </CardFooter>
                  </Card>

                  <Card variant="warning">
                    <CardHeader>
                      <CardTitle>Card Atenção</CardTitle>
                      <CardDescription>
                        Card com tema de aviso
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-warning-700">
                        Para alertas e avisos importantes.
                      </p>
                    </CardContent>
                    <CardFooter>
                      <Button variant="warning" size="sm">Revisar</Button>
                    </CardFooter>
                  </Card>

                  <Card variant="interactive" onClick={() => showToastExample('info')}>
                    <CardHeader>
                      <CardTitle>Card Interativo</CardTitle>
                      <CardDescription>
                        Card clicável com focus ring
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Clique para ver o efeito interativo.
                      </p>
                    </CardContent>
                  </Card>

                  <Card variant="ghost">
                    <CardHeader>
                      <CardTitle>Card Ghost</CardTitle>
                      <CardDescription>
                        Card transparente com hover
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-muted-foreground">
                        Estilo minimalista e sutil.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>

              {/* Card Sizes and Padding */}
              <div>
                <h3 className="text-xl font-medium mb-4">Tamanhos e Padding</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card padding="sm" size="sm">
                    <CardHeader padding="sm">
                      <CardTitle>Compacto</CardTitle>
                    </CardHeader>
                    <CardContent padding="sm">
                      <p className="text-xs">Card pequeno com padding reduzido</p>
                    </CardContent>
                  </Card>

                  <Card padding="default" size="default">
                    <CardHeader>
                      <CardTitle>Padrão</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm">Card padrão com espaçamento normal</p>
                    </CardContent>
                  </Card>

                  <Card padding="lg" size="lg">
                    <CardHeader padding="lg">
                      <CardTitle>Grande</CardTitle>
                    </CardHeader>
                    <CardContent padding="lg">
                      <p className="text-base">Card grande com padding expandido</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </section>

          {/* Loading Components */}
          <section>
            <h2 className="text-3xl font-semibold mb-8">Loading Unificado</h2>
            
            <div className="space-y-8">
              {/* Spinner Variants */}
              <div>
                <h3 className="text-xl font-medium mb-4">Spinners</h3>
                <div className="flex items-center gap-6 p-6 bg-white rounded-lg border">
                  <div className="text-center">
                    <Spinner variant="primary" size="xs" />
                    <p className="text-xs mt-2">XS Primary</p>
                  </div>
                  <div className="text-center">
                    <Spinner variant="success" size="sm" />
                    <p className="text-xs mt-2">SM Success</p>
                  </div>
                  <div className="text-center">
                    <Spinner variant="warning" size="default" />
                    <p className="text-xs mt-2">Default Warning</p>
                  </div>
                  <div className="text-center">
                    <Spinner variant="danger" size="lg" />
                    <p className="text-xs mt-2">LG Danger</p>
                  </div>
                  <div className="text-center">
                    <Spinner variant="info" size="xl" />
                    <p className="text-xs mt-2">XL Info</p>
                  </div>
                </div>
              </div>

              {/* Loading Variants */}
              <div>
                <h3 className="text-xl font-medium mb-4">Loading States</h3>
                <div className="space-y-4">
                  <div className="flex gap-4 mb-4">
                    <Button onClick={() => setLoadingVariant('inline')}>Inline</Button>
                    <Button onClick={() => setLoadingVariant('compact')}>Compact</Button>
                    <Button onClick={() => setLoadingVariant('card')}>Card</Button>
                    <Button onClick={handleLoadingTest}>Test Loading (3s)</Button>
                  </div>

                  <div className="bg-white rounded-lg border p-6">
                    {isLoading ? (
                      <Loading 
                        variant={loadingVariant}
                        message="Carregando dados..."
                        spinner={{ variant: 'primary', size: 'lg' }}
                      />
                    ) : (
                      <Loading
                        variant={loadingVariant}
                        message="Estado de exemplo"
                        spinner={{ variant: 'muted', size: 'default' }}
                      />
                    )}
                  </div>
                </div>
              </div>

              {/* Loading Buttons */}
              <div>
                <h3 className="text-xl font-medium mb-4">Loading Buttons</h3>
                <div className="flex gap-4">
                  <LoadingButton 
                    loading={isLoading} 
                    onClick={handleLoadingTest}
                    className="bg-primary text-white px-4 py-2 rounded-md"
                    variant="primary"
                  >
                    Processar
                  </LoadingButton>
                  <LoadingButton 
                    loading={isLoading} 
                    onClick={handleLoadingTest}
                    className="bg-success text-white px-4 py-2 rounded-md"
                    variant="success"
                  >
                    Salvar
                  </LoadingButton>
                  <LoadingButton 
                    loading={isLoading} 
                    onClick={handleLoadingTest}
                    className="bg-danger text-white px-4 py-2 rounded-md"
                    variant="danger"
                  >
                    Deletar
                  </LoadingButton>
                </div>
              </div>

              {/* Skeletons */}
              <div>
                <h3 className="text-xl font-medium mb-4">Skeletons</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium mb-3">Card Skeleton</h4>
                    <CardSkeleton showAvatar={true} lines={4} />
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-3">Stat Skeleton</h4>
                    <StatSkeleton showIcon={true} />
                  </div>
                  
                  <div className="md:col-span-2">
                    <h4 className="text-sm font-medium mb-3">Table Skeleton</h4>
                    <TableSkeleton rows={3} columns={4} />
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-3">Chart Skeleton</h4>
                    <ChartSkeleton height="h-48" showLegend={true} />
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium mb-3">List Skeleton</h4>
                    <ListSkeleton items={3} showAvatar={true} showActions={true} />
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Modal Components */}
          <section>
            <h2 className="text-3xl font-semibold mb-8">Modal System</h2>
            
            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-medium mb-4">Modal Variants</h3>
                <div className="flex flex-wrap gap-4">
                  <Button onClick={basicModal.open}>
                    Modal Básico
                  </Button>
                  <Button variant="primary" onClick={confirmModal.open}>
                    Modal de Confirmação
                  </Button>
                  <Button variant="outline" onClick={fullscreenModal.open}>
                    Modal Fullscreen
                  </Button>
                </div>

                {/* Basic Modal */}
                <Modal
                  isOpen={basicModal.isOpen}
                  onClose={basicModal.close}
                  title="Modal de Exemplo"
                  description="Este é um modal básico com título e descrição"
                  size="lg"
                  footer={
                    <>
                      <Button variant="outline" onClick={basicModal.close}>
                        Cancelar
                      </Button>
                      <Button variant="primary" onClick={basicModal.close}>
                        Confirmar
                      </Button>
                    </>
                  }
                >
                  <div className="space-y-4">
                    <p>Este é o conteúdo do modal. Você pode incluir qualquer componente aqui.</p>
                    <Input
                      placeholder="Digite algo..."
                      leftIcon={UserIcon}
                    />
                    <div className="grid grid-cols-2 gap-4">
                      <Card variant="filled" padding="sm">
                        <CardContent padding="sm">
                          <p className="text-sm">Card dentro do modal</p>
                        </CardContent>
                      </Card>
                      <Card variant="outline" padding="sm">
                        <CardContent padding="sm">
                          <p className="text-sm">Outro card</p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                </Modal>

                {/* Confirm Modal */}
                <ConfirmModal
                  isOpen={confirmModal.isOpen}
                  onClose={confirmModal.close}
                  onConfirm={() => showToastExample('success')}
                  title="Confirmar Ação"
                  description="Tem certeza que deseja continuar? Esta ação não pode ser desfeita."
                  confirmText="Sim, continuar"
                  cancelText="Cancelar"
                  variant="danger"
                />

                {/* Fullscreen Modal */}
                <Modal
                  isOpen={fullscreenModal.isOpen}
                  onClose={fullscreenModal.close}
                  title="Modal Fullscreen"
                  description="Modal que ocupa toda a tela"
                  variant="fullscreen"
                  padding="xl"
                  footer={
                    <Button variant="primary" onClick={fullscreenModal.close}>
                      Fechar
                    </Button>
                  }
                >
                  <div className="space-y-8">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <Card variant="primary">
                        <CardHeader>
                          <CardTitle>Seção 1</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p>Conteúdo da primeira seção</p>
                        </CardContent>
                      </Card>
                      <Card variant="success">
                        <CardHeader>
                          <CardTitle>Seção 2</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p>Conteúdo da segunda seção</p>
                        </CardContent>
                      </Card>
                      <Card variant="info">
                        <CardHeader>
                          <CardTitle>Seção 3</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p>Conteúdo da terceira seção</p>
                        </CardContent>
                      </Card>
                    </div>
                    
                    <div className="bg-gray-100 p-6 rounded-lg">
                      <h4 className="font-medium mb-4">Área de conteúdo expandida</h4>
                      <p className="text-muted-foreground">
                        Este modal fullscreen permite exibir muito conteúdo sem limitações de espaço.
                        Perfeito para formulários complexos, dashboards ou interfaces detalhadas.
                      </p>
                    </div>
                  </div>
                </Modal>
              </div>
            </div>
          </section>

          {/* Toast Examples */}
          <section>
            <h2 className="text-3xl font-semibold mb-8">Toast System</h2>
            
            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-medium mb-4">Toast Variants</h3>
                <div className="flex flex-wrap gap-4">
                  <Button onClick={() => showToastExample('success')}>
                    Toast Sucesso
                  </Button>
                  <Button onClick={() => showToastExample('error')}>
                    Toast Erro
                  </Button>
                  <Button onClick={() => showToastExample('warning')}>
                    Toast Aviso
                  </Button>
                  <Button onClick={() => showToastExample('info')}>
                    Toast Info
                  </Button>
                  <Button onClick={() => showToastExample('default')}>
                    Toast Padrão
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-medium mb-4">Toast com Ação</h3>
                <Button 
                  variant="primary"
                  onClick={() => addToast({
                    variant: 'info',
                    title: 'Nova mensagem',
                    message: 'Você tem uma nova mensagem não lida.',
                    action: {
                      label: 'Ver agora',
                      onClick: () => showToastExample('success')
                    }
                  })}
                >
                  Toast com Ação
                </Button>
              </div>
            </div>
          </section>

          {/* Form Components (Phase 3) */}
          <section>
            <h2 className="text-3xl font-semibold mb-8">Componentes de Formulário</h2>
            <Card variant="elevated" padding="lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PresentationChartLineIcon className="h-5 w-5" />
                  Formulário de Cadastro
                </CardTitle>
                <CardDescription>
                  Exemplo completo de formulário com validação e integração de componentes.
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <Form formMethods={formMethods} onSubmit={onSubmit}>
                  <div className="space-y-6">
                    <Field name="username" label="Nome de Usuário" helpText="Seu nome público no sistema.">
                      <Input
                        placeholder="Ex: joaodasilva"
                        leftIcon={UserIcon}
                        {...formMethods.register('username')}
                        state={formMethods.formState.errors.username ? 'error' : formMethods.watch('username') ? 'success' : 'default'}
                        errorMessage={formMethods.formState.errors.username?.message}
                      />
                    </Field>

                    <Field name="email" label="E-mail" helpText="Usaremos para contato.">
                      <Input
                        placeholder="<EMAIL>"
                        leftIcon={EnvelopeIcon}
                        type="email"
                        {...formMethods.register('email')}
                        state={formMethods.formState.errors.email ? 'error' : formMethods.watch('email') ? 'success' : 'default'}
                        errorMessage={formMethods.formState.errors.email?.message}
                      />
                    </Field>

                    <Field name="password" label="Senha" helpText="Mínimo de 8 caracteres.">
                      <Input
                        placeholder="********"
                        leftIcon={LockClosedIcon}
                        type="password"
                        {...formMethods.register('password')}
                        state={formMethods.formState.errors.password ? 'error' : formMethods.watch('password') ? 'success' : 'default'}
                        errorMessage={formMethods.formState.errors.password?.message}
                      />
                    </Field>

                    <Field name="country" label="País" helpText="Selecione seu país de residência.">
                      <Select
                        value={formMethods.watch('country')}
                        onValueChange={(value) => formMethods.setValue('country', value, { shouldValidate: true })}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione..." />
                        </SelectTrigger>
                        <SelectContent>
                          {countryOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value} disabled={option.disabled}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </Field>

                    <Field name="gender" label="Gênero">
                      <div className="flex gap-4">
                        <Radio
                          name="gender"
                          value="male"
                          label="Masculino"
                          {...formMethods.register('gender')}
                        />
                        <Radio
                          name="gender"
                          value="female"
                          label="Feminino"
                          {...formMethods.register('gender')}
                        />
                        <Radio
                          name="gender"
                          value="other"
                          label="Outro"
                          {...formMethods.register('gender')}
                        />
                      </div>
                    </Field>

                    <Checkbox
                      name="newsletter"
                      label="Desejo receber novidades por e-mail"
                      description="Fique por dentro das últimas ofertas e atualizações."
                      {...formMethods.register('newsletter')}
                    />

                    <Switch
                      name="notifications"
                      label="Ativar notificações"
                      description="Receber alertas de sistema importantes."
                      checked={formMethods.watch('notifications')}
                      onCheckedChange={(checked) => formMethods.setValue('notifications', checked, { shouldValidate: true })}
                    />

                    <Checkbox
                      name="terms"
                      label="Concordo com os termos e condições"
                      description="Leia nossos termos para prosseguir."
                      {...formMethods.register('terms')}
                      state={formMethods.formState.errors.terms ? 'error' : 'default'}
                      errorMessage={formMethods.formState.errors.terms?.message}
                    />
                  </div>
                </Form>
              </CardContent>
              
              <CardFooter alignment="end">
                <Button type="submit" onClick={formMethods.handleSubmit(onSubmit)} variant="primary">
                  Cadastrar
                </Button>
              </CardFooter>
            </Card>
          </section>

          {/* Integration Example (Old) */}
          <section>
            <h2 className="text-3xl font-semibold mb-8">Exemplo de Integração (Antigo)</h2>
            
            <Card variant="elevated" padding="lg">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingCartIcon className="h-5 w-5" />
                  Formulário de Pedido
                </CardTitle>
                <CardDescription>
                  Exemplo prático integrando todos os componentes refatorados
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Nome completo"
                      placeholder="Digite seu nome"
                      leftIcon={UserIcon}
                      state={inputValue.length > 0 ? 'success' : 'default'}
                      value={inputValue}
                      onChange={handleInputChange}
                    />
                    <Input
                      label="E-mail"
                      placeholder="<EMAIL>"
                      type="email"
                      state={inputState}
                      errorMessage={inputState === 'error' ? 'E-mail deve ter pelo menos 3 caracteres' : undefined}
                      successMessage={inputState === 'success' ? 'E-mail válido' : undefined}
                      warningMessage={inputState === 'warning' ? 'E-mail muito longo' : undefined}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card variant="ghost" padding="sm">
                      <CardContent padding="sm" className="text-center">
                        <ChartBarIcon className="h-8 w-8 mx-auto mb-2 text-primary" />
                        <p className="text-sm font-medium">3 Produtos</p>
                        <p className="text-xs text-muted-foreground">No carrinho</p>
                      </CardContent>
                    </Card>
                    <Card variant="ghost" padding="sm">
                      <CardContent padding="sm" className="text-center">
                        <StarIcon className="h-8 w-8 mx-auto mb-2 text-warning" />
                        <p className="text-sm font-medium">4.8/5</p>
                        <p className="text-xs text-muted-foreground">Avaliação</p>
                      </CardContent>
                    </Card>
                    <Card variant="ghost" padding="sm">
                      <CardContent padding="sm" className="text-center">
                        <HeartIcon className="h-8 w-8 mx-auto mb-2 text-danger" />
                        <p className="text-sm font-medium">12 Favoritos</p>
                        <p className="text-xs text-muted-foreground">Salvos</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
              
              <CardFooter alignment="between">
                <div className="flex items-center gap-2">
                  <Spinner variant="muted" size="sm" />
                  <span className="text-sm text-muted-foreground">Calculando frete...</span>
                </div>
                <div className="flex gap-3">
                  <Button variant="outline">
                    Salvar Rascunho
                  </Button>
                  <LoadingButton
                    loading={isLoading}
                    onClick={() => {
                      handleLoadingTest();
                      setTimeout(() => showToastExample('success'), 3100);
                    }}
                    className="bg-primary text-white px-6 py-2 rounded-md font-medium"
                    variant="primary"
                  >
                    Finalizar Pedido
                  </LoadingButton>
                </div>
              </CardFooter>
            </Card>
          </section>

          {/* Layout Components */}
          <section>
            <h2 className="text-3xl font-semibold mb-8">Layout Components</h2>
            
            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-medium mb-4">Container</h3>
                <div className="mb-8 p-4 border border-design-neutral-300 rounded">
                  <Container size="md" padding="lg" className="bg-design-primary-100 text-design-primary-900 rounded">
                    <h4 className="font-medium text-lg mb-2">Medium Container (padding lg)</h4>
                    <p className="text-sm">
                      This content is inside a Container component with `size="md"` and `padding="lg"`.
                      It automatically centers and limits its width based on the defined breakpoints.
                    </p>
                  </Container>
                  <div className="mt-4"></div>
                  <Container size="sm" padding="md" className="bg-design-secondary-100 text-design-secondary-900 rounded">
                    <h4 className="font-medium text-lg mb-2">Small Container (padding md)</h4>
                    <p className="text-sm">
                      Another example with `size="sm"` and `padding="md"`.
                    </p>
                  </Container>
                </div>

                <h3 className="text-xl font-medium mb-4">Stack</h3>
                <div className="mb-8 p-4 border border-design-neutral-300 rounded">
                  <h4 className="font-medium text-lg mb-2">Column Stack (default)</h4>
                  <Stack className="border border-design-neutral-400 p-4 rounded">
                    <Button variant="primary">Item 1</Button>
                    <Button variant="secondary">Item 2</Button>
                    <Button variant="tertiary">Item 3</Button>
                  </Stack>

                  <h4 className="font-medium text-lg mb-2 mt-6">Row Stack with custom spacing</h4>
                  <Stack direction="row" spacing="xl" justify="center" align="center" className="border border-design-neutral-400 p-4 rounded">
                    <Button variant="primary">Item A</Button>
                    <Button variant="secondary">Item B</Button>
                    <Button variant="tertiary">Item C</Button>
                  </Stack>

                  <h4 className="font-medium text-lg mb-2 mt-6">Wrapping Stack</h4>
                  <Stack direction="row" spacing="md" wrap={true} className="w-64 border border-design-neutral-400 p-4 rounded">
                    <Button variant="primary" size="sm">Wrap 1</Button>
                    <Button variant="secondary" size="sm">Wrap 2</Button>
                    <Button variant="tertiary" size="sm">Wrap 3</Button>
                    <Button variant="primary" size="sm">Wrap 4</Button>
                    <Button variant="secondary" size="sm">Wrap 5</Button>
                    <Button variant="tertiary" size="sm">Wrap 6</Button>
                  </Stack>
                </div>

                <h3 className="text-lg font-semibold mb-4">Grid</h3>
                <div className="mb-8 p-4 border border-design-neutral-300 rounded">
                  <h4 className="font-medium text-lg mb-2">Basic Grid (3 columns, md gap)</h4>
                  <Grid cols={3} gap="md" className="border border-design-neutral-400 p-4 rounded">
                    <div className="bg-design-primary-200 p-4 rounded text-center">Item 1</div>
                    <div className="bg-design-primary-300 p-4 rounded text-center">Item 2</div>
                    <div className="bg-design-primary-400 p-4 rounded text-center">Item 3</div>
                    <div className="bg-design-primary-500 p-4 rounded text-center">Item 4</div>
                    <div className="bg-design-primary-600 p-4 rounded text-center">Item 5</div>
                    <div className="bg-design-primary-700 p-4 rounded text-center">Item 6</div>
                  </Grid>

                  <h4 className="font-medium text-lg mb-2 mt-6">Grid with custom gaps (2 columns)</h4>
                  <Grid cols={2} gapX="xl" gapY="lg" className="border border-design-neutral-400 p-4 rounded">
                    <div className="bg-design-secondary-200 p-4 rounded text-center">Item A</div>
                    <div className="bg-design-secondary-300 p-4 rounded text-center">Item B</div>
                    <div className="bg-design-secondary-400 p-4 rounded text-center">Item C</div>
                    <div className="bg-design-secondary-500 p-4 rounded text-center">Item D</div>
                  </Grid>
                </div>

                <h3 className="text-lg font-semibold mb-4">Divider</h3>
                <div className="mb-8 p-4 border border-design-neutral-300 rounded flex flex-col items-center">
                  <h4 className="font-medium text-lg mb-2">Horizontal Divider</h4>
                  <p className="text-sm">Content above</p>
                  <Divider className="my-4" />
                  <p className="text-sm">Content below</p>

                  <h4 className="font-medium text-lg mb-2 mt-6">Vertical Divider</h4>
                  <div className="flex h-20 items-center justify-center space-x-4">
                    <p className="text-sm">Left Content</p>
                    <Divider orientation="vertical" spacing="lg" />
                    <p className="text-sm">Right Content</p>
                  </div>
                </div>

                <h3 className="text-lg font-semibold mb-4">Spacer</h3>
                <div className="mb-8 p-4 border border-design-neutral-300 rounded flex flex-col items-center">
                  <h4 className="font-medium text-lg mb-2">Horizontal Spacer (axis="x")</h4>
                  <div className="flex items-center">
                    <p className="text-sm">Left Text</p>
                    <Spacer size="lg" axis="x" className="bg-design-primary-300" />
                    <p className="text-sm">Right Text</p>
                  </div>

                  <h4 className="font-medium text-lg mb-2 mt-6">Vertical Spacer (axis="y")</h4>
                  <div className="flex flex-col items-center">
                    <p className="text-sm">Top Text</p>
                    <Spacer size="xl" axis="y" className="bg-design-secondary-300" />
                    <p className="text-sm">Bottom Text</p>
                  </div>

                  <h4 className="font-medium text-lg mb-2 mt-6">Both Axis Spacer (default)</h4>
                  <div className="flex items-center">
                    <div className="bg-design-neutral-200 p-2">Block 1</div>
                    <Spacer size="md" className="bg-design-tertiary-300" />
                    <div className="bg-design-neutral-200 p-2">Block 2</div>
                  </div>
                </div>
              </div>
            </div>
          </section>

        </div>
      </div>
    </div>
  );
} 