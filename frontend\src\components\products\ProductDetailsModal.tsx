import React, { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { But<PERSON> } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import StockManager from './StockManager';
import StockIndicators from './StockIndicators';
import { 
  ExternalLink, 
  Package, 
  DollarSign, 
  TrendingUp,
  Calendar,
  BarChart3,
  History,
  Settings,
  RefreshCw
} from 'lucide-react';
import type { ProductWithStock, StockHistoryEntry } from '../../types/api';

interface ProductDetailsModalProps {
  product: ProductWithStock | null;
  isOpen: boolean;
  onClose: () => void;
  onUpdateStock?: (productId: string, quantity: number, reason?: string) => Promise<void>;
  onGetHistory?: (productId: string) => Promise<StockHistoryEntry[]>;
  onSyncProduct?: (productId: string) => Promise<void>;
}

export default function ProductDetailsModal({
  product,
  isOpen,
  onClose,
  onUpdateStock,
  onGetHistory,
  onSyncProduct
}: ProductDetailsModalProps) {
  const [activeTab, setActiveTab] = useState('overview');
  const [isUpdatingStock, setIsUpdatingStock] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setActiveTab('overview');
    }
  }, [isOpen]);

  if (!product) return null;

  const handleUpdateStock = async (productId: string, quantity: number, reason?: string) => {
    if (!onUpdateStock) return;
    setIsUpdatingStock(true);
    try {
      await onUpdateStock(productId, quantity, reason);
    } finally {
      setIsUpdatingStock(false);
    }
  };

  const handleSyncProduct = async () => {
    if (!onSyncProduct) return;
    setIsSyncing(true);
    try {
      await onSyncProduct(product.id);
    } finally {
      setIsSyncing(false);
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <DialogTitle className="text-lg line-clamp-2 mb-2">
                {product.title}
              </DialogTitle>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <span>ML ID: {product.mlId}</span>
                {product.sku && (
                  <>
                    <span>•</span>
                    <span>SKU: {product.sku}</span>
                  </>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2 ml-4">
              {onSyncProduct && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSyncProduct}
                  disabled={isSyncing}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isSyncing ? 'animate-spin' : ''}`} />
                  Sincronizar
                </Button>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(product.permalink, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Ver no ML
              </Button>
            </div>
          </div>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Visão Geral</TabsTrigger>
            <TabsTrigger value="stock">Estoque</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="history">Histórico</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Imagem do produto */}
              <Card>
                <CardContent className="pt-6">
                  {product.thumbnail ? (
                    <img
                      src={product.thumbnail}
                      alt={product.title}
                      className="w-full h-48 object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-full h-48 bg-muted rounded-lg flex items-center justify-center">
                      <Package className="h-16 w-16 text-muted-foreground" />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Informações básicas */}
              <Card className="md:col-span-2">
                <CardHeader>
                  <CardTitle className="text-base">Informações do Produto</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Preço</label>
                      <div className="text-lg font-semibold">{formatCurrency(product.price)}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Vendidos</label>
                      <div className="text-lg font-semibold">{product.soldQuantity}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Categoria</label>
                      <div className="text-sm">{product.category}</div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Condição</label>
                      <div className="text-sm capitalize">{product.condition}</div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2">
                    <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                      {product.status}
                    </Badge>
                    {product.brand && (
                      <Badge variant="outline">{product.brand}</Badge>
                    )}
                  </div>

                  <div className="pt-2 border-t">
                    <StockIndicators product={product} showDetailed size="md" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Métricas rápidas */}
            {product.metrics && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {product.metrics.averageDailySales && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-blue-500" />
                        <div>
                          <div className="text-2xl font-bold">
                            {product.metrics.averageDailySales.toFixed(1)}
                          </div>
                          <div className="text-xs text-muted-foreground">Vendas/dia</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {product.metrics.stockCoverageDays && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-green-500" />
                        <div>
                          <div className="text-2xl font-bold">
                            {product.metrics.stockCoverageDays}
                          </div>
                          <div className="text-xs text-muted-foreground">Dias cobertura</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {product.metrics.stockTurnover && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <BarChart3 className="h-4 w-4 text-purple-500" />
                        <div>
                          <div className="text-2xl font-bold">
                            {product.metrics.stockTurnover.toFixed(1)}x
                          </div>
                          <div className="text-xs text-muted-foreground">Giro/ano</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {product.metrics.profitMargin && (
                  <Card>
                    <CardContent className="pt-6">
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4 text-yellow-500" />
                        <div>
                          <div className="text-2xl font-bold">
                            {product.metrics.profitMargin.toFixed(1)}%
                          </div>
                          <div className="text-xs text-muted-foreground">Margem</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="stock" className="space-y-4">
            <StockManager
              product={product}
              onUpdateStock={handleUpdateStock}
              onGetHistory={onGetHistory}
              isUpdating={isUpdatingStock}
            />

            {/* Cálculos de estoque */}
            {product.stockCalculation && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Cálculos de Estoque</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Estoque Mínimo</label>
                      <div className="text-lg font-semibold">
                        {product.stockCalculation.minimumStock}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Estoque Máximo</label>
                      <div className="text-lg font-semibold">
                        {product.stockCalculation.maximumStock}
                      </div>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Ponto Reposição</label>
                      <div className="text-lg font-semibold">
                        {product.stockCalculation.reorderPoint}
                      </div>
                    </div>
                    {product.stockCalculation.gap > 0 && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Gap</label>
                        <div className="text-lg font-semibold text-red-600">
                          {product.stockCalculation.gap}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Performance de Vendas</CardTitle>
              </CardHeader>
              <CardContent>
                {product.recentSales && product.recentSales.length > 0 ? (
                  <div className="space-y-2">
                    {product.recentSales.slice(0, 5).map((sale, index) => (
                      <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                        <div>
                          <div className="font-medium">{sale.quantity} unidades</div>
                          <div className="text-sm text-muted-foreground">
                            {formatDate(sale.date)}
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-medium">{formatCurrency(sale.unitPrice)}</div>
                          <div className="text-sm text-muted-foreground">
                            Total: {formatCurrency(sale.unitPrice * sale.quantity)}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Nenhuma venda recente encontrada</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Histórico de Alterações</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8 text-muted-foreground">
                  <History className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Histórico será carregado quando disponível</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
