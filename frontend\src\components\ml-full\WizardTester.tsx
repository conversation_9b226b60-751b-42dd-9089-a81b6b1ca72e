import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { useMLFullWizardStore } from '../../store/mlFullWizardStore';
import { MLFullValidationSystem } from '../../utils/validationSystem';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Play, 
  RotateCcw,
  FileText,
  Package,
  Settings,
  Upload,
  TestTube
} from 'lucide-react';

interface TestResult {
  name: string;
  status: 'passed' | 'failed' | 'warning';
  message: string;
  details?: string;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  passed: number;
  failed: number;
  warnings: number;
}

export default function WizardTester() {
  const [testResults, setTestResults] = useState<TestSuite[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  const {
    loadProducts,
    resetWizard,
    selectedProducts,
    availableProducts,
    productQuantities,
    generatedFile,
    uploadedPDF,
    validateStep,
    selectProduct,
    updateQuantity,
    generateSpreadsheet,
  } = useMLFullWizardStore();

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    const suites: TestSuite[] = [];

    // Test Suite 1: Data Loading
    setCurrentTest('Testando carregamento de dados...');
    const dataLoadingSuite = await testDataLoading();
    suites.push(dataLoadingSuite);

    // Test Suite 2: Product Selection
    setCurrentTest('Testando seleção de produtos...');
    const productSelectionSuite = await testProductSelection();
    suites.push(productSelectionSuite);

    // Test Suite 3: Quantity Configuration
    setCurrentTest('Testando configuração de quantidades...');
    const quantityConfigSuite = await testQuantityConfiguration();
    suites.push(quantityConfigSuite);

    // Test Suite 4: Validation System
    setCurrentTest('Testando sistema de validação...');
    const validationSuite = await testValidationSystem();
    suites.push(validationSuite);

    // Test Suite 5: File Generation
    setCurrentTest('Testando geração de arquivos...');
    const fileGenerationSuite = await testFileGeneration();
    suites.push(fileGenerationSuite);

    setTestResults(suites);
    setIsRunning(false);
    setCurrentTest('');
  };

  const testDataLoading = async (): Promise<TestSuite> => {
    const tests: TestResult[] = [];
    
    try {
      // Reset wizard first
      resetWizard();
      await new Promise(resolve => setTimeout(resolve, 100));

      // Test product loading
      await loadProducts();
      await new Promise(resolve => setTimeout(resolve, 500));

      if (availableProducts.length > 0) {
        tests.push({
          name: 'Carregamento de produtos',
          status: 'passed',
          message: `${availableProducts.length} produtos carregados com sucesso`,
        });
      } else {
        tests.push({
          name: 'Carregamento de produtos',
          status: 'failed',
          message: 'Nenhum produto foi carregado',
        });
      }

      // Test product eligibility
      const eligibleProducts = availableProducts.filter(p => p.isEligible);
      if (eligibleProducts.length > 0) {
        tests.push({
          name: 'Produtos elegíveis',
          status: 'passed',
          message: `${eligibleProducts.length} produtos elegíveis encontrados`,
        });
      } else {
        tests.push({
          name: 'Produtos elegíveis',
          status: 'warning',
          message: 'Nenhum produto elegível encontrado',
        });
      }

      // Test product data integrity
      const productsWithAllData = availableProducts.filter(p => 
        p.title && p.price > 0 && p.availableQuantity >= 0
      );
      
      if (productsWithAllData.length === availableProducts.length) {
        tests.push({
          name: 'Integridade dos dados',
          status: 'passed',
          message: 'Todos os produtos têm dados completos',
        });
      } else {
        tests.push({
          name: 'Integridade dos dados',
          status: 'warning',
          message: `${availableProducts.length - productsWithAllData.length} produtos com dados incompletos`,
        });
      }

    } catch (error) {
      tests.push({
        name: 'Carregamento de dados',
        status: 'failed',
        message: 'Erro durante carregamento',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return {
      name: 'Carregamento de Dados',
      tests,
      passed: tests.filter(t => t.status === 'passed').length,
      failed: tests.filter(t => t.status === 'failed').length,
      warnings: tests.filter(t => t.status === 'warning').length,
    };
  };

  const testProductSelection = async (): Promise<TestSuite> => {
    const tests: TestResult[] = [];

    try {
      // Test selecting products
      const eligibleProducts = availableProducts.filter(p => p.isEligible).slice(0, 3);
      
      if (eligibleProducts.length === 0) {
        tests.push({
          name: 'Seleção de produtos',
          status: 'failed',
          message: 'Nenhum produto elegível disponível para teste',
        });
        return {
          name: 'Seleção de Produtos',
          tests,
          passed: 0,
          failed: 1,
          warnings: 0,
        };
      }

      // Select products
      eligibleProducts.forEach(product => {
        selectProduct(product.id);
      });

      await new Promise(resolve => setTimeout(resolve, 100));

      if (selectedProducts.length === eligibleProducts.length) {
        tests.push({
          name: 'Seleção de produtos',
          status: 'passed',
          message: `${selectedProducts.length} produtos selecionados corretamente`,
        });
      } else {
        tests.push({
          name: 'Seleção de produtos',
          status: 'failed',
          message: `Esperado ${eligibleProducts.length}, selecionado ${selectedProducts.length}`,
        });
      }

      // Test step 1 validation
      const step1Validation = validateStep(1);
      if (step1Validation.isValid) {
        tests.push({
          name: 'Validação Step 1',
          status: 'passed',
          message: 'Validação do step 1 passou',
        });
      } else {
        tests.push({
          name: 'Validação Step 1',
          status: 'failed',
          message: `Validação falhou: ${step1Validation.errors.map(e => e.message).join(', ')}`,
        });
      }

    } catch (error) {
      tests.push({
        name: 'Seleção de produtos',
        status: 'failed',
        message: 'Erro durante seleção',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return {
      name: 'Seleção de Produtos',
      tests,
      passed: tests.filter(t => t.status === 'passed').length,
      failed: tests.filter(t => t.status === 'failed').length,
      warnings: tests.filter(t => t.status === 'warning').length,
    };
  };

  const testQuantityConfiguration = async (): Promise<TestSuite> => {
    const tests: TestResult[] = [];

    try {
      // Test quantity updates
      const selectedProductIds = selectedProducts.slice(0, 2);
      
      selectedProductIds.forEach(productId => {
        const product = availableProducts.find(p => p.id === productId);
        if (product) {
          const quantity = Math.min(5, product.availableQuantity);
          updateQuantity(productId, quantity);
        }
      });

      await new Promise(resolve => setTimeout(resolve, 100));

      // Check if quantities were set
      const quantitiesSet = selectedProductIds.every(id => 
        productQuantities[id] && productQuantities[id].quantity > 0
      );

      if (quantitiesSet) {
        tests.push({
          name: 'Configuração de quantidades',
          status: 'passed',
          message: 'Quantidades configuradas corretamente',
        });
      } else {
        tests.push({
          name: 'Configuração de quantidades',
          status: 'failed',
          message: 'Falha ao configurar quantidades',
        });
      }

      // Test step 2 validation
      const step2Validation = validateStep(2);
      if (step2Validation.isValid) {
        tests.push({
          name: 'Validação Step 2',
          status: 'passed',
          message: 'Validação do step 2 passou',
        });
      } else {
        tests.push({
          name: 'Validação Step 2',
          status: 'failed',
          message: `Validação falhou: ${step2Validation.errors.map(e => e.message).join(', ')}`,
        });
      }

    } catch (error) {
      tests.push({
        name: 'Configuração de quantidades',
        status: 'failed',
        message: 'Erro durante configuração',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return {
      name: 'Configuração de Quantidades',
      tests,
      passed: tests.filter(t => t.status === 'passed').length,
      failed: tests.filter(t => t.status === 'failed').length,
      warnings: tests.filter(t => t.status === 'warning').length,
    };
  };

  const testValidationSystem = async (): Promise<TestSuite> => {
    const tests: TestResult[] = [];

    try {
      // Test validation with valid data
      const validProducts = availableProducts.filter(p => p.isEligible).slice(0, 2);
      const validQuantities: Record<string, any> = {};
      
      validProducts.forEach(product => {
        validQuantities[product.id] = {
          productId: product.id,
          quantity: Math.min(3, product.availableQuantity),
          isValid: true,
        };
      });

      const validationResult = MLFullValidationSystem.validateQuantityConfiguration(
        validProducts,
        validQuantities
      );

      if (validationResult.isValid) {
        tests.push({
          name: 'Validação com dados válidos',
          status: 'passed',
          message: 'Sistema de validação funcionando corretamente',
        });
      } else {
        tests.push({
          name: 'Validação com dados válidos',
          status: 'failed',
          message: 'Validação falhou com dados válidos',
        });
      }

      // Test validation with invalid data
      const invalidQuantities: Record<string, any> = {};
      validProducts.forEach(product => {
        invalidQuantities[product.id] = {
          productId: product.id,
          quantity: product.availableQuantity + 100, // Exceeds stock
          isValid: false,
        };
      });

      const invalidValidationResult = MLFullValidationSystem.validateQuantityConfiguration(
        validProducts,
        invalidQuantities
      );

      if (!invalidValidationResult.isValid && invalidValidationResult.errors.length > 0) {
        tests.push({
          name: 'Validação com dados inválidos',
          status: 'passed',
          message: 'Sistema detectou dados inválidos corretamente',
        });
      } else {
        tests.push({
          name: 'Validação com dados inválidos',
          status: 'failed',
          message: 'Sistema não detectou dados inválidos',
        });
      }

    } catch (error) {
      tests.push({
        name: 'Sistema de validação',
        status: 'failed',
        message: 'Erro no sistema de validação',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return {
      name: 'Sistema de Validação',
      tests,
      passed: tests.filter(t => t.status === 'passed').length,
      failed: tests.filter(t => t.status === 'failed').length,
      warnings: tests.filter(t => t.status === 'warning').length,
    };
  };

  const testFileGeneration = async (): Promise<TestSuite> => {
    const tests: TestResult[] = [];

    try {
      // Test spreadsheet generation
      await generateSpreadsheet('excel');
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait for generation

      if (generatedFile) {
        tests.push({
          name: 'Geração de planilha',
          status: 'passed',
          message: `Planilha gerada: ${generatedFile.filename}`,
        });
      } else {
        tests.push({
          name: 'Geração de planilha',
          status: 'failed',
          message: 'Falha na geração da planilha',
        });
      }

      // Test step 3 validation
      const step3Validation = validateStep(3);
      if (step3Validation.isValid) {
        tests.push({
          name: 'Validação Step 3',
          status: 'passed',
          message: 'Validação do step 3 passou',
        });
      } else {
        tests.push({
          name: 'Validação Step 3',
          status: 'failed',
          message: `Validação falhou: ${step3Validation.errors.map(e => e.message).join(', ')}`,
        });
      }

    } catch (error) {
      tests.push({
        name: 'Geração de arquivos',
        status: 'failed',
        message: 'Erro durante geração',
        details: error instanceof Error ? error.message : 'Erro desconhecido',
      });
    }

    return {
      name: 'Geração de Arquivos',
      tests,
      passed: tests.filter(t => t.status === 'passed').length,
      failed: tests.filter(t => t.status === 'failed').length,
      warnings: tests.filter(t => t.status === 'warning').length,
    };
  };

  const getStatusIcon = (status: 'passed' | 'failed' | 'warning') => {
    switch (status) {
      case 'passed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: 'passed' | 'failed' | 'warning') => {
    const variants = {
      passed: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      warning: 'bg-yellow-100 text-yellow-800',
    };

    return (
      <Badge className={variants[status]}>
        {status === 'passed' ? 'Passou' : status === 'failed' ? 'Falhou' : 'Aviso'}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <TestTube className="h-6 w-6" />
            Testes do Sistema ML Full
          </h2>
          <p className="text-muted-foreground">
            Validação completa do fluxo do wizard e funcionalidades
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            onClick={() => resetWizard()}
            variant="outline"
            disabled={isRunning}
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={runTests}
            disabled={isRunning}
          >
            <Play className="h-4 w-4 mr-2" />
            {isRunning ? 'Executando...' : 'Executar Testes'}
          </Button>
        </div>
      </div>

      {/* Current Test */}
      {isRunning && currentTest && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="animate-spin">
                <TestTube className="h-5 w-5 text-blue-500" />
              </div>
              <span className="font-medium">{currentTest}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {testResults.map((suite, index) => (
        <Card key={index}>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>{suite.name}</span>
              <div className="flex gap-2">
                {suite.passed > 0 && (
                  <Badge className="bg-green-100 text-green-800">
                    {suite.passed} passou
                  </Badge>
                )}
                {suite.warnings > 0 && (
                  <Badge className="bg-yellow-100 text-yellow-800">
                    {suite.warnings} aviso
                  </Badge>
                )}
                {suite.failed > 0 && (
                  <Badge className="bg-red-100 text-red-800">
                    {suite.failed} falhou
                  </Badge>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {suite.tests.map((test, testIndex) => (
                <div key={testIndex} className="flex items-start gap-3 p-3 border rounded-lg">
                  {getStatusIcon(test.status)}
                  <div className="flex-grow">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{test.name}</span>
                      {getStatusBadge(test.status)}
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {test.message}
                    </p>
                    {test.details && (
                      <p className="text-xs text-red-600 mt-1 font-mono">
                        {test.details}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Summary */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Resumo dos Testes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {testResults.reduce((sum, suite) => sum + suite.passed, 0)}
                </div>
                <div className="text-sm text-green-700">Testes Passaram</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">
                  {testResults.reduce((sum, suite) => sum + suite.warnings, 0)}
                </div>
                <div className="text-sm text-yellow-700">Avisos</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {testResults.reduce((sum, suite) => sum + suite.failed, 0)}
                </div>
                <div className="text-sm text-red-700">Testes Falharam</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
