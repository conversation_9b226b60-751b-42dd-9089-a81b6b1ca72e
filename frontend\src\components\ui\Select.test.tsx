 
import { render, screen, fireEvent } from '@testing-library/react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './Select';
import { describe, it, expect, vi } from 'vitest';
import userEvent from '@testing-library/user-event';

describe('Select', () => {
  it('should render a select trigger with a placeholder', () => {
    render(
      <Select>
        <SelectTrigger>
          <SelectValue placeholder="Selecione uma opção" />
        </SelectTrigger>
      </Select>
    );
    expect(screen.getByText('Selecione uma opção')).toBeInTheDocument();
  });

  it('should open the content when trigger is clicked', async () => {
    render(
      <Select>
        <SelectTrigger>
          <SelectValue placeholder="Selecione" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Opção 1</SelectItem>
        </SelectContent>
      </Select>
    );
    await userEvent.click(screen.getByRole('button', { name: /selecione/i }));
    expect(screen.getByText('Opção 1')).toBeInTheDocument();
  });

  it('should display selected value after an item is clicked', async () => {
    render(
      <Select>
        <SelectTrigger>
          <SelectValue placeholder="Selecione" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Opção 1</SelectItem>
          <SelectItem value="option2">Opção 2</SelectItem>
        </SelectContent>
      </Select>
    );
    await userEvent.click(screen.getByRole('button', { name: /selecione/i }));
    await userEvent.click(screen.getByText('Opção 2'));
    expect(screen.getByText('Opção 2')).toBeInTheDocument();
    expect(screen.queryByText('Selecione')).not.toBeInTheDocument();
  });

  it('should call onValueChange when a new item is selected', async () => {
    const handleValueChange = vi.fn();
    render(
      <Select onValueChange={handleValueChange}>
        <SelectTrigger>
          <SelectValue placeholder="Selecione" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="option1">Opção 1</SelectItem>
          <SelectItem value="option2">Opção 2</SelectItem>
        </SelectContent>
      </Select>
    );
    await userEvent.click(screen.getByRole('button', { name: /selecione/i }));
    await userEvent.click(screen.getByText('Opção 2'));
    expect(handleValueChange).toHaveBeenCalledTimes(1);
    expect(handleValueChange).toHaveBeenCalledWith('option2');
  });

  it('should be disabled when disabled prop is true', () => {
    render(
      <Select disabled>
        <SelectTrigger>
          <SelectValue placeholder="Disabled" />
        </SelectTrigger>
      </Select>
    );
    expect(screen.getByRole('button', { name: /disabled/i })).toBeDisabled();
  });
}); 
