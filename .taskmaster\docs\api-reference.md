# API Reference - Magnow

## Sistema de Controle Inteligente de Estoque para Mercado Livre

### Versão da API: v1.0.0

---

## 📋 Índice

1. [Autenticação](#autenticação)
2. [Estrutura de Resposta](#estrutura-de-resposta)
3. [Códigos de Status](#códigos-de-status)
4. [Rate Limiting](#rate-limiting)
5. [Endpoints](#endpoints)
   - [Auth](#auth)
   - [<PERSON>tas ML](#contas-ml)
   - [Produtos](#produtos)
   - [Cálculos](#cálculos)
   - [Planilhas](#planilhas)
   - [Dashboard](#dashboard)

---

## 🔐 Autenticação

### <PERSON><PERSON> as requisições (exceto login/register) devem incluir o header:
```
Authorization: Bearer {access_token}
```

### Tenant Context
O tenant é automaticamente detectado pelo token JWT. Não é necessário enviá-lo manualmente.

---

## 📝 Estrutura de Resposta

### Sucesso
```json
{
  "success": true,
  "data": {
    // dados da resposta
  },
  "meta": {
    "timestamp": "2025-01-08T10:30:00Z",
    "page": 1,
    "totalPages": 5,
    "totalItems": 100
  }
}
```

### Erro
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Dados de entrada inválidos",
    "details": [
      {
        "field": "email",
        "message": "Email é obrigatório"
      }
    ]
  },
  "meta": {
    "timestamp": "2025-01-08T10:30:00Z"
  }
}
```

---

## 🚦 Códigos de Status

| Código | Significado |
|--------|-------------|
| 200 | OK - Sucesso |
| 201 | Created - Recurso criado |
| 400 | Bad Request - Dados inválidos |
| 401 | Unauthorized - Token inválido/expirado |
| 403 | Forbidden - Sem permissão |
| 404 | Not Found - Recurso não encontrado |
| 429 | Too Many Requests - Rate limit excedido |
| 500 | Internal Server Error - Erro interno |

---

## ⏱️ Rate Limiting

- **Limite geral**: 100 requisições por minuto por usuário
- **Login**: 5 tentativas por minuto por IP
- **Sincronização**: 10 operações por minuto por tenant

Headers de resposta:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 85
X-RateLimit-Reset: 1609459200
```

---

## 🚀 Endpoints

## Auth

### POST /api/auth/login
Autentica um usuário no sistema.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "senhaSegura123"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "name": "Nome do Usuário",
      "role": "admin",
      "tenantId": "uuid"
    },
    "tokens": {
      "accessToken": "jwt_token_here",
      "refreshToken": "refresh_token_here",
      "expiresIn": 900
    }
  }
}
```

### POST /api/auth/register
Registra um novo usuário (apenas admins).

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "senhaSegura123",
  "name": "Nome Completo",
  "role": "user"
}
```

### POST /api/auth/refresh
Atualiza o token de acesso.

**Request Body:**
```json
{
  "refreshToken": "refresh_token_here"
}
```

### POST /api/auth/logout
Invalida os tokens do usuário.

---

## Contas ML

### GET /api/ml-accounts
Lista todas as contas do Mercado Livre conectadas.

**Query Parameters:**
- `page` (number): Página atual (default: 1)
- `limit` (number): Items por página (default: 20)
- `status` (string): Filtrar por status (active, inactive)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "userId": "ML_USER_123",
      "nickname": "LOJA_EXEMPLO",
      "email": "<EMAIL>",
      "isActive": true,
      "lastSync": "2025-01-08T10:00:00Z",
      "productsCount": 150,
      "createdAt": "2025-01-01T00:00:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "totalPages": 1,
    "totalItems": 1
  }
}
```

### POST /api/ml-accounts/connect
Inicia o processo de conexão OAuth com o Mercado Livre.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "authUrl": "https://auth.mercadolivre.com.br/authorization?...",
    "state": "random_state_string"
  }
}
```

### POST /api/ml-accounts/callback
Processa o callback do OAuth (uso interno).

### PUT /api/ml-accounts/:id/refresh
Force refresh dos tokens de uma conta.

### DELETE /api/ml-accounts/:id
Remove uma conta do Mercado Livre.

---

## Produtos

### GET /api/products
Lista todos os produtos sincronizados.

**Query Parameters:**
- `page` (number): Página atual
- `limit` (number): Items por página (max: 100)
- `search` (string): Buscar por título ou SKU
- `accountId` (string): Filtrar por conta ML
- `status` (string): active, paused, closed
- `hasStock` (boolean): Produtos com/sem estoque

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "mlItemId": "MLB123456789",
      "sku": "PROD-001",
      "title": "Produto Exemplo",
      "currentStock": 25,
      "price": 99.90,
      "status": "active",
      "categoryId": "MLB5672",
      "mlAccount": {
        "id": "uuid",
        "nickname": "LOJA_EXEMPLO"
      },
      "stockCalculation": {
        "idealStock": 50,
        "gapQuantity": 25,
        "lastCalculated": "2025-01-08T09:00:00Z"
      },
      "lastSync": "2025-01-08T10:00:00Z"
    }
  ]
}
```

### GET /api/products/:id
Obtém detalhes de um produto específico.

### POST /api/products/sync
Sincroniza produtos de todas as contas ativas.

**Request Body (opcional):**
```json
{
  "accountIds": ["uuid1", "uuid2"],
  "forceRefresh": true
}
```

### PUT /api/products/:id
Atualiza dados locais de um produto.

**Request Body:**
```json
{
  "sku": "NOVO-SKU",
  "customFields": {
    "supplier": "Fornecedor A",
    "minStock": 10
  }
}
```

---

## Cálculos

### GET /api/calculations
Lista cálculos de estoque realizados.

**Query Parameters:**
- `productId` (string): Filtrar por produto
- `dateFrom` (date): Data inicial
- `dateTo` (date): Data final
- `hasGap` (boolean): Apenas com gap

### POST /api/calculations/calculate
Executa cálculo de estoque para produtos.

**Request Body:**
```json
{
  "productIds": ["uuid1", "uuid2"],
  "settings": {
    "periodDays": 30,
    "coverageDays": 15,
    "safetyStock": 5
  }
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "jobId": "calc-uuid",
    "status": "processing",
    "productsCount": 2,
    "estimatedTime": 120
  }
}
```

### GET /api/calculations/gaps
Lista produtos com gap de estoque.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "productId": "uuid",
      "product": {
        "sku": "PROD-001",
        "title": "Produto Exemplo"
      },
      "currentStock": 10,
      "idealStock": 35,
      "gapQuantity": 25,
      "priority": "high",
      "averageSales": 2.5,
      "daysOfStock": 4
    }
  ]
}
```

### GET /api/calculations/job/:jobId
Verifica status de um job de cálculo.

---

## Planilhas

### GET /api/spreadsheets
Lista planilhas geradas.

### POST /api/spreadsheets/generate
Gera nova planilha de envio.

**Request Body:**
```json
{
  "name": "Envio Janeiro 2025",
  "includeProducts": ["uuid1", "uuid2"],
  "warehouse": "Principal",
  "settings": {
    "minGap": 5,
    "maxQuantity": 100,
    "adjustments": {
      "uuid1": 30
    }
  }
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Envio Janeiro 2025",
    "status": "generated",
    "productsCount": 25,
    "totalQuantity": 500,
    "downloadUrl": "/api/spreadsheets/uuid/download",
    "createdAt": "2025-01-08T10:30:00Z"
  }
}
```

### GET /api/spreadsheets/:id/download
Faz download da planilha.

**Response:** Arquivo Excel (.xlsx)

### PUT /api/spreadsheets/:id
Atualiza planilha (apenas status e nome).

### DELETE /api/spreadsheets/:id
Remove planilha.

---

## Dashboard

### GET /api/dashboard/stats
Estatísticas gerais do dashboard.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "overview": {
      "totalProducts": 150,
      "activeAccounts": 3,
      "productsWithGap": 35,
      "totalGapValue": 15420.50
    },
    "stockHealth": {
      "healthy": 85,
      "warning": 30,
      "critical": 35
    },
    "recentActivity": {
      "lastSync": "2025-01-08T10:00:00Z",
      "lastCalculation": "2025-01-08T09:30:00Z",
      "lastSpreadsheet": "2025-01-07T16:45:00Z"
    }
  }
}
```

### GET /api/dashboard/charts/sales
Dados para gráfico de vendas.

**Query Parameters:**
- `period` (string): 7d, 30d, 90d (default: 30d)
- `productIds` (array): Filtrar produtos específicos

### GET /api/dashboard/charts/stock
Dados para gráfico de evolução do estoque.

### GET /api/dashboard/alerts
Lista alertas ativos do sistema.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "type": "stock_low",
      "severity": "high",
      "message": "Produto PROD-001 com estoque crítico (2 unidades)",
      "productId": "uuid",
      "createdAt": "2025-01-08T08:00:00Z",
      "isRead": false
    }
  ]
}
```

---

## 🔧 Webhooks (Futuro)

### POST /api/webhooks/ml
Webhook do Mercado Livre para notificações.

**Eventos suportados:**
- `orders` - Nova venda
- `items` - Alteração em produto
- `questions` - Nova pergunta

---

## 📊 Health Check

### GET /api/health
Verifica saúde da aplicação.

**Response (200):**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-08T10:30:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "mercadolivre": "healthy"
  },
  "uptime": 86400
}
```

---

## 📚 Códigos de Erro

### Autenticação
- `AUTH_001` - Token inválido
- `AUTH_002` - Token expirado
- `AUTH_003` - Credenciais inválidas
- `AUTH_004` - Usuário inativo

### Validação
- `VAL_001` - Campo obrigatório
- `VAL_002` - Formato inválido
- `VAL_003` - Valor fora do range

### Negócio
- `BUS_001` - Conta ML não encontrada
- `BUS_002` - Produto não sincronizado
- `BUS_003` - Cálculo em andamento
- `BUS_004` - Limite de contas atingido

### Sistema
- `SYS_001` - Erro de conexão com ML
- `SYS_002` - Erro no banco de dados
- `SYS_003` - Serviço indisponível

---

**Última atualização:** Janeiro 2025
**Versão da API:** 1.0.0 