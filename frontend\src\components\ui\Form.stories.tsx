
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useForm,
  zodResolver,
  z,
} from './Form';
import { Button } from './Button';
import { Input } from './Input';
import { Field } from './Field';
import { Stack } from './Stack';

const meta = {
  title: 'Components/Form',
  component: Form,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof Form>;

export default meta;
type Story = StoryObj<typeof meta>;

const formSchema = z.object({
  username: z.string().min(2, { message: 'Nome de usuário deve ter pelo menos 2 caracteres.' }),
  email: z.string().email({ message: 'Email inválido.' }),
  password: z.string().min(6, { message: '<PERSON>ha deve ter pelo menos 6 caracteres.' }),
});

export const LoginForm: Story = {
  render: () => {
    const form = useForm<z.infer<typeof formSchema>>({
      resolver: zodResolver(formSchema),
      defaultValues: {
        username: '',
        email: '',
        password: '',
      },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
      console.log(values);
      alert(JSON.stringify(values, null, 2));
    }

    return (
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-96 p-6 border rounded-lg shadow-md">
          <h3 className="text-xl font-semibold text-center mb-6">Login</h3>
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nome de Usuário</FormLabel>
                <FormControl>
                  <Input placeholder="shadcn" {...field} />
                </FormControl>
                <FormDescription>
                  Este é o seu nome de exibição público.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Senha</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="********" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <Button type="submit" className="w-full mt-6">Entrar</Button>
        </form>
      </Form>
    );
  },
};

export const FieldComponent: Story = {
  render: () => (
    <Stack spacing="md" className="w-96 p-6 border rounded-lg shadow-md">
      <h3 className="text-xl font-semibold text-center mb-6">Field Component Examples</h3>
      <Field label="Nome Completo" placeholder="Digite seu nome" />
      <Field label="Telefone" type="tel" helpText="Inclua o DDD." />
      <Field label="Email" type="email" errorText="Email inválido." status="error" />
      <Field label="Endereço" defaultValue="Rua Exemplo, 123" disabled />
    </Stack>
  ),
}; 
