/**
 * Componente de Preview de Planilhas
 * Sistema Magnow - Interface Aprimorada
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { <PERSON><PERSON> } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { ScrollArea } from '../ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Alert, AlertDescription } from '../ui/alert';
import { Progress } from '../ui/progress';
import { 
  Eye, 
  Download, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  Info,
  FileSpreadsheet,
  BarChart3,
  Package,
  Warehouse,
  TrendingUp,
  AlertCircle
} from 'lucide-react';

interface PreviewData {
  totalProducts: number;
  totalQuantity: number;
  estimatedFileSize: string;
  warehouseBreakdown: WarehouseBreakdown[];
  sampleRows: PreviewRow[];
  columnInfo: ColumnInfo[];
  validationResults: ValidationResults;
  generationTime: number;
  cacheHit: boolean;
}

interface WarehouseBreakdown {
  warehouseCode: string;
  warehouseName: string;
  productCount: number;
  totalQuantity: number;
  totalValue: number;
  percentage: number;
}

interface PreviewRow {
  id: string;
  sku: string;
  title: string;
  quantity: number;
  price: number;
  warehouseName: string;
  category: string;
  hasIssues: boolean;
  issues?: string[];
}

interface ColumnInfo {
  name: string;
  displayName: string;
  dataType: string;
  isRequired: boolean;
  sampleValue: string;
  validationRules: string[];
}

interface ValidationResults {
  totalErrors: number;
  totalWarnings: number;
  errors: ValidationIssue[];
  warnings: ValidationIssue[];
  summary: {
    missingRequiredFields: number;
    invalidDataTypes: number;
    duplicateSkus: number;
    outOfRangeValues: number;
  };
}

interface ValidationIssue {
  type: 'error' | 'warning';
  field: string;
  message: string;
  affectedRows: number;
  severity: 'low' | 'medium' | 'high';
}

interface SpreadsheetPreviewProps {
  previewData: PreviewData | null;
  isLoading: boolean;
  onRefresh: () => void;
  onGenerate: () => void;
  onClose: () => void;
  config: {
    format: 'xlsx' | 'csv';
    templateName: string;
    splitByWarehouse: boolean;
    includeImages: boolean;
  };
}

const SpreadsheetPreview: React.FC<SpreadsheetPreviewProps> = ({
  previewData,
  isLoading,
  onRefresh,
  onGenerate,
  onClose,
  config
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showAllIssues, setShowAllIssues] = useState(false);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12 space-y-4">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
          <FileSpreadsheet className="w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-blue-500" />
        </div>
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900">Gerando Preview</h3>
          <p className="text-gray-600">Analisando dados e validando informações...</p>
        </div>
        <Progress value={65} className="w-64" />
      </div>
    );
  }

  if (!previewData) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Nenhum dado para preview</h3>
        <p className="text-gray-600 mb-6">Não foi possível gerar o preview da planilha.</p>
        <Button onClick={onRefresh} variant="outline">
          <RefreshCw className="w-4 h-4 mr-2" />
          Tentar Novamente
        </Button>
      </div>
    );
  }

  const hasIssues = previewData.validationResults.totalErrors > 0 || previewData.validationResults.totalWarnings > 0;
  const canGenerate = previewData.validationResults.totalErrors === 0;

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* Estatísticas Principais */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Package className="w-8 h-8 text-blue-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-600">
              {previewData.totalProducts.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Produtos</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <BarChart3 className="w-8 h-8 text-green-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-600">
              {previewData.totalQuantity.toLocaleString()}
            </div>
            <div className="text-sm text-gray-600">Quantidade</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <FileSpreadsheet className="w-8 h-8 text-purple-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-purple-600">
              {previewData.estimatedFileSize}
            </div>
            <div className="text-sm text-gray-600">Tamanho Est.</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-orange-500 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-600">
              {previewData.generationTime}ms
            </div>
            <div className="text-sm text-gray-600">Tempo Proc.</div>
          </CardContent>
        </Card>
      </div>

      {/* Configurações da Planilha */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileSpreadsheet className="w-5 h-5 mr-2" />
            Configurações da Planilha
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Template:</span>
                <Badge variant="outline">{config.templateName}</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Formato:</span>
                <Badge>{config.format.toUpperCase()}</Badge>
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Dividir por armazém:</span>
                <Badge variant={config.splitByWarehouse ? "default" : "secondary"}>
                  {config.splitByWarehouse ? 'Sim' : 'Não'}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Incluir imagens:</span>
                <Badge variant={config.includeImages ? "default" : "secondary"}>
                  {config.includeImages ? 'Sim' : 'Não'}
                </Badge>
              </div>
            </div>
          </div>
          
          {previewData.cacheHit && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
                <span className="text-sm text-green-700">Dados obtidos do cache (processamento otimizado)</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Status de Validação */}
      {hasIssues && (
        <Alert className={previewData.validationResults.totalErrors > 0 ? "border-red-200 bg-red-50" : "border-yellow-200 bg-yellow-50"}>
          <AlertTriangle className={`h-4 w-4 ${previewData.validationResults.totalErrors > 0 ? 'text-red-500' : 'text-yellow-500'}`} />
          <AlertDescription>
            <div className="space-y-1">
              {previewData.validationResults.totalErrors > 0 && (
                <div className="text-red-700">
                  <strong>{previewData.validationResults.totalErrors} erro(s)</strong> encontrado(s) que impedem a geração.
                </div>
              )}
              {previewData.validationResults.totalWarnings > 0 && (
                <div className="text-yellow-700">
                  <strong>{previewData.validationResults.totalWarnings} aviso(s)</strong> encontrado(s).
                </div>
              )}
            </div>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );

  const renderWarehousesTab = () => (
    <div className="space-y-4">
      <div className="grid gap-4">
        {previewData.warehouseBreakdown.map((warehouse, index) => (
          <Card key={warehouse.warehouseCode}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center">
                  <Warehouse className="w-5 h-5 text-blue-500 mr-2" />
                  <div>
                    <h4 className="font-semibold">{warehouse.warehouseName}</h4>
                    <p className="text-sm text-gray-600">{warehouse.warehouseCode}</p>
                  </div>
                </div>
                <Badge variant="outline">{warehouse.percentage.toFixed(1)}%</Badge>
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <div className="text-gray-600">Produtos</div>
                  <div className="font-semibold">{warehouse.productCount.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-gray-600">Quantidade</div>
                  <div className="font-semibold">{warehouse.totalQuantity.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-gray-600">Valor Total</div>
                  <div className="font-semibold">R$ {warehouse.totalValue.toLocaleString()}</div>
                </div>
              </div>
              
              <div className="mt-3">
                <Progress value={warehouse.percentage} className="h-2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderSampleTab = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Amostra de Dados</h3>
        <Badge variant="outline">
          {previewData.sampleRows.length} de {previewData.totalProducts} produtos
        </Badge>
      </div>
      
      <ScrollArea className="h-96 border rounded-md">
        <div className="p-4">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b">
                <th className="text-left p-2 font-medium">SKU</th>
                <th className="text-left p-2 font-medium">Produto</th>
                <th className="text-left p-2 font-medium">Qtd</th>
                <th className="text-left p-2 font-medium">Preço</th>
                <th className="text-left p-2 font-medium">Armazém</th>
                <th className="text-left p-2 font-medium">Status</th>
              </tr>
            </thead>
            <tbody>
              {previewData.sampleRows.map((row, index) => (
                <tr key={row.id} className={`border-b ${row.hasIssues ? 'bg-red-50' : index % 2 === 0 ? 'bg-gray-50' : ''}`}>
                  <td className="p-2 font-mono text-xs">{row.sku}</td>
                  <td className="p-2 max-w-48 truncate" title={row.title}>{row.title}</td>
                  <td className="p-2">{row.quantity.toLocaleString()}</td>
                  <td className="p-2">R$ {row.price.toFixed(2)}</td>
                  <td className="p-2">{row.warehouseName}</td>
                  <td className="p-2">
                    {row.hasIssues ? (
                      <Badge variant="destructive" className="text-xs">
                        {row.issues?.length} problema(s)
                      </Badge>
                    ) : (
                      <Badge variant="default" className="text-xs">OK</Badge>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </ScrollArea>
    </div>
  );

  const renderValidationTab = () => (
    <div className="space-y-6">
      {/* Resumo de Validação */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">
              {previewData.validationResults.totalErrors}
            </div>
            <div className="text-sm text-gray-600">Erros</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {previewData.validationResults.totalWarnings}
            </div>
            <div className="text-sm text-gray-600">Avisos</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {previewData.validationResults.summary.duplicateSkus}
            </div>
            <div className="text-sm text-gray-600">SKUs Duplicados</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">
              {previewData.validationResults.summary.missingRequiredFields}
            </div>
            <div className="text-sm text-gray-600">Campos Obrigatórios</div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Problemas */}
      {hasIssues && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Problemas Encontrados</span>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setShowAllIssues(!showAllIssues)}
              >
                {showAllIssues ? 'Mostrar Menos' : 'Mostrar Todos'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {/* Erros */}
              {previewData.validationResults.errors.slice(0, showAllIssues ? undefined : 3).map((error, index) => (
                <div key={`error-${index}`} className="flex items-start p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="w-4 h-4 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="font-medium text-red-800">{error.message}</div>
                    <div className="text-sm text-red-600">Campo: {error.field} • {error.affectedRows} linha(s) afetada(s)</div>
                  </div>
                  <Badge variant="destructive" className="text-xs">
                    {error.severity}
                  </Badge>
                </div>
              ))}
              
              {/* Avisos */}
              {previewData.validationResults.warnings.slice(0, showAllIssues ? undefined : 3).map((warning, index) => (
                <div key={`warning-${index}`} className="flex items-start p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                  <AlertTriangle className="w-4 h-4 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="flex-1">
                    <div className="font-medium text-yellow-800">{warning.message}</div>
                    <div className="text-sm text-yellow-600">Campo: {warning.field} • {warning.affectedRows} linha(s) afetada(s)</div>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {warning.severity}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Preview da Planilha</h2>
          <p className="text-gray-600">Revise os dados antes de gerar a planilha final</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Atualizar
          </Button>
          <Button variant="outline" onClick={onClose}>
            Fechar
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Visão Geral</TabsTrigger>
          <TabsTrigger value="warehouses">Armazéns</TabsTrigger>
          <TabsTrigger value="sample">Amostra</TabsTrigger>
          <TabsTrigger value="validation" className={hasIssues ? "text-red-600" : ""}>
            Validação
            {hasIssues && (
              <Badge variant="destructive" className="ml-2 text-xs">
                {previewData.validationResults.totalErrors + previewData.validationResults.totalWarnings}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">{renderOverviewTab()}</TabsContent>
        <TabsContent value="warehouses">{renderWarehousesTab()}</TabsContent>
        <TabsContent value="sample">{renderSampleTab()}</TabsContent>
        <TabsContent value="validation">{renderValidationTab()}</TabsContent>
      </Tabs>

      {/* Botões de Ação */}
      <div className="flex items-center justify-between pt-6 border-t">
        <div className="flex items-center space-x-2">
          {!canGenerate && (
            <Alert className="border-red-200 bg-red-50">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-700">
                Corrija os erros de validação antes de gerar a planilha.
              </AlertDescription>
            </Alert>
          )}
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button 
            onClick={onGenerate}
            disabled={!canGenerate}
            className={canGenerate ? "" : "opacity-50 cursor-not-allowed"}
          >
            <Download className="w-4 h-4 mr-2" />
            Gerar Planilha
          </Button>
        </div>
      </div>
    </div>
  );
};

export default SpreadsheetPreview;