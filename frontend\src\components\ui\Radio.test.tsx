 
import { render, screen, fireEvent } from '@testing-library/react';
import { RadioGroup, RadioGroupItem } from './Radio';
import { describe, it, expect } from 'vitest';

describe('RadioGroup', () => {
  it('should render a radio group with items', () => {
    render(
      <RadioGroup defaultValue="option-a">
        <RadioGroupItem value="option-a" id="option-a" label="Option A" />
        <RadioGroupItem value="option-b" id="option-b" label="Option B" />
      </RadioGroup>
    );
    expect(screen.getByLabelText('Option A')).toBeInTheDocument();
    expect(screen.getByLabelText('Option B')).toBeInTheDocument();
  });

  it('should select the default value', () => {
    render(
      <RadioGroup defaultValue="option-a">
        <RadioGroupItem value="option-a" id="option-a" label="Option A" />
        <RadioGroupItem value="option-b" id="option-b" label="Option B" />
      </RadioGroup>
    );
    expect(screen.getByLabelText('Option A')).toBeChecked();
    expect(screen.getByLabelText('Option B')).not.toBeChecked();
  });

  it('should change selection on click', () => {
    render(
      <RadioGroup>
        <RadioGroupItem value="option-a" id="option-a" label="Option A" />
        <RadioGroupItem value="option-b" id="option-b" label="Option B" />
      </RadioGroup>
    );
    const optionB = screen.getByLabelText('Option B');
    fireEvent.click(optionB);
    expect(optionB).toBeChecked();
    expect(screen.getByLabelText('Option A')).not.toBeChecked();
  });

  it('should call onValueChange when selection changes', () => {
    const handleChange = vi.fn();
    render(
      <RadioGroup onValueChange={handleChange}>
        <RadioGroupItem value="option-a" id="option-a" label="Option A" />
        <RadioGroupItem value="option-b" id="option-b" label="Option B" />
      </RadioGroup>
    );
    fireEvent.click(screen.getByLabelText('Option B'));
    expect(handleChange).toHaveBeenCalledTimes(1);
    expect(handleChange).toHaveBeenCalledWith('option-b');
  });

  it('should disable radio items when disabled prop is true on item', () => {
    render(
      <RadioGroup>
        <RadioGroupItem value="option-a" id="option-a" label="Option A" disabled />
      </RadioGroup>
    );
    expect(screen.getByLabelText('Option A')).toBeDisabled();
  });

  it('should apply custom variant classes to radio item', () => {
    render(
      <RadioGroup>
        <RadioGroupItem value="option-a" id="option-a" label="Option A" variant="primary" data-testid="radio-item" />
      </RadioGroup>
    );
    const radioItem = screen.getByTestId('radio-item');
    expect(radioItem).toHaveClass('text-design-primary-600');
  });

  it('should apply custom size classes to radio item', () => {
    render(
      <RadioGroup>
        <RadioGroupItem value="option-a" id="option-a" label="Option A" size="lg" data-testid="radio-item" />
      </RadioGroup>
    );
    const radioItem = screen.getByTestId('radio-item');
    expect(radioItem).toHaveClass('h-6');
    expect(radioItem).toHaveClass('w-6');
  });
}); 
