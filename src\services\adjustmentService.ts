import { ProductAdjustment, ShippingProduct } from "../types/stock";

/**
 * Histórico de ajustes para auditoria
 */
interface AdjustmentHistory {
  id: string;
  tenantId: string;
  sku: string;
  action: 'include' | 'exclude' | 'modify';
  oldQuantity?: number;
  newQuantity?: number;
  oldWarehouse?: string;
  newWarehouse?: string;
  reason?: string;
  appliedBy: string;
  appliedAt: Date;
}

/**
 * Resultado da aplicação de ajustes
 */
interface AdjustmentResult {
  adjustedProducts: ShippingProduct[];
  appliedAdjustments: ProductAdjustment[];
  skippedAdjustments: ProductAdjustment[];
  errors: string[];
  warnings: string[];
}

/**
 * Serviço para aplicação de ajustes manuais
 * nos produtos antes da geração da planilha
 */
class AdjustmentService {
  private adjustmentHistory: Map<string, AdjustmentHistory[]> = new Map();

  /**
   * Aplica ajustes aos produtos
   */
  async applyAdjustments(
    products: ShippingProduct[],
    adjustments: ProductAdjustment[],
    tenantId: string,
    userId: string
  ): Promise<AdjustmentResult> {
    const result: AdjustmentResult = {
      adjustedProducts: [...products],
      appliedAdjustments: [],
      skippedAdjustments: [],
      errors: [],
      warnings: []
    };

    for (const adjustment of adjustments) {
      try {
        const applied = await this.applyAdjustment(
          result.adjustedProducts,
          adjustment,
          tenantId,
          userId
        );

        if (applied) {
          result.appliedAdjustments.push(adjustment);
        } else {
          result.skippedAdjustments.push(adjustment);
          result.warnings.push(`Ajuste para SKU ${adjustment.sku} foi ignorado`);
        }
      } catch (error) {
        result.skippedAdjustments.push(adjustment);
        result.errors.push(
          `Erro ao aplicar ajuste para SKU ${adjustment.sku}: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
        );
      }
    }

    return result;
  }

  /**
   * Aplica um ajuste específico
   */
  private async applyAdjustment(
    products: ShippingProduct[],
    adjustment: ProductAdjustment,
    tenantId: string,
    userId: string
  ): Promise<boolean> {
    const productIndex = products.findIndex(p => p.sku === adjustment.sku);

    switch (adjustment.action) {
      case 'include':
        return this.includeProduct(products, adjustment, tenantId, userId);
      
      case 'exclude':
        return this.excludeProduct(products, adjustment, tenantId, userId);
      
      case 'modify':
        return this.modifyProduct(products, productIndex, adjustment, tenantId, userId);
      
      default:
        throw new Error(`Ação de ajuste inválida: ${adjustment.action}`);
    }
  }

  /**
   * Inclui um novo produto
   */
  private includeProduct(
    products: ShippingProduct[],
    adjustment: ProductAdjustment,
    tenantId: string,
    userId: string
  ): boolean {
    // Verificar se o produto já existe
    const existingProduct = products.find(p => p.sku === adjustment.sku);
    if (existingProduct) {
      return false; // Produto já existe, não incluir
    }

    // Criar novo produto baseado no ajuste
    const newProduct: ShippingProduct = {
      sku: adjustment.sku,
      title: `Produto ${adjustment.sku}`,
      quantity: adjustment.newQuantity || 0,
      price: 0,
      category: '',
      description: '',
      warehouseId: adjustment.newWarehouse || 'default'
    };

    products.push(newProduct);

    // Registrar no histórico
    this.addToHistory({
      id: this.generateId(),
      tenantId,
      sku: adjustment.sku,
      action: 'include',
      newQuantity: newProduct.quantity,
      newWarehouse: newProduct.warehouseId,
      reason: adjustment.reason,
      appliedBy: userId,
      appliedAt: new Date()
    });

    return true;
  }

  /**
   * Exclui um produto
   */
  private excludeProduct(
    products: ShippingProduct[],
    adjustment: ProductAdjustment,
    tenantId: string,
    userId: string
  ): boolean {
    const productIndex = products.findIndex(p => p.sku === adjustment.sku);
    if (productIndex === -1) {
      return false; // Produto não encontrado
    }

    const removedProduct = products[productIndex];
    products.splice(productIndex, 1);

    // Registrar no histórico
    this.addToHistory({
      id: this.generateId(),
      tenantId,
      sku: adjustment.sku,
      action: 'exclude',
      oldQuantity: removedProduct.quantity,
      oldWarehouse: removedProduct.warehouseId,
      reason: adjustment.reason,
      appliedBy: userId,
      appliedAt: new Date()
    });

    return true;
  }

  /**
   * Modifica um produto existente
   */
  private modifyProduct(
    products: ShippingProduct[],
    productIndex: number,
    adjustment: ProductAdjustment,
    tenantId: string,
    userId: string
  ): boolean {
    if (productIndex === -1) {
      return false; // Produto não encontrado
    }

    const product = products[productIndex];
    const oldQuantity = product.quantity;
    const oldWarehouse = product.warehouseId;

    // Aplicar modificações
    if (adjustment.newQuantity !== undefined) {
      product.quantity = adjustment.newQuantity;
    }
    if (adjustment.newWarehouse) {
      product.warehouseId = adjustment.newWarehouse;
    }

    // Registrar no histórico
    this.addToHistory({
      id: this.generateId(),
      tenantId,
      sku: adjustment.sku,
      action: 'modify',
      oldQuantity,
      newQuantity: product.quantity,
      oldWarehouse,
      newWarehouse: product.warehouseId,
      reason: adjustment.reason,
      appliedBy: userId,
      appliedAt: new Date()
    });

    return true;
  }

  /**
   * Obtém histórico de ajustes
   */
  getAdjustmentHistory(
    tenantId: string,
    sku?: string,
    limit: number = 50
  ): AdjustmentHistory[] {
    const history = this.adjustmentHistory.get(tenantId) || [];
    
    let filtered = history;
    if (sku) {
      filtered = history.filter(h => h.sku === sku);
    }

    return filtered
      .sort((a, b) => b.appliedAt.getTime() - a.appliedAt.getTime())
      .slice(0, limit);
  }

  /**
   * Valida ajustes antes da aplicação
   */
  validateAdjustments(
    adjustments: ProductAdjustment[]
  ): { valid: ProductAdjustment[]; invalid: { adjustment: ProductAdjustment; errors: string[] }[] } {
    const valid: ProductAdjustment[] = [];
    const invalid: { adjustment: ProductAdjustment; errors: string[] }[] = [];

    for (const adjustment of adjustments) {
      const errors = this.validateAdjustment(adjustment);
      
      if (errors.length === 0) {
        valid.push(adjustment);
      } else {
        invalid.push({ adjustment, errors });
      }
    }

    return { valid, invalid };
  }

  /**
   * Valida um ajuste específico
   */
  private validateAdjustment(adjustment: ProductAdjustment): string[] {
    const errors: string[] = [];

    // Validações básicas
    if (!adjustment.sku || adjustment.sku.trim() === '') {
      errors.push('SKU é obrigatório');
    }

    if (!['include', 'exclude', 'modify'].includes(adjustment.action)) {
      errors.push('Ação deve ser include, exclude ou modify');
    }

    // Validações específicas por ação
    switch (adjustment.action) {
      case 'include':
        if (adjustment.newQuantity === undefined || adjustment.newQuantity < 0) {
          errors.push('Quantidade deve ser informada e não negativa para inclusão');
        }
        break;

      case 'modify':
        if (adjustment.newQuantity !== undefined && adjustment.newQuantity < 0) {
          errors.push('Nova quantidade não pode ser negativa');
        }
        break;
    }

    return errors;
  }

  /**
   * Limpa histórico antigo
   */
  cleanupHistory(tenantId: string, olderThanDays: number = 30): number {
    const history = this.adjustmentHistory.get(tenantId) || [];
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const filtered = history.filter(h => h.appliedAt > cutoffDate);
    const removedCount = history.length - filtered.length;

    this.adjustmentHistory.set(tenantId, filtered);
    return removedCount;
  }

  /**
   * Adiciona entrada ao histórico
   */
  private addToHistory(entry: AdjustmentHistory): void {
    const history = this.adjustmentHistory.get(entry.tenantId) || [];
    history.push(entry);
    this.adjustmentHistory.set(entry.tenantId, history);
  }

  /**
   * Gera ID único
   */
  private generateId(): string {
    return `adj-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Obtém estatísticas de ajustes
   */
  getAdjustmentStatistics(tenantId: string): {
    totalAdjustments: number;
    includeCount: number;
    excludeCount: number;
    modifyCount: number;
    lastAdjustment?: Date;
  } {
    const history = this.adjustmentHistory.get(tenantId) || [];
    
    const stats = {
      totalAdjustments: history.length,
      includeCount: history.filter(h => h.action === 'include').length,
      excludeCount: history.filter(h => h.action === 'exclude').length,
      modifyCount: history.filter(h => h.action === 'modify').length,
      lastAdjustment: history.length > 0 
        ? new Date(Math.max(...history.map(h => h.appliedAt.getTime())))
        : undefined
    };

    return stats;
  }
}

export const adjustmentService = new AdjustmentService();
export { AdjustmentService };
export type { AdjustmentHistory, AdjustmentResult };