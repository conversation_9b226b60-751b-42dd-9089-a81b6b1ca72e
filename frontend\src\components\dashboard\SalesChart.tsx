import React from 'react';
import { CustomLineChart, CustomAreaChart } from '../ui/Chart';
import { Card } from '../ui/Card';
import { CalendarIcon, ArrowTrendingUpIcon, ArrowTrendingDownIcon } from '@heroicons/react/24/outline';

interface SalesDataPoint {
  date: string;
  sales: number;
  revenue: number;
  orders: number;
}

interface SalesChartProps {
  data: SalesDataPoint[];
  period: '7d' | '30d' | '90d' | '1y';
  loading?: boolean;
  onPeriodChange?: (period: '7d' | '30d' | '90d' | '1y') => void;
}

export const SalesChart: React.FC<SalesChartProps> = ({
  data,
  period,
  loading = false,
  onPeriodChange
}) => {
  const periodLabels = {
    '7d': 'Últimos 7 dias',
    '30d': 'Últimos 30 dias',
    '90d': 'Últimos 90 dias',
    '1y': 'Último ano'
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
      minimumFractionDigits: 0
    }).format(value);
  };

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    if (period === '7d' || period === '30d') {
      return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
    }
    return date.toLocaleDateString('pt-BR', { month: 'short', year: '2-digit' });
  };

  // Calcular estatísticas do período
  const totalRevenue = data.reduce((sum, item) => sum + item.revenue, 0);
  const totalOrders = data.reduce((sum, item) => sum + item.orders, 0);
  const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

  // Calcular tendência (comparação com período anterior)
  const currentPeriodRevenue = data.slice(-Math.floor(data.length / 2)).reduce((sum, item) => sum + item.revenue, 0);
  const previousPeriodRevenue = data.slice(0, Math.floor(data.length / 2)).reduce((sum, item) => sum + item.revenue, 0);
  const revenueGrowth = previousPeriodRevenue > 0 
    ? ((currentPeriodRevenue - previousPeriodRevenue) / previousPeriodRevenue) * 100 
    : 0;

  const chartData = data.map(item => ({
    ...item,
    formattedDate: formatDate(item.date),
    revenueFormatted: formatCurrency(item.revenue)
  }));

  return (
    <Card
      title="Vendas e Faturamento"
      subtitle={`Análise de ${periodLabels[period].toLowerCase()}`}
      loading={loading}
      className="h-full"
      actions={
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          <select
            value={period}
            onChange={(e) => onPeriodChange?.(e.target.value as any)}
            className="text-sm border-border bg-background text-foreground rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="7d">7 dias</option>
            <option value="30d">30 dias</option>
            <option value="90d">90 dias</option>
            <option value="1y">1 ano</option>
          </select>
        </div>
      }
    >
      <div className="space-y-6">
        {/* Estatísticas resumidas */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {formatCurrency(totalRevenue)}
            </div>
            <div className="text-sm text-blue-600 dark:text-blue-400">Faturamento Total</div>
          </div>
          <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="text-lg font-bold text-green-600 dark:text-green-400">{totalOrders}</div>
            <div className="text-sm text-green-600 dark:text-green-400">Pedidos</div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-950/20 rounded-lg border border-purple-200 dark:border-purple-800">
            <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
              {formatCurrency(averageOrderValue)}
            </div>
            <div className="text-sm text-purple-600 dark:text-purple-400">Ticket Médio</div>
          </div>
        </div>

        {/* Indicador de crescimento */}
        <div className="flex items-center justify-center gap-2 p-3 bg-muted/30 rounded-lg">
          {revenueGrowth >= 0 ? (
            <ArrowTrendingUpIcon className="h-5 w-5 text-green-500 dark:text-green-400" />
          ) : (
            <ArrowTrendingDownIcon className="h-5 w-5 text-red-500 dark:text-red-400" />
          )}
          <span className={`font-medium ${revenueGrowth >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
            {revenueGrowth >= 0 ? '+' : ''}{revenueGrowth.toFixed(1)}%
          </span>
          <span className="text-muted-foreground text-sm">vs período anterior</span>
        </div>

        {/* Gráfico de faturamento */}
        <div className="h-64">
          <h4 className="text-sm font-medium text-foreground mb-3">Faturamento por Período</h4>
          <CustomAreaChart
            data={chartData}
            xKey="formattedDate"
            dataKey="revenue"
            color="#3B82F6"
            formatValue={formatCurrency}
            height={200}
          />
        </div>

        {/* Gráfico de pedidos */}
        <div className="h-64">
          <h4 className="text-sm font-medium text-foreground mb-3">Número de Pedidos</h4>
          <CustomLineChart
            data={chartData}
            xKey="formattedDate"
            dataKey="orders"
            color="#10B981"
            height={200}
          />
        </div>
      </div>
    </Card>
  );
};

export default SalesChart;