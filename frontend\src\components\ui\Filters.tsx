import React, { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';

// Tipos para filtros
export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface FilterGroup {
  id: string;
  label: string;
  type: 'select' | 'multiselect' | 'range' | 'date' | 'toggle';
  options?: FilterOption[];
  value?: any;
  placeholder?: string;
  min?: number;
  max?: number;
}

export interface ActiveFilter {
  groupId: string;
  groupLabel: string;
  value: any;
  label: string;
}

// Props para os componentes
interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  onClear?: () => void;
}

interface FilterDropdownProps {
  group: FilterGroup;
  onChange: (groupId: string, value: any) => void;
  className?: string;
}

interface ActiveFiltersProps {
  filters: ActiveFilter[];
  onRemove: (groupId: string) => void;
  onClearAll: () => void;
  className?: string;
}

interface FilterPanelProps {
  groups: FilterGroup[];
  onChange: (groupId: string, value: any) => void;
  isOpen: boolean;
  onToggle: () => void;
  className?: string;
}

// Componente de barra de pesquisa
export const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Pesquisar...",
  className = "",
  onClear
}) => {
  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
      </div>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      />
      {value && onClear && (
        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
          <button
            onClick={onClear}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>
      )}
    </div>
  );
};

// Componente de dropdown para filtros individuais
export const FilterDropdown: React.FC<FilterDropdownProps> = ({
  group,
  onChange,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleOptionSelect = (optionValue: string) => {
    if (group.type === 'multiselect') {
      const currentValues = Array.isArray(group.value) ? group.value : [];
      const newValues = currentValues.includes(optionValue)
        ? currentValues.filter((v: string) => v !== optionValue)
        : [...currentValues, optionValue];
      onChange(group.id, newValues);
    } else {
      onChange(group.id, optionValue);
      setIsOpen(false);
    }
  };

  const getDisplayValue = () => {
    if (!group.value) return group.placeholder || `Selecionar ${group.label}`;
    
    if (group.type === 'multiselect' && Array.isArray(group.value)) {
      if (group.value.length === 0) return group.placeholder || `Selecionar ${group.label}`;
      if (group.value.length === 1) {
        const option = group.options?.find(o => o.value === group.value[0]);
        return option?.label || group.value[0];
      }
      return `${group.value.length} selecionados`;
    }
    
    const option = group.options?.find(o => o.value === group.value);
    return option?.label || group.value;
  };

  const isSelected = (optionValue: string) => {
    if (group.type === 'multiselect' && Array.isArray(group.value)) {
      return group.value.includes(optionValue);
    }
    return group.value === optionValue;
  };

  return (
    <div className={`relative ${className}`}>
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="relative w-full bg-white border border-gray-300 rounded-md shadow-sm pl-3 pr-10 py-2 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
      >
        <span className="block truncate text-sm">
          {getDisplayValue()}
        </span>
        <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <ChevronDownIcon className="h-5 w-5 text-gray-400" />
        </span>
      </button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute z-20 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none">
            {group.options?.map((option) => (
              <div
                key={option.value}
                onClick={() => handleOptionSelect(option.value)}
                className={`cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-50 ${
                  isSelected(option.value) ? 'bg-blue-50 text-blue-900' : 'text-gray-900'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="block truncate text-sm">
                    {option.label}
                  </span>
                  {option.count !== undefined && (
                    <span className="text-xs text-gray-500">
                      ({option.count})
                    </span>
                  )}
                </div>
                {isSelected(option.value) && (
                  <span className="absolute inset-y-0 right-0 flex items-center pr-4 text-blue-600">
                    <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  </span>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </div>
  );
};

// Componente para mostrar filtros ativos
export const ActiveFilters: React.FC<ActiveFiltersProps> = ({
  filters,
  onRemove,
  onClearAll,
  className = ""
}) => {
  if (filters.length === 0) return null;

  return (
    <div className={`flex flex-wrap items-center gap-2 ${className}`}>
      <span className="text-sm text-gray-600">Filtros ativos:</span>
      {filters.map((filter) => (
        <span
          key={`${filter.groupId}-${filter.value}`}
          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
        >
          <span className="mr-1 font-normal text-blue-600">{filter.groupLabel}:</span>
          {filter.label}
          <button
            type="button"
            onClick={() => onRemove(filter.groupId)}
            className="ml-1 inline-flex items-center justify-center h-4 w-4 rounded-full text-blue-400 hover:bg-blue-200 hover:text-blue-600 focus:outline-none"
          >
            <XMarkIcon className="h-3 w-3" />
          </button>
        </span>
      ))}
      {filters.length > 1 && (
        <button
          type="button"
          onClick={onClearAll}
          className="text-xs text-gray-500 hover:text-gray-700 underline"
        >
          Limpar todos
        </button>
      )}
    </div>
  );
};

// Componente principal do painel de filtros
export const FilterPanel: React.FC<FilterPanelProps> = ({
  groups,
  onChange,
  isOpen,
  onToggle,
  className = ""
}) => {
  const activeFiltersCount = groups.filter(g => {
    if (Array.isArray(g.value)) return g.value.length > 0;
    return g.value !== undefined && g.value !== null && g.value !== '';
  }).length;

  return (
    <div className={className}>
      {/* Botão para abrir/fechar filtros */}
      <button
        type="button"
        onClick={onToggle}
        className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <FunnelIcon className="h-4 w-4 mr-2" />
        Filtros
        {activeFiltersCount > 0 && (
          <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            {activeFiltersCount}
          </span>
        )}
        <ChevronDownIcon className={`ml-2 h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {/* Painel de filtros */}
      {isOpen && (
        <div className="absolute z-30 mt-2 w-80 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5">
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Filtros</h3>
              <button
                onClick={onToggle}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-5 w-5" />
              </button>
            </div>
            
            <div className="space-y-4">
              {groups.map((group) => (
                <div key={group.id}>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {group.label}
                  </label>
                  
                  {group.type === 'range' ? (
                    <div className="grid grid-cols-2 gap-2">
                      <input
                        type="number"
                        placeholder="Mín"
                        min={group.min}
                        max={group.max}
                        value={group.value?.min || ''}
                        onChange={(e) => onChange(group.id, {
                          ...group.value,
                          min: e.target.value ? Number(e.target.value) : undefined
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <input
                        type="number"
                        placeholder="Máx"
                        min={group.min}
                        max={group.max}
                        value={group.value?.max || ''}
                        onChange={(e) => onChange(group.id, {
                          ...group.value,
                          max: e.target.value ? Number(e.target.value) : undefined
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  ) : group.type === 'date' ? (
                    <div className="grid grid-cols-2 gap-2">
                      <input
                        type="date"
                        value={group.value?.start || ''}
                        onChange={(e) => onChange(group.id, {
                          ...group.value,
                          start: e.target.value
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <input
                        type="date"
                        value={group.value?.end || ''}
                        onChange={(e) => onChange(group.id, {
                          ...group.value,
                          end: e.target.value
                        })}
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  ) : group.type === 'toggle' ? (
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={group.value || false}
                        onChange={(e) => onChange(group.id, e.target.checked)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-sm text-gray-700">
                        {group.placeholder || 'Ativar filtro'}
                      </span>
                    </label>
                  ) : (
                    <FilterDropdown
                      group={group}
                      onChange={onChange}
                    />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default {
  SearchBar,
  FilterDropdown,
  ActiveFilters,
  FilterPanel
}; 
