# Guia do Usuário - Magnow

Bem-vindo ao Magnow! Este guia foi projetado para ajudá-lo a aproveitar ao máximo nossa plataforma de gestão de estoque para o Mercado Livre Full.

## 1. Primeiro Acesso e Configuração

### 1.1. Login

Acesse a plataforma através do seu navegador com o endereço fornecido. Utilize as credenciais (email e senha) criadas durante o seu cadastro.

### 1.2. Conectando sua Conta do Mercado Livre

Após o primeiro login, você será direcionado para conectar sua conta do Mercado Livre. Siga os passos abaixo:

1.  Clique em **"Conectar com o Mercado Livre"**.
2.  Você será redirecionado para a página de autorização do Mercado Livre.
3.  Faça login com sua conta de vendedor do Mercado Livre.
4.  Autorize o Magnow a acessar os dados da sua conta.

Após a autorização, você será redirecionado de volta para o Magnow, e seus dados começarão a ser sincronizados.

## 2. Dashboard Principal

O Dashboard é a sua central de informações. Aqui você encontrará:

-   **Visão Geral do Estoque**: Um resumo do status atual do seu inventário no Full.
-   **Principais Alertas**: Produtos que precisam de atenção imediata (estoque baixo, risco de ruptura, etc.).
-   **Velocidade de Vendas**: Gráficos que mostram a performance de vendas dos seus principais produtos.
-   **Atalhos Rápidos**: Links para as funcionalidades mais importantes, como criar uma nova remessa.

## 3. Calculadora Inteligente de Estoque

A Calculadora Inteligente é o coração do Magnow. Ela analisa seus dados de vendas para recomendar a quantidade ideal de estoque a ser enviada para o Full.

### 3.1. Como Funciona

-   **Análise de Vendas**: O sistema analisa o histórico de vendas (últimos 7, 15, 30, 60 ou 90 dias) para calcular a velocidade de venda diária de cada produto.
-   **Cobertura de Estoque**: Você define por quantos dias deseja ter estoque garantido no Full (ex: 30 dias).
-   **Cálculo do Estoque Alvo**: Com base na velocidade de vendas e na cobertura desejada, o sistema calcula a quantidade ideal de cada item que você deve ter no centro de distribuição.

### 3.2. Utilizando a Calculadora

1.  Navegue até a seção **"Calculadora de Estoque"**.
2.  Selecione o período de análise de vendas (ex: "Últimos 30 dias").
3.  Defina a **cobertura de estoque** desejada (em dias).
4.  Clique em **"Calcular"**.

O sistema exibirá uma lista de produtos com as seguintes informações:
-   **Estoque Atual no Full**
-   **Velocidade de Venda (unidades/dia)**
-   **Estoque Alvo (quantidade ideal)**
-   **Necessidade de Envio (quantidade a ser enviada)**

## 4. Gerando uma Remessa de Entrada (Inbound)

Com base nos cálculos, você pode gerar uma remessa de entrada de forma automatizada.

1.  Na tela da Calculadora, selecione os produtos que deseja incluir na remessa.
2.  Clique em **"Gerar Remessa"**.
3.  O sistema irá gerar automaticamente a planilha de entrada (`.xlsx`) no formato exigido pelo Mercado Livre.
4.  A planilha será preenchida com os SKUs e as quantidades calculadas.
5.  Faça o download da planilha e siga o processo de envio no portal do Mercado Livre.

## 5. Relatórios e Análises

O Magnow oferece relatórios detalhados para uma tomada de decisão mais estratégica.

-   **Relatório de Vendas**: Acompanhe a performance de vendas por produto, categoria ou período.
-   **Relatório de Cobertura de Estoque**: Identifique rapidamente produtos com excesso ou falta de estoque.
-   **Relatório de Custos de Armazenagem**: Monitore os custos associados ao estoque parado no Full.

Para acessar, navegue até a seção **"Relatórios"** e selecione o tipo de análise que deseja visualizar.

## 6. Gestão de Alertas

Configure alertas para ser notificado sobre eventos importantes:

-   **Alerta de Estoque Baixo**: Seja avisado quando um produto atingir um nível crítico.
-   **Alerta de Ruptura Iminente**: Receba uma notificação quando o estoque de um item estiver prestes a acabar, com base na velocidade de vendas.
-   **Alerta de Vendas Anormais**: Detecte picos ou quedas inesperadas nas vendas.

## 7. Suporte

Se encontrar qualquer problema ou tiver dúvidas, entre em contato com nossa equipe de suporte através do email **<EMAIL>** ou pelo chat disponível na plataforma.