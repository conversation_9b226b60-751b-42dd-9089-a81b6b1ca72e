// Tipos para integração OAuth com API do Mercado Livre

export interface MercadoLivreOAuthConfig {
  clientId: string;
  clientSecret: string;
  redirectUri: string;
  baseUrl: string;
  authUrl: string;
  tokenUrl: string;
  scope: string[];
  codeChallenge?: string;
  codeChallengeMethod?: 'S256' | 'plain';
  codeVerifier?: string;
}

export interface MercadoLivreAuthorizationRequest {
  response_type: 'code';
  client_id: string;
  redirect_uri: string;
  state?: string;
  code_challenge?: string;
  code_challenge_method?: 'S256' | 'plain';
}

export interface MercadoLivreTokenRequest {
  grant_type: 'authorization_code' | 'refresh_token';
  client_id: string;
  client_secret: string;
  code?: string;
  redirect_uri?: string;
  refresh_token?: string;
  code_verifier?: string;
}

export interface MercadoLivreTokenResponse {
  access_token: string;
  token_type: 'Bearer';
  expires_in: number;
  refresh_token: string;
  scope: string;
  user_id: number;
}

export interface MercadoLivreUser {
  id: number;
  nickname: string;
  email: string;
  first_name: string;
  last_name: string;
  country_id: string;
  site_id: string;
  user_type: 'normal' | 'brand' | 'company';
  tags: string[];
  logo?: string;
  points: number;
  permalink: string;
  registration_date: string;
}

export interface MercadoLivreAccount {
  id: string;
  mlUserId: string;
  nickname: string;
  email: string;
  siteId: string;
  countryId: string;
  accessToken: string;
  refreshToken: string;
  tokenExpiresAt: Date;
  isActive: boolean;
  lastSyncAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Novos tipos para endpoints de leitura

export interface MercadoLivreItem {
  id: string;
  site_id: string;
  title: string;
  seller_id: number;
  category_id: string;
  official_store_id?: number;
  price: number;
  base_price: number;
  original_price?: number;
  currency_id: string;
  initial_quantity: number;
  available_quantity: number;
  sold_quantity: number;
  sale_terms: MercadoLivreSaleTerm[];
  buying_mode: 'buy_it_now' | 'auction';
  listing_type_id: string;
  start_time: string;
  stop_time: string;
  condition: 'new' | 'used' | 'not_specified';
  permalink: string;
  thumbnail: string;
  secure_thumbnail: string;
  pictures: MercadoLivrePicture[];
  video_id?: string;
  shipping: MercadoLivreShipping;
  seller_address: MercadoLivreAddress;
  attributes: MercadoLivreAttribute[];
  variations: MercadoLivreVariation[];
  status: 'active' | 'paused' | 'closed' | 'under_review' | 'inactive';
  sub_status: string[];
  tags: string[];
  warranty?: string;
  catalog_product_id?: string;
  domain_id?: string;
  user_product_id?: string;
  family_name?: string;
  family_id?: string;
  date_created: string;
  last_updated: string;
  health?: number;
  catalog_listing: boolean;
  channels: string[];
}

export interface MercadoLivreSaleTerm {
  id: string;
  name?: string;
  value_id?: string;
  value_name: string;
  value_struct?: any;
}

export interface MercadoLivrePicture {
  id: string;
  url: string;
  secure_url: string;
  size: string;
  max_size: string;
  quality: string;
}

export interface MercadoLivreShipping {
  mode: 'me1' | 'me2' | 'custom' | 'not_specified';
  methods: any[];
  tags: string[];
  dimensions?: string;
  local_pick_up: boolean;
  free_shipping: boolean;
  logistic_type: string;
  store_pick_up: boolean;
}

export interface MercadoLivreAddress {
  id: number;
  address_line?: string;
  zip_code?: string;
  city?: {
    id: string;
    name: string;
  };
  state?: {
    id: string;
    name: string;
  };
  country?: {
    id: string;
    name: string;
  };
  latitude?: number;
  longitude?: number;
}

export interface MercadoLivreAttribute {
  id: string;
  name: string;
  value_id?: string;
  value_name?: string;
  value_struct?: any;
  values?: Array<{
    id?: string;
    name: string;
    struct?: any;
  }>;
  value_type?: string;
  attribute_group_id?: string;
  attribute_group_name?: string;
}

export interface MercadoLivreVariation {
  id: number;
  price: number;
  attribute_combinations: MercadoLivreAttributeCombination[];
  available_quantity: number;
  sold_quantity: number;
  sale_terms: MercadoLivreSaleTerm[];
  picture_ids: string[];
  catalog_product_id?: string;
}

export interface MercadoLivreAttributeCombination {
  id: string;
  name: string;
  value_id: string;
  value_name: string;
}

export interface MercadoLivreOrder {
  id: number;
  status: 'confirmed' | 'payment_required' | 'payment_in_process' | 'paid' | 'shipped' | 'delivered' | 'cancelled';
  status_detail?: string;
  date_created: string;
  date_closed?: string;
  order_items: MercadoLivreOrderItem[];
  total_amount: number;
  currency_id: string;
  buyer: MercadoLivreOrderUser;
  seller: MercadoLivreOrderUser;
  payments: MercadoLivrePayment[];
  shipping: MercadoLivreOrderShipping;
  feedback: {
    purchase?: any;
    sale?: any;
  };
  context: {
    channel: string;
    site_id: string;
    flows: string[];
  };
  tags: string[];
  pack_id?: number;
  pickup_id?: number;
  order_request?: any;
  fulfilled?: boolean;
  mediations?: any[];
  last_updated: string;
}

export interface MercadoLivreOrderItem {
  item: {
    id: string;
    title: string;
    category_id: string;
    variation_id?: number;
    seller_custom_field?: string;
    variation_attributes?: MercadoLivreAttributeCombination[];
    warranty?: string;
    condition: string;
    seller_sku?: string;
  };
  quantity: number;
  unit_price: number;
  full_unit_price: number;
  currency_id: string;
  manufacturing_days?: number;
  sale_fee: number;
  listing_type_id: string;
}

export interface MercadoLivreOrderUser {
  id: number;
  nickname: string;
  email?: string;
  phone?: {
    area_code: string;
    number: string;
    extension?: string;
    verified: boolean;
  };
  first_name?: string;
  last_name?: string;
  billing_info?: any;
}

export interface MercadoLivrePayment {
  id: number;
  order_id: number;
  payer_id: number;
  collector: {
    id: number;
  };
  currency_id: string;
  status: 'pending' | 'approved' | 'authorized' | 'in_process' | 'in_mediation' | 'rejected' | 'cancelled' | 'refunded' | 'charged_back';
  status_detail?: string;
  status_code?: string;
  date_created: string;
  date_last_modified: string;
  date_approved?: string;
  money_release_date?: string;
  operation_type: string;
  issuer_id?: string;
  payment_method_id: string;
  payment_type: string;
  transaction_amount: number;
  transaction_amount_refunded: number;
  coupon_amount: number;
  shipping_cost: number;
  installments: number;
  deduction_schema?: any;
  installment_amount?: number;
  finance_charge?: number;
  net_received_amount: number;
  total_paid_amount: number;
  overpaid_amount: number;
  external_reference?: string;
  statement_descriptor?: string;
  authorization_code?: string;
  taxes_amount: number;
  coupon_id?: string;
  available_actions: string[];
  marketplace_fee: number;
}

export interface MercadoLivreOrderShipping {
  id: number;
  shipment_type: string;
  status: 'pending' | 'handling' | 'ready_to_ship' | 'shipped' | 'delivered' | 'not_delivered' | 'cancelled';
  substatus?: string;
  items: any[];
  date_created: string;
  last_updated: string;
  tracking_number?: string;
  tracking_method?: string;
  service_id?: number;
  carrier_info?: any;
  shipping_items: any[];
  shipping_option: any;
  comments?: string;
  date_first_printed?: string;
  market_place: string;
  return_details?: any;
  tags: string[];
  delay?: any[];
  type: string;
  logistic_type: string;
  mode: string;
  created_by: string;
  application_id?: string;
  cost_components?: any;
  receiver_address: MercadoLivreAddress;
  sender_address: MercadoLivreAddress;
}

export interface MercadoLivreSearchParams {
  seller_id?: string;
  category_id?: string;
  q?: string;
  sort?: 'relevance' | 'price_asc' | 'price_desc';
  limit?: number;
  offset?: number;
  status?: 'active' | 'paused' | 'closed' | 'under_review' | 'inactive';
  listing_type?: string;
  condition?: 'new' | 'used' | 'not_specified';
}

export interface MercadoLivreSearchResponse {
  site_id: string;
  country_default_time_zone: string;
  query?: string;
  paging: {
    total: number;
    primary_results: number;
    offset: number;
    limit: number;
  };
  results: MercadoLivreSearchResult[];
  sort: {
    id: string;
    name: string;
  };
  available_sorts: Array<{
    id: string;
    name: string;
  }>;
  filters: any[];
  available_filters: any[];
  pdp_tracking?: any;
}

export interface MercadoLivreSearchResult {
  id: string;
  title: string;
  condition: string;
  thumbnail_id: string;
  thumbnail: string;
  price: number;
  original_price?: number;
  currency_id: string;
  available_quantity: number;
  sold_quantity: number;
  buying_mode: string;
  listing_type_id: string;
  stop_time: string;
  permalink: string;
  seller: {
    id: number;
    nickname: string;
    car_dealer?: boolean;
    real_estate_agency?: boolean;
    _class?: string;
    tags: string[];
    eshop?: any;
    seller_reputation: {
      level_id?: string;
      power_seller_status?: string;
      transactions: {
        period: string;
        total: number;
        completed: number;
        canceled: number;
        ratings: {
          positive: number;
          negative: number;
          neutral: number;
        };
      };
      metrics: {
        claims: {
          period: string;
          rate: number;
          value: number;
        };
        delayed_handling_time: {
          period: string;
          rate: number;
          value: number;
        };
        sales: {
          period: string;
          completed: number;
        };
      };
    };
  };
  seller_address: MercadoLivreAddress;
  attributes: MercadoLivreAttribute[];
  installments?: {
    quantity: number;
    amount: number;
    rate: number;
    currency_id: string;
  };
  winner_item_id?: string;
  catalog_product_id?: string;
  domain_id?: string;
  tags: string[];
  order_backend?: number;
  use_thumbnail_id?: boolean;
  offer_score?: any;
  offer_share?: any;
  match_score?: any;
  melicoin?: any;
  discounts?: any;
  promotions?: any[];
  inventory_id?: string;
}

// Tipos para gerenciamento de estoque
export interface MercadoLivreStockUpdate {
  available_quantity: number;
  user_product_id?: string;
  warehouse_id?: string;
}

export interface MercadoLivreStockResponse {
  id: string;
  available_quantity: number;
  sold_quantity: number;
  reserved_quantity?: number;
  last_updated: string;
}

// Tipos para erros da API
export interface MercadoLivreOAuthError {
  error: string;
  error_description: string;
  message: string;
  status: number;
  cause?: any[];
}

export interface MercadoLivreApiResponse<T = any> {
  data?: T;
  error?: MercadoLivreOAuthError;
  status: number;
  headers: Record<string, string>;
}

// Cache de tokens
export interface TokenCacheEntry {
  token: string;
  expiresAt: Date;
  refreshToken: string;
  userId: string;
  tenantId: string;
}

// Configurações por país
export interface CountryConfig {
  id: string;
  name: string;
  currency_id: string;
  site_id: string;
  auth_url: string;
  api_url: string;
  decimal_separator: string;
  thousands_separator: string;
  time_zone: string;
}

// Constantes
export const MERCADO_LIVRE_COUNTRIES: Record<string, CountryConfig> = {
  AR: {
    id: 'AR',
    name: 'Argentina',
    currency_id: 'ARS',
    site_id: 'MLA',
    auth_url: 'https://auth.mercadolibre.com.ar',
    api_url: 'https://api.mercadolibre.com',
    decimal_separator: ',',
    thousands_separator: '.',
    time_zone: 'America/Argentina/Buenos_Aires'
  },
  BR: {
    id: 'BR',
    name: 'Brasil',
    currency_id: 'BRL',
    site_id: 'MLB',
    auth_url: 'https://auth.mercadolivre.com.br',
    api_url: 'https://api.mercadolibre.com',
    decimal_separator: ',',
    thousands_separator: '.',
    time_zone: 'America/Sao_Paulo'
  },
  MX: {
    id: 'MX',
    name: 'México',
    currency_id: 'MXN',
    site_id: 'MLM',
    auth_url: 'https://auth.mercadolibre.com.mx',
    api_url: 'https://api.mercadolibre.com',
    decimal_separator: '.',
    thousands_separator: ',',
    time_zone: 'America/Mexico_City'
  },
  CO: {
    id: 'CO',
    name: 'Colombia',
    currency_id: 'COP',
    site_id: 'MCO',
    auth_url: 'https://auth.mercadolibre.com.co',
    api_url: 'https://api.mercadolibre.com',
    decimal_separator: ',',
    thousands_separator: '.',
    time_zone: 'America/Bogota'
  },
  CL: {
    id: 'CL',
    name: 'Chile',
    currency_id: 'CLP',
    site_id: 'MLC',
    auth_url: 'https://auth.mercadolibre.cl',
    api_url: 'https://api.mercadolibre.com',
    decimal_separator: ',',
    thousands_separator: '.',
    time_zone: 'America/Santiago'
  },
  UY: {
    id: 'UY',
    name: 'Uruguay',
    currency_id: 'UYU',
    site_id: 'MLU',
    auth_url: 'https://auth.mercadolibre.com.uy',
    api_url: 'https://api.mercadolibre.com',
    decimal_separator: ',',
    thousands_separator: '.',
    time_zone: 'America/Montevideo'
  },
  PE: {
    id: 'PE',
    name: 'Perú',
    currency_id: 'PEN',
    site_id: 'MPE',
    auth_url: 'https://auth.mercadolibre.com.pe',
    api_url: 'https://api.mercadolibre.com',
    decimal_separator: '.',
    thousands_separator: ',',
    time_zone: 'America/Lima'
  }
};

export const MERCADO_LIVRE_SCOPES = {
  OFFLINE_ACCESS: 'offline_access',
  READ: 'read',
  WRITE: 'write'
} as const;

export const MERCADO_LIVRE_ENDPOINTS = {
  OAUTH_TOKEN: '/oauth/token',
  USER_ME: '/users/me',
  ITEMS_SEARCH: '/sites/{site_id}/search',
  ITEMS_BY_SELLER: '/users/{user_id}/items/search',
  ITEM_DETAIL: '/items/{item_id}',
  ORDERS_SEARCH: '/orders/search',
  ORDER_DETAIL: '/orders/{order_id}',
  STOCK_UPDATE: '/items/{item_id}',
  USER_PRODUCTS: '/user-products/{user_product_id}',
  NOTIFICATIONS: '/myfeeds'
} as const;

export const MERCADO_LIVRE_ERRORS = {
  INVALID_CLIENT: 'invalid_client',
  INVALID_GRANT: 'invalid_grant',
  INVALID_SCOPE: 'invalid_scope',
  UNAUTHORIZED_CLIENT: 'unauthorized_client',
  UNSUPPORTED_GRANT_TYPE: 'unsupported_grant_type',
  INVALID_REQUEST: 'invalid_request',
  ACCESS_DENIED: 'access_denied',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  RESOURCE_NOT_FOUND: 'resource_not_found',
  INTERNAL_SERVER_ERROR: 'internal_server_error'
} as const;
