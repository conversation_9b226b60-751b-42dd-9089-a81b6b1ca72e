/**
 * Serviço de Agendamento de Estoque - Versão Simplificada
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { logger } from '../utils/logger';
import { StockCalculationService } from './stockCalculationService';
import { StockAlertService } from './stockAlertService';

// Tipos simplificados para evitar erros complexos
interface SimpleJobConfig {
  name: string;
  tenantId: string;
  intervalMs: number;
  enabled: boolean;
}

interface SimpleJobExecution {
  id: string;
  tenantId: string;
  startedAt: Date;
  finishedAt?: Date;
  status: 'running' | 'completed' | 'failed';
  error?: string;
}

export class StockSchedulerService {
  private stockCalculationService: StockCalculationService;
  private stockAlertService: StockAlertService;
  private activeJobs: Map<string, NodeJS.Timeout> = new Map();
  private jobExecutions: Map<string, SimpleJobExecution[]> = new Map();

  constructor(stockCalculationService: StockCalculationService, stockAlertService: StockAlertService) {
    this.stockCalculationService = stockCalculationService;
    this.stockAlertService = stockAlertService;
  }

  /**
   * Configura job de recálculo automático para um tenant
   */
  public async setupTenantJob(tenantId: string, config: SimpleJobConfig): Promise<void> {
    try {
      logger.info('Configurando job de recálculo automático', { tenantId, config });

      // Para job existente se houver
      await this.stopTenantJob(tenantId);

      if (config.enabled) {
        // Cria job com setInterval
        const interval = setInterval(async () => {
          await this.executeStockCalculationJob(tenantId, config.name);
        }, config.intervalMs);

        this.activeJobs.set(tenantId, interval);
        
        logger.info('Job de recálculo iniciado', { 
          tenantId, 
          intervalMs: config.intervalMs
        });
      }

    } catch (error: any) {
      logger.error('Erro ao configurar job de recálculo', { tenantId, error: error.message });
      throw error;
    }
  }

  /**
   * Para job de um tenant específico
   */
  public async stopTenantJob(tenantId: string): Promise<void> {
    const existingJob = this.activeJobs.get(tenantId);
    if (existingJob) {
      clearInterval(existingJob);
      this.activeJobs.delete(tenantId);
      logger.info('Job de recálculo parado', { tenantId });
    }
  }

  /**
   * Executa job de cálculo de estoque para um tenant
   */
  private async executeStockCalculationJob(tenantId: string, jobName: string): Promise<void> {
    const executionId = `${tenantId}_${Date.now()}`;
    const startTime = new Date();

    logger.info('Iniciando execução de job de recálculo', { 
      tenantId, 
      executionId,
      jobName 
    });

    const execution: SimpleJobExecution = {
      id: executionId,
      tenantId,
      startedAt: startTime,
      status: 'running'
    };

    try {
      // Registra início da execução
      this.addJobExecution(tenantId, execution);

      // Executa cálculo de estoque (versão simplificada)
      const calculationResult = await this.stockCalculationService.calculateBatchIdealStock(
        tenantId, 
        {}
      );

      // Atualiza execução com sucesso
      execution.finishedAt = new Date();
      execution.status = 'completed';

      logger.info('Job de recálculo concluído com sucesso', {
        tenantId,
        executionId,
        totalItems: calculationResult.totalItems,
        successful: calculationResult.successfulCalculations,
        failed: calculationResult.failedCalculations
      });

    } catch (error: any) {
      // Atualiza execução com erro
      execution.finishedAt = new Date();
      execution.status = 'failed';
      execution.error = error.message;

      logger.error('Erro na execução do job de recálculo', {
        tenantId,
        executionId,
        error: error.message
      });

    } finally {
      // Atualiza registro da execução
      this.updateJobExecution(tenantId, execution);
    }
  }

  /**
   * Executa job manualmente
   */
  public async executeManualJob(tenantId: string, userId: string): Promise<SimpleJobExecution> {
    const executionId = `${tenantId}_manual_${Date.now()}`;
    const startTime = new Date();

    logger.info('Iniciando execução manual de job de recálculo', { 
      tenantId, 
      userId,
      executionId 
    });

    const execution: SimpleJobExecution = {
      id: executionId,
      tenantId,
      startedAt: startTime,
      status: 'running'
    };

    try {
      // Registra início da execução
      this.addJobExecution(tenantId, execution);

      // Executa cálculo
      const calculationResult = await this.stockCalculationService.calculateBatchIdealStock(
        tenantId, 
        {}
      );

      // Atualiza execução com sucesso
      execution.finishedAt = new Date();
      execution.status = 'completed';

      logger.info('Execução manual de job concluída', {
        tenantId,
        userId,
        executionId,
        totalItems: calculationResult.totalItems,
        successful: calculationResult.successfulCalculations
      });

      return execution;

    } catch (error: any) {
      execution.finishedAt = new Date();
      execution.status = 'failed';
      execution.error = error.message;

      logger.error('Erro na execução manual do job', {
        tenantId,
        userId,
        executionId,
        error: error.message
      });

      throw error;
    } finally {
      this.updateJobExecution(tenantId, execution);
    }
  }

  /**
   * Lista execuções de jobs para um tenant
   */
  public getJobExecutions(tenantId: string, limit: number = 50): SimpleJobExecution[] {
    const executions = this.jobExecutions.get(tenantId) || [];
    return executions
      .sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime())
      .slice(0, limit);
  }

  /**
   * Verifica se job está ativo
   */
  public isJobActive(tenantId: string): boolean {
    return this.activeJobs.has(tenantId);
  }

  /**
   * Para todos os jobs ativos
   */
  public async stopAllJobs(): Promise<void> {
    logger.info('Parando todos os jobs de recálculo');
    
    for (const tenantId of this.activeJobs.keys()) {
      try {
        await this.stopTenantJob(tenantId);
      } catch (error: any) {
        logger.error('Erro ao parar job', { tenantId, error: error.message });
      }
    }

    logger.info('Todos os jobs foram parados');
  }

  /**
   * Adiciona execução ao histórico
   */
  private addJobExecution(tenantId: string, execution: SimpleJobExecution): void {
    if (!this.jobExecutions.has(tenantId)) {
      this.jobExecutions.set(tenantId, []);
    }
    
    const executions = this.jobExecutions.get(tenantId)!;
    executions.unshift(execution);
    
    // Mantém apenas as últimas 100 execuções
    if (executions.length > 100) {
      executions.splice(100);
    }
  }

  /**
   * Atualiza execução no histórico
   */
  private updateJobExecution(tenantId: string, execution: SimpleJobExecution): void {
    const executions = this.jobExecutions.get(tenantId);
    if (executions) {
      const index = executions.findIndex(e => e.id === execution.id);
      if (index !== -1) {
        executions[index] = execution;
      }
    }
  }

  /**
   * Obtém métricas básicas dos jobs
   */
  public getJobMetrics(tenantId: string, days: number = 7): any {
    const executions = this.getJobExecutions(tenantId, 1000);
    const since = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    const recentExecutions = executions.filter(e => e.startedAt >= since);

    const successful = recentExecutions.filter(e => e.status === 'completed');
    const failed = recentExecutions.filter(e => e.status === 'failed');

    return {
      totalExecutions: recentExecutions.length,
      successful: successful.length,
      failed: failed.length,
      successRate: recentExecutions.length > 0 ? (successful.length / recentExecutions.length) * 100 : 0,
      lastExecution: recentExecutions[0]?.startedAt || null,
      period: `${days} days`
    };
  }
} 