import type { ReactNode } from 'react';
import React from 'react';

// Interfaces existentes
interface Column {
  key: string;
  title: string;
  width?: string;
  align?: 'left' | 'center' | 'right';
  sortable?: boolean;
  render?: (value: any, row: any, index: number) => ReactNode;
}

interface TableProps {
  columns: Column[];
  data: any[];
  loading?: boolean;
  error?: string;
  emptyMessage?: string;
  onSort?: (column: string, direction: 'asc' | 'desc') => void;
  sortColumn?: string;
  sortDirection?: 'asc' | 'desc';
  className?: string;
}

// Componentes modulares para compatibilidade com shadcn/ui
interface TableComponentProps {
  children: ReactNode;
  className?: string;
}

interface TableCellProps {
  children: ReactNode;
  className?: string;
  colSpan?: number;
}

// Componente Table modular
export const Table = React.forwardRef<HTMLTableElement, TableComponentProps>(
  ({ className = '', children, ...props }, ref) => (
    <div className="relative w-full overflow-auto">
      <table
        ref={ref}
        className={`w-full caption-bottom text-sm ${className}`}
        {...props}
      >
        {children}
      </table>
    </div>
  )
);
Table.displayName = 'Table';

// Componente TableHeader modular
export const TableHeader = React.forwardRef<HTMLTableSectionElement, TableComponentProps>(
  ({ className = '', children, ...props }, ref) => (
    <thead
      ref={ref}
      className={`bg-muted/50 ${className}`}
      {...props}
    >
      {children}
    </thead>
  )
);
TableHeader.displayName = 'TableHeader';

// Componente TableBody modular
export const TableBody = React.forwardRef<HTMLTableSectionElement, TableComponentProps>(
  ({ className = '', children, ...props }, ref) => (
    <tbody
      ref={ref}
      className={`bg-card divide-y divide-border ${className}`}
      {...props}
    >
      {children}
    </tbody>
  )
);
TableBody.displayName = 'TableBody';

// Componente TableRow modular
export const TableRow = React.forwardRef<HTMLTableRowElement, TableComponentProps>(
  ({ className = '', children, ...props }, ref) => (
    <tr
      ref={ref}
      className={`hover:bg-accent/50 ${className}`}
      {...props}
    >
      {children}
    </tr>
  )
);
TableRow.displayName = 'TableRow';

// Componente TableHead modular
export const TableHead = React.forwardRef<HTMLTableCellElement, TableComponentProps>(
  ({ className = '', children, ...props }, ref) => (
    <th
      ref={ref}
      className={`px-6 py-3 text-xs font-medium text-muted-foreground uppercase tracking-wider text-left ${className}`}
      {...props}
    >
      {children}
    </th>
  )
);
TableHead.displayName = 'TableHead';

// Componente TableCell modular
export const TableCell = React.forwardRef<HTMLTableCellElement, TableCellProps>(
  ({ className = '', children, colSpan, ...props }, ref) => (
    <td
      ref={ref}
      className={`px-6 py-4 whitespace-nowrap text-sm ${className}`}
      colSpan={colSpan}
      {...props}
    >
      {children}
    </td>
  )
);
TableCell.displayName = 'TableCell';

// Componente Table original (mantido para compatibilidade)
export default function TableComponent({
  columns,
  data,
  loading = false,
  error,
  emptyMessage = 'Nenhum dado encontrado',
  onSort,
  sortColumn,
  sortDirection,
  className = '',
}: TableProps) {
  const handleSort = (column: Column) => {
    if (!column.sortable || !onSort) return;
    
    const newDirection = 
      sortColumn === column.key && sortDirection === 'asc' ? 'desc' : 'asc';
    onSort(column.key, newDirection);
  };

  const getSortIcon = (column: Column) => {
    if (!column.sortable) return null;
    
    if (sortColumn !== column.key) {
      return <span className="text-muted-foreground/60 ml-1">↕</span>;
    }
    
    return (
      <span className="text-primary-600 ml-1">
        {sortDirection === 'asc' ? '↑' : '↓'}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="bg-card shadow rounded-lg">
        <div className="px-6 py-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Carregando dados...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-card shadow rounded-lg">
        <div className="px-6 py-8 text-center">
          <div className="text-red-500 text-sm mb-2">⚠️ Erro ao carregar dados</div>
          <div className="text-muted-foreground text-xs">{error}</div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-card shadow rounded-lg overflow-hidden ${className}`}>
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-border">
          <thead className="bg-muted/50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-6 py-3 text-xs font-medium text-muted-foreground uppercase tracking-wider ${
                    column.align === 'center' ? 'text-center' :
                    column.align === 'right' ? 'text-right' : 'text-left'
                  } ${column.sortable ? 'cursor-pointer hover:bg-accent/50' : ''}`}
                  style={{ width: column.width }}
                  onClick={() => handleSort(column)}
                >
                  <div className="flex items-center">
                    {column.title}
                    {getSortIcon(column)}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-card divide-y divide-border">
            {data.length === 0 ? (
              <tr>
                <td 
                  colSpan={columns.length} 
                  className="px-6 py-8 text-center text-sm text-muted-foreground"
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <tr key={index} className="hover:bg-accent/50">
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`px-6 py-4 whitespace-nowrap text-sm ${
                        column.align === 'center' ? 'text-center' :
                        column.align === 'right' ? 'text-right' : 'text-left'
                      }`}
                    >
                      {column.render 
                        ? column.render(row[column.key], row, index)
                        : row[column.key]
                      }
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}

// Componente para células com badge de status
interface StatusBadgeProps {
  status: string;
  variant?: 'success' | 'warning' | 'danger' | 'info' | 'gray';
}

export function StatusBadge({ status, variant = 'gray' }: StatusBadgeProps) {
  const variantClasses = {
    success: 'bg-green-100 text-green-800',
    warning: 'bg-yellow-100 text-yellow-800',
    danger: 'bg-red-100 text-red-800',
    info: 'bg-blue-100 text-blue-800',
    gray: 'bg-muted text-muted-foreground',
  };

  return (
    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${variantClasses[variant]}`}>
      {status}
    </span>
  );
}
