import { useState, useCallback, useEffect, useRef } from 'react';
import { useStockStore } from '../store/stockStore';
import type { ProductWithStock } from '../types/api';

interface SyncStatus {
  productId: string;
  status: 'idle' | 'syncing' | 'success' | 'error';
  message?: string;
  lastSyncAt?: string;
}

interface UseStockSyncReturn {
  // State
  syncStatuses: Record<string, SyncStatus>;
  isSyncing: boolean;
  
  // Actions
  syncProduct: (productId: string) => Promise<void>;
  syncAllProducts: () => Promise<void>;
  syncProductsWithGaps: () => Promise<void>;
  clearSyncStatus: (productId: string) => void;
  clearAllSyncStatuses: () => void;
  
  // Utilities
  getSyncStatus: (productId: string) => SyncStatus;
  getFailedSyncs: () => SyncStatus[];
  getSuccessfulSyncs: () => SyncStatus[];
}

export const useStockSync = (): UseStockSyncReturn => {
  const [syncStatuses, setSyncStatuses] = useState<Record<string, SyncStatus>>({});
  const [isSyncing, setIsSyncing] = useState(false);
  const isMountedRef = useRef(true);
  const abortControllerRef = useRef<AbortController | null>(null);
  
  const {
    productsWithStock,
    syncProductStock,
    loadProductsWithStock,
    loadStockCalculations,
    stockCalculations,
  } = useStockStore();

  // Update sync status for a product
  const updateSyncStatus = useCallback((productId: string, status: Partial<SyncStatus>) => {
    setSyncStatuses(prev => ({
      ...prev,
      [productId]: {
        ...prev[productId],
        productId,
        ...status,
        lastSyncAt: status.status === 'success' ? new Date().toISOString() : prev[productId]?.lastSyncAt,
      }
    }));
  }, []);

  // Sync individual product
  const syncProduct = useCallback(async (productId: string) => {
    updateSyncStatus(productId, { status: 'syncing', message: 'Sincronizando...' });
    
    try {
      await syncProductStock(productId);
      updateSyncStatus(productId, { 
        status: 'success', 
        message: 'Sincronizado com sucesso' 
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro na sincronização';
      updateSyncStatus(productId, { 
        status: 'error', 
        message: errorMessage 
      });
      throw error;
    }
  }, [syncProductStock, updateSyncStatus]);

  // Sync all products
  const syncAllProducts = useCallback(async () => {
    if (productsWithStock.length === 0) {
      return;
    }

    setIsSyncing(true);
    
    try {
      // Initialize all products as syncing
      productsWithStock.forEach(product => {
        updateSyncStatus(product.id, { status: 'syncing', message: 'Aguardando...' });
      });

      // Sync products in batches to avoid overwhelming the API
      const batchSize = 5;
      const batches = [];
      
      for (let i = 0; i < productsWithStock.length; i += batchSize) {
        batches.push(productsWithStock.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        const syncPromises = batch.map(async (product) => {
          try {
            updateSyncStatus(product.id, { status: 'syncing', message: 'Sincronizando...' });
            await syncProductStock(product.id);
            updateSyncStatus(product.id, { 
              status: 'success', 
              message: 'Sincronizado com sucesso' 
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Erro na sincronização';
            updateSyncStatus(product.id, { 
              status: 'error', 
              message: errorMessage 
            });
          }
        });

        // Wait for current batch to complete before starting next
        await Promise.all(syncPromises);
        
        // Small delay between batches to respect rate limits
        if (batches.indexOf(batch) < batches.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      // Refresh data after all syncs
      await loadProductsWithStock();
      await loadStockCalculations();
      
    } finally {
      setIsSyncing(false);
    }
  }, [
    productsWithStock, 
    syncProductStock, 
    updateSyncStatus, 
    loadProductsWithStock, 
    loadStockCalculations
  ]);

  // Sync only products with stock gaps
  const syncProductsWithGaps = useCallback(async () => {
    const productsWithGaps = productsWithStock.filter(product => {
      const calculation = stockCalculations[product.id];
      return calculation && calculation.gap > 0;
    });

    if (productsWithGaps.length === 0) {
      return;
    }

    setIsSyncing(true);
    
    try {
      // Initialize products with gaps as syncing
      productsWithGaps.forEach(product => {
        updateSyncStatus(product.id, { status: 'syncing', message: 'Sincronizando gap...' });
      });

      // Sync products with gaps
      for (const product of productsWithGaps) {
        try {
          await syncProductStock(product.id);
          updateSyncStatus(product.id, { 
            status: 'success', 
            message: 'Gap sincronizado' 
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro na sincronização';
          updateSyncStatus(product.id, { 
            status: 'error', 
            message: errorMessage 
          });
        }
        
        // Small delay between syncs
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      // Refresh calculations
      await loadStockCalculations();
      
    } finally {
      setIsSyncing(false);
    }
  }, [
    productsWithStock, 
    stockCalculations, 
    syncProductStock, 
    updateSyncStatus, 
    loadStockCalculations
  ]);

  // Clear sync status for a product
  const clearSyncStatus = useCallback((productId: string) => {
    setSyncStatuses(prev => {
      const { [productId]: removed, ...rest } = prev;
      return rest;
    });
  }, []);

  // Clear all sync statuses
  const clearAllSyncStatuses = useCallback(() => {
    setSyncStatuses({});
  }, []);

  // Get sync status for a product
  const getSyncStatus = useCallback((productId: string): SyncStatus => {
    return syncStatuses[productId] || { 
      productId, 
      status: 'idle' 
    };
  }, [syncStatuses]);

  // Get failed syncs
  const getFailedSyncs = useCallback((): SyncStatus[] => {
    return Object.values(syncStatuses).filter(status => status.status === 'error');
  }, [syncStatuses]);

  // Get successful syncs
  const getSuccessfulSyncs = useCallback((): SyncStatus[] => {
    return Object.values(syncStatuses).filter(status => status.status === 'success');
  }, [syncStatuses]);

  // Cleanup effect to prevent memory leaks
  useEffect(() => {
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;
      // Cancel any ongoing sync operations
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  return {
    // State
    syncStatuses,
    isSyncing,
    
    // Actions
    syncProduct,
    syncAllProducts,
    syncProductsWithGaps,
    clearSyncStatus,
    clearAllSyncStatuses,
    
    // Utilities
    getSyncStatus,
    getFailedSyncs,
    getSuccessfulSyncs,
  };
};

export default useStockSync;
