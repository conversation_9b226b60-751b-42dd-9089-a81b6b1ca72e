import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/Switch';
import { Badge } from '../ui/badge';
import { Alert, AlertDescription } from '../ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select-radix';
import {
  Bell,
  Save,
  RotateCcw,
  Mail,
  Clock,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { useNotificationStore } from '../../store/notificationStore';

interface AlertConfiguration {
  emailEnabled: boolean;
  emailAddresses: string[];
  smsEnabled: boolean;
  smsNumbers: string[];
  criticalAlertEnabled: boolean;
  lowStockAlertEnabled: boolean;
  outOfStockAlertEnabled: boolean;
  gapAlertEnabled: boolean;
  overStockAlertEnabled: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
  alertThrottleEnabled: boolean;
}

interface AlertConfigurationPanelProps {
  className?: string;
}

export const AlertConfigurationPanel: React.FC<AlertConfigurationPanelProps> = ({ className }) => {
  const { 
    notifications, 
    unreadCount,
    setPollingInterval,
    setEnableToasts 
  } = useNotificationStore();
  
  // MOCK DATA FOR DEVELOPMENT - Replace with real configuration store
  const [config, setConfig] = useState<AlertConfiguration>({
    emailEnabled: true,
    emailAddresses: ['<EMAIL>'],
    smsEnabled: false,
    smsNumbers: [],
    criticalAlertEnabled: true,
    lowStockAlertEnabled: true,
    outOfStockAlertEnabled: true,
    gapAlertEnabled: true,
    overStockAlertEnabled: false,
    quietHoursEnabled: true,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    alertThrottleEnabled: true,
  });

  const [isLoading, setIsLoading] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [newEmail, setNewEmail] = useState('');

  const handleConfigChange = (key: keyof AlertConfiguration, value: any) => {
    setConfig(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
    
    // Apply some changes immediately if needed
  };

  const handleAddEmail = () => {
    if (newEmail && !config.emailAddresses.includes(newEmail)) {
      handleConfigChange('emailAddresses', [...config.emailAddresses, newEmail]);
      setNewEmail('');
    }
  };

  const handleRemoveEmail = (email: string) => {
    handleConfigChange('emailAddresses', config.emailAddresses.filter(e => e !== email));
  };

  const handleSave = async () => {
    setIsLoading(true);
    try {
      // TODO: Replace with real API call
      // await alertConfigService.updateConfiguration(config);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setHasChanges(false);
      console.log('Configurações de alertas salvas:', config);
    } catch (error) {
      console.error('Erro ao salvar configurações de alertas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestAlert = async () => {
    try {
      // TODO: Replace with real test alert
      console.log('Enviando alerta de teste...');
      
      // Simulate test notification
      const { addNotification } = useNotificationStore.getState();
      addNotification({
        type: 'system',
        title: 'Teste de Alerta',
        message: 'Este é um alerta de teste para verificar suas configurações.',
        severity: 'info',
        autoClose: true,
        duration: 5000
      });
    } catch (error) {
      console.error('Erro ao enviar alerta de teste:', error);
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Configurações de Alertas
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Canais de Notificação */}
        <div className="space-y-4">
          <h3 className="font-semibold">Canais de Notificação</h3>
          
          <div className="space-y-4">
            {/* Email */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <Label>Email</Label>
                </div>
                <Switch
                  checked={config.emailEnabled}
                  onCheckedChange={(checked) => handleConfigChange('emailEnabled', checked)}
                />
              </div>
              
              {config.emailEnabled && (
                <div className="space-y-2 ml-6">
                  <div className="flex gap-2">
                    <Input
                      placeholder="<EMAIL>"
                      value={newEmail}
                      onChange={(e) => setNewEmail(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleAddEmail()}
                    />
                    <Button onClick={handleAddEmail} size="sm">
                      Adicionar
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {config.emailAddresses.map((email) => (
                      <Badge key={email} variant="secondary" className="flex items-center gap-1">
                        {email}
                        <button
                          onClick={() => handleRemoveEmail(email)}
                          className="ml-1 hover:text-destructive"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>


          </div>
        </div>

        {/* Tipos de Alertas */}
        <div className="space-y-4">
          <h3 className="font-semibold">Tipos de Alertas</h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <Label>Estoque Crítico</Label>
              </div>
              <Switch
                checked={config.criticalAlertEnabled}
                onCheckedChange={(checked) => handleConfigChange('criticalAlertEnabled', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <Label>Estoque Baixo</Label>
              </div>
              <Switch
                checked={config.lowStockAlertEnabled}
                onCheckedChange={(checked) => handleConfigChange('lowStockAlertEnabled', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <Label>Sem Estoque</Label>
              </div>
              <Switch
                checked={config.outOfStockAlertEnabled}
                onCheckedChange={(checked) => handleConfigChange('outOfStockAlertEnabled', checked)}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-blue-500" />
                <Label>Gap de Estoque</Label>
              </div>
              <Switch
                checked={config.gapAlertEnabled}
                onCheckedChange={(checked) => handleConfigChange('gapAlertEnabled', checked)}
              />
            </div>
          </div>
        </div>



        {/* Status Atual */}
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            {unreadCount} alertas não lidos • 
            {notifications.length} notificações totais • 
            Sistema ativo
          </AlertDescription>
        </Alert>

        {/* Ações */}
        <div className="flex items-center justify-between pt-4 border-t">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleTestAlert}
            >
              Testar Alerta
            </Button>
            <Button
              variant="outline"
              onClick={() => setConfig({
                ...config,
                emailEnabled: true,
                pushEnabled: true,
                criticalAlertEnabled: true,
                lowStockAlertEnabled: true,
                outOfStockAlertEnabled: true,
                checkFrequency: 60,
              })}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Restaurar Padrões
            </Button>
          </div>
          
          <div className="flex items-center gap-2">
            {hasChanges && (
              <Badge variant="secondary">
                Alterações não salvas
              </Badge>
            )}
            <Button
              onClick={handleSave}
              disabled={!hasChanges || isLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {isLoading ? 'Salvando...' : 'Salvar'}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AlertConfigurationPanel;
