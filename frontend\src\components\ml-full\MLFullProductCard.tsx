import React, { useState } from 'react';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import StockIndicators from '../products/StockIndicators';
import {
  Package,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Edit3,
  ExternalLink,
  Calculator
} from 'lucide-react';
import type { ProductWithStock } from '../../types/api';

interface MLFullProductCardProps {
  product: ProductWithStock;
  quantity: number;
  onQuantityChange: (productId: string, quantity: number) => void;
  isSelected: boolean;
  onToggleSelection: (productId: string) => void;
  showQuantityInput?: boolean;
  maxQuantity?: number;
  minQuantity?: number;
}

export default function MLFullProductCard({
  product,
  quantity,
  onQuantityChange,
  isSelected,
  onToggleSelection,
  showQuantityInput = true,
  maxQuantity,
  minQuantity = 1
}: MLFullProductCardProps) {
  const [isEditingQuantity, setIsEditingQuantity] = useState(false);
  const [tempQuantity, setTempQuantity] = useState(quantity.toString());

  const getStockStatus = () => {
    const stock = product.availableQuantity;
    if (stock === 0) return { 
      status: 'Sem estoque', 
      color: 'destructive', 
      icon: AlertTriangle,
      bgColor: 'bg-red-50 border-red-200'
    };
    if (stock <= 5) return { 
      status: 'Estoque baixo', 
      color: 'warning', 
      icon: AlertTriangle,
      bgColor: 'bg-yellow-50 border-yellow-200'
    };
    if (stock <= 10) return { 
      status: 'Estoque médio', 
      color: 'secondary', 
      icon: Package,
      bgColor: 'bg-blue-50 border-blue-200'
    };
    return { 
      status: 'Estoque OK', 
      color: 'default', 
      icon: CheckCircle,
      bgColor: 'bg-green-50 border-green-200'
    };
  };

  const handleQuantitySubmit = () => {
    const newQuantity = parseInt(tempQuantity);
    const maxAllowed = maxQuantity || product.availableQuantity;
    
    if (isNaN(newQuantity) || newQuantity < minQuantity) {
      setTempQuantity(minQuantity.toString());
      onQuantityChange(product.id, minQuantity);
    } else if (newQuantity > maxAllowed) {
      setTempQuantity(maxAllowed.toString());
      onQuantityChange(product.id, maxAllowed);
    } else {
      onQuantityChange(product.id, newQuantity);
    }
    
    setIsEditingQuantity(false);
  };

  const handleQuantityCancel = () => {
    setTempQuantity(quantity.toString());
    setIsEditingQuantity(false);
  };

  const stockStatus = getStockStatus();
  const totalValue = quantity * product.price;
  const isEligible = product.isEligible !== false && product.availableQuantity > 0;

  return (
    <Card 
      className={`transition-all duration-200 h-full ${
        isSelected ? 'ring-2 ring-blue-500 bg-blue-50/50' : ''
      } ${!isEligible ? 'opacity-60' : 'hover:shadow-md'} ${stockStatus.bgColor}`}
    >
      <CardContent className="p-4 h-full flex flex-col">
        {/* Header: Selection + Thumbnail */}
        <div className="flex items-start justify-between gap-3 mb-3">
          <input
            type="checkbox"
            checked={isSelected}
            disabled={!isEligible}
            onChange={() => isEligible && onToggleSelection(product.id)}
            className="mt-1 flex-shrink-0 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
          
          {product.thumbnail && (
            <img
              src={product.thumbnail}
              alt={product.title}
              className="w-16 h-16 object-cover rounded border flex-shrink-0"
            />
          )}
        </div>

        {/* Title and IDs */}
        <div className="mb-3 flex-grow">
          <h3 className="font-semibold text-base line-clamp-2 mb-2 leading-tight">
            {product.title}
          </h3>
          
          <div className="space-y-1">
            {product.sku && (
              <p className="text-xs text-muted-foreground">
                <span className="font-medium">SKU:</span> {product.sku}
              </p>
            )}
            <p className="text-xs text-muted-foreground">
              <span className="font-medium">ML ID:</span> {product.mlId}
            </p>
          </div>
        </div>

        {/* Sales Metrics */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-muted-foreground">Vendidos:</span>
            <div className="flex items-center gap-1">
              <TrendingUp className="h-3 w-3 text-green-500" />
              <span className="font-medium text-sm">{product.soldQuantity}</span>
            </div>
          </div>
          
          {product.metrics?.averageDailySales && (
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Média diária:</span>
              <span className="font-medium text-sm">
                {product.metrics.averageDailySales.toFixed(1)}/dia
              </span>
            </div>
          )}
        </div>

        {/* Stock Info */}
        <div className="mb-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Estoque:</span>
            <div className="flex items-center gap-2">
              <span className="font-semibold">{product.availableQuantity}</span>
              <Badge variant={stockStatus.color as any} className="text-xs">
                {stockStatus.status}
              </Badge>
            </div>
          </div>
          
          {/* Stock Indicators */}
          <div className="flex justify-center mb-2">
            <StockIndicators 
              product={product} 
              showDetailed={false}
              size="sm"
            />
          </div>
          
          {/* Gap - If exists, show highlighted */}
          {product.stockCalculation?.gap && product.stockCalculation.gap > 0 && (
            <div className="bg-orange-50 border border-orange-200 rounded-md p-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-orange-800">Gap:</span>
                <span className="text-sm font-bold text-orange-600">
                  +{product.stockCalculation.gap} unidades
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Quantity Configuration */}
        {showQuantityInput && isSelected && (
          <div className="mb-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Quantidade:</span>
              {isEditingQuantity ? (
                <div className="flex items-center gap-2">
                  <Input
                    type="number"
                    value={tempQuantity}
                    onChange={(e) => setTempQuantity(e.target.value)}
                    className="w-20 h-8"
                    min={minQuantity}
                    max={maxQuantity || product.availableQuantity}
                  />
                  <Button
                    size="sm"
                    onClick={handleQuantitySubmit}
                    className="h-8"
                  >
                    ✓
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleQuantityCancel}
                    className="h-8"
                  >
                    ✕
                  </Button>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="font-bold text-lg">{quantity}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => setIsEditingQuantity(true)}
                    className="h-6 w-6 p-0"
                  >
                    <Edit3 className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
            
            {/* Total Value */}
            <div className="bg-green-50 border border-green-200 rounded-md p-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-green-800">Total:</span>
                <span className="text-sm font-bold text-green-600">
                  R$ {totalValue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Price - Footer */}
        <div className="mt-auto pt-3 border-t">
          <div className="text-center">
            <span className="text-xl font-bold text-green-600">
              R$ {product.price.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
            </span>
            <div className="text-xs text-muted-foreground mt-1">por unidade</div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-center gap-2 mt-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => window.open(product.permalink, '_blank')}
              className="text-xs"
            >
              <ExternalLink className="h-3 w-3 mr-1" />
              Ver no ML
            </Button>
          </div>
          
          {/* Eligibility Message */}
          {!isEligible && (
            <div className="text-xs text-red-600 text-center mt-2">
              {product.availableQuantity === 0 ? 'Sem estoque' : 'Produto não elegível para ML Full'}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
