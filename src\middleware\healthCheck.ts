import { Request, Response } from 'express';
import { getDatabase } from '@/config/database';
import { getRedisClient, getRedisStatus } from '@/config/redis';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: {
      status: 'connected' | 'disconnected' | 'error';
      responseTime?: number;
      error?: string;
    };
    redis: {
      status: 'connected' | 'disconnected' | 'error';
      responseTime?: number;
      error?: string;
    };
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  system: {
    platform: string;
    nodeVersion: string;
    pid: number;
  };
}

// Função para verificar status do banco de dados
const checkDatabaseHealth = async () => {
  try {
    const startTime = Date.now();
    const db = getDatabase();
    await db.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'connected' as const,
      responseTime,
    };
  } catch (error) {
    return {
      status: 'error' as const,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Função para verificar status do Redis
const checkRedisHealth = async () => {
  try {
    const redis = getRedisClient();
    
    if (!redis) {
      return {
        status: 'disconnected' as const,
        error: 'Redis not available - using memory cache fallback',
      };
    }
    
    const startTime = Date.now();
    await redis.ping();
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'connected' as const,
      responseTime,
    };
  } catch (error) {
    return {
      status: 'disconnected' as const,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

// Função para obter informações de memória
const getMemoryInfo = () => {
  const memUsage = process.memoryUsage();
  const totalMemory = memUsage.heapTotal;
  const usedMemory = memUsage.heapUsed;
  
  return {
    used: Math.round(usedMemory / 1024 / 1024), // MB
    total: Math.round(totalMemory / 1024 / 1024), // MB
    percentage: Math.round((usedMemory / totalMemory) * 100),
  };
};

// Middleware principal de health check
export const healthCheck = async (req: Request, res: Response): Promise<void> => {
  try {
    // Verificar serviços
    const [databaseHealth, redisHealth] = await Promise.all([
      checkDatabaseHealth(),
      checkRedisHealth(),
    ]);

    // Determinar status geral (Redis não é crítico)
    const isHealthy = databaseHealth.status === 'connected';

    // Montar resposta
    const healthStatus: HealthStatus = {
      status: isHealthy ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: databaseHealth,
        redis: redisHealth,
      },
      memory: getMemoryInfo(),
      system: {
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid,
      },
    };

    // Definir status code baseado na saúde (apenas database é crítico)
    const statusCode = isHealthy ? 200 : 503;
    
    res.status(statusCode).json(healthStatus);
  } catch (error) {
    // Erro no próprio health check
    const errorResponse: HealthStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      uptime: Math.floor(process.uptime()),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: {
          status: 'error',
          error: 'Health check failed',
        },
        redis: {
          status: 'error',
          error: 'Health check failed',
        },
      },
      memory: getMemoryInfo(),
      system: {
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid,
      },
    };

    res.status(503).json(errorResponse);
  }
};

// Health check simples (apenas status 200)
export const simpleHealthCheck = (req: Request, res: Response): void => {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
  });
};

export default healthCheck; 