import React, { useEffect, useState } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Separator } from '../ui/separator';
import {
  ClockIcon,
  ComputerDesktopIcon,
  GlobeAltIcon,
  ArrowRightOnRectangleIcon,
  DocumentTextIcon,
  UserPlusIcon,
  Cog6ToothIcon,
  KeyIcon,
  CloudArrowUpIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { useAuthStore } from '../../store/authStore';
import {
  getUserActivities,
  getSessionInfo,
  formatLastLogin,
  type ActivityEntry,
  type SessionInfo,
} from '../../mocks/profileMock';
import { FadeIn, SlideIn } from '../ui/Animations';

interface ActivityHistoryProps {
  showTitle?: boolean;
  maxActivities?: number;
  className?: string;
}

const ActivityHistory: React.FC<ActivityHistoryProps> = ({
  showTitle = true,
  maxActivities = 10,
  className = '',
}) => {
  const { user } = useAuthStore();
  const [activities, setActivities] = useState<ActivityEntry[]>([]);
  const [sessionInfo, setSessionInfo] = useState<SessionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      if (!user?.id) return;

      try {
        setIsLoading(true);
        setError(null);

        const [activitiesData, sessionData] = await Promise.all([
          getUserActivities(user.id, maxActivities),
          getSessionInfo(user.id),
        ]);

        setActivities(activitiesData);
        setSessionInfo(sessionData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro ao carregar dados');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [user?.id, maxActivities]);

  const getActivityIcon = (action: string) => {
    const iconClass = "h-4 w-4";
    
    switch (action) {
      case 'LOGIN':
        return <ArrowRightOnRectangleIcon className={`${iconClass} text-green-500`} />;
      case 'LOGOUT':
        return <ArrowRightOnRectangleIcon className={`${iconClass} text-gray-500`} />;
      case 'UPDATE':
        return <DocumentTextIcon className={`${iconClass} text-blue-500`} />;
      case 'CREATE':
        return <UserPlusIcon className={`${iconClass} text-green-500`} />;
      case 'DELETE':
        return <DocumentTextIcon className={`${iconClass} text-red-500`} />;
      case 'EXPORT':
        return <CloudArrowUpIcon className={`${iconClass} text-purple-500`} />;
      case 'SYNC':
        return <ArrowPathIcon className={`${iconClass} text-blue-500`} />;
      case 'CONFIG_CHANGE':
        return <Cog6ToothIcon className={`${iconClass} text-orange-500`} />;
      case 'PASSWORD_CHANGE':
        return <KeyIcon className={`${iconClass} text-yellow-500`} />;
      default:
        return <DocumentTextIcon className={`${iconClass} text-gray-500`} />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'info':
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora mesmo';
    if (diffInMinutes < 60) return `${diffInMinutes} min atrás`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} dias atrás`;
    
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader>
            <CardTitle>Histórico de Atividades</CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        {showTitle && (
          <CardHeader>
            <CardTitle>Histórico de Atividades</CardTitle>
          </CardHeader>
        )}
        <CardContent>
          <div className="text-red-600 text-sm">
            Erro ao carregar histórico: {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showTitle && (
        <CardHeader>
          <CardTitle>Histórico de Atividades</CardTitle>
          <p className="text-sm text-muted-foreground">
            Suas atividades recentes e informações de sessão.
          </p>
        </CardHeader>
      )}
      <CardContent className="space-y-6">
        {/* Current Session Info */}
        {sessionInfo && (
          <FadeIn>
            <div className="space-y-4">
              <h3 className="font-medium text-sm">Sessão Atual</h3>
              <div className="bg-muted/50 rounded-lg p-4 space-y-3">
                <div className="flex items-center space-x-2">
                  <ClockIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">
                    Iniciada em {formatTimestamp(sessionInfo.currentSession.startedAt)}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <ComputerDesktopIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">
                    {sessionInfo.currentSession.ipAddress}
                  </span>
                </div>
                
                {sessionInfo.currentSession.location && (
                  <div className="flex items-center space-x-2">
                    <GlobeAltIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-muted-foreground">
                      {sessionInfo.currentSession.location}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </FadeIn>
        )}

        {/* Last Login */}
        {user?.lastLoginAt && (
          <FadeIn delay={50}>
            <div className="space-y-2">
              <h3 className="font-medium text-sm">Último Login</h3>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs">
                  {formatLastLogin(user.lastLoginAt)}
                </Badge>
              </div>
            </div>
          </FadeIn>
        )}

        <Separator />

        {/* Recent Activities */}
        <FadeIn delay={100}>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium text-sm">Atividades Recentes</h3>
              <Badge variant="secondary" className="text-xs">
                {activities.length} atividades
              </Badge>
            </div>

            <div className="space-y-3">
              {activities.map((activity, index) => (
                <SlideIn key={activity.id} delay={index * 50} direction="left">
                  <div className="flex items-start space-x-3 p-3 rounded-lg border border-border hover:bg-muted/50 transition-colors">
                    <div className="flex-shrink-0 mt-0.5">
                      {getActivityIcon(activity.action)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-foreground">
                          {activity.description}
                        </p>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getSeverityColor(activity.severity)}`}
                        >
                          {activity.action}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center space-x-4 mt-1">
                        <span className="text-xs text-muted-foreground">
                          {formatTimestamp(activity.timestamp)}
                        </span>
                        
                        {activity.ipAddress && (
                          <span className="text-xs text-muted-foreground">
                            {activity.ipAddress}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </SlideIn>
              ))}
            </div>

            {activities.length === 0 && (
              <div className="text-center py-8">
                <DocumentTextIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-sm text-muted-foreground">
                  Nenhuma atividade recente encontrada.
                </p>
              </div>
            )}
          </div>
        </FadeIn>

        {/* Recent Sessions */}
        {sessionInfo?.recentSessions && sessionInfo.recentSessions.length > 0 && (
          <FadeIn delay={150}>
            <div className="space-y-4">
              <Separator />
              
              <h3 className="font-medium text-sm">Sessões Recentes</h3>
              
              <div className="space-y-2">
                {sessionInfo.recentSessions.slice(0, 3).map((session, index) => (
                  <SlideIn key={session.id} delay={index * 30} direction="left">
                    <div className="flex items-center justify-between p-2 rounded border border-border">
                      <div className="flex items-center space-x-2">
                        <ComputerDesktopIcon className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-xs font-medium">
                            {formatTimestamp(session.startedAt)}
                          </p>
                          {session.location && (
                            <p className="text-xs text-muted-foreground">
                              {session.location}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      <div className="text-right">
                        {session.duration && (
                          <p className="text-xs text-muted-foreground">
                            {session.duration}
                          </p>
                        )}
                        <p className="text-xs text-muted-foreground">
                          {session.ipAddress}
                        </p>
                      </div>
                    </div>
                  </SlideIn>
                ))}
              </div>
            </div>
          </FadeIn>
        )}
      </CardContent>
    </Card>
  );
};

export default ActivityHistory;
