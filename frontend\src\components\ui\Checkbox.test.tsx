 
import { render, screen, fireEvent } from '@testing-library/react';
import { Checkbox } from './Checkbox';
import { describe, it, expect } from 'vitest';

describe('Checkbox', () => {
  it('should render a checkbox', () => {
    render(<Checkbox data-testid="test-checkbox" />);
    expect(screen.getByTestId('test-checkbox')).toBeInTheDocument();
    expect(screen.getByTestId('test-checkbox')).toHaveAttribute('type', 'checkbox');
  });

  it('should render with a label', () => {
    render(<Checkbox label="Remember me" />);
    expect(screen.getByLabelText('Remember me')).toBeInTheDocument();
  });

  it('should be checked when defaultChecked is true', () => {
    render(<Checkbox defaultChecked />);
    expect(screen.getByRole('checkbox')).toBeChecked();
  });

  it('should toggle checked state on click', () => {
    render(<Checkbox />);
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).not.toBeChecked();
    fireEvent.click(checkbox);
    expect(checkbox).toBeChecked();
    fireEvent.click(checkbox);
    expect(checkbox).not.toBeChecked();
  });

  it('should call onChange handler when clicked', () => {
    const handleChange = vi.fn();
    render(<Checkbox onChange={handleChange} />);
    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);
    expect(handleChange).toHaveBeenCalledTimes(1);
  });

  it('should be disabled when disabled prop is true', () => {
    render(<Checkbox disabled />);
    expect(screen.getByRole('checkbox')).toBeDisabled();
  });

  it('should apply custom variant classes', () => {
    render(<Checkbox variant="primary" data-testid="checkbox-primary" />);
    const checkbox = screen.getByTestId('checkbox-primary');
    expect(checkbox).toHaveClass('text-design-primary-600');
  });

  it('should apply custom size classes', () => {
    render(<Checkbox size="lg" data-testid="checkbox-lg" />);
    const checkbox = screen.getByTestId('checkbox-lg');
    expect(checkbox).toHaveClass('h-6');
    expect(checkbox).toHaveClass('w-6');
  });
}); 
