/**
 * Componente de Configurações de Alertas de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select-radix';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Switch } from '../ui/Switch';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Alert, AlertDescription } from '../ui/alert';
import {
  Bell,
  Clock,
  Settings,
  AlertTriangle,
  CheckCircle,
  Play,
  Pause,
  RotateCcw,
  Info,
  Zap
} from 'lucide-react';
import { useToast } from '../ui/Toast';

interface FrequencyPreset {
  name: string;
  description: string;
  verificationsPerDay: number;
  frequencyMinutes: number;
  recommended: boolean;
  useCase: string;
}

interface AlertConfiguration {
  enabled: boolean;
  lowStockThreshold: number;
  criticalStockThreshold: number;
  checkFrequencyMinutes: number;
  notificationChannels: string[];
  autoResolveEnabled: boolean;
  escalationEnabled: boolean;
  escalationThresholdHours: number;
  scheduler: {
    isActive: boolean;
    frequencyMinutes: number;
    verificationsPerDay: number;
    nextExecution?: string;
  };
}

interface AlertStatistics {
  totalAlerts: number;
  activeAlerts: number;
  resolvedAlerts: number;
  criticalAlerts: number;
  warningAlerts: number;
  averageResolutionTime: number;
}

interface JobMetrics {
  totalExecutions: number;
  successful: number;
  failed: number;
  successRate: number;
  totalAlertsGenerated: number;
  totalNotificationsSent: number;
  lastExecution: string | null;
}

export const StockAlertSettings: React.FC = () => {
  const { addToast } = useToast();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [executing, setExecuting] = useState(false);
  
  const [config, setConfig] = useState<AlertConfiguration | null>(null);
  const [statistics, setStatistics] = useState<AlertStatistics | null>(null);
  const [jobMetrics, setJobMetrics] = useState<JobMetrics | null>(null);
  const [presets, setPresets] = useState<FrequencyPreset[]>([]);
  
  const [formData, setFormData] = useState({
    enabled: true,
    lowStockThreshold: 10,
    criticalStockThreshold: 5,
    verificationsPerDay: 4,
    notificationChannels: ['email', 'system'],
    autoResolveEnabled: true,
    escalationEnabled: false,
    escalationThresholdHours: 24
  });

  // Carregar dados iniciais
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Carregar configuração atual
      const configResponse = await fetch('/api/stock-alerts/config');
      if (configResponse.ok) {
        const configData = await configResponse.json();
        setConfig(configData.configuration);
        setStatistics(configData.statistics);
        setJobMetrics(configData.jobMetrics);
        
        // Atualizar formulário com dados atuais
        setFormData({
          enabled: configData.configuration.enabled,
          lowStockThreshold: configData.configuration.lowStockThreshold,
          criticalStockThreshold: configData.configuration.criticalStockThreshold,
          verificationsPerDay: configData.configuration.scheduler.verificationsPerDay,
          notificationChannels: configData.configuration.notificationChannels,
          autoResolveEnabled: configData.configuration.autoResolveEnabled,
          escalationEnabled: configData.configuration.escalationEnabled,
          escalationThresholdHours: configData.configuration.escalationThresholdHours
        });
      }
      
      // Carregar presets de frequência
      const presetsResponse = await fetch('/api/stock-alerts/frequency-presets');
      if (presetsResponse.ok) {
        const presetsData = await presetsResponse.json();
        setPresets(presetsData.presets);
      }
      
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
      addToast({
        title: 'Erro',
        message: 'Erro ao carregar configurações de alertas',
        variant: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/stock-alerts/config', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });
      
      if (response.ok) {
        const result = await response.json();
        setConfig(result.configuration);
        
        addToast({
          title: 'Sucesso',
          message: 'Configurações de alertas atualizadas com sucesso',
          variant: 'success'
        });
        
        // Recarregar dados para refletir mudanças
        await loadData();
      } else {
        throw new Error('Erro ao salvar configurações');
      }
      
    } catch (error) {
      console.error('Erro ao salvar:', error);
      addToast({
        title: 'Erro',
        message: 'Erro ao salvar configurações',
        variant: 'error'
      });
    } finally {
      setSaving(false);
    }
  };

  const handleToggleSystem = async () => {
    try {
      const response = await fetch('/api/stock-alerts/toggle', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled: !formData.enabled })
      });
      
      if (response.ok) {
        setFormData(prev => ({ ...prev, enabled: !prev.enabled }));
        await loadData();
        
        addToast({
          title: 'Sucesso',
          message: `Sistema de alertas ${!formData.enabled ? 'ativado' : 'desativado'}`,
          variant: 'success'
        });
      }
    } catch (error) {
      console.error('Erro ao alterar status:', error);
      addToast({
        title: 'Erro',
        message: 'Erro ao alterar status do sistema',
        variant: 'error'
      });
    }
  };

  const handleManualExecution = async () => {
    try {
      setExecuting(true);
      
      const response = await fetch('/api/stock-alerts/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ force: true })
      });
      
      if (response.ok) {
        const result = await response.json();
        
        addToast({
          title: 'Execução Concluída',
          message: `${result.execution.alertsGenerated} alertas gerados, ${result.execution.notificationsSent} notificações enviadas`,
          variant: 'success'
        });
        
        // Recarregar dados
        await loadData();
      } else {
        throw new Error('Erro na execução manual');
      }
      
    } catch (error) {
      console.error('Erro na execução manual:', error);
      addToast({
        title: 'Erro',
        message: 'Erro na execução manual de alertas',
        variant: 'error'
      });
    } finally {
      setExecuting(false);
    }
  };

  const handlePresetSelect = (preset: FrequencyPreset) => {
    setFormData(prev => ({
      ...prev,
      verificationsPerDay: preset.verificationsPerDay
    }));
  };

  const formatNextExecution = (dateString?: string) => {
    if (!dateString) return 'Não agendado';
    
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = date.getTime() - now.getTime();
    
    if (diffMs < 0) return 'Atrasado';
    
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `Em ${diffHours}h ${diffMinutes}m`;
    } else {
      return `Em ${diffMinutes}m`;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status do Sistema */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <CardTitle>Sistema de Alertas de Estoque</CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant={config?.scheduler.isActive ? 'default' : 'secondary'}>
                {config?.scheduler.isActive ? 'Ativo' : 'Inativo'}
              </Badge>
              <Switch
                checked={formData.enabled}
                onCheckedChange={handleToggleSystem}
              />
            </div>
          </div>
          <CardDescription>
            Configure a frequência e parâmetros dos alertas de produtos com baixo estoque
          </CardDescription>
        </CardHeader>
        <CardContent>
          {config?.scheduler.isActive && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="flex items-center justify-between">
                  <span>
                    Verificando estoque {config.scheduler.verificationsPerDay}x por dia
                    (a cada {Math.round(config.scheduler.frequencyMinutes / 60)}h)
                  </span>
                  <span className="text-sm text-muted-foreground">
                    Próxima: {formatNextExecution(config.scheduler.nextExecution)}
                  </span>
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Configuração de Frequência */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Frequência de Verificação</span>
          </CardTitle>
          <CardDescription>
            Defina quantas vezes por dia o sistema deve verificar o estoque
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Presets de Frequência */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {presets.map((preset) => (
              <Card 
                key={preset.verificationsPerDay}
                className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                  formData.verificationsPerDay === preset.verificationsPerDay 
                    ? 'ring-2 ring-blue-500 bg-blue-50' 
                    : ''
                }`}
                onClick={() => handlePresetSelect(preset)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{preset.name}</h4>
                    {preset.recommended && (
                      <Badge variant="default" className="text-xs">
                        Recomendado
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {preset.description}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {preset.useCase}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Configuração Personalizada */}
          <Separator />
          <div className="space-y-4">
            <Label>Configuração Personalizada</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="verificationsPerDay">Verificações por Dia</Label>
                <Input
                  id="verificationsPerDay"
                  type="number"
                  min="1"
                  max="24"
                  value={formData.verificationsPerDay}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    verificationsPerDay: parseInt(e.target.value) || 1
                  }))}
                />
              </div>
              <div className="space-y-2">
                <Label>Intervalo Calculado</Label>
                <div className="p-2 bg-muted rounded text-sm">
                  A cada {Math.round(1440 / formData.verificationsPerDay)} minutos
                  ({Math.round(1440 / formData.verificationsPerDay / 60)} horas)
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Configurações de Limites */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5" />
            <span>Limites de Estoque</span>
          </CardTitle>
          <CardDescription>
            Configure os limites que acionam os alertas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="lowStockThreshold">Limite de Estoque Baixo</Label>
              <Input
                id="lowStockThreshold"
                type="number"
                min="1"
                value={formData.lowStockThreshold}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  lowStockThreshold: parseInt(e.target.value) || 1
                }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="criticalStockThreshold">Limite Crítico</Label>
              <Input
                id="criticalStockThreshold"
                type="number"
                min="0"
                value={formData.criticalStockThreshold}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  criticalStockThreshold: parseInt(e.target.value) || 0
                }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Estatísticas */}
      {statistics && jobMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Alertas Ativos</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{statistics.activeAlerts}</div>
              <p className="text-xs text-muted-foreground">
                {statistics.criticalAlerts} críticos
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Taxa de Sucesso</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{jobMetrics.successRate.toFixed(1)}%</div>
              <p className="text-xs text-muted-foreground">
                {jobMetrics.successful}/{jobMetrics.totalExecutions} execuções
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Última Execução</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-sm">
                {jobMetrics.lastExecution 
                  ? new Date(jobMetrics.lastExecution).toLocaleString()
                  : 'Nunca'
                }
              </div>
              <p className="text-xs text-muted-foreground">
                {jobMetrics.totalAlertsGenerated} alertas gerados
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Ações */}
      <div className="flex items-center justify-between">
        <Button
          variant="outline"
          onClick={handleManualExecution}
          disabled={executing || !formData.enabled}
          className="flex items-center space-x-2"
        >
          {executing ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
          ) : (
            <Zap className="h-4 w-4" />
          )}
          <span>{executing ? 'Executando...' : 'Executar Agora'}</span>
        </Button>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={loadData}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Atualizar
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? 'Salvando...' : 'Salvar Configurações'}
          </Button>
        </div>
      </div>
    </div>
  );
};