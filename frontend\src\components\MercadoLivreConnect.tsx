import React, { useEffect, useState } from 'react';
import { useMercadoLivreStore } from '../store/mercadoLivreStore';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import { 
  ShoppingBag, 
  RefreshCw, 
  Trash2, 
  ExternalLink,
  CheckCircle,
  AlertCircle,
  Clock
} from 'lucide-react';
// Função simples para formatar tempo relativo
const formatTimeAgo = (date: Date): string => {
  const now = new Date();
  const diffInMs = now.getTime() - date.getTime();
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  // Se for data futura (token expira em...)
  if (diffInMs < 0) {
    const absDiffInMinutes = Math.abs(diffInMinutes);
    const absDiffInHours = Math.abs(diffInHours);
    const absDiffInDays = Math.abs(diffInDays);

    if (absDiffInMinutes < 60) return `em ${absDiffInMinutes} minuto${absDiffInMinutes > 1 ? 's' : ''}`;
    if (absDiffInHours < 24) return `em ${absDiffInHours} hora${absDiffInHours > 1 ? 's' : ''}`;
    return `em ${absDiffInDays} dia${absDiffInDays > 1 ? 's' : ''}`;
  }

  // Se for data passada
  if (diffInMinutes < 1) return 'agora mesmo';
  if (diffInMinutes < 60) return `há ${diffInMinutes} minuto${diffInMinutes > 1 ? 's' : ''}`;
  if (diffInHours < 24) return `há ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`;
  return `há ${diffInDays} dia${diffInDays > 1 ? 's' : ''}`;
};

interface MercadoLivreConnectProps {
  onAccountSelected?: (accountId: string) => void;
}

export default function MercadoLivreConnect({ onAccountSelected }: MercadoLivreConnectProps) {
  const {
    accounts,
    selectedAccount,
    accountsLoading,
    accountsError,
    productsError,
    statsError,
    syncStatus,
    loadAccounts,
    selectAccount,
    disconnectAccount,
    syncProducts,
    initiateAuth
  } = useMercadoLivreStore();

  const [isConnecting, setIsConnecting] = useState(false);

  useEffect(() => {
    loadAccounts();
  }, [loadAccounts]);

  const handleConnect = async () => {
    setIsConnecting(true);
    try {
      const authUrl = await initiateAuth();
      // Open in new window for OAuth flow
      const authWindow = window.open(authUrl, 'mercadolivre-auth', 'width=600,height=700');

      if (!authWindow) {
        throw new Error('Não foi possível abrir a janela de autenticação. Verifique se o bloqueador de pop-ups está desabilitado.');
      }

      // Listen for OAuth completion
      const handleMessage = (event: MessageEvent) => {
        if (event.data.type === 'MERCADOLIVRE_AUTH_SUCCESS') {
          loadAccounts();
          window.removeEventListener('message', handleMessage);
        } else if (event.data.type === 'MERCADOLIVRE_AUTH_ERROR') {
          console.error('Erro na autenticação ML:', event.data.error);
          window.removeEventListener('message', handleMessage);
        }
      };

      window.addEventListener('message', handleMessage);

      // Cleanup if window is closed manually
      const checkClosed = setInterval(() => {
        if (authWindow.closed) {
          clearInterval(checkClosed);
          window.removeEventListener('message', handleMessage);
          setIsConnecting(false);
        }
      }, 1000);

    } catch (error) {
      console.error('Erro ao conectar:', error);
      // TODO: Show user-friendly error message via notification system
    } finally {
      setIsConnecting(false);
    }
  };

  const handleDisconnect = async (accountId: string) => {
    if (confirm('Tem certeza que deseja desconectar esta conta?')) {
      try {
        await disconnectAccount(accountId);
        // Success notification will be handled by the store
      } catch (error) {
        console.error('Erro ao desconectar conta:', error);
        // TODO: Show user-friendly error message via notification system
      }
    }
  };

  const handleSync = async (accountId: string) => {
    try {
      await syncProducts(accountId);
      // Success notification will be handled by the store
    } catch (error) {
      console.error('Erro ao sincronizar produtos:', error);
      // Error notification will be handled by the store
    }
  };

  const handleSelectAccount = (account: any) => {
    selectAccount(account);
    onAccountSelected?.(account.id);
  };

  const getAccountStatus = (account: any) => {
    const expiresAt = new Date(account.expiresAt);
    const now = new Date();
    const isExpired = expiresAt < now;
    const isExpiringSoon = expiresAt.getTime() - now.getTime() < 24 * 60 * 60 * 1000; // 24 hours

    if (isExpired) {
      return { status: 'expired', color: 'destructive', icon: AlertCircle };
    } else if (isExpiringSoon) {
      return { status: 'expiring', color: 'warning', icon: Clock };
    } else {
      return { status: 'active', color: 'success', icon: CheckCircle };
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingBag className="h-5 w-5" />
          Integração Mercado Livre
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connect Button */}
        <div className="flex justify-between items-center">
          <div>
            <p className="text-sm text-muted-foreground">
              {accounts.length === 0 
                ? 'Conecte sua conta do Mercado Livre para sincronizar produtos e vendas'
                : `${accounts.length} conta(s) conectada(s)`
              }
            </p>
          </div>
          <Button 
            onClick={handleConnect} 
            disabled={isConnecting || accountsLoading}
            className="flex items-center gap-2"
          >
            {isConnecting ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <ExternalLink className="h-4 w-4" />
            )}
            {isConnecting ? 'Conectando...' : 'Conectar Conta'}
          </Button>
        </div>

        {/* Loading State */}
        {accountsLoading && (
          <>
            <Separator />
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span className="text-sm text-muted-foreground">Carregando contas...</span>
            </div>
          </>
        )}

        {/* Empty State */}
        {!accountsLoading && accounts.length === 0 && (
          <>
            <Separator />
            <div className="text-center py-8 space-y-3">
              <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center">
                <ShoppingBag className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <h4 className="text-sm font-medium">Nenhuma conta conectada</h4>
                <p className="text-xs text-muted-foreground mt-1">
                  Conecte sua conta do Mercado Livre para começar a sincronizar produtos e vendas
                </p>
              </div>
            </div>
          </>
        )}

        {/* Connected Accounts */}
        {!accountsLoading && accounts.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Contas Conectadas</h4>
              {accounts.map((account) => {
                const accountStatus = getAccountStatus(account);
                const StatusIcon = accountStatus.icon;
                
                return (
                  <div
                    key={account.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedAccount?.id === account.id
                        ? 'border-primary bg-primary/5'
                        : 'border-border hover:border-primary/50'
                    }`}
                    onClick={() => handleSelectAccount(account)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <StatusIcon className={`h-4 w-4 ${
                          accountStatus.color === 'success' ? 'text-green-500' :
                          accountStatus.color === 'warning' ? 'text-yellow-500' :
                          'text-red-500'
                        }`} />
                        <div>
                          <p className="font-medium">{account.nickname}</p>
                          <p className="text-xs text-muted-foreground">{account.email}</p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Badge variant={
                          accountStatus.color === 'success' ? 'default' :
                          accountStatus.color === 'warning' ? 'secondary' :
                          'destructive'
                        }>
                          {accountStatus.status === 'active' ? 'Ativo' :
                           accountStatus.status === 'expiring' ? 'Expirando' :
                           'Expirado'}
                        </Badge>
                        
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleSync(account.id);
                          }}
                          disabled={syncStatus.isLoading}
                          aria-label={`Sincronizar produtos da conta ${account.nickname}`}
                          title="Sincronizar produtos"
                        >
                          <RefreshCw className={`h-3 w-3 ${syncStatus.isLoading ? 'animate-spin' : ''}`} />
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDisconnect(account.id);
                          }}
                          aria-label={`Desconectar conta ${account.nickname}`}
                          title="Desconectar conta"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    
                    {/* Account Details */}
                    <div className="mt-2 text-xs text-muted-foreground">
                      <p>Token expira em: {formatTimeAgo(new Date(account.expiresAt))}</p>
                      {syncStatus.lastSync && selectedAccount?.id === account.id && (
                        <p>Última sincronização: {formatTimeAgo(new Date(syncStatus.lastSync))}</p>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </>
        )}

        {/* Sync Status */}
        {syncStatus.isLoading && (
          <>
            <Separator />
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Sincronizando produtos...</span>
                {syncStatus.progress > 0 && (
                  <span className="text-muted-foreground">({syncStatus.progress}%)</span>
                )}
              </div>
              {syncStatus.progress > 0 && (
                <div className="w-full bg-muted rounded-full h-2">
                  <div
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${syncStatus.progress}%` }}
                  />
                </div>
              )}
              {syncStatus.itemsProcessed && syncStatus.totalItems && (
                <div className="text-xs text-muted-foreground">
                  {syncStatus.itemsProcessed} de {syncStatus.totalItems} produtos processados
                </div>
              )}
            </div>
          </>
        )}

        {/* Error States */}
        {(accountsError || productsError || statsError || syncStatus.error) && (
          <>
            <Separator />
            <div className="space-y-2">
              {accountsError && (
                <div className="flex items-center gap-2 text-sm text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span>Erro ao carregar contas: {accountsError}</span>
                </div>
              )}
              {productsError && (
                <div className="flex items-center gap-2 text-sm text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span>Erro ao carregar produtos: {productsError}</span>
                </div>
              )}
              {statsError && (
                <div className="flex items-center gap-2 text-sm text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span>Erro ao carregar estatísticas: {statsError}</span>
                </div>
              )}
              {syncStatus.error && (
                <div className="flex items-center gap-2 text-sm text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span>Erro na sincronização: {syncStatus.error}</span>
                </div>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
