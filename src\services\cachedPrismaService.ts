import { PrismaClient } from '@prisma/client';
import CacheService, { CacheNamespaces, CacheConfigs } from './cacheService';
import { logger } from '../utils/logger';

export interface QueryCacheOptions {
  ttl?: number;
  namespace?: string;
  keyPrefix?: string;
  bypassCache?: boolean;
}

export interface CachedQueryResult<T> {
  data: T;
  fromCache: boolean;
  cacheKey?: string;
  queryTime: number;
}

export class CachedPrismaService {
  private prisma: PrismaClient;
  private cache: CacheService;
  private queryMetrics: Map<string, { count: number; totalTime: number; lastUsed: Date }>;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.cache = new CacheService();
    this.queryMetrics = new Map();
  }

  /**
   * Executar query com cache automático
   */
  async cachedQuery<T>(
    operation: () => Promise<T>,
    options: QueryCacheOptions = {},
    tenantId?: string
  ): Promise<CachedQueryResult<T>> {
    const startTime = Date.now();
    
    // Configurações padrão
    const {
      ttl = CacheConfigs.DATABASE_QUERIES.ttl,
      namespace = CacheNamespaces.DATABASE,
      keyPrefix = 'query',
      bypassCache = false
    } = options;

    // Gerar chave única para a query
    const queryKey = this.generateQueryKey(operation, keyPrefix);
    
    // Verificar se deve usar cache
    if (!bypassCache) {
      try {
        const cached = await this.cache.get(namespace, queryKey, tenantId);
        if (cached) {
          const queryTime = Date.now() - startTime;
          this.updateQueryMetrics(queryKey, queryTime, true);
          
          logger.debug(`Cache HIT para query: ${queryKey}`, {
            queryTime,
            namespace,
            tenantId
          });

          return {
            data: cached,
            fromCache: true,
            cacheKey: queryKey,
            queryTime
          };
        }
      } catch (error) {
        logger.warn('Erro ao buscar cache, executando query normal', { error, queryKey });
      }
    }

    // Executar query no banco
    try {
      const result = await operation();
      const queryTime = Date.now() - startTime;
      
      // Armazenar no cache se não for bypass
      if (!bypassCache && result !== null && result !== undefined) {
        setImmediate(async () => {
          try {
            await this.cache.set(namespace, queryKey, result, { ttl }, tenantId);
          } catch (error) {
            logger.warn('Erro ao armazenar no cache', { error, queryKey });
          }
        });
      }

      this.updateQueryMetrics(queryKey, queryTime, false);
      
      logger.debug(`Query executada: ${queryKey}`, {
        queryTime,
        fromCache: false,
        tenantId
      });

      return {
        data: result,
        fromCache: false,
        cacheKey: queryKey,
        queryTime
      };
    } catch (error) {
      const queryTime = Date.now() - startTime;
      logger.error('Erro na execução da query', { error, queryKey, queryTime });
      throw error;
    }
  }

  /**
   * Cache específico para busca de usuários
   */
  async getUsersWithCache(
    filters: any,
    tenantId: string,
    options: QueryCacheOptions = {}
  ) {
    return this.cachedQuery(
      () => this.prisma.user.findMany({
        where: {
          tenantId,
          ...filters
        }
      }),
      {
        ...options,
        keyPrefix: 'users',
        ttl: CacheConfigs.PRODUCTS.ttl
      },
      tenantId
    );
  }

  /**
   * Cache específico para contas do Mercado Livre
   */
  async getMercadoLivreAccountsWithCache(
    filters: any,
    tenantId: string,
    options: QueryCacheOptions = {}
  ) {
    return this.cachedQuery(
      () => this.prisma.mercadoLivreAccount.findMany({
        where: {
          tenantId,
          ...filters
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      {
        ...options,
        keyPrefix: 'ml-accounts',
        ttl: CacheConfigs.MEDIUM.ttl
      },
      tenantId
    );
  }

  /**
   * Cache específico para tenants
   */
  async getTenantsWithCache(
    filters: any,
    options: QueryCacheOptions = {}
  ) {
    return this.cachedQuery(
      () => this.prisma.tenant.findMany({
        where: filters,
        include: {
          users: true
        }
      }),
      {
        ...options,
        keyPrefix: 'tenants',
        ttl: CacheConfigs.LONG.ttl // Tenants mudam raramente
      }
    );
  }

  /**
   * Cache específico para configurações do tenant
   */
  async getTenantConfigWithCache(
    tenantId: string,
    options: QueryCacheOptions = {}
  ) {
    return this.cachedQuery(
      () => this.prisma.tenant.findUnique({
        where: { id: tenantId },
        include: {
          users: true
        }
      }),
      {
        ...options,
        keyPrefix: `tenant-config`,
        ttl: CacheConfigs.LONG.ttl // Configurações mudam raramente
      },
      tenantId
    );
  }

  /**
   * Cache específico para métricas
   */
  async getMetricsWithCache(
    period: string,
    tenantId: string,
    options: QueryCacheOptions = {}
  ) {
    return this.cachedQuery(
      async () => {
        const endDate = new Date();
        const startDate = new Date();
        
        // Calcular período
        switch (period) {
          case 'day':
            startDate.setDate(endDate.getDate() - 1);
            break;
          case 'week':
            startDate.setDate(endDate.getDate() - 7);
            break;
          case 'month':
            startDate.setMonth(endDate.getMonth() - 1);
            break;
          default:
            startDate.setDate(endDate.getDate() - 7);
        }

                 const [calculations, sales, products] = await Promise.all([
           this.prisma.stockCalculation.count({
             where: {
               tenantId,
               createdAt: {
                 gte: startDate,
                 lte: endDate
               }
             }
           }),
           this.prisma.sale.count({
             where: {
               product: {
                 tenantId
               },
               createdAt: {
                 gte: startDate,
                 lte: endDate
               }
             }
           }),
           this.prisma.product.count({
             where: { tenantId }
           })
         ]);

                 return {
           period,
           calculations,
           sales,
           products,
           generatedAt: new Date().toISOString()
         };
      },
      {
        ...options,
        keyPrefix: `metrics-${period}`,
        ttl: CacheConfigs.MEDIUM.ttl
      },
      tenantId
    );
  }

  /**
   * Invalidar cache específico
   */
  async invalidateCache(
    namespace: string,
    pattern: string,
    tenantId?: string
  ): Promise<number> {
    try {
      const deleted = await this.cache.deleteByPattern(pattern, tenantId);
      logger.info(`Cache invalidado: ${namespace}/${pattern}`, { deleted, tenantId });
      return deleted;
    } catch (error) {
      logger.error('Erro ao invalidar cache', { error, namespace, pattern });
      return 0;
    }
  }

  /**
   * Invalidar cache de usuários
   */
  async invalidateUsersCache(tenantId: string): Promise<number> {
    return this.invalidateCache(CacheNamespaces.DATABASE, 'users', tenantId);
  }

  /**
   * Invalidar cache de contas do Mercado Livre
   */
  async invalidateMercadoLivreAccountsCache(tenantId: string): Promise<number> {
    return this.invalidateCache(CacheNamespaces.DATABASE, 'ml-accounts', tenantId);
  }

  /**
   * Invalidar cache de tenants
   */
  async invalidateTenantsCache(): Promise<number> {
    return this.invalidateCache(CacheNamespaces.DATABASE, 'tenants');
  }

  /**
   * Obter métricas de queries
   */
  getQueryMetrics(): any[] {
    return Array.from(this.queryMetrics.entries()).map(([query, metrics]) => ({
      query,
      count: metrics.count,
      avgTime: Math.round(metrics.totalTime / metrics.count),
      totalTime: metrics.totalTime,
      lastUsed: metrics.lastUsed
    })).sort((a, b) => b.count - a.count);
  }

  /**
   * Limpar métricas antigas
   */
  cleanupOldMetrics(): void {
    const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 horas
    
    for (const [query, metrics] of this.queryMetrics.entries()) {
      if (metrics.lastUsed < cutoff) {
        this.queryMetrics.delete(query);
      }
    }
  }

  /**
   * Gerar chave única para a query
   */
  private generateQueryKey(operation: Function, prefix: string): string {
    // Usar o toString da função para gerar uma chave única
    const functionString = operation.toString();
    const hash = this.simpleHash(functionString);
    return `${prefix}:${hash}`;
  }

  /**
   * Hash simples para chaves de cache
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    return Math.abs(hash).toString(16);
  }

  /**
   * Atualizar métricas de query
   */
  private updateQueryMetrics(queryKey: string, queryTime: number, fromCache: boolean): void {
    const current = this.queryMetrics.get(queryKey) || { count: 0, totalTime: 0, lastUsed: new Date() };
    
    current.count++;
    current.totalTime += queryTime;
    current.lastUsed = new Date();
    
    this.queryMetrics.set(queryKey, current);
  }
}

// Instância singleton
let cachedPrismaInstance: CachedPrismaService | null = null;

export function getCachedPrismaService(prisma: PrismaClient): CachedPrismaService {
  if (!cachedPrismaInstance) {
    cachedPrismaInstance = new CachedPrismaService(prisma);
  }
  return cachedPrismaInstance;
}

export default CachedPrismaService; 