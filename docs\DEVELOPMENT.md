# Guia de Desenvolvimento

Este guia cobre tudo o que você precisa saber para configurar e rodar o Magnow em um ambiente de desenvolvimento local.

## Configuração do Ambiente

Antes de começar, certifique-se de ter todos os pré-requisitos listados no [Guia de Instalação](./INSTALLATION.md) devidamente instalados e configurados.

### Variáveis de Ambiente

O Magnow utiliza variáveis de ambiente para configurar a aplicação. Para o desenvolvimento local, você precisará criar um arquivo `.env` na raiz do projeto.

1.  Copie o arquivo de exemplo:

    ```bash
    cp .env.example .env
    ```

2.  Edite o arquivo `.env` com as suas configurações locais. As variáveis mais importantes para o desenvolvimento são:

    ```dotenv
    # Ambiente da Aplicação
    NODE_ENV=development
    PORT=3000

    # URL do Banco de Dados PostgreSQL
    # Formato: postgresql://USUARIO:SENHA@HOST:PORTA/NOME_DO_BANCO
    DATABASE_URL="postgresql://docker:docker@localhost:5432/magnow_development?schema=public"

    # Chaves para assinatura de Tokens JWT (pode usar qualquer string segura)
    JWT_SECRET="seu-segredo-super-secreto-para-desenvolvimento"
    JWT_EXPIRES_IN="1d"

    # Credenciais do App do Mercado Livre (obtidas no portal de desenvolvedores)
    MERCADO_LIVRE_APP_ID="SEU_APP_ID"
    MERCADO_LIVRE_CLIENT_SECRET="SEU_CLIENT_SECRET"
    MERCADO_LIVRE_REDIRECT_URI="http://localhost:3000/api/v1/auth/mercadolivre/callback"
    ```

## Rodando a Aplicação

Com o ambiente configurado, você pode iniciar a aplicação.

### Usando Docker (Recomendado)

O Docker simplifica o processo, gerenciando o banco de dados e a aplicação para você.

```bash
# Inicia todos os serviços em background
docker-compose up -d
```

### Manualmente

Se preferir não usar o Docker, você pode rodar a aplicação diretamente.

```bash
# Instala as dependências (se ainda não o fez)
npm install

# Executa as migrações do banco de dados
npm run migrate

# Inicia o servidor em modo de desenvolvimento com hot-reload
npm run dev
```

A aplicação estará disponível em `http://localhost:3000`.

## Scripts Disponíveis

O projeto vem com vários scripts para auxiliar no desenvolvimento:

-   `npm run dev`: Inicia o servidor em modo de desenvolvimento com `ts-node-dev`.
-   `npm run build`: Compila o código TypeScript para JavaScript (para produção).
-   `npm run start`: Inicia o servidor em modo de produção (requer `npm run build` antes).
-   `npm run migrate`: Aplica as migrações pendentes do Prisma ao banco de dados.
-   `npm run seed`: Popula o banco de dados com dados de exemplo (se configurado).
-   `npm run lint`: Executa o ESLint para verificar a qualidade do código.
-   `npm run format`: Formata todo o código com o Prettier.
-   `npm test`: Roda toda a suíte de testes (unitários e de integração).