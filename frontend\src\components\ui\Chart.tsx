import type { ReactElement } from 'react';
import {
  Responsive<PERSON><PERSON>r,
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from 'recharts';

// Cores padrão para os gráficos
const COLORS = [
  '#3B82F6', // blue-500
  '#10B981', // green-500
  '#F59E0B', // yellow-500
  '#EF4444', // red-500
  '#8B5CF6', // purple-500
  '#06B6D4', // cyan-500
  '#F97316', // orange-500
  '#84CC16', // lime-500
];

// Tipos base para props dos gráficos
interface BaseChartProps {
  data: any[];
  loading?: boolean;
  error?: string;
  height?: number;
  title?: string;
  className?: string;
}

// Container base para todos os gráficos
function ChartContainer({ 
  children, 
  loading, 
  error, 
  height = 300, 
  title, 
  className = '' 
}: {
  children: ReactElement;
  loading?: boolean;
  error?: string;
  height?: number;
  title?: string;
  className?: string;
}) {
  return (
    <div className={`w-full ${className}`}>
      {title && (
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{title}</h3>
      )}
      
      {loading ? (
        <div className="flex items-center justify-center" style={{ height }}>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : error ? (
        <div className="flex items-center justify-center" style={{ height }}>
          <div className="text-center">
            <div className="text-red-500 text-sm mb-2">⚠️ Erro ao carregar gráfico</div>
            <div className="text-gray-500 text-xs">{error}</div>
          </div>
        </div>
      ) : (
        <ResponsiveContainer width="100%" height={height}>
          {children}
        </ResponsiveContainer>
      )}
    </div>
  );
}

// Gráfico de linha simples (compatível com SalesChart)
interface SimpleLineChartProps extends BaseChartProps {
  xKey: string;
  dataKey: string;
  color?: string;
  formatValue?: (value: number) => string;
}

export function CustomLineChart({ 
  data, 
  xKey, 
  dataKey, 
  color = COLORS[0],
  formatValue,
  loading, 
  error, 
  height, 
  title, 
  className 
}: SimpleLineChartProps) {
  return (
    <ChartContainer 
      loading={loading} 
      error={error} 
      height={height} 
      title={title} 
      className={className}
    >
      <LineChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey={xKey} />
        <YAxis tickFormatter={formatValue} />
        <Tooltip 
          formatter={formatValue ? formatValue : undefined}
        />
        <Line
          type="monotone"
          dataKey={dataKey}
          stroke={color}
          strokeWidth={2}
          dot={{ r: 4 }}
          activeDot={{ r: 6 }}
        />
      </LineChart>
    </ChartContainer>
  );
}

// Gráfico de área simples (compatível com SalesChart)
interface SimpleAreaChartProps extends BaseChartProps {
  xKey: string;
  dataKey: string;
  color?: string;
  formatValue?: (value: number) => string;
}

export function CustomAreaChart({ 
  data, 
  xKey, 
  dataKey, 
  color = COLORS[0],
  formatValue,
  loading, 
  error, 
  height, 
  title, 
  className 
}: SimpleAreaChartProps) {
  return (
    <ChartContainer 
      loading={loading} 
      error={error} 
      height={height} 
      title={title} 
      className={className}
    >
      <AreaChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey={xKey} />
        <YAxis tickFormatter={formatValue} />
        <Tooltip 
          formatter={formatValue ? formatValue : undefined}
        />
        <Area
          type="monotone"
          dataKey={dataKey}
          stroke={color}
          fill={color}
          fillOpacity={0.6}
        />
      </AreaChart>
    </ChartContainer>
  );
}

// Gráfico de barras
interface BarChartProps extends BaseChartProps {
  xKey: string;
  bars: {
    key: string;
    name: string;
    color?: string;
  }[];
}

export function CustomBarChart({ 
  data, 
  xKey, 
  bars, 
  loading, 
  error, 
  height, 
  title, 
  className 
}: BarChartProps) {
  return (
    <ChartContainer 
      loading={loading} 
      error={error} 
      height={height} 
      title={title} 
      className={className}
    >
      <BarChart data={data}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey={xKey} />
        <YAxis />
        <Tooltip />
        <Legend />
        {bars.map((bar, index) => (
          <Bar
            key={bar.key}
            dataKey={bar.key}
            name={bar.name}
            fill={bar.color || COLORS[index % COLORS.length]}
          />
        ))}
      </BarChart>
    </ChartContainer>
  );
}

// Gráfico de pizza
interface PieChartProps extends BaseChartProps {
  nameKey: string;
  valueKey: string;
  showPercentage?: boolean;
}

export function CustomPieChart({ 
  data, 
  nameKey, 
  valueKey, 
  showPercentage = true,
  loading, 
  error, 
  height, 
  title, 
  className 
}: PieChartProps) {
  const renderLabel = (entry: any) => {
    if (!showPercentage) return entry[nameKey];
    
    const total = data.reduce((sum, item) => sum + item[valueKey], 0);
    const percent = ((entry[valueKey] / total) * 100).toFixed(1);
    return `${entry[nameKey]} (${percent}%)`;
  };

  return (
    <ChartContainer 
      loading={loading} 
      error={error} 
      height={height} 
      title={title} 
      className={className}
    >
      <PieChart>
        <Pie
          data={data}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={renderLabel}
          outerRadius={80}
          fill="#8884d8"
          dataKey={valueKey}
        >
          {data.map((_entry, index) => (
            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
          ))}
        </Pie>
        <Tooltip />
      </PieChart>
    </ChartContainer>
  );
} 
