import { useState, useEffect } from 'react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '../components/ui/card';
import { Input } from '../components/ui/input';
import { Button } from '../components/ui/button';
import { Label } from '../components/ui/label';
import { Checkbox } from '../components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select-radix';
import MercadoLivreConnect from '../components/MercadoLivreConnect';
import { useNotificationStore } from '../store/notificationStore';
import { useConfigStore } from '../store/configStore';
import { ProfileForm, PasswordChangeForm } from '../components/profile';

type Tab = 'perfil' | 'tenant' | 'senha' | 'ml';

export default function Settings() {
  const [tab, setTab] = useState<Tab>('perfil');

  const renderTabButton = (id: Tab, label: string) => (
    <button
      key={id}
      className={`px-4 py-2 rounded-t-lg font-medium text-sm focus:outline-none transition-colors ${
        tab === id
          ? 'bg-card text-primary border-border border-b-transparent'
          : 'bg-muted text-muted-foreground hover:text-foreground'
      }`}
      onClick={() => setTab(id)}
    >
      {label}
    </button>
  );

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-foreground">Configurações</h1>
        <p className="mt-1 text-sm text-muted-foreground">
          Gerencie suas preferências, perfil e integrações.
        </p>
      </div>

      {/* Navegação de abas */}
      <div className="flex space-x-1 border-b border-border">
        {renderTabButton('perfil', 'Perfil')}
        {renderTabButton('tenant', 'Tenant')}
        {renderTabButton('senha', 'Alterar Senha')}
        {renderTabButton('ml', 'Integração ML')}
      </div>

      {/* Conteúdo da aba */}
      {tab === 'perfil' && <ProfileForm />}
      {tab === 'tenant' && <TenantForm />}
      {tab === 'senha' && <PasswordChangeForm />}
      {tab === 'ml' && <MLForm />}
    </div>
  );
}



function TenantForm() {
  const {
    tenantConfig,
    tenantConfigLoading,
    tenantConfigError,
    loadTenantConfig,
    updateTenantConfig
  } = useConfigStore();
  const { addNotification } = useNotificationStore();

  // Estados locais para formulário
  const [name, setName] = useState('');
  const [cnpj, setCnpj] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [website, setWebsite] = useState('');

  // Endereço
  const [street, setStreet] = useState('');
  const [number, setNumber] = useState('');
  const [complement, setComplement] = useState('');
  const [neighborhood, setNeighborhood] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');

  // Load tenant config on mount
  useEffect(() => {
    loadTenantConfig();
  }, [loadTenantConfig]);

  // Update form when config loads
  useEffect(() => {
    if (tenantConfig) {
      setName(tenantConfig.name);
      setCnpj(tenantConfig.cnpj || '');
      setEmail(tenantConfig.contact.email);
      setPhone(tenantConfig.contact.phone || '');
      setWebsite(tenantConfig.contact.website || '');

      if (tenantConfig.address) {
        setStreet(tenantConfig.address.street);
        setNumber(tenantConfig.address.number);
        setComplement(tenantConfig.address.complement || '');
        setNeighborhood(tenantConfig.address.neighborhood);
        setCity(tenantConfig.address.city);
        setState(tenantConfig.address.state);
        setZipCode(tenantConfig.address.zipCode);
      }
    }
  }, [tenantConfig]);

  // CNPJ validation
  const validateCNPJ = (cnpj: string): boolean => {
    const cleanCNPJ = cnpj.replace(/[^\d]/g, '');
    return cleanCNPJ.length === 14;
  };

  // CEP validation and auto-fill
  const handleCEPChange = async (cep: string) => {
    setZipCode(cep);

    const cleanCEP = cep.replace(/[^\d]/g, '');
    if (cleanCEP.length === 8) {
      try {
        // Simulate CEP API call
        await new Promise(resolve => setTimeout(resolve, 500));
        // In real implementation, call ViaCEP API
        // const response = await fetch(`https://viacep.com.br/ws/${cleanCEP}/json/`);
        // const data = await response.json();
        // if (!data.erro) {
        //   setStreet(data.logradouro);
        //   setNeighborhood(data.bairro);
        //   setCity(data.localidade);
        //   setState(data.uf);
        // }
      } catch (error) {
        console.warn('Erro ao buscar CEP:', error);
      }
    }
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validations
    if (!validateCNPJ(cnpj)) {
      addNotification({
        title: 'CNPJ Inválido',
        message: 'Por favor, insira um CNPJ válido com 14 dígitos.',
        type: 'error',
        severity: 'error',
        duration: 5000
      });
      return;
    }

    try {
      await updateTenantConfig({
        name,
        cnpj,
        contact: {
          email,
          phone: phone || undefined,
          website: website || undefined,
        },
        address: street ? {
          street,
          number,
          complement: complement || undefined,
          neighborhood,
          city,
          state,
          zipCode,
          country: 'Brasil',
        } : undefined,
      });

      addNotification({
        title: 'Configurações Salvas',
        message: 'As configurações da empresa foram salvas com sucesso.',
        type: 'system',
        severity: 'success',
        duration: 5000
      });
    } catch (error) {
      addNotification({
        title: 'Erro ao Salvar',
        message: 'Não foi possível salvar as configurações da empresa.',
        type: 'error',
        severity: 'error',
        duration: 8000
      });
    }
  };

  if (tenantConfigError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Configurações da Empresa</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-red-600 text-sm">
            Erro ao carregar configurações: {tenantConfigError}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configurações da Empresa</CardTitle>
        <p className="text-sm text-muted-foreground">
          Gerencie as informações da sua empresa e dados de contato.
        </p>
      </CardHeader>
      <CardContent>
        <form className="space-y-6 max-w-2xl" onSubmit={handleSave}>
          {/* Informações Básicas */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Informações Básicas</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Nome da Empresa</Label>
                <Input
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  disabled={tenantConfigLoading}
                  placeholder="Magnow Empresa Ltda"
                />
              </div>

              <div className="space-y-2">
                <Label>CNPJ</Label>
                <Input
                  value={cnpj}
                  onChange={(e) => setCnpj(e.target.value)}
                  required
                  disabled={tenantConfigLoading}
                  placeholder="12.345.678/0001-90"
                  maxLength={18}
                />
              </div>
            </div>
          </div>

          {/* Informações de Contato */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Informações de Contato</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Email</Label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  disabled={tenantConfigLoading}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label>Telefone</Label>
                <Input
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  disabled={tenantConfigLoading}
                  placeholder="+55 11 3333-4444"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Website</Label>
              <Input
                type="url"
                value={website}
                onChange={(e) => setWebsite(e.target.value)}
                disabled={tenantConfigLoading}
                placeholder="https://www.empresa.com.br"
              />
            </div>
          </div>

          {/* Endereço */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Endereço</h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>CEP</Label>
                <Input
                  value={zipCode}
                  onChange={(e) => handleCEPChange(e.target.value)}
                  disabled={tenantConfigLoading}
                  placeholder="01234-567"
                  maxLength={9}
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label>Rua</Label>
                <Input
                  value={street}
                  onChange={(e) => setStreet(e.target.value)}
                  disabled={tenantConfigLoading}
                  placeholder="Rua das Empresas"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Número</Label>
                <Input
                  value={number}
                  onChange={(e) => setNumber(e.target.value)}
                  disabled={tenantConfigLoading}
                  placeholder="123"
                />
              </div>

              <div className="space-y-2 md:col-span-2">
                <Label>Complemento</Label>
                <Input
                  value={complement}
                  onChange={(e) => setComplement(e.target.value)}
                  disabled={tenantConfigLoading}
                  placeholder="Sala 456"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Bairro</Label>
                <Input
                  value={neighborhood}
                  onChange={(e) => setNeighborhood(e.target.value)}
                  disabled={tenantConfigLoading}
                  placeholder="Centro"
                />
              </div>

              <div className="space-y-2">
                <Label>Cidade</Label>
                <Input
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  disabled={tenantConfigLoading}
                  placeholder="São Paulo"
                />
              </div>

              <div className="space-y-2">
                <Label>Estado</Label>
                <Select value={state} onValueChange={setState}>
                  <SelectTrigger disabled={tenantConfigLoading}>
                    <SelectValue placeholder="Selecione o estado" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AC">Acre</SelectItem>
                    <SelectItem value="AL">Alagoas</SelectItem>
                    <SelectItem value="AP">Amapá</SelectItem>
                    <SelectItem value="AM">Amazonas</SelectItem>
                    <SelectItem value="BA">Bahia</SelectItem>
                    <SelectItem value="CE">Ceará</SelectItem>
                    <SelectItem value="DF">Distrito Federal</SelectItem>
                    <SelectItem value="ES">Espírito Santo</SelectItem>
                    <SelectItem value="GO">Goiás</SelectItem>
                    <SelectItem value="MA">Maranhão</SelectItem>
                    <SelectItem value="MT">Mato Grosso</SelectItem>
                    <SelectItem value="MS">Mato Grosso do Sul</SelectItem>
                    <SelectItem value="MG">Minas Gerais</SelectItem>
                    <SelectItem value="PA">Pará</SelectItem>
                    <SelectItem value="PB">Paraíba</SelectItem>
                    <SelectItem value="PR">Paraná</SelectItem>
                    <SelectItem value="PE">Pernambuco</SelectItem>
                    <SelectItem value="PI">Piauí</SelectItem>
                    <SelectItem value="RJ">Rio de Janeiro</SelectItem>
                    <SelectItem value="RN">Rio Grande do Norte</SelectItem>
                    <SelectItem value="RS">Rio Grande do Sul</SelectItem>
                    <SelectItem value="RO">Rondônia</SelectItem>
                    <SelectItem value="RR">Roraima</SelectItem>
                    <SelectItem value="SC">Santa Catarina</SelectItem>
                    <SelectItem value="SP">São Paulo</SelectItem>
                    <SelectItem value="SE">Sergipe</SelectItem>
                    <SelectItem value="TO">Tocantins</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Button type="submit" disabled={tenantConfigLoading}>
            {tenantConfigLoading ? 'Salvando...' : 'Salvar Configurações'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}




function MLForm() {
  const [autoSync, setAutoSync] = useState(true);
  const [syncFrequency, setSyncFrequency] = useState('hourly');
  const [syncProducts, setSyncProducts] = useState(true);
  const [syncOrders, setSyncOrders] = useState(true);
  const [syncStock, setSyncStock] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  const { addNotification } = useNotificationStore();

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      // TODO: Save ML sync settings to API
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      addNotification({
        title: 'Configurações Salvas',
        message: 'As configurações de sincronização do Mercado Livre foram salvas com sucesso.',
        type: 'system',
        severity: 'success',
        duration: 5000
      });
    } catch (error) {
      addNotification({
        title: 'Erro ao Salvar',
        message: 'Não foi possível salvar as configurações. Tente novamente.',
        type: 'error',
        severity: 'error',
        duration: 8000
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Integração com Mercado Livre</CardTitle>
          <p className="text-sm text-muted-foreground">
            Configure e gerencie suas conexões com contas do Mercado Livre para sincronização automática de produtos e vendas.
          </p>
        </CardHeader>
        <CardContent>
          <MercadoLivreConnect />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Configurações de Sincronização</CardTitle>
          <p className="text-sm text-muted-foreground">
            Defina como e quando os dados devem ser sincronizados com o Mercado Livre.
          </p>
        </CardHeader>
        <CardContent>
          <form className="space-y-6 max-w-lg" onSubmit={handleSaveSettings}>
            {/* Auto Sync */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="autoSync"
                name="autoSync"
                checked={autoSync}
                onChange={(e) => setAutoSync(e.target.checked)}
              />
              <Label htmlFor="autoSync" className="text-sm font-medium">
                Sincronização automática
              </Label>
            </div>

            {/* Sync Frequency */}
            {autoSync && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Frequência de sincronização</Label>
                <Select value={syncFrequency} onValueChange={setSyncFrequency}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a frequência" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15min">A cada 15 minutos</SelectItem>
                    <SelectItem value="30min">A cada 30 minutos</SelectItem>
                    <SelectItem value="hourly">A cada hora</SelectItem>
                    <SelectItem value="daily">Diariamente</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Sync Options */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Dados para sincronizar</Label>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="syncProducts"
                  name="syncProducts"
                  checked={syncProducts}
                  onChange={(e) => setSyncProducts(e.target.checked)}
                />
                <Label htmlFor="syncProducts" className="text-sm">
                  Produtos e informações
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="syncOrders"
                  name="syncOrders"
                  checked={syncOrders}
                  onChange={(e) => setSyncOrders(e.target.checked)}
                />
                <Label htmlFor="syncOrders" className="text-sm">
                  Pedidos e vendas
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="syncStock"
                  name="syncStock"
                  checked={syncStock}
                  onChange={(e) => setSyncStock(e.target.checked)}
                />
                <Label htmlFor="syncStock" className="text-sm">
                  Níveis de estoque
                </Label>
              </div>
            </div>

            <Button type="submit" disabled={isSaving}>
              {isSaving ? 'Salvando...' : 'Salvar Configurações'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}