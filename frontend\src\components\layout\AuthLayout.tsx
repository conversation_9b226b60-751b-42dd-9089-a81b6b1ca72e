import type { ReactNode } from 'react';
import { Package } from 'lucide-react'; // Ícone para o logo

interface AuthLayoutProps {
  children: ReactNode;
}

export default function AuthLayout({ children }: AuthLayoutProps) {
  return (
    <div className="w-full lg:grid lg:min-h-screen lg:grid-cols-2">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-auth-background"
        />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <Package className="mr-2 h-6 w-6" />
          Magnow
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              “Esta plataforma transformou a gestão do meu estoque. A análise preditiva é incrivelmente precisa e me poupa horas de trabalho manual.”
            </p>
            <footer className="text-sm"><PERSON>, Vendedora Platinum</footer>
          </blockquote>
        </div>
      </div>
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          {children}
        </div>
      </div>
    </div>
  );
} 