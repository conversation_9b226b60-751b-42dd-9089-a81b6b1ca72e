

# **Magnow: Plataforma SaaS para Gestão Inteligente de Estoque Mercado Livre Full**

## **Sobre o Magnow**

**Magnow** é o nome comercial da plataforma SaaS desenvolvida para otimizar a gestão de estoque de vendedores que utilizam o serviço Mercado Livre Full. O nome reflete a missão da plataforma de **magnificar** e **potencializar** os resultados dos vendedores através de automação inteligente e análises preditivas.

### **Visão do Produto**
Transformar a gestão de estoque no Mercado Livre Full de um processo manual e reativo em uma operação automatizada, inteligente e proativa, permitindo que vendedores maximizem suas vendas enquanto minimizam custos de armazenagem e rupturas de estoque.

### **Documentação Relacionada**
Este documento de requisitos de negócio faz parte de um conjunto abrangente de documentação técnica e operacional:

- **Arquitetura Técnica**: <mcfile name="ARCHITECTURE_DETAILS.md" path="docs/ARCHITECTURE_DETAILS.md"></mcfile> - Detalhes da arquitetura backend e infraestrutura
- **Arquitetura Frontend**: <mcfile name="FRONTEND_ARCHITECTURE_DETAILS.md" path="docs/FRONTEND_ARCHITECTURE_DETAILS.md"></mcfile> - Especificações da interface do usuário e experiência
- **Manual do Usuário**: <mcfile name="USER_GUIDE.md" path="docs/USER_GUIDE.md"></mcfile> - Guia completo para utilização da plataforma
- **Guia de Desenvolvimento**: <mcfile name="DEVELOPMENT.md" path="docs/DEVELOPMENT.md"></mcfile> - Instruções para desenvolvedores
- **Guia de Instalação**: <mcfile name="INSTALLATION.md" path="docs/INSTALLATION.md"></mcfile> - Procedimentos de instalação e configuração
- **Estratégias de Teste**: <mcfile name="TESTING.md" path="docs/TESTING.md"></mcfile> - Metodologias e práticas de teste

## **Resumo Executivo**

Esta proposta detalha o desenvolvimento do **Magnow**, uma plataforma SaaS inovadora projetada para otimizar a gestão de estoque de vendedores que utilizam o serviço Mercado Livre Full. O Magnow visa automatizar e centralizar o monitoramento de inventário, a previsão de demanda e a geração de documentação para envios de reabastecimento. Ao integrar-se diretamente com as APIs oficiais do Mercado Livre, a solução proporcionará visibilidade em tempo real do estoque, análises preditivas para otimizar o reabastecimento e a geração simplificada de planilhas de remessa de entrada, tudo isso entregue através de um modelo SaaS escalável.

O Produto Mínimo Viável (MVP) do Magnow se concentrará em três pilares fundamentais: uma integração robusta com a API do Mercado Livre para aquisição de dados, uma calculadora inteligente de estoque-alvo para prevenir rupturas e excessos de estoque, e a geração automatizada de arquivos de remessa de entrada compatíveis com o Mercado Livre. Esta fase inicial estabelecerá as bases para um sistema de gestão de inventário altamente eficiente e orientado por dados, reduzindo significativamente o esforço manual e otimizando a lucratividade dos vendedores.

## **1\. Introdução: O Imperativo Estratégico da Automação no Mercado Livre Full**

### **1.1. Visão do Projeto: Solucionando os Desafios de Inventário no Mercado Livre Full**

O serviço Mercado Livre Full oferece vantagens logísticas significativas para os vendedores, ao delegar a complexidade de armazenamento e envio de produtos. No entanto, a gestão do inventário dentro deste ecossistema – que inclui a compreensão do estoque atual, a previsão da demanda futura e a garantia de reabastecimento oportuno – frequentemente permanece como um processo complexo e manual. Vendedores enfrentam desafios como rupturas de estoque, que resultam em vendas perdidas, excesso de estoque, que acarreta custos de armazenagem, e a tediosa preparação manual de remessas de entrada.

O **Magnow** visa desenvolver uma plataforma SaaS que automatize essas tarefas críticas de gestão de inventário. A solução proposta proporcionará aos vendedores uma vantagem competitiva por meio de decisões baseadas em dados e eficiência operacional. O Magnow atuará como um intermediário inteligente, traduzindo dados brutos do Mercado Livre em informações acionáveis e automatizando tarefas repetitivas, liberando os vendedores para focar em suas estratégias de negócio.

### **1.2. Compreendendo o Mercado Livre Full: Fluxo Logístico e Operacional**

O Mercado Livre Full é um serviço logístico abrangente onde o Mercado Livre assume a responsabilidade pelo armazenamento do inventário do vendedor e pela preparação de cada pedido para envio imediato após a confirmação da venda.1 Este serviço está disponível em países como Brasil, Argentina, México, Chile e Colômbia.1 Ele simplifica as operações para os vendedores, desonerando-os das complexidades de armazenagem e expedição.

Os aspectos operacionais chave incluem:

* **Armazenamento de Inventário:** Os vendedores enviam seus produtos para os centros de distribuição do Mercado Livre.  
* **Processamento de Pedidos:** Após uma venda, o Mercado Livre se encarrega da separação, embalagem e envio do produto.  
* **Gestão de Estoque:** Os vendedores precisam monitorar os níveis de estoque, incluindo a available\_quantity (quantidade disponível para venda) e a not\_available\_quantity (quantidade não disponível, detalhada por motivos como damaged, lost, withdrawal, internal\_process, transfer, noFiscalCoverage) para garantir a disponibilidade contínua dos produtos.1  
* **Remessas de Entrada (Inbound):** Produtos são enviados do estoque próprio do vendedor para os centros de distribuição do Mercado Livre Full.

Uma consideração crítica para o desenvolvimento desta plataforma reside na forma como as remessas de entrada são gerenciadas. A documentação da API do Mercado Livre Full esclarece que o envio de produtos para os armazéns do Mercado Livre é realizado pelo vendedor através do "Seller Center" (interface web).2 A API, por sua vez, permite apenas a consulta de estoque e operações já realizadas no Full.2 Isso significa que a funcionalidade de "Geração de Envios para o Full" da plataforma não poderá acionar programaticamente o envio via API, mas sim gerar o arquivo de dados compatível para upload manual pelo vendedor no Seller Center. Esta distinção é fundamental, pois define o limite de automação para esta etapa específica do processo, diferenciando-a de outras plataformas de marketplace (como a Amazon, que oferece uma API para criar planos de entrada 3). A plataforma, portanto, se concentrará em otimizar a preparação dos dados para este processo manual.

### **1.3. Cenário Competitivo: Posicionamento do Magnow frente à Magiic**

A plataforma Magiic Business Intelligence foi citada como referência para este projeto. Embora o acesso direto à base de conhecimento da Magiic não tenha sido possível 4, a descrição do usuário e a planilha de exemplo fornecida 5 permitem inferir algumas de suas funcionalidades.

As capacidades inferidas da Magiic incluem:

* **Monitoramento de Estoque:** Provavelmente oferece visibilidade em tempo real ou quase em tempo real do estoque no Mercado Livre Full.  
* **Cálculo de Reabastecimento:** Sugere um mecanismo para identificar produtos que necessitam de reabastecimento e calcular as quantidades ideais.  
* **Geração de Remessas:** Gera uma planilha para envios de entrada, correspondendo ao formato da planilha de exemplo.5

**Vantagens Competitivas do Magnow:**

O Magnow visará igualar e superar essas capacidades inferidas, focando em:

* **Integração Robusta com a API:** Utilização das APIs oficiais do Mercado Livre para aquisição de dados abrangente e confiável.  
* **Previsão Inteligente:** Implementação de algoritmos baseados em dados para recomendações de estoque.  
* **Arquitetura SaaS Escalável:** Projetada desde o início para multi-tenancy, segurança e crescimento futuro.  
* **Experiência do Usuário:** Embora não seja o foco principal desta análise técnica, uma interface de usuário intuitiva e uma experiência de usuário otimizada seriam diferenciais importantes do Magnow.

## **2\. Fase 1 do MVP: Funcionalidades Essenciais e Análise Técnica Detalhada**

### **2.1. Integração com a API do Mercado Livre: A Espinha Dorsal Digital**

A capacidade de interagir de forma segura e eficiente com as APIs do Mercado Livre é a base fundamental da plataforma. Isso engloba a autenticação, a recuperação de dados e o gerenciamento de tokens de acesso.

#### **2.1.1. Autenticação Segura OAuth e Gestão de Múltiplas Contas**

O Mercado Livre emprega o fluxo "Authorization Code Grant Type" do protocolo OAuth 2.0 para autenticação segura.6 Este processo envolve redirecionar o usuário (vendedor) para o site de autorização do Mercado Livre, onde ele concede permissão ao aplicativo. Após a concessão, o aplicativo recebe um código de autorização, que é então trocado por um

access\_token e um refresh\_token.6

Como uma plataforma SaaS multi-inquilino, a solução deve suportar múltiplas contas de vendedores, cada uma exigindo seu próprio fluxo de autenticação OAuth e gerenciamento de tokens. Isso implica o armazenamento seguro e a associação de cada token de vendedor à sua respectiva conta de inquilino dentro da plataforma.

Para garantir a segurança, as melhores práticas devem ser seguidas:

* Os parâmetros OAuth para a troca de tokens devem ser enviados no corpo da requisição (POST), e não como parâmetros de consulta na URL.9  
* Um parâmetro state deve ser implementado durante as requisições de autorização. Isso ajuda a prevenir ataques de CSRF (Cross-Site Request Forgery) e garante que a resposta recebida pertence a uma requisição iniciada pela própria aplicação.9  
* A redirect\_uri enviada na requisição de autorização deve corresponder exatamente à URL configurada nas configurações do aplicativo no Mercado Livre.9  
* É crucial que a opção offline\_access seja selecionada para o aplicativo no Mercado Livre, pois isso permite a utilização de refresh\_tokens, que são essenciais para manter o acesso contínuo sem exigir logins repetidos do usuário.10

#### **2.1.2. Aquisição de Dados em Tempo Real: Anúncios, Vendas, Estoque e Envios em Trânsito**

Para fornecer uma visão abrangente do inventário e das operações, a plataforma se conectará às principais fontes de dados do Mercado Livre:

* **Níveis de Estoque:** A consulta ao endpoint /inventories/$INVENTORY\_ID/stock/fulfillment fornece a available\_quantity (quantidade disponível) e a not\_available\_quantity (quantidade não disponível), juntamente com detalhes sobre o status dos itens nos armazéns Full.1 O  
  inventory\_id necessário para essa consulta é obtido através do recurso /items com base no ITEM\_ID do anúncio.1  
* **Dados de Vendas:** O endpoint /stock/fulfillment/operations/search, filtrado por type=sale\_confirmation, seller\_id e um intervalo de datas (date\_from/date\_to), permite recuperar o histórico de vendas de itens no Fulfillment.1 Essas informações são vitais para o cálculo da velocidade de vendas.  
* **Anúncios (Listagens):** O recurso /items 1 possibilita a recuperação de detalhes do item, como  
  id, title, seller\_id, category\_id, price e inventory\_id. Isso é fundamental para correlacionar os dados de estoque com as listagens de produtos específicas.  
* **Unidades em Trânsito (Inferido):** Embora a API não forneça um endpoint direto para rastrear envios *inbound* em trânsito do vendedor para o Mercado Livre Full, o tópico de notificação stock\_fulfillment 11 pode fornecer detalhes de operações executadas no estoque  
  *armazenado* nos centros FBM. Isso implica que o rastreamento de itens *a caminho* do vendedor para o ML Full provavelmente exigirá que a plataforma gerencie internamente essa informação, uma vez que a remessa de entrada seja iniciada manualmente pelo vendedor e a planilha correspondente gerada.

Para alcançar a "leitura em tempo real" dos dados, a plataforma deve se inscrever nos tópicos de notificação (webhooks) do Mercado Livre.11 Os tópicos cruciais para o MVP incluem:

* orders\_v2: Recomendado para notificações de vendas confirmadas e suas alterações.11  
* stock\_fulfillment: Fornece detalhes de operações executadas no estoque nos armazéns FBM.11 Este é vital para atualizações em tempo real sobre movimentações de estoque, como  
  inbound\_reception (recebimento de novo estoque), sale\_confirmation (confirmação de venda), withdrawal\_delivery (retirada pelo vendedor), transfer\_delivery (transferência interna) e ajustement (ajustes internos de estoque).

A implementação de webhooks exige que a plataforma tenha uma "Notifications Callback URL" publicamente acessível para receber requisições POST do Mercado Livre.11 É imperativo que a plataforma responda com um código de status HTTP 200 prontamente para confirmar o recebimento e evitar tentativas de reenvio.12 Para lidar com volumes potencialmente altos de notificações e garantir um processamento robusto, é altamente recomendável utilizar um sistema de filas para processar as notificações de forma assíncrona. Esta abordagem é crucial para obter um monitoramento verdadeiramente "em tempo real" e otimizar o uso da API, evitando a necessidade de polling excessivo, que pode levar a limites de taxa e latência na atualização de dados. Ao reagir instantaneamente a vendas, recebimentos de entrada ou ajustes de estoque, a "Calculadora de Estoque Alvo" operará com os dados mais recentes, resultando em recomendações de reabastecimento mais precisas. Além disso, essa estratégia reduz a carga sobre a infraestrutura da plataforma, melhorando a escalabilidade e diminuindo os custos operacionais.

#### **2.1.3. Armazenamento Robusto de Tokens e Estratégia de Atualização Automática**

Os access\_tokens do Mercado Livre geralmente têm uma validade de 6 horas.6 Para manter o acesso contínuo sem exigir que os usuários façam login repetidamente, a plataforma deve implementar uma estratégia de

refresh\_tokens. O refresh\_token possui uma validade mais longa (por exemplo, 6 meses 6) e é utilizado para obter um novo

access\_token.8

É importante notar que o refresh\_token é de uso único; um novo refresh\_token é emitido a cada operação de atualização bem-sucedida.10 A plataforma deve, portanto, armazenar o

*mais recente* refresh\_token de forma segura. Tanto os access\_tokens quanto os refresh\_tokens são dados sensíveis e devem ser armazenados de forma segura, preferencialmente criptografados em repouso e em trânsito, com rigorosos controles de acesso.13

Um módulo dedicado será responsável por:

* Armazenar tokens criptografados associados a cada conta de vendedor.  
* Monitorar a expiração dos access\_tokens.  
* Iniciar automaticamente requisições de refresh\_token para o endpoint /oauth/token utilizando grant\_type=refresh\_token, client\_id, client\_secret e o refresh\_token atual.8  
* Atualizar o access\_token armazenado e o *novo* refresh\_token após uma atualização bem-sucedida.  
* Lidar com cenários de invalidação de tokens, como mudança de senha do usuário, revogação de permissões ou inatividade por 4 meses.6

**Tabela 1: Principais Endpoints da API Mercado Livre Full para o MVP**

Esta tabela serve como uma referência rápida para desenvolvedores, delineando as chamadas essenciais da API, seu propósito e os parâmetros críticos necessários para implementar as funcionalidades de aquisição de dados do MVP. Ela centraliza informações coletadas de diversas fontes da documentação do Mercado Livre.

| Endpoint da API | Método HTTP | Propósito | Parâmetros Chave/Notas | Snippets Relevantes |
| :---- | :---- | :---- | :---- | :---- |
| /items/$ITEM\_ID | GET | Recuperar detalhes do item e inventory\_id para Fulfillment. | $ITEM\_ID | 1 |
| /inventories/$INVENTORY\_ID/stock/fulfillment | GET | Consultar níveis de estoque atuais (disponíveis, não disponíveis) no ML Full. | $INVENTORY\_ID | 1 |
| /stock/fulfillment/operations/search | GET | Buscar operações históricas de estoque (vendas, entrada, ajustes). | seller\_id, inventory\_id (lista separada por vírgulas), date\_from, date\_to (máx. 60 dias), type (ex: sale\_confirmation, inbound\_reception) | 1 |
| /oauth/token | POST | Trocar código de autorização por tokens de acesso/atualização; atualizar tokens expirados. | grant\_type (authorization\_code ou refresh\_token), client\_id, client\_secret, code (para inicial), refresh\_token (para atualização), redirect\_uri | 6 |
| URL de Callback de Notificações | POST | Receber atualizações em tempo real para tópicos inscritos. | URL configurada, topic (ex: orders\_v2, stock\_fulfillment), user\_id, resource | 11 |

### **2.2. Calculadora Inteligente de Estoque Alvo: Otimizando o Fluxo de Inventário**

Este módulo representa o cérebro da plataforma, transformando dados brutos de vendas e estoque em recomendações acionáveis de reabastecimento.

#### **2.2.1. Análise da Velocidade de Vendas: Cálculo de Médias (15/30 Dias) por SKU**

A base para a previsão de estoque é a compreensão da velocidade de vendas de cada produto. Os dados de vendas históricas serão recuperados da API /stock/fulfillment/operations/search do Mercado Livre, filtrando especificamente por type=sale\_confirmation para cada inventory\_id (SKU).1 A plataforma agregará essas operações de

sale\_confirmation para calcular a média diária de vendas para cada SKU nos últimos 15 e 30 dias, conforme solicitado. Esta é uma forma básica de Análise de Séries Temporais, utilizando médias móveis para identificar padrões de demanda.14 A capacidade de consultar operações por

inventory\_id e intervalo de datas 1 proporciona a granularidade necessária para uma análise precisa da velocidade de vendas por SKU.

#### **2.2.2. Definição de Níveis de Estoque Ideais com Base em Dias de Cobertura**

Os vendedores poderão definir um "dias de cobertura" desejado (por exemplo, 30 dias de estoque) para cada SKU ou categoria de produto. O cálculo do estoque ideal será determinado pela fórmula: Estoque Ideal \= Média Diária de Vendas (15/30 dias) \* Dias de Cobertura Desejados.

Embora a requisição inicial não tenha explicitamente mencionado o tempo de espera (lead time), este é um componente crítico na previsão de inventário.14 A fórmula do "Ponto de Reabastecimento" (Reorder Point) inclui

Demanda durante o Lead Time \+ Estoque de Segurança.14 Para o MVP, o lead time pode ser uma entrada estática fornecida pelo vendedor. Em futuras iterações, ele poderia ser calculado dinamicamente ou inferido. O estoque de segurança, que atua como um buffer contra flutuações inesperadas de demanda, pode ser uma margem simples para o MVP (por exemplo, 10% do estoque ideal), com cálculos mais sofisticados (baseados na variabilidade da demanda e do lead time) a serem introduzidos em fases futuras.14

#### **2.2.3. Comparação Dinâmica de Estoque: Quantidades Atuais, em Trânsito e Ideais**

A plataforma realizará uma comparação contínua entre as quantidades de estoque para cada SKU:

* **Estoque Atual:** Obtido diretamente da available\_quantity do endpoint /inventories/$INVENTORY\_ID/stock/fulfillment.1  
* **Unidades em Trânsito:** Esta informação exigirá que a plataforma rastreie as remessas de entrada *após* o vendedor as ter iniciado manualmente via Mercado Livre Seller Center e a plataforma ter gerado a planilha correspondente. A plataforma precisará de um mecanismo para que os vendedores marquem as remessas como "em trânsito" e insiram as quantidades esperadas. Embora as notificações stock\_fulfillment do Mercado Livre possam indicar inbound\_reception uma vez que as unidades cheguem 1, o rastreamento  
  *antes* da chegada é uma responsabilidade interna da plataforma.  
* **Lógica de Comparação:** A plataforma comparará a soma do (Estoque Atual \+ Unidades em Trânsito) com o Estoque Ideal para identificar discrepâncias.  
* **Recomendação de Reabastecimento:** Se (Estoque Atual \+ Unidades em Trânsito) \< Estoque Ideal, a plataforma recomendará o envio de Estoque Ideal \- (Estoque Atual \+ Unidades em Trânsito) unidades para o Mercado Livre Full.

**Tabela 2: Métricas Essenciais de Inventário e Fórmulas de Cálculo**

Esta tabela centraliza a base matemática para a "Calculadora Inteligente de Estoque Alvo", proporcionando clareza sobre como as principais métricas de inventário são derivadas. Ela garante transparência e consistência na lógica central da plataforma.

| Métrica | Fórmula | Descrição | Relevância para o MVP | Snippets Relevantes |
| :---- | :---- | :---- | :---- | :---- |
| **Média Diária de Vendas (MDV)** | Total de Vendas no Período / Número de Dias no Período | O número médio de unidades vendidas por dia para um dado SKU em um período específico (ex: 15 ou 30 dias). | Suporta diretamente o "Cálculo de média de vendas por SKU". Dados das operações de sale\_confirmation. | 1 |
| **Nível de Estoque Ideal** | MDV \* Dias de Cobertura Desejados | A quantidade alvo de um SKU a ser mantida no Mercado Livre Full para cobrir vendas por um número especificado de dias. | Suporta diretamente a "Definição de estoque ideal por cobertura de dias". | Consulta do Usuário, 14 |
| **Demanda durante o Lead Time (DLT)** | Tempo Médio de Lead Time em Dias \* MDV | A demanda esperada por um produto durante o tempo que leva para receber um novo pedido do fornecedor/vendedor. | Crucial para o Ponto de Reabastecimento. Pode ser uma entrada estática para o MVP. | 14 |
| **Ponto de Reabastecimento (PR)** | DLT \+ Estoque de Segurança | O nível de estoque no qual um novo pedido precisa ser feito para evitar rupturas. | Fundamental para gatilhos de reabastecimento automatizados. Estoque de Segurança pode ser um buffer simples para o MVP. | 14 |
| **Quantidade de Reabastecimento** | Nível de Estoque Ideal \- (Estoque Disponível Atual \+ Unidades em Trânsito) | O número de unidades recomendadas para serem enviadas ao Mercado Livre Full para atingir o nível de estoque ideal. | Saída principal da "Calculadora de Estoque Alvo". | Consulta do Usuário, 1 |

Embora o MVP se concentre em médias de vendas de 15/30 dias, é importante reconhecer as limitações de médias simples, especialmente para produtos com demanda flutuante devido a sazonalidade ou promoções.14 Médias móveis simples podem ser lentas para reagir a picos ou quedas repentinas, levando a imprecisões no estoque ideal. Para o futuro, a arquitetura da plataforma deve prever a necessidade de métodos de previsão mais sofisticados, como Análise de Séries Temporais (por exemplo, Suavização Exponencial, modelos ARIMA) ou até mesmo "Demand Sensing" (com IA e dados em tempo real).15 Esta visão antecipada garante a viabilidade a longo prazo da plataforma e sua vantagem competitiva, evoluindo além de cálculos básicos para uma inteligência preditiva mais avançada.

### **2.3. Geração Automatizada de Envios para o Mercado Livre Full: Otimizando o Reabastecimento**

Este módulo automatiza a identificação de produtos que necessitam de reabastecimento e gera a planilha necessária para upload manual no Seller Center do Mercado Livre para remessas de entrada.

#### **2.3.1. Identificação Automática dos Produtos que Precisam Ser Enviados**

Com base na "Comparação Dinâmica de Estoque" do módulo anterior, o sistema identificará automaticamente os SKUs onde (Estoque Atual \+ Unidades em Trânsito) \< Estoque Ideal. Para o MVP, uma lista simples de produtos a serem enviados será suficiente. Em iterações futuras, a priorização pode ser introduzida, baseada em fatores como risco de ruptura de estoque, lucratividade do produto ou velocidade de vendas.

#### **2.3.2. Geração de Planilha no Formato Aceito pelo Mercado Livre**

A plataforma gerará uma planilha que corresponda precisamente ao formato do arquivo MAGNOW LTDA-08072025180723-full-preenchido.xlsx \- Dados Mercado Livre.csv fornecido pelo usuário.5 A estrutura inferida do CSV de exemplo 5 é bastante simples:

,,,MLB\_ID,,Quantity. Isso indica que a planilha requer apenas o ID do Item do Mercado Livre (MLB\_ID) e a quantidade a ser enviada. A plataforma precisará mapear seus identificadores internos de SKU para o MLB\_ID do Mercado Livre (que pode ser obtido do endpoint /items via inventory\_id ou diretamente dos dados da listagem).

Para cada SKU identificado que necessita de envio, a plataforma preencherá uma linha na planilha com seu MLB\_ID e a Quantidade de Reabastecimento calculada. A planilha gerada estará disponível para download pelo vendedor, que então a carregará manualmente no Mercado Livre Seller Center.

#### **2.3.3. Suporte a Multi-Armazém e Otimização de Envios**

A requisição do usuário explicitamente solicita "Organização por armazém (multi-armazém suportado)". Isso implica que a plataforma precisa considerar para qual armazém do Mercado Livre Full os produtos devem ser enviados. A rede de fulfillment do Mercado Livre frequentemente dita para qual armazém os produtos são enviados com base na localização do vendedor, categoria do produto e demanda. A documentação da API 1 não detalha a seleção de armazéns via API para envios de entrada. Isso sugere que o aspecto "multi-armazém" para entrada pode ser gerenciado pelo Seller Center do Mercado Livre durante o processo de upload manual, ou pode exigir que o vendedor gere planilhas separadas por armazém designado.

Para o MVP, a plataforma pode gerar uma única planilha consolidada. Se o processo de upload do Mercado Livre exigir arquivos separados por armazém, a plataforma precisaria agrupar as recomendações de reabastecimento pelo armazém designado (se essa informação puder ser obtida ou configurada pelo vendedor). Além do MVP, a otimização de remessas poderia envolver a otimização dos tamanhos dos envios (por exemplo, consolidando múltiplos SKUs em uma única remessa para atender a quantidades mínimas ou otimizar o frete), mas isso está fora do escopo inicial.

**Tabela 3: Estrutura da Planilha de Entrada do Mercado Livre Full**

Esta tabela serve como um modelo direto para o arquivo de saída, garantindo que a planilha gerada esteja em conformidade com os requisitos do Mercado Livre com base na amostra fornecida pelo usuário. É uma especificação técnica crucial para a equipe de desenvolvimento.

| Cabeçalho da Coluna (Inferido) | Índice da Coluna (base 0\) | Tipo de Dados | Descrição | Valor de Exemplo 5 | Notas |
| :---- | :---- | :---- | :---- | :---- | :---- |
| (Em Branco) | 0 | (N/A) | Coluna vazia, deve estar presente. |  |  |
| (Em Branco) | 1 | (N/A) | Coluna vazia, deve estar presente. |  |  |
| (Em Branco) | 2 | (N/A) | Coluna vazia, deve estar presente. |  |  |
| ID do Item do Mercado Livre (MLB\_ID) | 3 | String | Identificador único para o anúncio do Mercado Livre. | MLB3698494580 | Este é o ITEM\_ID usado nas APIs do ML. |
| (Em Branco) | 4 | (N/A) | Coluna vazia, deve estar presente. |  |  |
| Quantidade a Enviar | 5 | Inteiro | O número de unidades do SKU a serem enviadas para o ML Full. | 2856 | Esta é a Quantidade de Reabastecimento calculada pela plataforma. |

## **3\. Design Arquitetural: Construindo uma Plataforma SaaS Escalável e Segura para o Magnow**

O **Magnow** será construído sobre uma arquitetura multi-inquilino robusta, escalável e segura para suportar o crescimento e garantir a integridade dos dados.

### **3.1. Arquitetura Multi-Inquilino: Princípios e Estratégias de Implementação**

A arquitetura SaaS multi-inquilino é um modelo de software que atende a múltiplos clientes (inquilinos) dentro da mesma instância de software, com cada inquilino possuindo um ambiente separado e isolado.13 Isso contrasta com a arquitetura de inquilino único, onde cada cliente possui sua própria instância dedicada.

Os benefícios de uma arquitetura multi-inquilino incluem:

* **Eficiência de Custos:** Infraestrutura compartilhada reduz os custos operacionais.  
* **Manutenção e Atualizações Simplificadas:** Atualizações e manutenção podem ser aplicadas a todos os inquilinos simultaneamente.  
* **Onboarding Rápido:** Novos inquilinos podem ser provisionados e integrados rapidamente.

Considerações chave na implementação:

* **Isolamento de Inquilinos:** Essencial para prevenir vazamento de dados e acesso não autorizado entre inquilinos.13  
* **Escalabilidade:** O design deve permitir o escalonamento horizontal, onde novas instâncias podem ser adicionadas sob demanda, e garantir que a carga seja distribuída eficientemente à medida que a base de usuários cresce.13  
* **Provisionamento Automatizado:** A implementação de Infraestrutura como Código (IaC) para configurações reproduzíveis e confiáveis em todos os ambientes de inquilinos permitirá um onboarding rápido e consistente.13  
* **Gestão Centralizada de Inquilinos:** Um modelo centralizado otimiza o desempenho, facilita a adição de funcionalidades e agiliza a implantação de atualizações para diferentes grupos de usuários.16

### **3.2. Isolamento de Dados e Segurança: Protegendo as Informações do Vendedor**

O isolamento de dados é de suma importância para a segurança e conformidade em um ambiente multi-inquilino.13

Estratégias para o MVP (e futuro):

* **Banco de Dados Compartilhado com Colunas Identificadoras (Particionamento Horizontal):** Para o MVP, esta é frequentemente a abordagem mais simples. Todos os dados dos inquilinos residem em um único banco de dados, mas cada tabela inclui uma coluna tenant\_id para separar logicamente os dados.16 Esta abordagem é eficiente para consultas e adição de recursos, mas apresenta o risco do "efeito vizinho barulhento" (um inquilino monopolizando recursos) e exige controles meticulosos no nível da aplicação para prevenir contaminação cruzada de dados.13  
* **Bancos de Dados Dedicados por Inquilino (Futuro/Premium):** Cada inquilino possui sua própria instância de banco de dados separada. Isso oferece o mais alto nível de isolamento e segurança de dados, melhores garantias de desempenho e facilidade de backup/restauração e migração por inquilino. No entanto, aumenta significativamente o custo de infraestrutura e a complexidade operacional.16 Esta poderia ser uma oferta de nível premium pós-MVP.  
* **Bancos de Dados Fragmentados (Sharded) (Futuro/Grande Escala):** Os dados de um único inquilino são distribuídos por múltiplos bancos de dados fragmentados (shards); múltiplos inquilinos podem compartilhar shards. Esta é uma solução para aplicações de grande escala, mas adiciona uma complexidade significativa.16

Medidas de segurança adicionais:

* **Criptografia:** Dados sensíveis (como tokens do Mercado Livre, dados de vendas do vendedor) devem ser criptografados em repouso e em trânsito, com controles de acesso rigorosos.13  
* **Controles de Acesso (IAM):** Implementar um robusto Gerenciamento de Identidade e Acesso para controlar o acesso dos usuários aos recursos, garantindo que os usuários certos tenham as permissões corretas.13 Isso inclui autenticação (via OAuth para o ML e interna para usuários da plataforma), autorização e processos de auditoria.  
* **Segurança da API:** Aderir às melhores práticas de segurança da API do Mercado Livre (por exemplo, POST para tokens, uso do parâmetro state, validação da redirect\_uri).9

**Tabela 4: Comparação de Estratégias de Isolamento de Dados Multi-Inquilino**

Esta tabela oferece uma comparação clara das estratégias comuns de isolamento de dados em SaaS multi-inquilino, permitindo uma decisão informada sobre a melhor abordagem para o MVP e o escalonamento futuro. Ela destaca as compensações entre custo, segurança e complexidade.

| Estratégia | Descrição | Vantagens | Desvantagens | Caso de Uso Ideal |
| :---- | :---- | :---- | :---- | :---- |
| **Banco de Dados Compartilhado (Colunas Identificadoras)** | Todos os inquilinos compartilham um único banco de dados, com uma coluna tenant\_id em cada tabela para separar logicamente os dados. | \- Menor custo de infraestrutura para o MVP. \- Configuração e manutenção iniciais mais fáceis. \- Utilização eficiente de recursos. | \- Efeito "vizinho barulhento" (um inquilino impacta outros). \- Maior risco de vazamento de dados se a lógica da aplicação for falha. \- Backup/restauração mais complexos para inquilinos individuais. | MVP, inquilinos menores, dados não sensíveis, prototipagem rápida. |
| **Banco de Dados Dedicado por Inquilino** | Cada inquilino possui sua própria instância de banco de dados separada. | \- Maior isolamento e segurança de dados. \- Melhores garantias de desempenho por inquilino. \- Backup/restauração e migração individuais mais fáceis. | \- Maior custo de infraestrutura. \- Gerenciamento e implantação mais complexos. \- Provisionamento mais lento para novos inquilinos. | Níveis premium, dados altamente sensíveis, requisitos de conformidade rigorosos. |
| **Banco de Dados Fragmentado (Sharded)** | Os dados de um único inquilino são distribuídos por múltiplos fragmentos de banco de dados; múltiplos inquilinos podem compartilhar fragmentos. | \- Excelente escalabilidade horizontal. \- Desempenho aprimorado pela distribuição da carga. \- Alocação flexível de recursos. | \- Complexidade arquitetural significativa. \- Migração e rebalanceamento de dados desafiadores. \- Requer lógica de fragmentação sofisticada. | Aplicações de muito grande escala com volumes massivos de dados. |

### **3.3. Registro (Logging) Robusto, Monitoramento e Alertas por Inquilino**

A implementação de registro, monitoramento e alertas é essencial para gerenciar o uso de recursos, manter o desempenho, solucionar problemas e garantir os níveis de serviço para cada inquilino.13

* **Registro (Logging):** Implementar registro estruturado (por exemplo, logs JSON) em todas as camadas da aplicação, incluindo interações de API, lógica de negócios e operações de banco de dados. Os logs devem incluir um tenant\_id para facilitar a filtragem e análise.  
* **Monitoramento:** Rastrear métricas chave do sistema (CPU, memória, I/O de rede, desempenho do banco de dados) e KPIs específicos da aplicação (tempos de resposta da API, taxas de erro, número de inquilinos ativos, volume de processamento de vendas por inquilino).  
* **Medição (Metering):** Rastrear o uso de recursos por inquilino (por exemplo, chamadas de API realizadas, dados armazenados, cálculos executados) para potencial faturamento futuro ou planejamento de capacidade.13  
* **Alertas:** Configurar alertas em tempo real para anomalias, degradação de desempenho, incidentes de segurança ou erros críticos, com notificações roteadas para a equipe de operações. Os alertas devem ser, idealmente, cientes do inquilino.

### **3.4. Estratégia de Cache com Redis/TTL para Otimização de Desempenho**

Para reduzir a carga do banco de dados, melhorar os tempos de resposta e aprimorar a experiência do usuário, uma camada de cache é essencial. O Redis é uma excelente escolha para um cache distribuído devido à sua velocidade e versatilidade.

Os alvos de cache incluem:

* **Respostas da API do Mercado Livre:** Armazenar em cache dados frequentemente acessados e relativamente estáticos das APIs do Mercado Livre (por exemplo, detalhes de itens, informações de categoria) para reduzir chamadas de API redundantes e permanecer dentro dos limites de taxa.  
* **Dados de Inventário Calculados:** Armazenar em cache os resultados da "Calculadora Inteligente de Estoque Alvo" (por exemplo, níveis de estoque ideais, recomendações de reabastecimento) para servir rapidamente às requisições da interface do usuário.  
* **Dados de Sessão:** Para aplicações multi-inquilino, o Redis pode gerenciar sessões de usuários e tokens OAuth de forma eficiente.

A implementação de TTLs (Time-To-Live) apropriados para os dados em cache é crucial para garantir a atualização. Para dados altamente dinâmicos (como estoque atual), os TTLs devem ser curtos, ou estratégias de invalidação de cache (por exemplo, acionadas por webhooks do Mercado Livre) devem ser utilizadas. Para dados menos dinâmicos, TTLs mais longos são aceitáveis.

### **3.5. Projetando para Escalabilidade Futura e Extensibilidade**

Embora um MVP possa começar com uma estrutura mais monolítica, o design com modularidade em mente (por exemplo, serviços distintos para integração de API, cálculo de estoque, geração de relatórios, autenticação) facilitará o escalonamento futuro e o desenvolvimento independente.16

Uma abordagem nativa da nuvem, alavancando plataformas de computação em nuvem (como AWS, Azure, GCP), é recomendada por sua escalabilidade inerente, serviços gerenciados e elasticidade. Opções como Amazon ECS (Microserviços) ou EKS (Kubernetes) oferecem diferentes níveis de isolamento e controle.16

O design "API-First" garantirá que todas as funcionalidades centrais sejam expostas via APIs internas, permitindo fácil integração com funcionalidades futuras ou sistemas externos. O planejamento para a escalabilidade do banco de dados (por exemplo, réplicas de leitura, fragmentação se não implementada inicialmente) é vital à medida que o volume de dados cresce.

A arquitetura deve ser facilmente adaptável para futuras funcionalidades, como:

* **Curva ABC (Análise ABC):** Classificação de produtos por volume/valor de vendas.  
* **Relatórios Avançados:** Dashboards personalizáveis e análises.  
* **Tags por Produto:** Metadados personalizados para produtos.  
* **Modelos de Previsão Mais Avançados:** Incorporação de IA/ML para detecção de demanda.15  
* **Gestão de Fornecedores:** Rastreamento de lead times e status de pedidos de fornecedores.

## **4\. Roteiro de Implementação e Aprimoramentos Futuros do Magnow**

### **4.1. Fases de Desenvolvimento do MVP e Marcos**

O desenvolvimento do MVP do **Magnow** será dividido em fases para garantir a entrega incremental de valor e a validação contínua.

* **Fase 1: Fundação e Integração Central (Semanas 1-4)**  
  * Configuração da infraestrutura SaaS multi-inquilino (banco de dados compartilhado com isolamento de inquilinos).  
  * Implementação da autenticação OAuth 2.0 do Mercado Livre e gerenciamento seguro de tokens.  
  * Desenvolvimento de wrappers de API para /items e /inventories/$INVENTORY\_ID/stock/fulfillment.  
  * Configuração do listener de webhook para notificações orders\_v2 e stock\_fulfillment.  
  * Desenvolvimento de uma interface de usuário básica para onboarding de vendedores e dashboard.  
* **Fase 2: Inteligência de Estoque e Relatórios (Semanas 5-8)**  
  * Implementação do cálculo da velocidade de vendas (15/30 dias) usando operações de sale\_confirmation.  
  * Desenvolvimento da lógica de cálculo de "Nível de Estoque Ideal" e "Quantidade de Reabastecimento".  
  * Exibição das comparações de estoque atual, em trânsito (entrada manual para MVP) e ideal no dashboard.  
  * Relatórios básicos sobre o status do estoque e necessidades de reabastecimento.  
* **Fase 3: Geração Automatizada de Arquivos de Remessa (Semanas 9-12)**  
  * Implementação da lógica para identificar produtos que precisam ser enviados com base nas recomendações de reabastecimento.  
  * Desenvolvimento do módulo de geração de planilhas, aderindo ao formato 5.  
  * Habilitação do download da planilha gerada para upload manual.  
  * Realização de Testes de Aceitação do Usuário (UAT) e lançamento beta inicial.

### **4.2. Funcionalidades Pós-MVP: Curva ABC, Relatórios Avançados, Tagging de Produtos, etc.**

Após a conclusão e estabilização do MVP do **Magnow**, a plataforma poderá ser aprimorada com funcionalidades que agregarão ainda mais valor aos vendedores.

* **Gestão Avançada de Inventário:**  
  * **Curva ABC (Análise ABC):** Classificar SKUs com base em sua contribuição para o volume de vendas ou receita, permitindo estratégias de inventário diferenciadas. Isso ajuda a priorizar itens de alto valor para monitoramento mais próximo.  
  * **Otimização do Estoque de Segurança:** Implementar o cálculo dinâmico do estoque de segurança com base na variabilidade da demanda e do tempo de espera, reduzindo tanto as rupturas quanto o excesso de estoque.  
  * **Rastreamento do Tempo de Espera (Lead Time):** Permitir que os vendedores insiram e rastreiem os tempos de espera de seus fornecedores, integrando isso nos cálculos do ponto de reabastecimento.  
  * **Previsão de Impacto Promocional:** Incorporar promoções planejadas e campanhas de marketing nas previsões de demanda para antecipar picos.15  
* **Relatórios e Análises Aprimorados:**  
  * **Dashboards Personalizáveis:** Permitir que os vendedores criem visualizações personalizadas de seu desempenho de inventário.  
  * **Tendências de Desempenho Histórico:** Visualizar tendências de vendas, níveis de estoque ao longo do tempo e histórico de reabastecimento.  
  * **Análise de Rentabilidade:** Integrar dados de custo para calcular o lucro bruto por SKU e identificar produtos de alta margem.  
* **Eficiências Operacionais:**  
  * **Tagging de Produtos:** Permitir que os vendedores adicionem tags personalizadas (por exemplo, "Sazonal", "Alta Margem", "Frágil") aos produtos para melhor organização e filtragem.  
  * **Otimização Multi-Armazém:** Se o Mercado Livre fornecer API para atribuição de armazéns ou se um processo manual mais sofisticado for necessário, aprimorar a geração de remessas para otimizar entre múltiplos centros de fulfillment.  
  * **Alertas e Notificações:** Alertas personalizáveis para baixo estoque, alta velocidade de vendas ou alterações no status da remessa de entrada.

## **5\. Conclusão e Próximos Passos Recomendados**

O **Magnow** oferece um valor estratégico significativo para os vendedores do Mercado Livre, automatizando tarefas complexas de gestão de inventário, otimizando os níveis de estoque e reduzindo o esforço manual. Ao focar em um MVP robusto, o projeto pode entregar rapidamente benefícios tangíveis, ao mesmo tempo em que estabelece as bases para uma solução altamente escalável e inteligente.

As principais forças do Magnow incluem:

* Alavancagem das APIs oficiais do Mercado Livre para dados confiáveis.  
* Emprego de cálculos inteligentes para níveis de estoque ideais.  
* Design com uma arquitetura SaaS multi-inquilino segura e escalável.  
* Um roteiro claro para aprimoramentos futuros.

Os próximos passos recomendados para o desenvolvimento do Magnow são:

1. **Especificação Técnica Detalhada:** Desenvolver especificações completas de integração de API, esquemas de banco de dados e designs de módulos com base neste esboço.  
2. **Seleção da Pilha Tecnológica:** Finalizar a pilha tecnológica (linguagens de programação, frameworks, banco de dados, provedor de nuvem) considerando escalabilidade, expertise do desenvolvedor e custo.  
3. **Design de UI/UX:** Iniciar o design da interface do usuário e da experiência do usuário, focando na intuição e clareza para os vendedores.  
4. **Prova de Conceito (PoC):** Desenvolver uma pequena PoC para integrações chave de API (OAuth, consulta de estoque, recepção de webhook) para validar a viabilidade técnica e o comportamento da API do Mercado Livre.  
5. **Desenvolvimento em Fases:** Iniciar o desenvolvimento do MVP seguindo as fases delineadas, com revisões regulares e ciclos de feedback.

