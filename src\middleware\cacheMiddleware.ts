import { Request, Response, NextFunction } from 'express';
import CacheService, { CacheNamespaces, CacheConfigs } from '../services/cacheService';
import { logger } from '../utils/logger';

interface CacheMiddlewareOptions {
  namespace: string;
  ttl?: number;
  keyGenerator?: (req: Request) => string;
  condition?: (req: Request) => boolean;
  invalidatePattern?: string;
}

export class CacheMiddleware {
  private cache: CacheService;

  constructor() {
    this.cache = new CacheService();
  }

  /**
   * Middleware para cache de leitura (GET)
   */
  read(options: CacheMiddlewareOptions) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        // Só aplicar cache em métodos GET
        if (req.method !== 'GET') {
          return next();
        }

        // Verificar condição customizada
        if (options.condition && !options.condition(req)) {
          return next();
        }

        const tenantId = req.user?.tenantId;
        const cacheKey = options.keyGenerator 
          ? options.keyGenerator(req)
          : this.generateDefaultKey(req);

        // Buscar no cache
        const cached = await this.cache.get(
          options.namespace,
          cacheKey,
          tenantId
        );

        if (cached) {
          logger.debug(`Cache middleware HIT: ${options.namespace}:${cacheKey}`);
          
          // Adicionar headers de cache
          res.set({
            'X-Cache': 'HIT',
            'X-Cache-Key': `${options.namespace}:${cacheKey}`,
            'Cache-Control': 'public, max-age=300'  // 5 minutos
          });

          return res.json(cached);
        }

        // Cache miss, interceptar resposta para armazenar
        const originalJson = res.json;
        const cacheInstance = this.cache;
        res.json = function(body: any) {
          // Armazenar resposta no cache se for sucesso
          if (res.statusCode >= 200 && res.statusCode < 300) {
            const ttl = options.ttl || CacheConfigs.MEDIUM.ttl;
            
            setImmediate(async () => {
              await cacheInstance.set(
                options.namespace,
                cacheKey,
                body,
                { ttl },
                tenantId
              );
            });
          }

          // Adicionar headers de cache
          res.set({
            'X-Cache': 'MISS',
            'X-Cache-Key': `${options.namespace}:${cacheKey}`
          });

          return originalJson.call(this, body);
        };

        next();
      } catch (error) {
        logger.error('Erro no middleware de cache de leitura', { error });
        next();
      }
    };
  }

  /**
   * Middleware para invalidação de cache (POST, PUT, DELETE)
   */
  invalidate(options: Pick<CacheMiddlewareOptions, 'namespace' | 'invalidatePattern'>) {
    return async (req: Request, res: Response, next: NextFunction) => {
      try {
        // Interceptar resposta para invalidar cache após sucesso
        const originalJson = res.json;
        const cacheInstance = this.cache;
        res.json = function(body: any) {
          // Invalidar cache se operação foi bem-sucedida
          if (res.statusCode >= 200 && res.statusCode < 300) {
            setImmediate(async () => {
              const tenantId = req.user?.tenantId;
              
              if (options.invalidatePattern) {
                await cacheInstance.deleteByPattern(options.invalidatePattern, tenantId);
              } else {
                // Invalidar todo o namespace se não especificado
                await cacheInstance.deleteByPattern(options.namespace, tenantId);
              }

              logger.debug(`Cache invalidado: ${options.namespace} (${options.invalidatePattern || 'all'})`);
            });
          }

          return originalJson.call(this, body);
        };

        next();
      } catch (error) {
        logger.error('Erro no middleware de invalidação de cache', { error });
        next();
      }
    };
  }

  /**
   * Gerar chave padrão baseada na requisição
   */
  private generateDefaultKey(req: Request): string {
    const { path, query } = req;
    const sortedQuery = Object.keys(query)
      .sort()
      .reduce((obj, key) => {
        obj[key] = query[key];
        return obj;
      }, {} as any);

    return `${path}:${JSON.stringify(sortedQuery)}`;
  }

  /**
   * Middleware específico para endpoints de estoque
   */
  static forStock() {
    const middleware = new CacheMiddleware();
    
    return {
      read: middleware.read({
        namespace: CacheNamespaces.STOCK,
        ttl: CacheConfigs.STOCK_CALCULATIONS.ttl,
        keyGenerator: (req) => {
          const { path, query } = req;
          // Incluir parâmetros importantes de estoque na chave
          const relevantParams = ['product_id', 'warehouse_id', 'date', 'status'];
          const filteredQuery = relevantParams.reduce((obj, param) => {
            if (query[param]) obj[param] = query[param];
            return obj;
          }, {} as any);
          
          return `${path}:${JSON.stringify(filteredQuery)}`;
        },
        condition: (req) => {
          // Não cachear requisições com filtros muito específicos
          const queryKeys = Object.keys(req.query);
          return queryKeys.length <= 5; // Limite para evitar cache excessivo
        }
      }),
      
      invalidate: middleware.invalidate({
        namespace: CacheNamespaces.STOCK,
        invalidatePattern: 'stock'
      })
    };
  }

  /**
   * Middleware específico para dados do Mercado Livre
   */
  static forMercadoLivre() {
    const middleware = new CacheMiddleware();
    
    return {
      read: middleware.read({
        namespace: CacheNamespaces.MERCADO_LIVRE,
        ttl: CacheConfigs.MERCADO_LIVRE.ttl,
        keyGenerator: (req) => {
          const { path, query } = req;
          // Incluir seller_id e outros parâmetros relevantes
          const relevantParams = ['seller_id', 'item_id', 'order_id', 'status'];
          const filteredQuery = relevantParams.reduce((obj, param) => {
            if (query[param]) obj[param] = query[param];
            return obj;
          }, {} as any);
          
          return `${path}:${JSON.stringify(filteredQuery)}`;
        }
      }),
      
      invalidate: middleware.invalidate({
        namespace: CacheNamespaces.MERCADO_LIVRE,
        invalidatePattern: 'ml'
      })
    };
  }

  /**
   * Middleware específico para planilhas
   */
  static forSpreadsheets() {
    const middleware = new CacheMiddleware();
    
    return {
      read: middleware.read({
        namespace: CacheNamespaces.SPREADSHEETS,
        ttl: CacheConfigs.LONG.ttl,
        keyGenerator: (req) => {
          const { path, query } = req;
          // Incluir parâmetros de período e filtros
          const relevantParams = ['start_date', 'end_date', 'format', 'type'];
          const filteredQuery = relevantParams.reduce((obj, param) => {
            if (query[param]) obj[param] = query[param];
            return obj;
          }, {} as any);
          
          return `${path}:${JSON.stringify(filteredQuery)}`;
        },
        condition: (req) => {
          // Só cachear planilhas com período definido
          return !!(req.query.start_date && req.query.end_date);
        }
      }),
      
      invalidate: middleware.invalidate({
        namespace: CacheNamespaces.SPREADSHEETS,
        invalidatePattern: 'spreadsheets'
      })
    };
  }

  /**
   * Middleware para métricas de cache
   */
  static metrics() {
    const middleware = new CacheMiddleware();
    
    return async (req: Request, res: Response, next: NextFunction) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        const cacheStatus = res.get('X-Cache') || 'BYPASS';
        
        logger.debug('Cache metrics', {
          path: req.path,
          method: req.method,
          cacheStatus,
          duration,
          statusCode: res.statusCode
        });
      });
      
      next();
    };
  }

  /**
   * Limpar cache de um tenant específico
   */
  static async clearTenantCache(tenantId: string): Promise<number> {
    const cache = new CacheService();
    return cache.clearTenant(tenantId);
  }

  /**
   * Obter métricas do cache
   */
  static getCacheMetrics() {
    const cache = new CacheService();
    return cache.getMetrics();
  }
}

export default CacheMiddleware; 