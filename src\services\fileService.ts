/**
 * File Service - Gerenciamento Centralizado de Arquivos
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import fs from 'fs';
import path from 'path';
import crypto from 'crypto';
import { PrismaClient } from '@prisma/client';
import { logger } from '@/utils/logger';

// Interfaces
export interface FileMetadata {
  id: string;
  tenantId: string;
  uploadedBy: string;
  originalName: string;
  storedName: string;
  filePath: string;
  mimeType: string;
  fileSize: number;
  fileType: 'avatar' | 'document' | 'spreadsheet';
  checksum?: string;
  isPublic: boolean;
  expiresAt?: Date;
  createdAt: Date;
}

export interface UploadResult {
  success: boolean;
  file?: FileMetadata;
  error?: string;
  url?: string;
}

export interface FileListOptions {
  tenantId: string;
  fileType?: string;
  uploadedBy?: string;
  limit?: number;
  offset?: number;
}

export interface FileStats {
  totalFiles: number;
  totalSize: number;
  filesByType: Record<string, number>;
  sizeByType: Record<string, number>;
}

export class FileService {
  private prisma: PrismaClient;
  private uploadsDir: string;
  private memoryStore: Map<string, FileMetadata> = new Map(); // Armazenamento temporário

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
    this.uploadsDir = path.join(process.cwd(), process.env.UPLOADS_DIR || 'uploads');

    // Garantir que o diretório de uploads existe
    this.ensureUploadsDirectory();
  }

  /**
   * Garantir que o diretório de uploads existe
   */
  private ensureUploadsDirectory(): void {
    try {
      if (!fs.existsSync(this.uploadsDir)) {
        fs.mkdirSync(this.uploadsDir, { recursive: true });
        logger.info('Uploads directory created', { uploadsDir: this.uploadsDir });
      }
    } catch (error) {
      logger.error('Error creating uploads directory', {
        uploadsDir: this.uploadsDir,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to create uploads directory');
    }
  }

  /**
   * Armazenar arquivo em memória (desenvolvimento)
   */
  private storeFileInMemory(file: FileMetadata): void {
    const key = `${file.tenantId}:${file.id}`;
    this.memoryStore.set(key, file);

    // Limitar tamanho do cache em memória (máximo 1000 arquivos)
    if (this.memoryStore.size > 1000) {
      const firstKey = this.memoryStore.keys().next().value;
      if (firstKey) {
        this.memoryStore.delete(firstKey);
      }
    }
  }

  /**
   * Buscar arquivo em memória (desenvolvimento)
   */
  private getFileFromMemory(fileId: string, tenantId: string): FileMetadata | null {
    const key = `${tenantId}:${fileId}`;
    return this.memoryStore.get(key) || null;
  }

  /**
   * Remover arquivo da memória (desenvolvimento)
   */
  private removeFileFromMemory(fileId: string, tenantId: string): void {
    const key = `${tenantId}:${fileId}`;
    this.memoryStore.delete(key);
  }

  /**
   * Calcular checksum MD5 do arquivo
   */
  private calculateChecksum(filePath: string): string {
    try {
      const fileBuffer = fs.readFileSync(filePath);
      return crypto.createHash('md5').update(fileBuffer).digest('hex');
    } catch (error) {
      logger.error('Error calculating checksum', {
        filePath,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to calculate file checksum');
    }
  }

  /**
   * Registrar arquivo no banco de dados
   */
  async registerFile(fileData: Omit<FileMetadata, 'id' | 'createdAt'>): Promise<FileMetadata> {
    try {
      // TODO: Implementar quando Prisma estiver configurado
      // const fileRecord = await this.prisma.files.create({
      //   data: {
      //     tenantId: fileData.tenantId,
      //     uploadedBy: fileData.uploadedBy,
      //     originalName: fileData.originalName,
      //     storedName: fileData.storedName,
      //     filePath: fileData.filePath,
      //     mimeType: fileData.mimeType,
      //     fileSize: fileData.fileSize,
      //     fileType: fileData.fileType,
      //     checksum: fileData.checksum,
      //     isPublic: fileData.isPublic,
      //     expiresAt: fileData.expiresAt,
      //     metadata: {}
      //   }
      // });

      // Por enquanto, simular registro
      const fileRecord: FileMetadata = {
        id: crypto.randomUUID(),
        ...fileData,
        createdAt: new Date()
      };

      // Simular armazenamento em memória para desenvolvimento
      this.storeFileInMemory(fileRecord);

      logger.info('File registered in database (simulated)', {
        fileId: fileRecord.id,
        tenantId: fileRecord.tenantId,
        fileName: fileRecord.originalName,
        fileType: fileRecord.fileType,
        fileSize: fileRecord.fileSize
      });

      return fileRecord;
    } catch (error) {
      logger.error('Error registering file in database', {
        fileName: fileData.originalName,
        tenantId: fileData.tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to register file in database');
    }
  }

  /**
   * Salvar arquivo no sistema de arquivos
   */
  async saveFile(
    file: Express.Multer.File,
    tenantId: string,
    uploadedBy: string,
    fileType: 'avatar' | 'document' | 'spreadsheet',
    isPublic: boolean = false,
    expiresAt?: Date
  ): Promise<UploadResult> {
    try {
      // Calcular checksum
      const checksum = this.calculateChecksum(file.path);

      // Verificar se arquivo já existe (deduplicação)
      const existingFile = await this.findFileByChecksum(checksum, tenantId);
      if (existingFile) {
        // Remover arquivo temporário
        fs.unlinkSync(file.path);
        
        logger.info('File already exists, returning existing record', {
          existingFileId: existingFile.id,
          checksum,
          tenantId
        });

        return {
          success: true,
          file: existingFile,
          url: this.generateFileUrl(existingFile.id)
        };
      }

      // Registrar arquivo no banco
      const fileRecord = await this.registerFile({
        tenantId,
        uploadedBy,
        originalName: file.originalname,
        storedName: file.filename,
        filePath: file.path,
        mimeType: file.mimetype,
        fileSize: file.size,
        fileType,
        checksum,
        isPublic,
        expiresAt
      });

      logger.info('File saved successfully', {
        fileId: fileRecord.id,
        originalName: file.originalname,
        storedName: file.filename,
        fileSize: file.size,
        tenantId,
        uploadedBy
      });

      return {
        success: true,
        file: fileRecord,
        url: this.generateFileUrl(fileRecord.id)
      };

    } catch (error) {
      // Limpar arquivo em caso de erro
      try {
        if (fs.existsSync(file.path)) {
          fs.unlinkSync(file.path);
        }
      } catch (cleanupError) {
        logger.error('Error cleaning up file after save failure', {
          filePath: file.path,
          cleanupError: cleanupError instanceof Error ? cleanupError.message : 'Unknown error'
        });
      }

      logger.error('Error saving file', {
        originalName: file.originalname,
        tenantId,
        uploadedBy,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Buscar arquivo por checksum (deduplicação)
   */
  async findFileByChecksum(checksum: string, tenantId: string): Promise<FileMetadata | null> {
    try {
      // TODO: Implementar busca no banco quando Prisma estiver configurado
      // const existingFile = await this.prisma.files.findFirst({
      //   where: { checksum, tenantId }
      // });

      // Por enquanto, buscar em memória
      for (const [key, file] of this.memoryStore.entries()) {
        if (file.tenantId === tenantId && file.checksum === checksum) {
          logger.info('File found by checksum (memory)', {
            fileId: file.id,
            checksum,
            tenantId
          });
          return file;
        }
      }

      return null;
    } catch (error) {
      logger.error('Error finding file by checksum', {
        checksum,
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }

  /**
   * Buscar arquivo por ID
   */
  async getFileById(fileId: string, tenantId: string): Promise<FileMetadata | null> {
    try {
      // TODO: Implementar busca no banco quando Prisma estiver configurado
      // const file = await this.prisma.files.findFirst({
      //   where: { id: fileId, tenantId }
      // });

      // Por enquanto, buscar em memória
      const file = this.getFileFromMemory(fileId, tenantId);

      if (file) {
        logger.info('File found by ID (memory)', { fileId, tenantId });
      } else {
        logger.debug('File not found by ID (memory)', { fileId, tenantId });
      }

      return file;
    } catch (error) {
      logger.error('Error getting file by ID', {
        fileId,
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }

  /**
   * Listar arquivos
   */
  async listFiles(options: FileListOptions): Promise<FileMetadata[]> {
    try {
      // TODO: Implementar listagem no banco quando Prisma estiver configurado
      // const files = await this.prisma.files.findMany({
      //   where: {
      //     tenantId: options.tenantId,
      //     ...(options.fileType && { fileType: options.fileType }),
      //     ...(options.uploadedBy && { uploadedBy: options.uploadedBy })
      //   },
      //   orderBy: { createdAt: 'desc' },
      //   take: options.limit || 50,
      //   skip: options.offset || 0
      // });

      // Por enquanto, filtrar em memória
      const allFiles = Array.from(this.memoryStore.values())
        .filter(file => {
          if (file.tenantId !== options.tenantId) return false;
          if (options.fileType && file.fileType !== options.fileType) return false;
          if (options.uploadedBy && file.uploadedBy !== options.uploadedBy) return false;
          return true;
        })
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(options.offset || 0, (options.offset || 0) + (options.limit || 50));

      logger.info('Files listed (memory)', {
        tenantId: options.tenantId,
        count: allFiles.length,
        total: this.memoryStore.size
      });

      return allFiles;
    } catch (error) {
      logger.error('Error listing files', {
        options,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Deletar arquivo
   */
  async deleteFile(fileId: string, tenantId: string, userId: string): Promise<boolean> {
    try {
      const file = await this.getFileById(fileId, tenantId);
      if (!file) {
        logger.warn('File not found for deletion', { fileId, tenantId });
        return false;
      }

      // Verificar permissões (apenas o uploader ou admin pode deletar)
      if (file.uploadedBy !== userId) {
        logger.warn('User not authorized to delete file', {
          fileId,
          tenantId,
          userId,
          fileUploadedBy: file.uploadedBy
        });
        return false;
      }

      // Remover arquivo físico
      if (fs.existsSync(file.filePath)) {
        fs.unlinkSync(file.filePath);
        logger.info('Physical file deleted', { filePath: file.filePath });
      }

      // TODO: Remover registro do banco quando Prisma estiver configurado
      // await this.prisma.files.delete({
      //   where: { id: fileId }
      // });

      // Por enquanto, remover da memória
      this.removeFileFromMemory(fileId, tenantId);

      logger.info('File deleted successfully', {
        fileId,
        tenantId,
        userId,
        originalName: file.originalName
      });

      return true;
    } catch (error) {
      logger.error('Error deleting file', {
        fileId,
        tenantId,
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Gerar URL de acesso ao arquivo
   */
  generateFileUrl(fileId: string): string {
    const baseUrl = process.env.API_BASE_URL || 'http://localhost:3000';
    return `${baseUrl}/api/files/download/${fileId}`;
  }

  /**
   * Obter estatísticas de arquivos
   */
  async getFileStats(tenantId: string): Promise<FileStats> {
    try {
      // TODO: Implementar estatísticas reais quando Prisma estiver configurado
      const stats: FileStats = {
        totalFiles: 0,
        totalSize: 0,
        filesByType: {},
        sizeByType: {}
      };

      logger.info('File stats calculated (simulated)', { tenantId, stats });
      return stats;
    } catch (error) {
      logger.error('Error calculating file stats', {
        tenantId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error('Failed to calculate file statistics');
    }
  }

  /**
   * Limpar arquivos expirados
   */
  async cleanupExpiredFiles(): Promise<number> {
    try {
      // TODO: Implementar limpeza real quando Prisma estiver configurado
      logger.info('Cleanup expired files (simulated)');
      return 0;
    } catch (error) {
      logger.error('Error cleaning up expired files', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  }

  /**
   * Verificar se usuário tem acesso ao arquivo
   */
  async hasFileAccess(fileId: string, tenantId: string, userId: string): Promise<boolean> {
    try {
      const file = await this.getFileById(fileId, tenantId);
      if (!file) {
        return false;
      }

      // Arquivo público (qualquer usuário do mesmo tenant)
      if (file.isPublic && file.tenantId === tenantId) {
        return true;
      }

      // Apenas o uploader tem acesso a arquivos privados
      return file.uploadedBy === userId && file.tenantId === tenantId;
    } catch (error) {
      logger.error('Error checking file access', {
        fileId,
        tenantId,
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }
}

export default FileService;
