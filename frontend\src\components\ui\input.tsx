import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';

const inputVariants = cva(
  // Base styles usando design tokens
  [
    'flex w-full rounded-md border bg-background px-3 py-2 text-sm',
    'placeholder:text-muted-foreground',
    'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
    'disabled:cursor-not-allowed disabled:opacity-50',
    'transition-all duration-150 ease-in-out',
    'file:border-0 file:bg-transparent file:text-sm file:font-medium',
  ],
  {
    variants: {
      variant: {
        // Default - estilo padrão
        default: [
          'border-border focus-visible:border-ring focus-visible:ring-ring',
          'hover:border-border/80',
        ],
        
        // Filled - com background
        filled: [
          'border-transparent bg-muted focus-visible:bg-background',
          'focus-visible:border-ring focus-visible:ring-ring',
          'hover:bg-muted/80',
        ],
        
        // Outline - apenas borda
        outline: [
          'border-2 border-border bg-transparent',
          'focus-visible:border-ring focus-visible:ring-ring',
          'hover:border-border/80',
        ],
        
        // Ghost - minimalista
        ghost: [
          'border-transparent bg-transparent',
          'focus-visible:border-border focus-visible:ring-ring',
          'hover:bg-accent/50',
        ],
      },
      
      size: {
        xs: 'h-6 px-2 py-1 text-xs',
        sm: 'h-8 px-2 py-1 text-sm',
        default: 'h-10 px-3 py-2 text-sm',
        lg: 'h-12 px-4 py-3 text-base',
        xl: 'h-14 px-5 py-4 text-lg',
      },
      
      state: {
        default: '',
        error: [
          'border-danger-500 focus-visible:border-danger-500 focus-visible:ring-danger-500',
          'text-danger-900 placeholder:text-danger-400',
        ],
        success: [
          'border-success-500 focus-visible:border-success-500 focus-visible:ring-success-500',
          'text-success-900 placeholder:text-success-400',
        ],
        warning: [
          'border-warning-500 focus-visible:border-warning-500 focus-visible:ring-warning-500',
          'text-warning-900 placeholder:text-warning-400',
        ],
      },
      
      rounded: {
        none: 'rounded-none',
        sm: 'rounded-sm',
        default: 'rounded-md',
        lg: 'rounded-lg',
        full: 'rounded-full',
      },
    },
    
    defaultVariants: {
      variant: 'default',
      size: 'default',
      state: 'default',
      rounded: 'default',
    },
  }
);

export interface InputProps 
  extends React.InputHTMLAttributes<HTMLInputElement>, 
         VariantProps<typeof inputVariants> {
  /** Ícone à esquerda */
  leftIcon?: React.ReactNode;
  /** Ícone à direita */
  rightIcon?: React.ReactNode;
  /** Estado do input */
  state?: 'default' | 'error' | 'success' | 'warning';
  /** Texto de erro */
  error?: string;
  /** Texto de help */
  helpText?: string;
  /** Label do input */
  label?: string;
  /** Indica se o campo é obrigatório */
  required?: boolean;
  /** Wrapper className */
  wrapperClassName?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ 
    className, 
    variant, 
    size, 
    state = 'default',
    rounded,
    leftIcon,
    rightIcon,
    error,
    helpText,
    label,
    required,
    wrapperClassName,
    id,
    ...props 
  }, ref) => {
    
    // Gerar ID único se não fornecido
    const inputId = id || React.useId();
    
    // Determinar o estado baseado no erro
    const inputState = error ? 'error' : state;
    
    // Classes para ícones baseadas no tamanho
    const getIconSize = () => {
      switch (size) {
        case 'xs': return 'h-3 w-3';
        case 'sm': return 'h-4 w-4';
        case 'lg': return 'h-5 w-5';
        case 'xl': return 'h-6 w-6';
        default: return 'h-4 w-4';
      }
    };
    
    // Padding adjustments para ícones
    const getPaddingWithIcons = () => {
      let paddingLeft = '';
      let paddingRight = '';
      
      if (leftIcon) {
        switch (size) {
          case 'xs': paddingLeft = 'pl-6'; break;
          case 'sm': paddingLeft = 'pl-7'; break;
          case 'lg': paddingLeft = 'pl-12'; break;
          case 'xl': paddingLeft = 'pl-14'; break;
          default: paddingLeft = 'pl-10'; break;
        }
      }
      
      if (rightIcon) {
        switch (size) {
          case 'xs': paddingRight = 'pr-6'; break;
          case 'sm': paddingRight = 'pr-7'; break;
          case 'lg': paddingRight = 'pr-12'; break;
          case 'xl': paddingRight = 'pr-14'; break;
          default: paddingRight = 'pr-10'; break;
        }
      }
      
      return cn(paddingLeft, paddingRight);
    };
    
    // Posição dos ícones
    const getIconPosition = () => {
      switch (size) {
        case 'xs': return 'top-1.5';
        case 'sm': return 'top-2';
        case 'lg': return 'top-3.5';
        case 'xl': return 'top-4';
        default: return 'top-3';
      }
    };
    
    const inputElement = (
      <div className={cn('relative', wrapperClassName)}>
        {/* Ícone à esquerda */}
        {leftIcon && (
          <div className={cn(
            'absolute left-3 flex items-center pointer-events-none',
            getIconPosition()
          )}>
            <span className={cn('text-muted-foreground', getIconSize())}>
              {leftIcon}
            </span>
          </div>
        )}
        
        {/* Input */}
    <input
          id={inputId}
      className={cn(
            inputVariants({ variant, size, state: inputState, rounded }),
            getPaddingWithIcons(),
        className
      )}
      ref={ref}
      {...props}
    />
        
        {/* Ícone à direita */}
        {rightIcon && (
          <div className={cn(
            'absolute right-3 flex items-center pointer-events-none',
            getIconPosition()
          )}>
            <span className={cn('text-muted-foreground', getIconSize())}>
              {rightIcon}
            </span>
          </div>
        )}
      </div>
    );
    
    // Se não há label, error ou helpText, retorna apenas o input
    if (!label && !error && !helpText) {
      return inputElement;
    }
    
    // Wrapper completo com label e textos de ajuda
    return (
      <div className="w-full">
        {/* Label */}
        {label && (
          <label 
            htmlFor={inputId}
            className={cn(
              'block text-sm font-medium text-foreground mb-1',
              props.disabled && 'text-muted-foreground cursor-not-allowed'
            )}
          >
            {label}
            {required && <span className="text-danger-500 ml-1">*</span>}
          </label>
        )}
        
        {/* Input */}
        {inputElement}
        
        {/* Texto de erro */}
        {error && (
          <p className="mt-1 text-sm text-danger-600" id={`${inputId}-error`}>
            {error}
          </p>
        )}
        
        {/* Texto de ajuda */}
        {helpText && !error && (
          <p className="mt-1 text-sm text-muted-foreground" id={`${inputId}-help`}>
            {helpText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export { Input, inputVariants };
export default Input;
