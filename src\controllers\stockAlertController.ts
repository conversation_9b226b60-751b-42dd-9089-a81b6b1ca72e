/**
 * Controller de Alertas de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { StockAlertService } from '../services/stockAlertService';
import { StockAlertSchedulerService } from '../services/stockAlertSchedulerService';
import { logger } from '../utils/logger';
// import { validateRequest } from '../middleware/validation'; // Arquivo não existe - temporariamente desabilitado
import { z } from 'zod';

const prisma = new PrismaClient();
const stockAlertService = new StockAlertService(prisma);
const stockAlertSchedulerService = new StockAlertSchedulerService(prisma);

// Schemas de validação
const configureAlertsSchema = z.object({
  enabled: z.boolean().optional(),
  lowStockThreshold: z.number().min(0).optional(),
  criticalStockThreshold: z.number().min(0).optional(),
  checkFrequencyMinutes: z.number().min(15).max(1440).optional(), // Entre 15 minutos e 24 horas
  verificationsPerDay: z.number().min(1).max(96).optional(), // Entre 1 e 96 verificações por dia
  notificationChannels: z.array(z.enum(['email', 'system', 'sms', 'webhook'])).optional(),
  autoResolveEnabled: z.boolean().optional(),
  escalationEnabled: z.boolean().optional(),
  escalationThresholdHours: z.number().min(1).max(168).optional() // Entre 1 hora e 7 dias
});

const manualExecutionSchema = z.object({
  force: z.boolean().optional().default(false)
});

/**
 * Obtém configuração atual de alertas
 */
export const getAlertConfiguration = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user?.tenantId;
    if (!tenantId) {
      return res.status(401).json({ error: 'Tenant não identificado' });
    }

    // Busca configuração do serviço de alertas
    const alertConfig = await stockAlertService.getAlertConfiguration(tenantId);
    
    // Busca configuração do scheduler
    const schedulerConfig = await stockAlertSchedulerService.getCurrentAlertConfig(tenantId);
    
    // Busca estatísticas recentes (últimos 30 dias)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - 30);
    const statistics = await stockAlertService.getAlertStatistics(tenantId, startDate, endDate);
    
    // Busca métricas dos jobs
    const jobMetrics = stockAlertSchedulerService.getAlertJobMetrics(tenantId, 7);

    res.json({
      configuration: {
        ...alertConfig,
        scheduler: schedulerConfig
      },
      statistics,
      jobMetrics
    });

  } catch (error: any) {
    logger.error('Erro ao obter configuração de alertas', {
      tenantId: req.user?.tenantId,
      error: error.message
    });
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

/**
 * Configura alertas de estoque
 */
export const configureAlerts = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user?.tenantId;
    if (!tenantId) {
      return res.status(401).json({ error: 'Tenant não identificado' });
    }

    const validation = configureAlertsSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        error: 'Dados inválidos',
        details: validation.error.errors
      });
    }

    const config = validation.data;

    // Se foi especificado verificationsPerDay, converte para checkFrequencyMinutes
    if (config.verificationsPerDay && !config.checkFrequencyMinutes) {
      config.checkFrequencyMinutes = Math.round(1440 / config.verificationsPerDay);
    }

    // Valida thresholds
    if (config.lowStockThreshold && config.criticalStockThreshold) {
      if (config.criticalStockThreshold >= config.lowStockThreshold) {
        return res.status(400).json({
          error: 'Limite crítico deve ser menor que limite de estoque baixo'
        });
      }
    }

    logger.info('Configurando alertas de estoque', {
      tenantId,
      userId: req.user?.id,
      config
    });

    // Atualiza configuração no serviço de alertas
    await stockAlertService.configureAlerts(tenantId, config);

    // Se a frequência foi alterada, atualiza o scheduler
    if (config.checkFrequencyMinutes || config.verificationsPerDay) {
      const verificationsPerDay = config.verificationsPerDay || 
        Math.round(1440 / (config.checkFrequencyMinutes || 360));
      
      await stockAlertSchedulerService.updateAlertFrequency(tenantId, verificationsPerDay);
    }

    // Retorna configuração atualizada
    const updatedConfig = await stockAlertService.getAlertConfiguration(tenantId);
    const schedulerConfig = await stockAlertSchedulerService.getCurrentAlertConfig(tenantId);

    res.json({
      message: 'Configuração de alertas atualizada com sucesso',
      configuration: {
        ...updatedConfig,
        scheduler: schedulerConfig
      }
    });

  } catch (error: any) {
    logger.error('Erro ao configurar alertas', {
      tenantId: req.user?.tenantId,
      userId: req.user?.id,
      error: error.message
    });
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

/**
 * Executa verificação manual de alertas
 */
export const executeManualCheck = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user?.tenantId;
    if (!tenantId) {
      return res.status(401).json({ error: 'Tenant não identificado' });
    }

    const validation = executeManualCheckSchema.safeParse(req.body);
    if (!validation.success) {
      return res.status(400).json({
        error: 'Dados inválidos',
        details: validation.error.errors
      });
    }

    const { force } = validation.data;

    // Executa verificação manual
    const result = await stockAlertService.processAlerts(tenantId);

    return res.json({
      success: true,
      data: {
        alertsGenerated: result.alertsGenerated,
        alertsResolved: result.alertsResolved,
        notificationsSent: result.notificationsSent,
        executedAt: new Date(),
        forced: force
      },
      message: 'Verificação manual executada com sucesso'
    });
  } catch (error) {
    logger.error('Erro ao executar verificação manual:', error);
    return res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

/**
 * Lista histórico de execuções de jobs
 */
export const getJobExecutions = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user?.tenantId;
    if (!tenantId) {
      return res.status(401).json({ error: 'Tenant não identificado' });
    }

    const { limit = 10 } = req.query;

    const executions = await stockAlertSchedulerService.getJobExecutions(
      tenantId,
      parseInt(limit as string)
    );

    return res.json({
      success: true,
      data: executions,
      message: 'Execuções de jobs recuperadas com sucesso'
    });
  } catch (error) {
    logger.error('Erro ao buscar execuções de jobs:', error);
    return res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

/**
 * Obtém estatísticas de alertas
 */
export const getAlertStatistics = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user?.tenantId;
    if (!tenantId) {
      return res.status(401).json({ error: 'Tenant não identificado' });
    }

    const { days = 30 } = req.query;
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(endDate.getDate() - parseInt(days as string));

    const alertStats = await stockAlertService.getAlertStatistics(tenantId, startDate, endDate);

    return res.json({
      success: true,
      data: alertStats,
      message: 'Estatísticas de alertas recuperadas com sucesso'
    });
  } catch (error) {
    logger.error('Erro ao buscar estatísticas de alertas:', error);
    return res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

/**
 * Ativa/desativa sistema de alertas
 */
export const toggleAlertSystem = async (req: Request, res: Response) => {
  try {
    const tenantId = req.user?.tenantId;
    if (!tenantId) {
      return res.status(401).json({ error: 'Tenant não identificado' });
    }

    const { enabled } = req.body;
    if (typeof enabled !== 'boolean') {
      return res.status(400).json({ error: 'Campo "enabled" deve ser um boolean' });
    }

    // Atualiza configuração usando isEnabled ao invés de enabled
    await stockAlertService.configureAlerts(tenantId, { isEnabled: enabled });

    // Busca configuração atualizada
    const config = await stockAlertService.getAlertConfiguration(tenantId);

    return res.json({
      success: true,
      data: {
        enabled: config.isEnabled,
        updatedAt: new Date()
      },
      message: `Sistema de alertas ${enabled ? 'ativado' : 'desativado'} com sucesso`
    });
  } catch (error) {
    logger.error('Erro ao alternar sistema de alertas:', error);
    return res.status(500).json({ error: 'Erro interno do servidor' });
  }
};

/**
 * Obtém presets de frequência comuns
 */
export const getFrequencyPresets = async (req: Request, res: Response) => {
  try {
    const presets = [
      {
        name: 'A cada 15 minutos',
        description: 'Verificação muito frequente (96x por dia)',
        verificationsPerDay: 96,
        frequencyMinutes: 15,
        recommended: false,
        useCase: 'Produtos de alta rotatividade'
      },
      {
        name: 'A cada 30 minutos',
        description: 'Verificação frequente (48x por dia)',
        verificationsPerDay: 48,
        frequencyMinutes: 30,
        recommended: false,
        useCase: 'Monitoramento intensivo'
      },
      {
        name: 'A cada hora',
        description: 'Verificação regular (24x por dia)',
        verificationsPerDay: 24,
        frequencyMinutes: 60,
        recommended: false,
        useCase: 'Controle padrão'
      },
      {
        name: 'A cada 3 horas',
        description: 'Verificação moderada (8x por dia)',
        verificationsPerDay: 8,
        frequencyMinutes: 180,
        recommended: false,
        useCase: 'Produtos estáveis'
      },
      {
        name: 'A cada 6 horas',
        description: 'Verificação equilibrada (4x por dia)',
        verificationsPerDay: 4,
        frequencyMinutes: 360,
        recommended: true,
        useCase: 'Configuração recomendada'
      },
      {
        name: 'A cada 12 horas',
        description: 'Verificação espaçada (2x por dia)',
        verificationsPerDay: 2,
        frequencyMinutes: 720,
        recommended: false,
        useCase: 'Produtos de baixa rotatividade'
      },
      {
        name: 'Uma vez por dia',
        description: 'Verificação diária (1x por dia)',
        verificationsPerDay: 1,
        frequencyMinutes: 1440,
        recommended: false,
        useCase: 'Monitoramento básico'
      }
    ];

    res.json({ presets });

  } catch (error: any) {
    logger.error('Erro ao obter presets de frequência', {
      error: error.message
    });
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
};