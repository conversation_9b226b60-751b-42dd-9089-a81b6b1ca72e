/**
 * Files Controller - Controlador de Arquivos
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Request, Response, NextFunction } from 'express';
import fs from 'fs';
import path from 'path';
import mime from 'mime-types';
import FileService from '../services/fileService';
import { logger } from '@/utils/logger';
import { ValidationError, NotFoundError, AuthorizationError } from '@/utils/errors';

// Interface para request com informações de usuário
interface AuthenticatedRequest extends Request {
  user?: {
    tenantId: string;
    userId: string;
    role: string;
  };
  file?: Express.Multer.File;
}

export class FilesController {
  private fileService: FileService;

  constructor() {
    this.fileService = new FileService();
  }

  /**
   * POST /api/files/upload/avatar
   * Upload de avatar de usuário
   */
  uploadAvatar = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const file = req.file;

      if (!file) {
        throw new ValidationError('Nenhum arquivo foi enviado');
      }

      logger.info('Avatar upload initiated', {
        tenantId,
        userId,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype
      });

      // Salvar arquivo
      const result = await this.fileService.saveFile(
        file,
        tenantId,
        userId,
        'avatar',
        false, // Avatar não é público por padrão
        undefined // Sem expiração
      );

      if (!result.success) {
        throw new Error(result.error || 'Erro ao salvar avatar');
      }

      logger.info('Avatar uploaded successfully', {
        tenantId,
        userId,
        fileId: result.file?.id,
        url: result.url
      });

      res.status(201).json({
        success: true,
        message: 'Avatar enviado com sucesso',
        data: {
          file: result.file,
          url: result.url
        }
      });

    } catch (error) {
      logger.error('Error uploading avatar', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * POST /api/files/upload/document
   * Upload de documentos (PDFs, etc.)
   */
  uploadDocument = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const file = req.file;
      const { isPublic = false, expiresIn } = req.body;

      if (!file) {
        throw new ValidationError('Nenhum arquivo foi enviado');
      }

      // Calcular data de expiração se fornecida
      let expiresAt: Date | undefined;
      if (expiresIn) {
        const expiresInMs = parseInt(expiresIn) * 1000; // Converter para ms
        expiresAt = new Date(Date.now() + expiresInMs);
      }

      logger.info('Document upload initiated', {
        tenantId,
        userId,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
        isPublic,
        expiresAt
      });

      // Salvar arquivo
      const result = await this.fileService.saveFile(
        file,
        tenantId,
        userId,
        'document',
        isPublic,
        expiresAt
      );

      if (!result.success) {
        throw new Error(result.error || 'Erro ao salvar documento');
      }

      logger.info('Document uploaded successfully', {
        tenantId,
        userId,
        fileId: result.file?.id,
        url: result.url
      });

      res.status(201).json({
        success: true,
        message: 'Documento enviado com sucesso',
        data: {
          file: result.file,
          url: result.url
        }
      });

    } catch (error) {
      logger.error('Error uploading document', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * POST /api/files/upload/spreadsheet
   * Upload de planilhas
   */
  uploadSpreadsheet = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const file = req.file;
      const { isPublic = false } = req.body;

      if (!file) {
        throw new ValidationError('Nenhum arquivo foi enviado');
      }

      logger.info('Spreadsheet upload initiated', {
        tenantId,
        userId,
        originalName: file.originalname,
        size: file.size,
        mimetype: file.mimetype,
        isPublic
      });

      // Salvar arquivo
      const result = await this.fileService.saveFile(
        file,
        tenantId,
        userId,
        'spreadsheet',
        isPublic,
        undefined // Planilhas não expiram por padrão
      );

      if (!result.success) {
        throw new Error(result.error || 'Erro ao salvar planilha');
      }

      logger.info('Spreadsheet uploaded successfully', {
        tenantId,
        userId,
        fileId: result.file?.id,
        url: result.url
      });

      res.status(201).json({
        success: true,
        message: 'Planilha enviada com sucesso',
        data: {
          file: result.file,
          url: result.url
        }
      });

    } catch (error) {
      logger.error('Error uploading spreadsheet', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * GET /api/files/download/:fileId
   * Download de arquivo por ID
   */
  downloadFile = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { fileId } = req.params;
      const { tenantId, userId } = req.user!;

      // Verificar acesso ao arquivo
      const hasAccess = await this.fileService.hasFileAccess(fileId, tenantId, userId);
      if (!hasAccess) {
        throw new AuthorizationError('Acesso negado ao arquivo');
      }

      // Buscar informações do arquivo
      const file = await this.fileService.getFileById(fileId, tenantId);
      if (!file) {
        throw new NotFoundError('Arquivo não encontrado');
      }

      // Verificar se arquivo físico existe
      if (!fs.existsSync(file.filePath)) {
        logger.error('Physical file not found', {
          fileId,
          filePath: file.filePath,
          tenantId,
          userId
        });
        throw new NotFoundError('Arquivo físico não encontrado');
      }

      logger.info('File download initiated', {
        fileId,
        fileName: file.originalName,
        tenantId,
        userId
      });

      // Configurar headers para download
      const mimeType = mime.lookup(file.originalName) || file.mimeType;
      res.setHeader('Content-Type', mimeType);
      res.setHeader('Content-Disposition', `attachment; filename="${file.originalName}"`);
      res.setHeader('Content-Length', file.fileSize);

      // Stream do arquivo
      const fileStream = fs.createReadStream(file.filePath);
      fileStream.pipe(res);

      fileStream.on('end', () => {
        logger.info('File download completed', {
          fileId,
          fileName: file.originalName,
          tenantId,
          userId
        });
      });

      fileStream.on('error', (error) => {
        logger.error('Error streaming file', {
          fileId,
          filePath: file.filePath,
          error: error.message,
          tenantId,
          userId
        });
        next(error);
      });

    } catch (error) {
      logger.error('Error downloading file', {
        fileId: req.params.fileId,
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * GET /api/files/stream/:fileId
   * Stream de arquivo (para arquivos grandes)
   */
  streamFile = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { fileId } = req.params;
      const { tenantId, userId } = req.user!;

      // Verificar acesso ao arquivo
      const hasAccess = await this.fileService.hasFileAccess(fileId, tenantId, userId);
      if (!hasAccess) {
        throw new AuthorizationError('Acesso negado ao arquivo');
      }

      // Buscar informações do arquivo
      const file = await this.fileService.getFileById(fileId, tenantId);
      if (!file) {
        throw new NotFoundError('Arquivo não encontrado');
      }

      // Verificar se arquivo físico existe
      if (!fs.existsSync(file.filePath)) {
        throw new NotFoundError('Arquivo físico não encontrado');
      }

      logger.info('File streaming initiated', {
        fileId,
        fileName: file.originalName,
        tenantId,
        userId
      });

      // Configurar headers para streaming
      const mimeType = mime.lookup(file.originalName) || file.mimeType;
      res.setHeader('Content-Type', mimeType);
      res.setHeader('Content-Length', file.fileSize);
      res.setHeader('Accept-Ranges', 'bytes');

      // Suporte a range requests (para streaming de vídeo/áudio)
      const range = req.headers.range;
      if (range) {
        const parts = range.replace(/bytes=/, "").split("-");
        const start = parseInt(parts[0], 10);
        const end = parts[1] ? parseInt(parts[1], 10) : file.fileSize - 1;
        const chunksize = (end - start) + 1;

        res.status(206);
        res.setHeader('Content-Range', `bytes ${start}-${end}/${file.fileSize}`);
        res.setHeader('Content-Length', chunksize);

        const fileStream = fs.createReadStream(file.filePath, { start, end });
        fileStream.pipe(res);
      } else {
        const fileStream = fs.createReadStream(file.filePath);
        fileStream.pipe(res);
      }

    } catch (error) {
      logger.error('Error streaming file', {
        fileId: req.params.fileId,
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * GET /api/files/preview/:fileId
   * Preview de arquivo (para imagens)
   */
  previewFile = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { fileId } = req.params;
      const { tenantId, userId } = req.user!;

      // Verificar acesso ao arquivo
      const hasAccess = await this.fileService.hasFileAccess(fileId, tenantId, userId);
      if (!hasAccess) {
        throw new AuthorizationError('Acesso negado ao arquivo');
      }

      // Buscar informações do arquivo
      const file = await this.fileService.getFileById(fileId, tenantId);
      if (!file) {
        throw new NotFoundError('Arquivo não encontrado');
      }

      // Verificar se é uma imagem
      if (!file.mimeType.startsWith('image/')) {
        throw new ValidationError('Preview disponível apenas para imagens');
      }

      // Verificar se arquivo físico existe
      if (!fs.existsSync(file.filePath)) {
        throw new NotFoundError('Arquivo físico não encontrado');
      }

      logger.info('File preview initiated', {
        fileId,
        fileName: file.originalName,
        tenantId,
        userId
      });

      // Configurar headers para preview
      res.setHeader('Content-Type', file.mimeType);
      res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache por 1 hora

      // Stream da imagem
      const fileStream = fs.createReadStream(file.filePath);
      fileStream.pipe(res);

    } catch (error) {
      logger.error('Error previewing file', {
        fileId: req.params.fileId,
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * GET /api/files
   * Listar arquivos do tenant
   */
  listFiles = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { 
        fileType, 
        limit = 50, 
        offset = 0,
        uploadedBy 
      } = req.query;

      const files = await this.fileService.listFiles({
        tenantId,
        fileType: fileType as string,
        uploadedBy: uploadedBy as string,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      });

      logger.info('Files listed', {
        tenantId,
        userId,
        count: files.length,
        fileType,
        limit,
        offset
      });

      res.json({
        success: true,
        data: {
          files,
          pagination: {
            limit: parseInt(limit as string),
            offset: parseInt(offset as string),
            total: files.length
          }
        }
      });

    } catch (error) {
      logger.error('Error listing files', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * GET /api/files/:fileId
   * Obter informações de um arquivo específico
   */
  getFileInfo = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { fileId } = req.params;
      const { tenantId, userId } = req.user!;

      // Verificar acesso ao arquivo
      const hasAccess = await this.fileService.hasFileAccess(fileId, tenantId, userId);
      if (!hasAccess) {
        throw new AuthorizationError('Acesso negado ao arquivo');
      }

      // Buscar informações do arquivo
      const file = await this.fileService.getFileById(fileId, tenantId);
      if (!file) {
        throw new NotFoundError('Arquivo não encontrado');
      }

      logger.info('File info retrieved', {
        fileId,
        fileName: file.originalName,
        tenantId,
        userId
      });

      res.json({
        success: true,
        data: {
          file,
          downloadUrl: this.fileService.generateFileUrl(fileId)
        }
      });

    } catch (error) {
      logger.error('Error getting file info', {
        fileId: req.params.fileId,
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * DELETE /api/files/:fileId
   * Deletar arquivo
   */
  deleteFile = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { fileId } = req.params;
      const { tenantId, userId } = req.user!;

      const success = await this.fileService.deleteFile(fileId, tenantId, userId);
      if (!success) {
        throw new NotFoundError('Arquivo não encontrado ou sem permissão para deletar');
      }

      logger.info('File deleted successfully', {
        fileId,
        tenantId,
        userId
      });

      res.json({
        success: true,
        message: 'Arquivo deletado com sucesso'
      });

    } catch (error) {
      logger.error('Error deleting file', {
        fileId: req.params.fileId,
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * PUT /api/files/:fileId/metadata
   * Atualizar metadados do arquivo
   */
  updateFileMetadata = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { fileId } = req.params;
      const { tenantId, userId } = req.user!;
      const { isPublic, expiresAt } = req.body;

      // TODO: Implementar atualização de metadados quando Prisma estiver configurado

      logger.info('File metadata update requested (not implemented)', {
        fileId,
        tenantId,
        userId,
        isPublic,
        expiresAt
      });

      res.json({
        success: true,
        message: 'Metadados atualizados com sucesso (simulado)'
      });

    } catch (error) {
      logger.error('Error updating file metadata', {
        fileId: req.params.fileId,
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * GET /api/files/stats/overview
   * Estatísticas gerais de arquivos do tenant
   */
  getFileStats = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;

      const stats = await this.fileService.getFileStats(tenantId);

      logger.info('File stats retrieved', {
        tenantId,
        userId,
        stats
      });

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      logger.error('Error getting file stats', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * GET /api/files/stats/usage
   * Estatísticas de uso de storage
   */
  getStorageUsage = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;

      // TODO: Implementar estatísticas de uso real
      const usage = {
        totalSize: 0,
        usedSize: 0,
        availableSize: 1024 * 1024 * 1024, // 1GB
        usagePercentage: 0,
        fileCount: 0
      };

      logger.info('Storage usage retrieved (simulated)', {
        tenantId,
        userId,
        usage
      });

      res.json({
        success: true,
        data: usage
      });

    } catch (error) {
      logger.error('Error getting storage usage', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * POST /api/files/cleanup/expired
   * Limpar arquivos expirados
   */
  cleanupExpiredFiles = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId, role } = req.user!;

      // Verificar se usuário é admin
      if (role !== 'admin') {
        throw new AuthorizationError('Apenas administradores podem executar limpeza');
      }

      const deletedCount = await this.fileService.cleanupExpiredFiles();

      logger.info('Expired files cleanup completed', {
        tenantId,
        userId,
        deletedCount
      });

      res.json({
        success: true,
        message: `${deletedCount} arquivos expirados foram removidos`,
        data: { deletedCount }
      });

    } catch (error) {
      logger.error('Error cleaning up expired files', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };

  /**
   * POST /api/files/cleanup/temp
   * Limpar arquivos temporários
   */
  cleanupTempFiles = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId, role } = req.user!;

      // Verificar se usuário é admin
      if (role !== 'admin') {
        throw new AuthorizationError('Apenas administradores podem executar limpeza');
      }

      // TODO: Implementar limpeza de arquivos temporários
      const deletedCount = 0;

      logger.info('Temp files cleanup completed (simulated)', {
        tenantId,
        userId,
        deletedCount
      });

      res.json({
        success: true,
        message: `${deletedCount} arquivos temporários foram removidos`,
        data: { deletedCount }
      });

    } catch (error) {
      logger.error('Error cleaning up temp files', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      next(error);
    }
  };
}

export default FilesController;
