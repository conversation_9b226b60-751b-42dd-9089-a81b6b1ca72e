/**
 * Testes Unitários - Serviço de Cálculo de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { StockCalculationService } from '../../services/stockCalculationService';
import { mockPrismaClient } from '../setup';
import { PrismaClient } from '@prisma/client';

// Mock dos serviços
jest.mock('../../services/salesAnalysisService');
jest.mock('../../services/mercadoLivreApiService');
jest.mock('../../services/cacheService');

describe('StockCalculationService', () => {
  let stockCalculationService: StockCalculationService;

  const mockTenantId = 'tenant-123';
  const mockItemId = 'MLB123456789';

  const mockConfiguration = {
    mlItemId: mockItemId,
    tenantId: mockTenantId,
    coverageDays: 30,
    safetyStockDays: 7,
    leadTimeDays: 5,
    seasonalityFactor: 1.0,
    trendFactor: 1.0,
    minStock: 10,
    maxStock: 1000,
    reorderPoint: 50,
    stockTolerancePercentage: 10,
    analysisWindowDays: 60,
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockSalesAnalysis = {
    mlItemId: mockItemId,
    tenantId: mockTenantId,
    analyzedPeriodDays: 30,
    totalSales: 100,
    averageDailySales: 3.33,
    salesStandardDeviation: 1.5,
    salesTrend: 'stable' as const,
    seasonalityFactor: 1.0,
    lastSaleDate: new Date(),
    salesDistribution: [1, 2, 3, 4, 5],
    peakSalesDays: ['monday', 'friday'],
    calculatedAt: new Date()
  };

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Configurar mocks do Prisma
    mockPrismaClient.stockConfiguration.findFirst.mockResolvedValue(mockConfiguration);
    mockPrismaClient.stockCalculation.create.mockResolvedValue({
      id: 'calc-123',
      tenantId: mockTenantId,
      productId: 'product-123',
      currentStock: 50,
      averageSales: 3.33,
      idealStock: 100,
      stockGap: 50,
      daysOfCoverage: 15,
      safetyStock: 10,
      unitsInTransit: 20,
      calculationDate: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Instanciar o serviço com PrismaClient
    stockCalculationService = new StockCalculationService(mockPrismaClient as unknown as PrismaClient);

    // Configurar mocks dos serviços internos
    const mockSalesService = (stockCalculationService as any).salesAnalysisService;
    const mockMLService = (stockCalculationService as any).mercadoLivreApi;
    const mockCache = (stockCalculationService as any).cache;

    if (mockSalesService && mockSalesService.analyzeSalesHistory) {
      mockSalesService.analyzeSalesHistory.mockResolvedValue(mockSalesAnalysis);
    }

    if (mockMLService && mockMLService.getItemStock) {
      mockMLService.getItemStock.mockResolvedValue({
        mlItemId: mockItemId,
        tenantId: mockTenantId,
        currentQuantity: 50,
        inTransitQuantity: 20,
        reservedQuantity: 5,
        availableQuantity: 45,
        lastUpdateAt: new Date(),
        source: 'mercadolivre' as const
      });
    }

         if (mockCache) {
       mockCache.get = jest.fn().mockResolvedValue(null);
       mockCache.set = jest.fn().mockResolvedValue(true);
       mockCache.del = jest.fn().mockResolvedValue(true);
     }
  });

  describe('calculateIdealStock', () => {
    it('deve instanciar o serviço corretamente', () => {
      expect(stockCalculationService).toBeDefined();
      expect(stockCalculationService).toBeInstanceOf(StockCalculationService);
    });

    it('deve ter métodos públicos definidos', () => {
      expect(typeof stockCalculationService.calculateIdealStock).toBe('function');
      expect(typeof stockCalculationService.calculateBatchIdealStock).toBe('function');
      expect(typeof stockCalculationService.getLatestCalculation).toBe('function');
    });

         it('deve aceitar parâmetros corretos no calculateIdealStock', async () => {
       // Mock das dependências internas
       mockPrismaClient.stockConfiguration.findFirst.mockResolvedValue(mockConfiguration);

       // Testar se o método pode ser chamado com parâmetros corretos
       try {
         await stockCalculationService.calculateIdealStock(mockTenantId, mockItemId);
         expect(true).toBe(true); // Se chegou aqui, não houve erro
       } catch (error) {
         // Esperamos que não haja erro de tipo/assinatura
         expect(error).toBeUndefined();
       }
     });

     it('deve aceitar configuração customizada', async () => {
       const customConfig = {
         coverageDays: 45,
         safetyStockDays: 10
       };

       try {
         await stockCalculationService.calculateIdealStock(
           mockTenantId,
           mockItemId,
           customConfig
         );
         expect(true).toBe(true); // Se chegou aqui, não houve erro
       } catch (error) {
         // Esperamos que não haja erro de tipo/assinatura
         expect(error).toBeUndefined();
       }
     });
  });

  describe('calculateBatchIdealStock', () => {
    beforeEach(() => {
      mockPrismaClient.stockConfiguration.findMany.mockResolvedValue([
        { ...mockConfiguration, mlItemId: 'MLB111' },
        { ...mockConfiguration, mlItemId: 'MLB222' }
      ]);
    });

         it('deve aceitar parâmetros corretos no calculateBatchIdealStock', async () => {
       try {
         await stockCalculationService.calculateBatchIdealStock(mockTenantId);
         expect(true).toBe(true);
       } catch (error) {
         expect(error).toBeUndefined();
       }
     });

     it('deve aceitar parâmetros opcionais', async () => {
       const params = { forceRecalculation: true };
       
       try {
         await stockCalculationService.calculateBatchIdealStock(mockTenantId, params);
         expect(true).toBe(true);
       } catch (error) {
         expect(error).toBeUndefined();
       }
     });
  });

  describe('Cache Integration', () => {
    it('deve ter métodos de cache definidos', () => {
      expect(typeof stockCalculationService.calculateIdealStockWithCache).toBe('function');
      expect(typeof stockCalculationService.invalidateProductCache).toBe('function');
    });

         it('deve aceitar parâmetros corretos no cache', async () => {
       try {
         await stockCalculationService.calculateIdealStockWithCache(mockTenantId, mockItemId);
         expect(true).toBe(true);
       } catch (error) {
         expect(error).toBeUndefined();
       }

       try {
         await stockCalculationService.invalidateProductCache(mockTenantId, mockItemId);
         expect(true).toBe(true);
       } catch (error) {
         expect(error).toBeUndefined();
       }
     });
  });

  describe('getLatestCalculation', () => {
    it('deve aceitar parâmetros corretos', async () => {
      mockPrismaClient.stockCalculation.findFirst.mockResolvedValue(null);

      const result = await stockCalculationService.getLatestCalculation(
        mockTenantId,
        mockItemId
      );

      expect(result).toBeNull();
      expect(mockPrismaClient.stockCalculation.findFirst).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            tenantId: mockTenantId
          })
        })
      );
    });
  });
});
