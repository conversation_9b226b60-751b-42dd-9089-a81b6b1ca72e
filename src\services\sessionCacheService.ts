import CacheService, { CacheNamespaces, CacheConfigs } from './cacheService';
import { logger } from '../utils/logger';
import * as jwt from 'jsonwebtoken';

export interface SessionData {
  userId: string;
  tenantId: string;
  email: string;
  role: string;
  lastActivity: Date;
  ipAddress?: string | undefined;
  userAgent?: string | undefined;
}

export interface TokenBlacklistEntry {
  tokenId: string;
  userId: string;
  reason: 'LOGOUT' | 'EXPIRED' | 'REVOKED' | 'SECURITY';
  timestamp: Date;
}

export class SessionCacheService {
  private cache: CacheService;
  private jwtSecret: string;

  constructor() {
    this.cache = new CacheService();
    this.jwtSecret = process.env.JWT_SECRET || 'fallback-secret';
  }

  /**
   * Armazenar dados de sessão do usuário
   */
  async storeUserSession(
    userId: string,
    tenantId: string,
    sessionData: SessionData
  ): Promise<void> {
    try {
      const sessionKey = `user-session:${userId}`;
      
      await this.cache.set(
        CacheNamespaces.SESSION,
        sessionKey,
        sessionData,
        CacheConfigs.USER_SESSIONS,
        tenantId
      );

      logger.debug('Sessão do usuário armazenada no cache', { userId, tenantId });
    } catch (error) {
      logger.error('Erro ao armazenar sessão do usuário', { error, userId, tenantId });
      throw error;
    }
  }

  /**
   * Recuperar dados de sessão do usuário
   */
  async getUserSession(
    userId: string,
    tenantId: string
  ): Promise<SessionData | null> {
    try {
      const sessionKey = `user-session:${userId}`;
      
      const sessionData = await this.cache.get(
        CacheNamespaces.SESSION,
        sessionKey,
        tenantId
      );

      if (sessionData) {
        logger.debug('Sessão do usuário recuperada do cache', { userId, tenantId });
      }

      return sessionData;
    } catch (error) {
      logger.error('Erro ao recuperar sessão do usuário', { error, userId, tenantId });
      return null;
    }
  }

  /**
   * Atualizar última atividade da sessão
   */
  async updateSessionActivity(
    userId: string,
    tenantId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      const currentSession = await this.getUserSession(userId, tenantId);
      
      if (currentSession) {
        const updatedSession: SessionData = {
          ...currentSession,
          lastActivity: new Date(),
          ipAddress: ipAddress || currentSession.ipAddress,
          userAgent: userAgent || currentSession.userAgent
        };

        await this.storeUserSession(userId, tenantId, updatedSession);
      }
    } catch (error) {
      logger.error('Erro ao atualizar atividade da sessão', { error, userId, tenantId });
    }
  }

  /**
   * Cache de verificação de token JWT
   */
  async cacheTokenValidation(
    tokenId: string,
    userId: string,
    tenantId: string,
    isValid: boolean,
    ttl: number = 300 // 5 minutos por padrão
  ): Promise<void> {
    try {
      const tokenKey = `token-validation:${tokenId}`;
      
      const tokenData = {
        userId,
        tenantId,
        isValid,
        cachedAt: new Date()
      };

      await this.cache.set(
        CacheNamespaces.SESSION,
        tokenKey,
        tokenData,
        { ttl },
        tenantId
      );

      logger.debug('Validação de token cacheada', { tokenId, userId, isValid });
    } catch (error) {
      logger.error('Erro ao cachear validação de token', { error, tokenId, userId });
    }
  }

  /**
   * Verificar token no cache
   */
  async getCachedTokenValidation(
    tokenId: string,
    tenantId: string
  ): Promise<{ userId: string; tenantId: string; isValid: boolean } | null> {
    try {
      const tokenKey = `token-validation:${tokenId}`;
      
      const tokenData = await this.cache.get(
        CacheNamespaces.SESSION,
        tokenKey,
        tenantId
      );

      if (tokenData) {
        logger.debug('Validação de token encontrada no cache', { tokenId, isValid: tokenData.isValid });
        return tokenData;
      }

      return null;
    } catch (error) {
      logger.error('Erro ao verificar token no cache', { error, tokenId });
      return null;
    }
  }

  /**
   * Adicionar token à blacklist
   */
  async addToBlacklist(
    tokenId: string,
    userId: string,
    tenantId: string,
    reason: TokenBlacklistEntry['reason'],
    ttl?: number
  ): Promise<void> {
    try {
      const blacklistKey = `blacklist:${tokenId}`;
      
      const entry: TokenBlacklistEntry = {
        tokenId,
        userId,
        reason,
        timestamp: new Date()
      };

      // TTL baseado na expiração normal do token ou padrão de 24h
      const cacheTTL = ttl || 24 * 60 * 60; // 24 horas

      await this.cache.set(
        CacheNamespaces.SESSION,
        blacklistKey,
        entry,
        { ttl: cacheTTL },
        tenantId
      );

      // Invalidar cache de validação do token
      await this.cache.delete(
        CacheNamespaces.SESSION,
        `token-validation:${tokenId}`,
        tenantId
      );

      logger.info('Token adicionado à blacklist', { tokenId, userId, reason });
    } catch (error) {
      logger.error('Erro ao adicionar token à blacklist', { error, tokenId, userId });
      throw error;
    }
  }

  /**
   * Verificar se token está na blacklist
   */
  async isTokenBlacklisted(
    tokenId: string,
    tenantId: string
  ): Promise<boolean> {
    try {
      const blacklistKey = `blacklist:${tokenId}`;
      
      const blacklistEntry = await this.cache.get(
        CacheNamespaces.SESSION,
        blacklistKey,
        tenantId
      );

      const isBlacklisted = !!blacklistEntry;
      
      if (isBlacklisted) {
        logger.debug('Token encontrado na blacklist', { tokenId, reason: blacklistEntry.reason });
      }

      return isBlacklisted;
    } catch (error) {
      logger.error('Erro ao verificar blacklist', { error, tokenId });
      return false; // Em caso de erro, assumir que não está na blacklist
    }
  }

  /**
   * Invalidar todas as sessões de um usuário
   */
  async invalidateUserSessions(
    userId: string,
    tenantId: string,
    reason: TokenBlacklistEntry['reason'] = 'SECURITY'
  ): Promise<void> {
    try {
      // Remover sessão do usuário
      const sessionKey = `user-session:${userId}`;
      await this.cache.delete(CacheNamespaces.SESSION, sessionKey, tenantId);

      // Invalidar todas as validações de token deste usuário
      await this.cache.deleteByPattern(`token-validation:*`, tenantId);

      logger.info('Todas as sessões do usuário invalidadas', { userId, tenantId, reason });
    } catch (error) {
      logger.error('Erro ao invalidar sessões do usuário', { error, userId, tenantId });
      throw error;
    }
  }

  /**
   * Cache de dados do usuário (perfil básico)
   */
  async cacheUserProfile(
    userId: string,
    tenantId: string,
    profileData: any,
    ttl: number = 1800 // 30 minutos
  ): Promise<void> {
    try {
      const profileKey = `user-profile:${userId}`;
      
      await this.cache.set(
        CacheNamespaces.USER,
        profileKey,
        profileData,
        { ttl },
        tenantId
      );

      logger.debug('Perfil do usuário cacheado', { userId, tenantId });
    } catch (error) {
      logger.error('Erro ao cachear perfil do usuário', { error, userId, tenantId });
    }
  }

  /**
   * Recuperar dados do usuário do cache
   */
  async getCachedUserProfile(
    userId: string,
    tenantId: string
  ): Promise<any | null> {
    try {
      const profileKey = `user-profile:${userId}`;
      
      const profileData = await this.cache.get(
        CacheNamespaces.USER,
        profileKey,
        tenantId
      );

      if (profileData) {
        logger.debug('Perfil do usuário recuperado do cache', { userId, tenantId });
      }

      return profileData;
    } catch (error) {
      logger.error('Erro ao recuperar perfil do usuário', { error, userId, tenantId });
      return null;
    }
  }

  /**
   * Limpar dados de sessão expirados
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      // Esta função seria chamada periodicamente por um job
      // Por agora, log apenas a intenção
      logger.info('Limpeza de sessões expiradas executada');
      
      // A limpeza automática é feita pelo Redis com TTL
      // Mas podemos implementar lógica adicional se necessário
    } catch (error) {
      logger.error('Erro na limpeza de sessões expiradas', { error });
    }
  }

  /**
   * Obter estatísticas de sessões ativas
   */
  async getSessionStats(tenantId: string): Promise<any> {
    try {
      // Esta é uma implementação básica
      // Em produção, você pode querer métricas mais detalhadas
      
      const stats = {
        timestamp: new Date(),
        tenant: tenantId,
        // Métricas básicas do cache
        cacheMetrics: this.cache.getMetrics()
      };

      return stats;
    } catch (error) {
      logger.error('Erro ao obter estatísticas de sessão', { error, tenantId });
      return null;
    }
  }

  /**
   * Extrair ID do token JWT
   */
  private extractTokenId(token: string): string | null {
    try {
      const decoded = jwt.decode(token) as any;
      return decoded?.jti || decoded?.id || null;
    } catch (error) {
      logger.error('Erro ao extrair ID do token', { error });
      return null;
    }
  }
}

// Instância singleton
let sessionCacheInstance: SessionCacheService | null = null;

export function getSessionCacheService(): SessionCacheService {
  if (!sessionCacheInstance) {
    sessionCacheInstance = new SessionCacheService();
  }
  return sessionCacheInstance;
}

export default SessionCacheService; 