module.exports = {
  apps: [
    {
      name: 'magnow',
      script: 'dist/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000
      },
      env_development: {
        NODE_ENV: 'development',
        PORT: 3000,
        LOG_LEVEL: 'debug'
      },
      env_staging: {
        NODE_ENV: 'staging',
        PORT: 3000,
        LOG_LEVEL: 'info'
      },
      // Configurações de monitoramento
      min_uptime: '10s',
      max_restarts: 10,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      
      // Logs
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Configurações avançadas
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      
      // Health check
      health_check_grace_period: 3000
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: ['api.magnow.com'],
      ref: 'origin/main',
      repo: '**************:username/magnow.git',
      path: '/var/www/magnow',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env production'
    },
    staging: {
      user: 'deploy',
      host: ['staging.magnow.com'],
      ref: 'origin/develop',
      repo: '**************:username/magnow.git',
      path: '/var/www/magnow-staging',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env staging'
    }
  }
}; 