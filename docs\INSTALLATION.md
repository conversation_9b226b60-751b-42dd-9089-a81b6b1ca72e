# Guia de Instalação

Este documento detalha os passos necessários para configurar o ambiente de desenvolvimento do Magnow.

## Pré-requisitos

Antes de começar, garanta que você tenha as seguintes ferramentas instaladas em sua máquina:

-   **Node.js**: Versão `18.x` ou `20.x`. Recomendamos usar um gerenciador de versões como o [NVM](https://github.com/nvm-sh/nvm) para facilitar a troca entre versões.
-   **NPM** ou **Yarn**: Gerenciador de pacotes do Node.js.
-   **Docker** e **Docker Compose**: Para executar a aplicação e os serviços (banco de dados, cache) em contêineres.
-   **Git**: Para clonar o repositório.

## Passos de Instalação

Siga os passos abaixo para ter a aplicação rodando localmente.

### 1. <PERSON>lonar o Repositório

Primeiro, clone o repositório do projeto para a sua máquina local:

```bash
git clone https://github.com/seu-usuario/magnow.git
cd magnow
```

### 2. Instalar Dependências

Instale todas as dependências do projeto usando NPM ou Yarn:

```bash
# Usando NPM
npm install

# Ou usando Yarn
yarn install
```

### 3. Configurar Variáveis de Ambiente

A aplicação utiliza um arquivo `.env` para gerenciar as variáveis de ambiente. Crie uma cópia do arquivo de exemplo `.env.example`:

```bash
cp .env.example .env
```

Agora, abra o arquivo `.env` e preencha as variáveis necessárias. Para o ambiente de desenvolvimento, os valores padrão fornecidos no `docker-compose.yml` geralmente são suficientes.

### 4. Iniciar os Serviços com Docker

A maneira mais simples de rodar o ambiente de desenvolvimento é usando Docker Compose. Ele irá iniciar o banco de dados (PostgreSQL) e o serviço de cache (Redis) necessários para a aplicação.

No terminal, na raiz do projeto, execute:

```bash
docker-compose up -d
```

Este comando irá baixar as imagens necessárias e iniciar os contêineres em segundo plano (`-d`).

### 5. Executar as Migrações do Banco de Dados

Com o banco de dados rodando, você precisa aplicar as migrações do Prisma para criar as tabelas e a estrutura de dados inicial.

```bash
npm run db:migrate:dev
```

### 6. Iniciar a Aplicação

Finalmente, inicie o servidor de desenvolvimento:

```bash
npm run dev
```

A aplicação estará disponível em `http://localhost:3000` (ou na porta que você configurou no seu arquivo `.env`). O servidor irá reiniciar automaticamente sempre que você fizer uma alteração nos arquivos do código-fonte.

## Verificação

Para verificar se tudo está funcionando corretamente, você pode:

1.  Acessar a documentação da API em `http://localhost:3000/api/docs`.
2.  Executar a suíte de testes com `npm test`.

Com isso, seu ambiente de desenvolvimento Magnow está pronto! ✨