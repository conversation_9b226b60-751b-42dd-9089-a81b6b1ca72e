import type { User, UserRole, UserPermissions, UserStats } from '../types/api';

// MOCK DATA FOR DEVELOPMENT - Remove for production
export const mockUsers: User[] = [
  {
    id: 'user-001',
    email: '<EMAIL>',
    name: '<PERSON>',
    tenantId: 'tenant-1',
    role: 'admin',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-07-30T14:30:00Z',
    isActive: true,
    lastLoginAt: '2024-07-31T09:15:00Z',
    phone: '+55 11 99999-1111',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    department: 'Administração',
    position: 'Administrador Geral',
  },
  {
    id: 'user-002',
    email: '<EMAIL>',
    name: '<PERSON>',
    tenantId: 'tenant-1',
    role: 'manager',
    createdAt: '2024-02-01T08:00:00Z',
    updatedAt: '2024-07-29T16:45:00Z',
    isActive: true,
    lastLoginAt: '2024-07-31T08:30:00Z',
    phone: '+55 11 99999-2222',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    department: 'Vendas',
    position: 'Gerente de Vendas',
  },
  {
    id: 'user-003',
    email: '<EMAIL>',
    name: 'João Oliveira',
    tenantId: 'tenant-1',
    role: 'user',
    createdAt: '2024-03-10T14:20:00Z',
    updatedAt: '2024-07-28T11:15:00Z',
    isActive: true,
    lastLoginAt: '2024-07-30T17:45:00Z',
    phone: '+55 11 99999-3333',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    department: 'Estoque',
    position: 'Analista de Estoque',
  },
  {
    id: 'user-004',
    email: '<EMAIL>',
    name: 'Ana Costa',
    tenantId: 'tenant-1',
    role: 'user',
    createdAt: '2024-04-05T09:30:00Z',
    updatedAt: '2024-07-25T13:20:00Z',
    isActive: true,
    lastLoginAt: '2024-07-29T15:10:00Z',
    phone: '+55 11 99999-4444',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    department: 'Marketing',
    position: 'Analista de Marketing',
  },
  {
    id: 'user-005',
    email: '<EMAIL>',
    name: 'Pedro Lima',
    tenantId: 'tenant-1',
    role: 'viewer',
    createdAt: '2024-05-12T16:45:00Z',
    updatedAt: '2024-07-20T10:30:00Z',
    isActive: true,
    lastLoginAt: '2024-07-28T12:00:00Z',
    phone: '+55 11 99999-5555',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    department: 'Financeiro',
    position: 'Assistente Financeiro',
  },
  {
    id: 'user-006',
    email: '<EMAIL>',
    name: 'Lúcia Ferreira',
    tenantId: 'tenant-1',
    role: 'manager',
    createdAt: '2024-06-01T11:15:00Z',
    updatedAt: '2024-07-15T14:45:00Z',
    isActive: false,
    lastLoginAt: '2024-07-10T09:30:00Z',
    phone: '+55 11 99999-6666',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    department: 'Logística',
    position: 'Gerente de Logística',
  },
  {
    id: 'user-007',
    email: '<EMAIL>',
    name: 'Rafael Souza',
    tenantId: 'tenant-1',
    role: 'user',
    createdAt: '2024-06-15T13:00:00Z',
    updatedAt: '2024-07-31T08:00:00Z',
    isActive: true,
    lastLoginAt: '2024-07-31T07:45:00Z',
    phone: '+55 11 99999-7777',
    avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',
    department: 'TI',
    position: 'Desenvolvedor',
  },
  {
    id: 'user-008',
    email: '<EMAIL>',
    name: 'Fernanda Alves',
    tenantId: 'tenant-1',
    role: 'user',
    createdAt: '2024-07-01T15:30:00Z',
    updatedAt: '2024-07-30T12:15:00Z',
    isActive: true,
    lastLoginAt: '2024-07-30T16:20:00Z',
    phone: '+55 11 99999-8888',
    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    department: 'Atendimento',
    position: 'Analista de Atendimento',
  },
];

// Permissões por role - MOCK DATA FOR DEVELOPMENT
export const rolePermissions: Record<UserRole, UserPermissions> = {
  admin: {
    products: { view: true, create: true, edit: true, delete: true, sync: true },
    stock: { view: true, create: true, edit: true, delete: true, adjust: true },
    shipments: { view: true, create: true, edit: true, delete: true, mlFull: true },
    reports: { view: true, export: true, advanced: true },
    settings: { view: true, edit: true, tenant: true },
    users: { view: true, create: true, edit: true, delete: true, manage: true },
    system: { logs: true, monitoring: true, api: true },
  },
  manager: {
    products: { view: true, create: true, edit: true, delete: false, sync: true },
    stock: { view: true, create: true, edit: true, delete: false, adjust: true },
    shipments: { view: true, create: true, edit: true, delete: false, mlFull: true },
    reports: { view: true, export: true, advanced: true },
    settings: { view: true, edit: false, tenant: false },
    users: { view: true, create: false, edit: false, delete: false, manage: false },
    system: { logs: false, monitoring: false, api: false },
  },
  user: {
    products: { view: true, create: false, edit: true, delete: false, sync: false },
    stock: { view: true, create: false, edit: true, delete: false, adjust: true },
    shipments: { view: true, create: true, edit: true, delete: false, mlFull: false },
    reports: { view: true, export: false, advanced: false },
    settings: { view: false, edit: false, tenant: false },
    users: { view: false, create: false, edit: false, delete: false, manage: false },
    system: { logs: false, monitoring: false, api: false },
  },
  viewer: {
    products: { view: true, create: false, edit: false, delete: false, sync: false },
    stock: { view: true, create: false, edit: false, delete: false, adjust: false },
    shipments: { view: true, create: false, edit: false, delete: false, mlFull: false },
    reports: { view: true, export: false, advanced: false },
    settings: { view: false, edit: false, tenant: false },
    users: { view: false, create: false, edit: false, delete: false, manage: false },
    system: { logs: false, monitoring: false, api: false },
  },
};

// Estatísticas mockadas - MOCK DATA FOR DEVELOPMENT
export const mockUserStats: UserStats = {
  totalUsers: mockUsers.length,
  activeUsers: mockUsers.filter(u => u.isActive).length,
  inactiveUsers: mockUsers.filter(u => !u.isActive).length,
  adminUsers: mockUsers.filter(u => u.role === 'admin').length,
  managerUsers: mockUsers.filter(u => u.role === 'manager').length,
  regularUsers: mockUsers.filter(u => u.role === 'user').length,
  viewerUsers: mockUsers.filter(u => u.role === 'viewer').length,
  recentLogins: mockUsers.filter(u => {
    if (!u.lastLoginAt) return false;
    const lastLogin = new Date(u.lastLoginAt);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return lastLogin > yesterday;
  }).length,
  newUsersThisMonth: mockUsers.filter(u => {
    const created = new Date(u.createdAt);
    const now = new Date();
    return created.getMonth() === now.getMonth() && created.getFullYear() === now.getFullYear();
  }).length,
};

// Departamentos disponíveis - MOCK DATA FOR DEVELOPMENT
export const mockDepartments = [
  'Administração',
  'Vendas',
  'Marketing',
  'Estoque',
  'Logística',
  'Financeiro',
  'TI',
  'Atendimento',
  'Recursos Humanos',
];

// Função utilitária para obter permissões de um usuário
export const getUserPermissions = (role: UserRole): UserPermissions => {
  return rolePermissions[role];
};

// Função utilitária para verificar se usuário tem permissão específica
export const hasPermission = (
  role: UserRole,
  module: keyof UserPermissions,
  action: string
): boolean => {
  const permissions = getUserPermissions(role);
  const modulePermissions = permissions[module] as any;
  return modulePermissions?.[action] === true;
};

// Função para simular delay de API
export const simulateApiDelay = (min: number = 200, max: number = 800): Promise<void> => {
  const delay = Math.floor(Math.random() * (max - min + 1)) + min;
  return new Promise(resolve => setTimeout(resolve, delay));
};
