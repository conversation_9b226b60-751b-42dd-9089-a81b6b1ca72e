import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import type {
  User,
  UserRole,
  UserPermissions,
  UserStats,
  UserFilters,
  CreateUserDto,
  UpdateUserDto,
  PaginatedResponse
} from '../types/api';

// MOCK DATA FOR DEVELOPMENT
import {
  mockUsers,
  mockUserStats,
  mockDepartments,
  getUserPermissions,
  hasPermission,
  simulateApiDelay
} from '../mocks/usersMock';

interface UserManagementState {
  // Estado dos usuários
  users: User[];
  filteredUsers: User[];
  selectedUser: User | null;
  usersLoading: boolean;
  usersError: string | null;

  // Estatísticas
  stats: UserStats;
  statsLoading: boolean;
  statsError: string | null;

  // Filtros e paginação
  filters: UserFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };

  // Departamentos
  departments: string[];

  // Modal/Form state
  isCreateModalOpen: boolean;
  isEditModalOpen: boolean;
  isDeleteModalOpen: boolean;
  formLoading: boolean;
  formError: string | null;

  // Actions - Loading
  loadUsers: () => Promise<void>;
  loadStats: () => Promise<void>;
  loadDepartments: () => Promise<void>;

  // Actions - CRUD
  createUser: (userData: CreateUserDto) => Promise<void>;
  updateUser: (userId: string, userData: UpdateUserDto) => Promise<void>;
  deleteUser: (userId: string) => Promise<void>;
  toggleUserStatus: (userId: string) => Promise<void>;

  // Actions - Selection
  selectUser: (user: User | null) => void;

  // Actions - Pagination
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;

  // Actions - Modals
  openCreateModal: () => void;
  openEditModal: (user: User) => void;
  openDeleteModal: (user: User) => void;
  closeModals: () => void;

  // Actions - Utils
  clearError: () => void;
  getUserPermissions: (role: UserRole) => UserPermissions;
  hasPermission: (role: UserRole, module: keyof UserPermissions, action: string) => boolean;
}

const defaultFilters: UserFilters = {
  search: '',
  role: 'all',
  department: '',
  isActive: undefined,
  sortBy: 'name',
  sortOrder: 'asc',
  page: 1,
  limit: 12,
};

export const useUserManagementStore = create<UserManagementState>()(
  devtools(
    (set, get) => ({
      // Initial state
      users: [],
      filteredUsers: [],
      selectedUser: null,
      usersLoading: false,
      usersError: null,

      stats: {
        totalUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        adminUsers: 0,
        managerUsers: 0,
        regularUsers: 0,
        viewerUsers: 0,
        recentLogins: 0,
        newUsersThisMonth: 0,
      },
      statsLoading: false,
      statsError: null,

      filters: defaultFilters,
      pagination: {
        page: 1,
        limit: 12,
        total: 0,
        totalPages: 0,
      },

      departments: [],

      isCreateModalOpen: false,
      isEditModalOpen: false,
      isDeleteModalOpen: false,
      formLoading: false,
      formError: null,

      // Actions - Loading
      loadUsers: async () => {
        set({ usersLoading: true, usersError: null });

        try {
          // MOCK DATA FOR DEVELOPMENT - Simulate API call
          await simulateApiDelay();

          const { filters } = get();
          const allUsers = [...mockUsers];

          // Simple pagination without filters
          const total = allUsers.length;
          const totalPages = Math.ceil(total / (filters.limit || 12));
          const startIndex = ((filters.page || 1) - 1) * (filters.limit || 12);
          const paginatedUsers = allUsers.slice(startIndex, startIndex + (filters.limit || 12));

          set({
            users: mockUsers,
            filteredUsers: paginatedUsers,
            pagination: {
              page: filters.page || 1,
              limit: filters.limit || 12,
              total,
              totalPages,
            },
            usersLoading: false,
          });
        } catch (error) {
          set({
            usersError: error instanceof Error ? error.message : 'Erro ao carregar usuários',
            usersLoading: false,
          });
        }
      },

      loadStats: async () => {
        set({ statsLoading: true, statsError: null });
        
        try {
          // MOCK DATA FOR DEVELOPMENT - Simulate API call
          await simulateApiDelay();
          
          set({
            stats: mockUserStats,
            statsLoading: false,
          });
        } catch (error) {
          set({
            statsError: error instanceof Error ? error.message : 'Erro ao carregar estatísticas',
            statsLoading: false,
          });
        }
      },

      loadDepartments: async () => {
        try {
          // MOCK DATA FOR DEVELOPMENT - Simulate API call
          await simulateApiDelay(100, 300);

          set({ departments: mockDepartments });
        } catch (error) {
          console.error('Erro ao carregar departamentos:', error);
        }
      },

      // Actions - CRUD
      createUser: async (userData: CreateUserDto) => {
        set({ formLoading: true, formError: null });

        try {
          // MOCK DATA FOR DEVELOPMENT - Simulate API call
          await simulateApiDelay();

          const newUser: User = {
            id: `user-${Date.now()}`,
            email: userData.email,
            name: userData.name,
            tenantId: 'tenant-1', // MOCK - would come from auth context
            role: userData.role,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isActive: userData.isActive ?? true,
            phone: userData.phone,
            department: userData.department,
            position: userData.position,
          };

          // Add to mock data
          mockUsers.push(newUser);

          set({ formLoading: false });

          // Reload users to reflect changes
          await get().loadUsers();
          await get().loadStats();
        } catch (error) {
          set({
            formError: error instanceof Error ? error.message : 'Erro ao criar usuário',
            formLoading: false,
          });
        }
      },

      updateUser: async (userId: string, userData: UpdateUserDto) => {
        set({ formLoading: true, formError: null });

        try {
          // MOCK DATA FOR DEVELOPMENT - Simulate API call
          await simulateApiDelay();

          const userIndex = mockUsers.findIndex(u => u.id === userId);
          if (userIndex === -1) {
            throw new Error('Usuário não encontrado');
          }

          // Update mock data
          mockUsers[userIndex] = {
            ...mockUsers[userIndex],
            ...userData,
            updatedAt: new Date().toISOString(),
          };

          set({ formLoading: false });

          // Reload users to reflect changes
          await get().loadUsers();
          await get().loadStats();
        } catch (error) {
          set({
            formError: error instanceof Error ? error.message : 'Erro ao atualizar usuário',
            formLoading: false,
          });
        }
      },

      deleteUser: async (userId: string) => {
        set({ formLoading: true, formError: null });

        try {
          // MOCK DATA FOR DEVELOPMENT - Simulate API call
          await simulateApiDelay();

          const userIndex = mockUsers.findIndex(u => u.id === userId);
          if (userIndex === -1) {
            throw new Error('Usuário não encontrado');
          }

          // Remove from mock data
          mockUsers.splice(userIndex, 1);

          set({ formLoading: false });

          // Reload users to reflect changes
          await get().loadUsers();
          await get().loadStats();
        } catch (error) {
          set({
            formError: error instanceof Error ? error.message : 'Erro ao excluir usuário',
            formLoading: false,
          });
        }
      },

      toggleUserStatus: async (userId: string) => {
        set({ formLoading: true, formError: null });

        try {
          // MOCK DATA FOR DEVELOPMENT - Simulate API call
          await simulateApiDelay();

          const userIndex = mockUsers.findIndex(u => u.id === userId);
          if (userIndex === -1) {
            throw new Error('Usuário não encontrado');
          }

          // Toggle status in mock data
          mockUsers[userIndex] = {
            ...mockUsers[userIndex],
            isActive: !mockUsers[userIndex].isActive,
            updatedAt: new Date().toISOString(),
          };

          set({ formLoading: false });

          // Reload users to reflect changes
          await get().loadUsers();
          await get().loadStats();
        } catch (error) {
          set({
            formError: error instanceof Error ? error.message : 'Erro ao alterar status do usuário',
            formLoading: false,
          });
        }
      },

      // Actions - Selection
      selectUser: (user: User | null) => {
        set({ selectedUser: user });
      },

      // Actions - Pagination
      setPage: (page: number) => {
        const currentFilters = get().filters;
        set({ filters: { ...currentFilters, page } });
        get().loadUsers();
      },

      setLimit: (limit: number) => {
        const currentFilters = get().filters;
        set({ filters: { ...currentFilters, limit, page: 1 } });
        get().loadUsers();
      },

      // Actions - Modals
      openCreateModal: () => {
        set({
          isCreateModalOpen: true,
          selectedUser: null,
          formError: null,
        });
      },

      openEditModal: (user: User) => {
        set({
          isEditModalOpen: true,
          selectedUser: user,
          formError: null,
        });
      },

      openDeleteModal: (user: User) => {
        set({
          isDeleteModalOpen: true,
          selectedUser: user,
          formError: null,
        });
      },

      closeModals: () => {
        set({
          isCreateModalOpen: false,
          isEditModalOpen: false,
          isDeleteModalOpen: false,
          selectedUser: null,
          formError: null,
        });
      },

      // Actions - Utils
      clearError: () => {
        set({ usersError: null, statsError: null, formError: null });
      },

      getUserPermissions: (role: UserRole) => {
        return getUserPermissions(role);
      },

      hasPermission: (role: UserRole, module: keyof UserPermissions, action: string) => {
        return hasPermission(role, module, action);
      },
    }),
    {
      name: 'user-management-store',
    }
  )
);
