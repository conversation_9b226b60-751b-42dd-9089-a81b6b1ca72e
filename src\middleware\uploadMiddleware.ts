/**
 * Upload Middleware - Multer Configuration
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import multer from 'multer';
import path from 'path';
import fs from 'fs';
import crypto from 'crypto';
import { Request } from 'express';
import { logger } from '@/utils/logger';

// Tipos permitidos por categoria
const ALLOWED_TYPES = {
  avatar: ['image/jpeg', 'image/png', 'image/webp'],
  document: ['application/pdf'],
  spreadsheet: [
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ]
};

// Tamanhos máximos por tipo (em bytes)
const MAX_SIZES = {
  avatar: 5 * 1024 * 1024,      // 5MB
  document: 10 * 1024 * 1024,   // 10MB
  spreadsheet: 15 * 1024 * 1024 // 15MB
};

// Interface para request com informações de upload
interface UploadRequest extends Request {
  user?: {
    tenantId: string;
    userId: string;
    role: string;
  };
  uploadType?: keyof typeof ALLOWED_TYPES;
}

/**
 * Configuração de storage do Multer
 */
const storage = multer.diskStorage({
  destination: (req: UploadRequest, file, cb) => {
    try {
      const { tenantId } = req.user!;
      const uploadType = req.uploadType || 'document';
      
      // Criar estrutura de diretórios: /uploads/{tenantId}/{type}/
      const uploadDir = path.join(
        process.cwd(),
        process.env.UPLOADS_DIR || 'uploads',
        tenantId,
        uploadType
      );

      // Criar diretório se não existir
      fs.mkdirSync(uploadDir, { recursive: true });

      logger.info('Upload destination created', {
        tenantId,
        uploadType,
        uploadDir,
        userId: req.user?.userId
      });

      cb(null, uploadDir);
    } catch (error) {
      logger.error('Error creating upload destination', {
        error: error instanceof Error ? error.message : 'Unknown error',
        tenantId: req.user?.tenantId,
        uploadType: req.uploadType
      });
      cb(error as Error, '');
    }
  },

  filename: (req: UploadRequest, file, cb) => {
    try {
      const { userId } = req.user!;
      const uploadType = req.uploadType || 'document';
      
      // Gerar nome único: {type}_{userId}_{timestamp}_{random}.{ext}
      const timestamp = Date.now();
      const randomString = crypto.randomBytes(8).toString('hex');
      const ext = path.extname(file.originalname).toLowerCase();
      
      const filename = `${uploadType}_${userId}_${timestamp}_${randomString}${ext}`;

      logger.info('Generated filename for upload', {
        originalName: file.originalname,
        generatedName: filename,
        uploadType,
        userId
      });

      cb(null, filename);
    } catch (error) {
      logger.error('Error generating filename', {
        error: error instanceof Error ? error.message : 'Unknown error',
        originalName: file.originalname,
        userId: req.user?.userId
      });
      cb(error as Error, '');
    }
  }
});

/**
 * Filtro de arquivos - validação de tipo e segurança
 */
const fileFilter = (req: UploadRequest, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  try {
    const uploadType = req.uploadType || 'document';
    const allowedTypes = ALLOWED_TYPES[uploadType];

    // Verificar MIME type
    if (!allowedTypes.includes(file.mimetype)) {
      logger.warn('File type not allowed', {
        mimetype: file.mimetype,
        uploadType,
        allowedTypes,
        originalName: file.originalname,
        userId: req.user?.userId
      });
      
      return cb(new Error(`Tipo de arquivo não permitido. Tipos aceitos para ${uploadType}: ${allowedTypes.join(', ')}`));
    }

    // Verificar extensão do arquivo
    const ext = path.extname(file.originalname).toLowerCase();
    const allowedExtensions = {
      avatar: ['.jpg', '.jpeg', '.png', '.webp'],
      document: ['.pdf'],
      spreadsheet: ['.xlsx', '.xls', '.csv']
    };

    if (!allowedExtensions[uploadType].includes(ext)) {
      logger.warn('File extension not allowed', {
        extension: ext,
        uploadType,
        allowedExtensions: allowedExtensions[uploadType],
        originalName: file.originalname,
        userId: req.user?.userId
      });
      
      return cb(new Error(`Extensão de arquivo não permitida. Extensões aceitas: ${allowedExtensions[uploadType].join(', ')}`));
    }

    // Verificar nome do arquivo (sanitização básica)
    const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-_]/g, '');
    if (sanitizedName !== file.originalname) {
      logger.warn('Filename contains invalid characters', {
        originalName: file.originalname,
        sanitizedName,
        userId: req.user?.userId
      });
    }

    logger.info('File passed validation', {
      mimetype: file.mimetype,
      extension: ext,
      uploadType,
      originalName: file.originalname,
      userId: req.user?.userId
    });

    cb(null, true);
  } catch (error) {
    logger.error('Error in file filter', {
      error: error instanceof Error ? error.message : 'Unknown error',
      originalName: file.originalname,
      userId: req.user?.userId
    });
    cb(error as Error);
  }
};

/**
 * Configuração principal do Multer
 */
const createUploadMiddleware = (uploadType: keyof typeof ALLOWED_TYPES) => {
  return multer({
    storage,
    fileFilter,
    limits: {
      fileSize: MAX_SIZES[uploadType],
      files: 1, // Apenas um arquivo por vez
      fieldSize: 1024 * 1024, // 1MB para campos de texto
    },
    // Middleware para definir o tipo de upload
    onError: (error, next) => {
      logger.error('Multer error', {
        error: error.message,
        code: error.code
      });
      next(error);
    }
  }).single('file');
};

/**
 * Middleware para definir tipo de upload
 */
export const setUploadType = (uploadType: keyof typeof ALLOWED_TYPES) => {
  return (req: UploadRequest, res: any, next: any) => {
    req.uploadType = uploadType;
    next();
  };
};

/**
 * Middlewares específicos por tipo
 */
export const uploadAvatar = [
  setUploadType('avatar'),
  createUploadMiddleware('avatar')
];

export const uploadDocument = [
  setUploadType('document'),
  createUploadMiddleware('document')
];

export const uploadSpreadsheet = [
  setUploadType('spreadsheet'),
  createUploadMiddleware('spreadsheet')
];

/**
 * Middleware genérico
 */
export const upload = createUploadMiddleware('document');

/**
 * Utilitários de validação
 */
export const validateFileSize = (file: Express.Multer.File, maxSize: number): boolean => {
  return file.size <= maxSize;
};

export const validateFileType = (file: Express.Multer.File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.mimetype);
};

/**
 * Cleanup de arquivos temporários
 */
export const cleanupTempFile = (filePath: string): void => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.info('Temporary file cleaned up', { filePath });
    }
  } catch (error) {
    logger.error('Error cleaning up temporary file', {
      filePath,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

export default {
  uploadAvatar,
  uploadDocument,
  uploadSpreadsheet,
  upload,
  setUploadType,
  validateFileSize,
  validateFileType,
  cleanupTempFile
};
