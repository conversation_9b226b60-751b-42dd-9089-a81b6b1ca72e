import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import apiService from '../services/api';
import type { ProductWithStock, MLSyncStatus, MLSyncResult } from '../types/api';

// Utility function for retry logic with timeout
const withRetryAndTimeout = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  timeout: number = 30000,
  retryDelay: number = 1000
): Promise<T> => {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Create timeout promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error(`Operação expirou após ${timeout}ms`)), timeout);
      });

      // Race between operation and timeout
      const result = await Promise.race([operation(), timeoutPromise]);
      return result;
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Erro desconhecido');

      if (attempt === maxRetries) {
        throw lastError;
      }

      // Wait before retry (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  throw lastError!;
};

export interface MercadoLivreAccount {
  id: string;
  userId: string;
  nickname: string;
  email: string;
  isActive: boolean;
  accessToken?: string;
  expiresAt: string;
  createdAt: string;
  updatedAt: string;
}

// Interface removida - usando ProductWithStock de types/api.ts

export interface MercadoLivreOrder {
  id: string;
  mlOrderId: string;
  status: string;
  totalAmount: number;
  dateCreated: string;
  items: Array<{
    id: string;
    title: string;
    quantity: number;
    unitPrice: number;
  }>;
}

// Interface removida - usando MLSyncStatus de types/api.ts

interface MercadoLivreState {
  // Accounts
  accounts: MercadoLivreAccount[];
  selectedAccount: MercadoLivreAccount | null;
  accountsLoading: boolean;
  accountsError: string | null;

  // Products
  products: ProductWithStock[];
  productsLoading: boolean;
  productsError: string | null;

  // Orders
  orders: MercadoLivreOrder[];
  ordersLoading: boolean;
  ordersError: string | null;

  // Sync
  syncStatus: MLSyncStatus;

  // Stats
  stats: any;
  statsLoading: boolean;
  statsError: string | null;
  
  // Actions
  loadAccounts: () => Promise<void>;
  selectAccount: (account: MercadoLivreAccount | null) => void;
  disconnectAccount: (accountId: string) => Promise<void>;

  // Product actions with stock integration
  loadProducts: (accountId: string, includeStock?: boolean) => Promise<void>;
  loadProductsWithStock: (accountId: string) => Promise<void>;
  refreshProduct: (productId: string) => Promise<void>;

  loadOrders: (accountId: string) => Promise<void>;
  loadStats: (accountId: string) => Promise<void>;

  // Stock management actions
  updateStock: (accountId: string, itemId: string, quantity: number) => Promise<void>;
  updateProductStock: (productId: string, quantity: number, reason?: string) => Promise<void>;
  bulkUpdateStock: (updates: Array<{productId: string, quantity: number, reason?: string}>) => Promise<void>;
  getStockCalculation: (productId: string) => Promise<void>;
  getStockHistory: (productId: string) => Promise<void>;

  // Sync actions
  syncProducts: (accountId: string) => Promise<MLSyncResult>;
  syncSingleProduct: (productId: string) => Promise<void>;

  // OAuth
  initiateAuth: () => Promise<string>;
  handleCallback: (code: string, state?: string) => Promise<void>;
}

export const useMercadoLivreStore = create<MercadoLivreState>()(
  devtools(
    (set, get) => ({
      // Initial state
      accounts: [],
      selectedAccount: null,
      accountsLoading: false,
      accountsError: null,

      products: [],
      productsLoading: false,
      productsError: null,

      orders: [],
      ordersLoading: false,
      ordersError: null,

      syncStatus: {
        isLoading: false,
        lastSync: null,
        error: null,
        progress: 0,
      },

      stats: null,
      statsLoading: false,
      statsError: null,
      
      // Actions
      loadAccounts: async () => {
        set({ accountsLoading: true, accountsError: null });
        try {
          const response = await apiService.getMercadoLivreAccounts();
          if (response.success) {
            set({ accounts: response.data, accountsLoading: false, accountsError: null });
          } else {
            const errorMessage = response.error || 'Erro ao carregar contas';
            console.error('Erro ao carregar contas:', errorMessage);
            set({ accountsLoading: false, accountsError: errorMessage });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao carregar contas';
          console.error('Erro ao carregar contas:', error);
          set({ accountsLoading: false, accountsError: errorMessage });
        }
      },
      
      selectAccount: (account) => {
        set({ selectedAccount: account });
        if (account) {
          // Auto-load data for selected account
          get().loadProducts(account.id);
          get().loadOrders(account.id);
          get().loadStats(account.id);
        }
      },
      
      disconnectAccount: async (accountId: string) => {
        try {
          const response = await apiService.disconnectMercadoLivreAccount(accountId);
          if (response.success) {
            // Reload accounts
            await get().loadAccounts();

            // Clear selected account if it was disconnected
            const { selectedAccount } = get();
            if (selectedAccount?.id === accountId) {
              set({ selectedAccount: null, products: [], orders: [], stats: null });
            }
          }
        } catch (error) {
          console.error('Erro ao desconectar conta:', error);
        }
      },
      
      loadProducts: async (accountId: string, includeStock: boolean = false) => {
        set({ productsLoading: true, productsError: null });
        try {
          const response = includeStock
            ? await apiService.getProductsWithStock(accountId)
            : await apiService.getMercadoLivreProducts(accountId);

          if (response.success) {
            set({ products: response.data.results || [], productsLoading: false, productsError: null });
          } else {
            const errorMessage = response.error || 'Erro ao carregar produtos';
            console.error('Erro ao carregar produtos:', errorMessage);
            set({ productsLoading: false, productsError: errorMessage });
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido ao carregar produtos';
          console.error('Erro ao carregar produtos:', error);
          set({ productsLoading: false, productsError: errorMessage });
        }
      },

      loadProductsWithStock: async (accountId: string) => {
        return get().loadProducts(accountId, true);
      },

      refreshProduct: async (productId: string) => {
        try {
          const response = await apiService.getProductWithStock(productId);
          if (response.success) {
            const { products } = get();
            const updatedProducts = products.map(product =>
              product.id === productId ? response.data : product
            );
            set({ products: updatedProducts });
          }
        } catch (error) {
          console.error('Erro ao atualizar produto:', error);
        }
      },
      
      loadOrders: async (accountId: string) => {
        set({ ordersLoading: true });
        try {
          const response = await apiService.getMercadoLivreOrders(accountId);
          if (response.success) {
            set({ orders: response.data.results || [], ordersLoading: false });
          } else {
            console.error('Erro ao carregar pedidos:', response.error);
            set({ ordersLoading: false });
          }
        } catch (error) {
          console.error('Erro ao carregar pedidos:', error);
          set({ ordersLoading: false });
        }
      },
      
      loadStats: async (accountId: string) => {
        set({ statsLoading: true });
        try {
          const response = await apiService.getMercadoLivreStats(accountId);
          if (response.success) {
            set({ stats: response.data, statsLoading: false });
          } else {
            console.error('Erro ao carregar estatísticas:', response.error);
            set({ statsLoading: false });
          }
        } catch (error) {
          console.error('Erro ao carregar estatísticas:', error);
          set({ statsLoading: false });
        }
      },
      
      updateStock: async (accountId: string, itemId: string, quantity: number) => {
        try {
          const response = await apiService.updateMercadoLivreStock(accountId, itemId, quantity);
          if (response.success) {
            // Update local product
            const { products } = get();
            const updatedProducts = products.map(product =>
              product.mlId === itemId
                ? { ...product, availableQuantity: quantity }
                : product
            );
            set({ products: updatedProducts });
          }
        } catch (error) {
          console.error('Erro ao atualizar estoque:', error);
        }
      },

      updateProductStock: async (productId: string, quantity: number, reason?: string) => {
        try {
          const response = await apiService.updateProductStock({
            productId,
            quantity,
            reason,
            syncWithML: true
          });

          if (response.success) {
            // Update local product with stock data
            const { products } = get();
            const updatedProducts = products.map(product =>
              product.id === productId ? response.data.product : product
            );
            set({ products: updatedProducts });
          }
        } catch (error) {
          console.error('Erro ao atualizar estoque do produto:', error);
        }
      },

      bulkUpdateStock: async (updates: Array<{productId: string, quantity: number, reason?: string}>) => {
        try {
          const response = await apiService.bulkUpdateStock({
            updates,
            syncWithML: true
          });

          if (response.success) {
            // Update local products
            const { products } = get();
            const updatedProducts = products.map(product => {
              const update = response.data.results.find(r => r.productId === product.id);
              return update?.product || product;
            });
            set({ products: updatedProducts });
          }
        } catch (error) {
          console.error('Erro ao atualizar estoque em lote:', error);
        }
      },

      getStockCalculation: async (productId: string) => {
        try {
          const response = await apiService.getStockCalculation(productId);
          if (response.success) {
            // Update product with stock calculation
            const { products } = get();
            const updatedProducts = products.map(product =>
              product.id === productId
                ? { ...product, stockCalculation: response.data }
                : product
            );
            set({ products: updatedProducts });
          }
        } catch (error) {
          console.error('Erro ao carregar cálculo de estoque:', error);
        }
      },

      getStockHistory: async (productId: string) => {
        try {
          const response = await apiService.getStockHistory(productId);
          if (response.success) {
            // Could store history in a separate state if needed
            return response.data;
          }
        } catch (error) {
          console.error('Erro ao carregar histórico de estoque:', error);
        }
      },
      
      syncProducts: async (accountId: string): Promise<MLSyncResult> => {
        // RACE CONDITION PROTECTION: Prevent multiple simultaneous syncs
        const currentState = get();
        if (currentState.syncStatus.isLoading) {
          throw new Error('Sincronização já em andamento. Aguarde a conclusão.');
        }

        set({
          syncStatus: {
            isLoading: true,
            lastSync: null,
            error: null,
            progress: 0
          }
        });

        try {
          // Apply retry logic and timeout to sync operation
          const response = await withRetryAndTimeout(
            () => apiService.syncMercadoLivreProducts(accountId),
            3, // max retries
            60000, // 60 second timeout
            2000 // 2 second delay between retries
          );

          if (response.success) {
            const syncResult = response.data as MLSyncResult;

            set({
              syncStatus: {
                isLoading: false,
                lastSync: new Date().toISOString(),
                error: null,
                progress: 100,
                itemsProcessed: syncResult.itemsProcessed,
                totalItems: syncResult.itemsProcessed
              }
            });

            // Reload products with stock after sync
            await get().loadProductsWithStock(accountId);

            // INTEGRATION: Send success notification
            try {
              const { useNotificationStore } = await import('./notificationStore');
              const notificationStore = useNotificationStore.getState();
              notificationStore.addNotification({
                title: 'Sincronização Concluída',
                message: `${syncResult.itemsProcessed} produtos sincronizados com sucesso`,
                type: 'success',
                duration: 5000
              });
            } catch (error) {
              console.warn('Failed to send sync notification:', error);
            }

            return syncResult;
          } else {
            const error = response.error || 'Erro na sincronização';
            set({
              syncStatus: {
                isLoading: false,
                lastSync: null,
                error,
                progress: 0
              }
            });

            throw new Error(error);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
          set({
            syncStatus: {
              isLoading: false,
              lastSync: null,
              error: errorMessage,
              progress: 0
            }
          });

          // INTEGRATION: Send error notification
          try {
            const { useNotificationStore } = await import('./notificationStore');
            const notificationStore = useNotificationStore.getState();
            notificationStore.addNotification({
              title: 'Erro na Sincronização',
              message: errorMessage,
              type: 'error',
              duration: 8000
            });
          } catch (notifError) {
            console.warn('Failed to send error notification:', notifError);
          }

          throw error;
        }
      },

      syncSingleProduct: async (productId: string) => {
        try {
          const response = await apiService.syncSingleProduct(productId);
          if (response.success) {
            // Refresh the specific product
            await get().refreshProduct(productId);
          }
        } catch (error) {
          console.error('Erro ao sincronizar produto:', error);
        }
      },
      
      initiateAuth: async (): Promise<string> => {
        const response = await apiService.getMercadoLivreAuthUrl();
        if (response.success) {
          return response.data.authUrl;
        }
        throw new Error(response.error || 'Erro ao iniciar autenticação');
      },

      handleCallback: async (code: string, state?: string) => {
        const response = await apiService.handleMercadoLivreCallback(code, state);
        if (response.success) {
          // Reload accounts after successful connection
          await get().loadAccounts();
        } else {
          throw new Error(response.error || 'Erro no callback de autenticação');
        }
      },
    }),
    {
      name: 'mercado-livre-store',
    }
  )
);
