 
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { Modal, ModalContent, ModalDescription, ModalFooter, ModalHeader, ModalTitle, useModal, ConfirmModal } from './Modal';
import { Button } from './Button';
import { Stack } from './Stack';
import { Input } from './Input';

const meta = {
  title: 'Components/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    isOpen: { control: 'boolean' },
    onClose: { action: 'closed' },
    size: {
      control: 'select',
      options: ['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl', 'full'],
    },
    position: {
      control: 'select',
      options: [
        'center',
        'top',
        'bottom',
        'left',
        'right',
        'top-left',
        'top-right',
        'bottom-left',
        'bottom-right',
      ],
    },
    variant: {
      control: 'select',
      options: ['default', 'elevated', 'outline', 'ghost', 'flat', 'primary', 'secondary', 'tertiary'],
    },
  },
} satisfies Meta<typeof Modal>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    isOpen: true,
    children: (
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Modal Padrão</ModalTitle>
          <ModalDescription>Este é um modal simples.</ModalDescription>
        </ModalHeader>
        <ModalFooter>
          <Button variant="secondary" onClick={() => console.log('Cancelar')}>Cancelar</Button>
          <Button variant="primary" onClick={() => console.log('Confirmar')}>Confirmar</Button>
        </ModalFooter>
      </ModalContent>
    ),
  },
};

export const WithForm: Story = {
  args: {
    isOpen: true,
    size: 'sm',
    children: (
      <ModalContent>
        <ModalHeader>
          <ModalTitle>Formulário no Modal</ModalTitle>
        </ModalHeader>
        <ModalContent className="px-6 py-4">
          <Stack spacing="md">
            <Input label="Nome" placeholder="Seu nome" />
            <Input label="Email" type="email" placeholder="<EMAIL>" />
          </Stack>
        </ModalContent>
        <ModalFooter>
          <Button variant="secondary">Fechar</Button>
          <Button variant="primary">Enviar</Button>
        </ModalFooter>
      </ModalContent>
    ),
  },
};

export const ConfirmationModal: Story = {
  render: (args) => {
    const { openModal, ConfirmModalComponent } = useModal();

    const handleConfirm = () => {
      console.log('Ação confirmada!');
      // Additional logic after confirmation
    };

    return (
      <>
        <Button onClick={() => openModal('confirm-modal')}>Abrir Confirmação</Button>
        <ConfirmModalComponent
          modalId="confirm-modal"
          title="Confirmar Ação"
          description="Você tem certeza que deseja prosseguir com esta ação?"
          onConfirm={handleConfirm}
          confirmText="Sim, Continuar"
          cancelText="Não, Cancelar"
          size="xs"
          variant="danger"
          {...args}
        />
      </>
    );
  },
  args: {
    isOpen: false,
  },
};

export const FullScreenModal: Story = {
  args: {
    isOpen: true,
    size: 'full',
    children: (
      <ModalContent className="h-full w-full flex items-center justify-center">
        <ModalTitle className="text-4xl text-design-primary-700">Modal Tela Cheia</ModalTitle>
      </ModalContent>
    ),
  },
}; 
