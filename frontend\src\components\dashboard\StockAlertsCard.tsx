import React from 'react';
import {
  ExclamationTriangleIcon,
  ClockIcon,
  InformationCircleIcon,
  BellIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { StatusBadge } from '../ui/Table';
import type { StockAlert } from '../../types/api';

interface StockAlertsCardProps {
  alerts: StockAlert[];
  loading?: boolean;
  onMarkAsRead?: (alertId: string) => void;
  onViewAll?: () => void;
}

export const StockAlertsCard: React.FC<StockAlertsCardProps> = ({
  alerts,
  loading = false,
  onMarkAsRead,
  onViewAll
}) => {
  const unreadAlerts = alerts.filter(alert => !alert.isRead);
  const criticalAlerts = alerts.filter(alert => alert.severity === 'critical');
  const highAlerts = alerts.filter(alert => alert.severity === 'high');

  const getAlertIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <ExclamationTriangleIcon className="h-4 w-4" />;
      case 'high':
        return <ClockIcon className="h-4 w-4" />;
      case 'medium':
        return <InformationCircleIcon className="h-4 w-4" />;
      default:
        return <BellIcon className="h-4 w-4" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'danger';
      case 'high':
        return 'warning';
      case 'medium':
        return 'primary';
      default:
        return 'secondary';
    }
  };

  const formatTimeAgo = (date: string) => {
    const now = new Date();
    const alertDate = new Date(date);
    const diffInMinutes = Math.floor((now.getTime() - alertDate.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 60) {
      return `${diffInMinutes} min atrás`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h atrás`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days}d atrás`;
    }
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Alertas de Estoque</CardTitle>
            <p className="text-sm text-muted-foreground">
              {unreadAlerts.length} alerta{unreadAlerts.length !== 1 ? 's' : ''} não lido{unreadAlerts.length !== 1 ? 's' : ''}
            </p>
          </div>
          {alerts.length > 0 && (
            <button
              onClick={onViewAll}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium"
            >
              Ver todos
            </button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
        {/* Resumo por severidade */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-red-50 dark:bg-red-950/20 rounded-lg border border-red-200 dark:border-red-800">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400">{criticalAlerts.length}</div>
            <div className="text-sm text-red-600 dark:text-red-400 font-medium">Críticos</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">{highAlerts.length}</div>
            <div className="text-sm text-yellow-600 dark:text-yellow-400 font-medium">Altos</div>
          </div>
          <div className="text-center p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">{alerts.filter(a => a.isRead).length}</div>
            <div className="text-sm text-green-600 dark:text-green-400 font-medium">Lidos</div>
          </div>
        </div>

        {/* Lista de alertas */}
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {alerts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircleIcon className="h-12 w-12 mx-auto mb-2 text-muted-foreground/50" />
              <p>Nenhum alerta de estoque</p>
              <p className="text-sm">Tudo está funcionando perfeitamente!</p>
            </div>
          ) : (
            alerts.slice(0, 10).map((alert) => (
              <div
                key={alert.id}
                className={`border rounded-lg p-4 transition-colors ${
                  alert.isRead 
                    ? 'bg-muted/30 border-border' 
                    : 'bg-card border-blue-200 dark:border-blue-800 shadow-sm'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <StatusBadge
                        status={alert.severity as any}
                        variant={getSeverityColor(alert.severity) as any}
                      />
                      <span className="text-xs text-muted-foreground">
                        {formatTimeAgo(alert.createdAt)}
                      </span>
                      {!alert.isRead && (
                        <div className="w-2 h-2 bg-blue-500 dark:bg-blue-400 rounded-full"></div>
                      )}
                    </div>
                    
                    <h4 className="font-medium text-foreground mb-1">
                      {alert.product.title}
                    </h4>
                    
                    <p className="text-sm text-muted-foreground mb-2">
                      {alert.message}
                    </p>
                    
                    <div className="text-xs text-muted-foreground">
                      SKU: {alert.product.sku} • 
                      Limite: {alert.threshold} • 
                      Atual: {alert.currentValue}
                    </div>
                  </div>
                  
                  <div className="ml-4 flex-shrink-0 flex flex-col items-center gap-2">
                    {getAlertIcon(alert.severity)}
                    {!alert.isRead && onMarkAsRead && (
                      <button
                        onClick={() => onMarkAsRead(alert.id)}
                        className="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                        title="Marcar como lido"
                      >
                        Marcar
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default StockAlertsCard;