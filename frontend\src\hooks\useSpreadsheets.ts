/**
 * Hook personalizado para gerenciamento de planilhas
 * Sistema Magnow - Interface Aprimorada
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';

// Interfaces
interface SpreadsheetTemplate {
  id: string;
  name: string;
  description: string;
  marketplace: string;
  columns: TemplateColumn[];
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  usageCount: number;
}

interface TemplateColumn {
  id: string;
  name: string;
  key: string;
  type: 'string' | 'number' | 'boolean' | 'date' | 'currency' | 'percentage';
  required: boolean;
  validation?: ColumnValidation;
  formatting?: ColumnFormatting;
  order: number;
}

interface ColumnValidation {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  allowedValues?: string[];
  forbiddenValues?: string[];
  customRules?: ValidationRule[];
}

interface ColumnFormatting {
  prefix?: string;
  suffix?: string;
  decimalPlaces?: number;
  thousandsSeparator?: boolean;
  conditionalFormatting?: ConditionalRule[];
}

interface ValidationRule {
  id: string;
  name: string;
  expression: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

interface ConditionalRule {
  id: string;
  condition: string;
  style: {
    backgroundColor?: string;
    textColor?: string;
    fontWeight?: 'normal' | 'bold';
    fontSize?: string;
  };
}

interface SpreadsheetConfig {
  templateId: string;
  warehouses: string[];
  categories: string[];
  format: 'xlsx' | 'csv';
  maxProducts?: number;
  minGapThreshold?: number;
  useStockCalculation: boolean;
  splitByWarehouse: boolean;
  automation?: AutomationRule[];
  marketplace?: MarketplaceConfig;
  notifications?: NotificationConfig;
  notes?: string;
}

interface AutomationRule {
  id: string;
  name: string;
  trigger: 'schedule' | 'stock_change' | 'price_change' | 'manual';
  conditions: Array<{
    field: string;
    operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
    value: any;
  }>;
  actions: Array<{
    type: 'generate_spreadsheet' | 'send_notification' | 'update_prices' | 'sync_inventory';
    parameters: Record<string, any>;
  }>;
  isActive: boolean;
}

interface MarketplaceConfig {
  platform: string;
  credentials: Record<string, string>;
  syncSettings: {
    autoSync: boolean;
    syncInterval: number;
    syncFields: string[];
  };
}

interface NotificationConfig {
  email: boolean;
  webhook: boolean;
  inApp: boolean;
  recipients: string[];
  webhookUrl?: string;
}

interface SpreadsheetGenerationResult {
  success: boolean;
  fileName?: string;
  downloadUrl?: string;
  totalProducts: number;
  processedProducts: number;
  skippedProducts: number;
  errors: SpreadsheetError[];
  warnings: SpreadsheetWarning[];
  generationTime: number;
  cacheUsed: boolean;
  metadata: {
    templateUsed: string;
    warehousesIncluded: string[];
    categoriesIncluded: string[];
    filters: Record<string, any>;
    generatedAt: string;
  };
}

interface SpreadsheetError {
  type: 'validation' | 'processing' | 'system' | 'template';
  message: string;
  details?: string;
  productId?: string;
  field?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
}

interface SpreadsheetWarning {
  type: 'data_quality' | 'performance' | 'compatibility';
  message: string;
  details?: string;
  productId?: string;
  field?: string;
  suggestion?: string;
  timestamp: string;
}

interface PreviewData {
  totalProducts: number;
  estimatedSize: string;
  warehouseBreakdown: Array<{
    warehouse: string;
    productCount: number;
    totalValue: number;
  }>;
  sampleRows: Array<Record<string, any>>;
  columnInfo: Array<{
    name: string;
    type: string;
    sampleValues: any[];
    nullCount: number;
  }>;
  validationResults: {
    errors: number;
    warnings: number;
    passed: number;
  };
  potentialIssues: Array<{
    type: string;
    count: number;
    description: string;
    severity: 'low' | 'medium' | 'high';
  }>;
}

interface AnalyticsData {
  overview: {
    totalSpreadsheets: number;
    totalDownloads: number;
    totalProducts: number;
    totalUsers: number;
    growthRate: number;
    avgGenerationTime: number;
    successRate: number;
    cacheHitRate: number;
  };
  usage: {
    dailyGenerations: number;
    weeklyGenerations: number;
    monthlyGenerations: number;
    peakHours: Array<{ hour: number; count: number }>;
    topTemplates: Array<{ name: string; usage: number; percentage: number }>;
    userActivity: Array<{ date: string; count: number }>;
  };
  performance: {
    avgResponseTime: number;
    p95ResponseTime: number;
    errorRate: number;
    throughput: number;
    cachePerformance: {
      hitRate: number;
      missRate: number;
      avgRetrievalTime: number;
    };
    systemLoad: {
      cpu: number;
      memory: number;
      disk: number;
    };
  };
  templates: Array<{
    id: string;
    name: string;
    usageCount: number;
    successRate: number;
    avgGenerationTime: number;
    errorCount: number;
    lastUsed: string;
    trend: 'up' | 'down' | 'stable';
  }>;
  trends: Array<{
    date: string;
    generations: number;
    downloads: number;
    errors: number;
    avgTime: number;
  }>;
  errors: {
    totalErrors: number;
    errorRate: number;
    topErrors: Array<{
      type: string;
      count: number;
      percentage: number;
      lastOccurrence: string;
    }>;
    errorsByTemplate: Array<{
      templateName: string;
      errorCount: number;
      errorRate: number;
    }>;
  };
  predictions: {
    nextWeekGenerations: number;
    peakLoadPrediction: {
      date: string;
      expectedLoad: number;
      confidence: number;
    };
    resourceRecommendations: Array<{
      type: 'cpu' | 'memory' | 'storage';
      recommendation: string;
      priority: 'low' | 'medium' | 'high';
    }>;
  };
}

// Hook principal
export const useSpreadsheets = () => {
  // Estados
  const [templates, setTemplates] = useState<SpreadsheetTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<SpreadsheetTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [generationResult, setGenerationResult] = useState<SpreadsheetGenerationResult | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [error, setError] = useState<string | null>(null);
  
  // Refs para controle de requisições
  const abortControllerRef = useRef<AbortController | null>(null);
  const previewTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Carregar templates
  const loadTemplates = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Simular carregamento de templates
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockTemplates: SpreadsheetTemplate[] = [
        {
          id: 'ml-default',
          name: 'Mercado Livre - Padrão',
          description: 'Template padrão para produtos do Mercado Livre',
          marketplace: 'mercadolivre',
          isDefault: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-15T00:00:00Z',
          usageCount: 245,
          columns: [
            {
              id: 'sku',
              name: 'SKU',
              key: 'sku',
              type: 'string',
              required: true,
              order: 1,
              validation: {
                minLength: 3,
                maxLength: 50,
                pattern: '^[A-Z0-9-]+$'
              }
            },
            {
              id: 'title',
              name: 'Título',
              key: 'title',
              type: 'string',
              required: true,
              order: 2,
              validation: {
                minLength: 10,
                maxLength: 60
              }
            },
            {
              id: 'price',
              name: 'Preço',
              key: 'price',
              type: 'currency',
              required: true,
              order: 3,
              formatting: {
                prefix: 'R$ ',
                decimalPlaces: 2,
                thousandsSeparator: true
              }
            },
            {
              id: 'stock',
              name: 'Estoque',
              key: 'stock',
              type: 'number',
              required: true,
              order: 4
            }
          ]
        },
        {
          id: 'shopee-default',
          name: 'Shopee - Padrão',
          description: 'Template padrão para produtos da Shopee',
          marketplace: 'shopee',
          isDefault: false,
          createdAt: '2024-01-05T00:00:00Z',
          updatedAt: '2024-01-10T00:00:00Z',
          usageCount: 89,
          columns: [
            {
              id: 'product_id',
              name: 'ID do Produto',
              key: 'product_id',
              type: 'string',
              required: true,
              order: 1
            },
            {
              id: 'name',
              name: 'Nome do Produto',
              key: 'name',
              type: 'string',
              required: true,
              order: 2
            },
            {
              id: 'price',
              name: 'Preço',
              key: 'price',
              type: 'currency',
              required: true,
              order: 3
            }
          ]
        }
      ];
      
      setTemplates(mockTemplates);
      
      // Definir template padrão se nenhum estiver selecionado
      if (!selectedTemplate) {
        const defaultTemplate = mockTemplates.find(t => t.isDefault);
        if (defaultTemplate) {
          setSelectedTemplate(defaultTemplate);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar templates';
      setError(errorMessage);
      toast.error('Erro ao carregar templates', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTemplate]);

  // Gerar preview
  const generatePreview = useCallback(async (config: SpreadsheetConfig): Promise<PreviewData | null> => {
    try {
      // Cancelar preview anterior se existir
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      if (previewTimeoutRef.current) {
        clearTimeout(previewTimeoutRef.current);
      }

      abortControllerRef.current = new AbortController();
      
      setIsLoading(true);
      setError(null);
      
      // Simular delay de geração de preview
      await new Promise(resolve => {
        previewTimeoutRef.current = setTimeout(resolve, 1500);
      });
      
      // Verificar se foi cancelado
      if (abortControllerRef.current.signal.aborted) {
        return null;
      }
      
      const mockPreview: PreviewData = {
        totalProducts: 1250,
        estimatedSize: '2.3 MB',
        warehouseBreakdown: [
          { warehouse: 'Principal', productCount: 850, totalValue: 125000 },
          { warehouse: 'Secundário', productCount: 400, totalValue: 75000 }
        ],
        sampleRows: [
          { sku: 'PROD-001', title: 'Produto Exemplo 1', price: 99.90, stock: 15 },
          { sku: 'PROD-002', title: 'Produto Exemplo 2', price: 149.90, stock: 8 },
          { sku: 'PROD-003', title: 'Produto Exemplo 3', price: 79.90, stock: 23 }
        ],
        columnInfo: [
          { name: 'SKU', type: 'string', sampleValues: ['PROD-001', 'PROD-002'], nullCount: 0 },
          { name: 'Título', type: 'string', sampleValues: ['Produto Exemplo 1'], nullCount: 2 },
          { name: 'Preço', type: 'currency', sampleValues: [99.90, 149.90], nullCount: 0 },
          { name: 'Estoque', type: 'number', sampleValues: [15, 8, 23], nullCount: 1 }
        ],
        validationResults: {
          errors: 5,
          warnings: 12,
          passed: 1233
        },
        potentialIssues: [
          {
            type: 'missing_dimensions',
            count: 45,
            description: 'Produtos sem dimensões definidas',
            severity: 'medium'
          },
          {
            type: 'zero_stock',
            count: 12,
            description: 'Produtos com estoque zerado',
            severity: 'low'
          }
        ]
      };
      
      setPreviewData(mockPreview);
      return mockPreview;
    } catch (err) {
      if (err instanceof Error && err.name === 'AbortError') {
        return null;
      }
      
      const errorMessage = err instanceof Error ? err.message : 'Erro ao gerar preview';
      setError(errorMessage);
      toast.error('Erro ao gerar preview', {
        description: errorMessage
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Gerar planilha
  const generateSpreadsheet = useCallback(async (config: SpreadsheetConfig): Promise<SpreadsheetGenerationResult | null> => {
    try {
      setIsGenerating(true);
      setError(null);
      
      // Simular geração de planilha
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const result: SpreadsheetGenerationResult = {
        success: true,
        fileName: `planilha_${config.templateId}_${Date.now()}.${config.format}`,
        downloadUrl: `/api/downloads/planilha_${Date.now()}.${config.format}`,
        totalProducts: 1250,
        processedProducts: 1245,
        skippedProducts: 5,
        errors: [
          {
            type: 'validation',
            message: 'SKU inválido',
            details: 'SKU deve conter apenas letras maiúsculas, números e hífens',
            productId: 'PROD-999',
            field: 'sku',
            severity: 'medium',
            timestamp: new Date().toISOString()
          }
        ],
        warnings: [
          {
            type: 'data_quality',
            message: 'Produto sem descrição',
            details: 'Recomenda-se adicionar descrição para melhor performance',
            productId: 'PROD-123',
            field: 'description',
            suggestion: 'Adicione uma descrição detalhada do produto',
            timestamp: new Date().toISOString()
          }
        ],
        generationTime: 2847,
        cacheUsed: true,
        metadata: {
          templateUsed: config.templateId,
          warehousesIncluded: config.warehouses,
          categoriesIncluded: config.categories,
          filters: {
            maxProducts: config.maxProducts,
            minGapThreshold: config.minGapThreshold
          },
          generatedAt: new Date().toISOString()
        }
      };
      
      setGenerationResult(result);
      
      toast.success('Planilha gerada com sucesso!', {
        description: `${result.processedProducts} produtos processados em ${(result.generationTime / 1000).toFixed(1)}s`
      });
      
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao gerar planilha';
      setError(errorMessage);
      toast.error('Erro ao gerar planilha', {
        description: errorMessage
      });
      return null;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Carregar analytics
  const loadAnalytics = useCallback(async (dateRange: string = '7d') => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Simular carregamento de analytics
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const mockAnalytics: AnalyticsData = {
        overview: {
          totalSpreadsheets: 1247,
          totalDownloads: 3891,
          totalProducts: 125000,
          totalUsers: 89,
          growthRate: 12.5,
          avgGenerationTime: 2340,
          successRate: 97.8,
          cacheHitRate: 84.2
        },
        usage: {
          dailyGenerations: 45,
          weeklyGenerations: 312,
          monthlyGenerations: 1247,
          peakHours: [
            { hour: 9, count: 15 },
            { hour: 10, count: 23 },
            { hour: 11, count: 18 },
            { hour: 14, count: 21 },
            { hour: 15, count: 19 },
            { hour: 16, count: 12 }
          ],
          topTemplates: [
            { name: 'Mercado Livre - Padrão', usage: 245, percentage: 65 },
            { name: 'Shopee - Padrão', usage: 89, percentage: 23 },
            { name: 'Amazon - Custom', usage: 45, percentage: 12 }
          ],
          userActivity: []
        },
        performance: {
          avgResponseTime: 2340,
          p95ResponseTime: 4200,
          errorRate: 2.2,
          throughput: 45,
          cachePerformance: {
            hitRate: 84.2,
            missRate: 15.8,
            avgRetrievalTime: 120
          },
          systemLoad: {
            cpu: 45,
            memory: 67,
            disk: 23
          }
        },
        templates: [
          {
            id: 'ml-default',
            name: 'Mercado Livre - Padrão',
            usageCount: 245,
            successRate: 98.2,
            avgGenerationTime: 2100,
            errorCount: 4,
            lastUsed: '2024-01-15T10:30:00Z',
            trend: 'up'
          },
          {
            id: 'shopee-default',
            name: 'Shopee - Padrão',
            usageCount: 89,
            successRate: 96.5,
            avgGenerationTime: 2800,
            errorCount: 3,
            lastUsed: '2024-01-15T09:15:00Z',
            trend: 'stable'
          }
        ],
        trends: [],
        errors: {
          totalErrors: 27,
          errorRate: 2.2,
          topErrors: [
            {
              type: 'Validation Error',
              count: 15,
              percentage: 55.6,
              lastOccurrence: '2024-01-15T11:45:00Z'
            },
            {
              type: 'Template Error',
              count: 8,
              percentage: 29.6,
              lastOccurrence: '2024-01-15T10:20:00Z'
            },
            {
              type: 'System Error',
              count: 4,
              percentage: 14.8,
              lastOccurrence: '2024-01-15T08:30:00Z'
            }
          ],
          errorsByTemplate: [
            {
              templateName: 'Mercado Livre - Padrão',
              errorCount: 4,
              errorRate: 1.6
            },
            {
              templateName: 'Shopee - Padrão',
              errorCount: 3,
              errorRate: 3.4
            }
          ]
        },
        predictions: {
          nextWeekGenerations: 380,
          peakLoadPrediction: {
            date: '2024-01-22',
            expectedLoad: 95,
            confidence: 87
          },
          resourceRecommendations: [
            {
              type: 'memory',
              recommendation: 'Considere aumentar a memória RAM para 16GB',
              priority: 'medium'
            },
            {
              type: 'cpu',
              recommendation: 'CPU está operando dentro dos limites normais',
              priority: 'low'
            }
          ]
        }
      };
      
      setAnalyticsData(mockAnalytics);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao carregar analytics';
      setError(errorMessage);
      toast.error('Erro ao carregar analytics', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Criar template
  const createTemplate = useCallback(async (template: Omit<SpreadsheetTemplate, 'id' | 'createdAt' | 'updatedAt' | 'usageCount'>) => {
    try {
      setIsLoading(true);
      
      const newTemplate: SpreadsheetTemplate = {
        ...template,
        id: `template_${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        usageCount: 0
      };
      
      setTemplates(prev => [...prev, newTemplate]);
      
      toast.success('Template criado com sucesso!');
      
      return newTemplate;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao criar template';
      setError(errorMessage);
      toast.error('Erro ao criar template', {
        description: errorMessage
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Atualizar template
  const updateTemplate = useCallback(async (id: string, updates: Partial<SpreadsheetTemplate>) => {
    try {
      setIsLoading(true);
      
      setTemplates(prev => prev.map(template => 
        template.id === id 
          ? { ...template, ...updates, updatedAt: new Date().toISOString() }
          : template
      ));
      
      // Atualizar template selecionado se for o mesmo
      if (selectedTemplate?.id === id) {
        setSelectedTemplate(prev => prev ? { ...prev, ...updates, updatedAt: new Date().toISOString() } : null);
      }
      
      toast.success('Template atualizado com sucesso!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao atualizar template';
      setError(errorMessage);
      toast.error('Erro ao atualizar template', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTemplate]);

  // Deletar template
  const deleteTemplate = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      
      setTemplates(prev => prev.filter(template => template.id !== id));
      
      // Limpar template selecionado se for o mesmo
      if (selectedTemplate?.id === id) {
        setSelectedTemplate(null);
      }
      
      toast.success('Template deletado com sucesso!');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Erro ao deletar template';
      setError(errorMessage);
      toast.error('Erro ao deletar template', {
        description: errorMessage
      });
    } finally {
      setIsLoading(false);
    }
  }, [selectedTemplate]);

  // Cancelar operações
  const cancelOperations = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    if (previewTimeoutRef.current) {
      clearTimeout(previewTimeoutRef.current);
    }
    
    setIsLoading(false);
    setIsGenerating(false);
  }, []);

  // Limpar estados
  const clearStates = useCallback(() => {
    setPreviewData(null);
    setGenerationResult(null);
    setError(null);
  }, []);

  // Efeito para carregar templates na inicialização
  useEffect(() => {
    loadTemplates();
  }, [loadTemplates]);

  // Cleanup
  useEffect(() => {
    return () => {
      cancelOperations();
    };
  }, [cancelOperations]);

  return {
    // Estados
    templates,
    selectedTemplate,
    isLoading,
    isGenerating,
    previewData,
    generationResult,
    analyticsData,
    error,
    
    // Ações
    setSelectedTemplate,
    loadTemplates,
    generatePreview,
    generateSpreadsheet,
    loadAnalytics,
    createTemplate,
    updateTemplate,
    deleteTemplate,
    cancelOperations,
    clearStates
  };
};

export default useSpreadsheets;