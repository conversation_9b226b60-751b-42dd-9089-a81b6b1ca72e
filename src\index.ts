// Carregar variáveis de ambiente PRIMEIRO
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Especificar caminho absoluto do .env
const envPath = path.resolve(__dirname, '../.env');
dotenv.config({ path: envPath });

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { PrismaClient } from '@prisma/client';

import { globalErrorHandler } from '@/middleware/errorHandler';
import { requestLogger } from '@/middleware/logger';
import { notFoundHandler } from '@/middleware/notFound';
import { healthCheck } from '@/middleware/healthCheck';
import { connectDatabase } from '@/config/database';
import { connectRedis } from '@/config/redis';
import { logger } from '@/utils/logger';
import { 
  unhandledErrorHandler, 
  initializeErrorMonitoring 
} from '@/middleware/errorHandler';
import rateLimiter, { rateLimiters } from './middleware/rateLimiter';
import { swaggerSpec, swaggerUi } from '@/config/swagger';
import { StockAlertSchedulerService } from '@/services/stockAlertSchedulerService';
import FileSystemInitializer from '@/services/fileSystemInitializer';

// Importar rotas
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import mercadoLivreRoutes from '@/routes/mercadolivre'; // Reativado
import stockRoutes from '@/routes/stock'; // Reativado
import stockAlertsRoutes from '@/routes/stockAlerts'; // Novo
import reportsRoutes from '@/routes/reports';
import spreadsheetsRoutes from '@/routes/spreadsheets'; // Reativado
import filesRoutes from '@/routes/files'; // Novo - Sistema de arquivos
import monitoringRoutes from '@/routes/monitoring';

const app = express();
const server = createServer(app);
const prisma = new PrismaClient();
const fileSystemInitializer = new FileSystemInitializer();

// Configurações básicas
const PORT = process.env.PORT || 3000;
const NODE_ENV = process.env.NODE_ENV || 'development';

// Configurar monitoramento de erros globais
unhandledErrorHandler();

// Inicializar serviço de monitoramento de erros
initializeErrorMonitoring(prisma);

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutos
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  message: 'Muitas requisições deste IP, tente novamente em alguns minutos.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Middlewares de segurança
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", process.env.CORS_ORIGIN || 'http://localhost:5173'],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS mais liberado para ambiente de desenvolvimento/teste
app.use(cors(
  NODE_ENV === 'development' || NODE_ENV === 'test'
    ? {
        origin: true, // Aceita qualquer origem
        credentials: true,
        methods: '*',
        allowedHeaders: '*'
      }
    : {
        origin: process.env.CORS_ORIGIN?.split(',') || ['http://localhost:3000', 'http://localhost:5173'],
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Tenant-ID']
      }
));

app.use(compression());
app.use(limiter);

// Middlewares de parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware de logging
app.use(requestLogger);

// Rota raiz - informações da API
app.get('/', (req, res) => {
  res.json({
    name: 'Magnow API',
    version: '1.0.0',
    description: 'Sistema SaaS de controle de estoque integrado ao Mercado Livre',
    status: 'running',
    timestamp: new Date().toISOString(),
    environment: NODE_ENV,
    endpoints: {
      health: '/health',
      docs: '/api-docs',
      auth: '/api/auth',
      users: '/api/users',
      files: '/api/files',
      reports: '/api/reports',
      monitoring: '/api/monitoring'
    },
    support: {
      documentation: '/api-docs',
      health_check: '/health'
    }
  });
});

// Health check
app.use('/health', healthCheck);

// Documentação da API com Swagger
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Magnow API Documentation',
  customfavIcon: '/favicon.ico'
}));

// Endpoint para obter a spec em JSON
app.get('/api-docs.json', (req, res) => {
  res.setHeader('Content-Type', 'application/json');
  res.send(swaggerSpec);
});

// Rotas da API
app.use('/api/auth', rateLimiters.auth, authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/mercadolivre', rateLimiters.heavy, mercadoLivreRoutes); // Reativado
app.use('/api/stock', rateLimiters.normal, stockRoutes); // Reativado
app.use('/api/stock-alerts', rateLimiters.normal, stockAlertsRoutes); // Novo
app.use('/api/files', rateLimiters.normal, filesRoutes); // Novo - Sistema de arquivos
app.use('/api/reports', reportsRoutes);
app.use('/api/spreadsheets', rateLimiters.heavy, spreadsheetsRoutes); // Reativado
app.use('/api/monitoring', rateLimiters.light, monitoringRoutes);

// Middleware para rotas não encontradas
app.use(notFoundHandler);

// Middleware global de tratamento de erros
app.use(globalErrorHandler);

// Função para inicializar o servidor
async function startServer() {
  try {
    // Conectar ao banco de dados
    await connectDatabase();
    logger.info(' Conexão com PostgreSQL estabelecida');

    // Conectar ao Redis (opcional)
    try {
      const redisClient = await connectRedis();
      if (redisClient) {
        logger.info('✅ Conexão com Redis estabelecida');
      } else {
        logger.info('⚠️  Funcionando sem Redis - usando cache em memória');
      }
    } catch (error) {
      logger.warn('⚠️  Redis não disponível - usando cache em memória como fallback');
    }

    // Inicializar sistema de arquivos
    try {
      await fileSystemInitializer.initialize();
      logger.info('✅ Sistema de arquivos inicializado');
    } catch (error) {
      logger.error('❌ Erro ao inicializar sistema de arquivos', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Não falhar o servidor por causa do sistema de arquivos
    }

    // Inicializar serviço de alertas de estoque
    try {
      const stockAlertScheduler = new StockAlertSchedulerService(prisma);
      
      // Buscar todos os tenants ativos e configurar jobs de alertas
      const activeTenants = await prisma.tenant.findMany({
        where: { 
          isActive: true,
          deletedAt: null 
        },
        select: { id: true, name: true }
      });

      logger.info(`🔔 Configurando jobs de alertas para ${activeTenants.length} tenants`);
      
      for (const tenant of activeTenants) {
        try {
          await stockAlertScheduler.setupTenantAlertJob(tenant.id);
          logger.info(`✅ Job de alertas configurado para tenant: ${tenant.name}`);
        } catch (error: any) {
          logger.error(`❌ Erro ao configurar job para tenant ${tenant.name}:`, error.message);
        }
      }
      
      logger.info('🔔 Sistema de alertas de estoque inicializado');
    } catch (error: any) {
      logger.error('❌ Erro ao inicializar sistema de alertas:', error.message);
    }

    // Iniciar servidor
    server.listen(PORT, () => {
      logger.info(`🚀 Servidor Magnow iniciado na porta ${PORT}`, {
        port: PORT,
        environment: NODE_ENV,
        timestamp: new Date().toISOString()
      });
      logger.info(` Ambiente: ${NODE_ENV}`);
      logger.info(` Rate limit: ${process.env.RATE_LIMIT_MAX_REQUESTS || 100} req/15min`);
    });

  } catch (error) {
    logger.error(' Erro ao inicializar servidor:', error);
    process.exit(1);
  }
}

// Tratamento de sinais do sistema
const gracefulShutdown = (signal: string) => {
  logger.info(`📥 Recebido sinal ${signal}. Iniciando shutdown graceful...`);
  
  server.close(async () => {
    logger.info('🔌 Servidor HTTP fechado');
    
    try {
      await prisma.$disconnect();
      logger.info('💾 Conexão com banco de dados fechada');
      
      logger.info('✅ Shutdown concluído com sucesso');
      process.exit(0);
    } catch (error) {
      logger.error('❌ Erro durante shutdown:', error);
      process.exit(1);
    }
  });
  
  // Força o shutdown após 10 segundos
  setTimeout(() => {
    logger.error('⏰ Forçando shutdown após timeout');
    process.exit(1);
  }, 10000);
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Tratamento de exceções não capturadas
process.on('uncaughtException', (error) => {
  logger.error('Exceção não capturada:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Promise rejeitada não tratada:', reason);
  process.exit(1);
});

// Inicializar servidor
startServer();

export default app;