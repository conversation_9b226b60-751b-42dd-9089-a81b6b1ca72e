import { render, screen } from '@testing-library/react';
import ReportsSales from '../../pages/ReportsSales';
import ReportsStock from '../../pages/ReportsStock';

describe('Reports pages', () => {
  it('renders sales report page', () => {
    render(<ReportsSales />);
    expect(screen.getByText(/Relatório de Vendas/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Filtrar/i })).toBeInTheDocument();
  });

  it('renders stock report page', () => {
    render(<ReportsStock />);
    expect(screen.getByText(/Relatório de Estoque/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Filtrar/i })).toBeInTheDocument();
  });
}); 