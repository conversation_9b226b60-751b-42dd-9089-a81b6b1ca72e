@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 9% 15%; /* Cor mais escura para melhor contraste */
    --card: 0 0% 100%;
    --card-foreground: 220 9% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 9% 15%;
    --primary: 221 83% 53%; /* Azul vibrante */
    --primary-foreground: 0 0% 98%;
    --secondary: 220 14% 96%;
    --secondary-foreground: 220 9% 15%;
    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%; /* Melhor contraste */
    --accent: 220 14% 96%;
    --accent-foreground: 220 9% 15%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 221 83% 53%;
    --radius: 0.5rem;
  }
 
  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;
    --primary: 210 40% 98%;
    --primary-foreground: 222 84% 5%;
    --secondary: 215 28% 17%;
    --secondary-foreground: 213 31% 91%;
    --muted: 215 28% 17%;
    --muted-foreground: 217 11% 65%; /* Melhor contraste no modo escuro */
    --accent: 215 28% 17%;
    --accent-foreground: 213 31% 91%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 213 31% 91%;
    --border: 215 28% 17%;
    --input: 215 28% 17%;
    --ring: 216 34% 17%;
  }

  /* Reset básico */
  * {
    box-sizing: border-box;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* Melhorar renderização de fontes */
  * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

@layer components {
  /* === COMPONENTES USANDO DESIGN TOKENS === */
  
  /* Button Base */
  .btn {
    @apply inline-flex items-center justify-center rounded-md font-medium transition-colors;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2;
    @apply disabled:opacity-50 disabled:pointer-events-none;
    font-weight: var(--button-font-weight);
    transition: var(--button-transition);
  }
  
  /* Button Variants */
  .btn-primary {
    @apply btn bg-primary text-primary-foreground hover:bg-primary/90;
    @apply shadow-sm;
  }
  
  .btn-secondary {
    @apply btn bg-secondary text-secondary-foreground hover:bg-secondary/80;
    @apply border border-border;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
    @apply shadow-sm;
  }
  
  .btn-warning {
    @apply btn bg-warning-500 text-white hover:bg-warning-600 active:bg-warning-700;
    @apply shadow-sm;
  }
  
  .btn-danger {
    @apply btn bg-destructive text-destructive-foreground hover:bg-destructive/90;
    @apply shadow-sm;
  }
  
  .btn-outline {
    @apply btn border border-input bg-background hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-ghost {
    @apply btn hover:bg-accent hover:text-accent-foreground;
  }
  
  .btn-link {
    @apply btn text-primary underline-offset-4 hover:underline;
  }
  
  /* Button Sizes */
  .btn-xs {
    @apply px-2 py-1 text-xs h-6;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-sm h-8;
  }
  
  .btn-md {
    @apply px-4 py-2 text-sm h-10;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base h-12;
  }
  
  .btn-xl {
    @apply px-8 py-4 text-lg h-14;
  }
  
  /* Card */
  .card {
    @apply rounded-lg border;
    background-color: var(--card-background);
    border-color: var(--card-border-color);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    color: hsl(var(--card-foreground));
  }
  
  .card-elevated {
    @apply elevation-lg;
  }
  
  .card-flat {
    @apply shadow-none border-0;
  }
  
  /* Input */
  .input {
    @apply flex h-10 w-full rounded-md border px-3 py-2 text-sm;
    @apply placeholder:text-muted-foreground;
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2;
    @apply disabled:cursor-not-allowed disabled:opacity-50;
    background-color: var(--input-background);
    border-color: var(--input-border-color);
    border-radius: var(--input-border-radius);
    color: hsl(var(--foreground));
  }
  
  .input:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-ring);
  }
  
  .input-sm {
    @apply h-8 px-2 py-1 text-sm;
  }
  
  .input-lg {
    @apply h-12 px-4 py-3 text-base;
  }
  
  .input-error {
    @apply border-destructive text-destructive;
  }
  
  .input-error:focus {
    @apply ring-destructive;
  }
  
  /* Badge */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge border-transparent bg-primary text-primary-foreground hover:bg-primary/80;
  }
  
  .badge-secondary {
    @apply badge border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80;
  }
  
  .badge-info {
    @apply badge bg-info-100 text-info-800;
  }
  
  .badge-gray {
    @apply badge bg-muted text-muted-foreground;
  }
  
  /* Alert */
  .alert {
    @apply relative w-full rounded-lg border p-4;
  }
  
  .alert-primary {
    @apply alert text-primary-foreground border-primary bg-primary;
  }
  
  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-danger {
    @apply alert text-destructive-foreground border-destructive bg-destructive;
  }
  
  .alert-info {
    @apply alert bg-info-50 border-info-200 text-info-800;
  }
  
  /* Surface */
  .surface-base {
    @apply surface-base;
  }
  
  .surface-elevated {
    @apply surface-elevated;
  }
  
  .surface-elevated-2 {
    @apply surface-elevated-2;
  }
}

@layer utilities {
  /* === UTILITÁRIOS CUSTOMIZADOS === */
  
  .text-balance {
    text-wrap: balance;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Gradientes */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary to-primary/80;
  }
  
  .gradient-success {
    @apply bg-gradient-to-r from-success-600 to-success-700;
  }
  
  .gradient-warning {
    @apply bg-gradient-to-r from-warning-500 to-warning-600;
  }
  
  .gradient-danger {
    @apply bg-gradient-to-r from-destructive to-destructive/80;
  }
  
  /* Animações */
  .animate-fade-in {
    animation: fadeIn var(--animation-duration-normal) var(--animation-ease);
  }
  
  .animate-slide-up {
    animation: slideUp var(--animation-duration-normal) var(--animation-ease);
  }
  
  .animate-slide-down {
    animation: slideDown var(--animation-duration-normal) var(--animation-ease);
  }
  
  .animate-scale-in {
    animation: scaleIn var(--animation-duration-normal) var(--animation-ease);
  }
  
  /* Truncate com tooltip */
  .truncate-tooltip {
    @apply truncate cursor-help;
  }
  
  /* Estados interativos */
  .interactive {
    @apply transition-all duration-150 ease-in-out;
  }
  
  .interactive:hover {
    @apply scale-105;
  }
  
  .interactive:active {
    @apply scale-95;
  }
}

/* === ANIMAÇÕES === */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* === PRINT STYLES === */
@media print {
  * {
    @apply text-black bg-white;
  }
  
  .no-print {
    display: none !important;
  }
}
