 
import type { Meta, StoryObj } from '@storybook/react';
import { Toast, ToastProvider, useToast } from './Toast';
import { Button } from './Button';
import { Stack } from './Stack';

const meta = {
  title: 'Components/Toast',
  component: Toast,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['default', 'info', 'success', 'warning', 'danger'],
    },
    duration: { control: 'number' },
    onOpenChange: { action: 'open/close' },
  },
  decorators: [
    (Story) => (
      <ToastProvider>
        <Story />
      </ToastProvider>
    ),
  ],
} satisfies Meta<typeof Toast>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  render: () => {
    const { toast } = useToast();
    return (
      <Button
        onClick={() =>
          toast({
            title: 'Notificação Padrão',
            description: 'Esta é uma mensagem de toast padrão.',
          })
        }
      >
        Mostrar Toast Padrão
      </Button>
    );
  },
};

export const Info: Story = {
  render: () => {
    const { toast } = useToast();
    return (
      <Button
        onClick={() =>
          toast({
            variant: 'info',
            title: 'Informação',
            description: 'Detalhes adicionais sobre algo.',
          })
        }
      >
        Mostrar Toast Info
      </Button>
    );
  },
};

export const Success: Story = {
  render: () => {
    const { toast } = useToast();
    return (
      <Button
        onClick={() =>
          toast({
            variant: 'success',
            title: 'Sucesso!',
            description: 'Sua operação foi concluída com êxito.',
          })
        }
      >
        Mostrar Toast Sucesso
      </Button>
    );
  },
};

export const Warning: Story = {
  render: () => {
    const { toast } = useToast();
    return (
      <Button
        onClick={() =>
          toast({
            variant: 'warning',
            title: 'Atenção!',
            description: 'Algo precisa da sua atenção.',
          })
        }
      >
        Mostrar Toast Alerta
      </Button>
    );
  },
};

export const Danger: Story = {
  render: () => {
    const { toast } = useToast();
    return (
      <Button
        onClick={() =>
          toast({
            variant: 'danger',
            title: 'Erro',
            description: 'Ocorreu um erro ao processar sua solicitação.',
          })
        }
      >
        Mostrar Toast Erro
      </Button>
    );
  },
};

export const CustomAction: Story = {
  render: () => {
    const { toast } = useToast();
    return (
      <Button
        onClick={() =>
          toast({
            title: 'Item Adicionado',
            description: 'Um novo item foi adicionado ao seu carrinho.',
            action: (
              <Button variant="link" size="sm" onClick={() => console.log('Ver Carrinho')}>Ver Carrinho</Button>
            ),
          })
        }
      >
        Mostrar Toast com Ação
      </Button>
    );
  },
};

export const PersistentToast: Story = {
  render: () => {
    const { toast } = useToast();
    return (
      <Button
        onClick={() =>
          toast({
            title: 'Toast Persistente',
            description: 'Este toast não desaparecerá automaticamente.',
            duration: Infinity, // Or a very large number
          })
        }
      >
        Mostrar Toast Persistente
      </Button>
    );
  },
}; 
