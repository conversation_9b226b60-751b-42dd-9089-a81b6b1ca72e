/**
 * Serviço para geração de planilhas Excel e CSV
 * para o Mercado Envios Full
 */

import * as XLSX from 'xlsx';
import * as fs from 'fs';
import * as path from 'path';
import { createObjectCsvWriter } from 'csv-writer';
import { 
    ShippingProduct, 
    SpreadsheetGenerationResult, 
    SpreadsheetConfig, 
    ShippingSpreadsheetRow,
    SpreadsheetError,
    SpreadsheetWarning,
    GenerateSpreadsheetParams
} from '../types/stock';

class SpreadsheetGeneratorService {
    private readonly outputDir: string;

    constructor(outputDir: string = './output') {
        this.outputDir = outputDir;
        this.ensureOutputDirectory();
    }

    private ensureOutputDirectory(): void {
        if (!fs.existsSync(this.outputDir)) {
            fs.mkdirSync(this.outputDir, { recursive: true });
        }
    }

    /**
     * Gera planilha Excel para Mercado Envios Full
     */
    async generateExcelSpreadsheet(
        products: ShippingProduct[],
        config: SpreadsheetConfig,
        tenantId: string,
        generatedBy: string
    ): Promise<SpreadsheetGenerationResult> {
        try {
            const { data, errors, warnings } = this.processProducts(products, config);
            
            const workbook = XLSX.utils.book_new();
            const worksheet = XLSX.utils.json_to_sheet(data);
            
            // Configurar largura das colunas
            const columnWidths = [
                { wch: 15 }, // SKU
                { wch: 30 }, // Título
                { wch: 10 }, // Quantidade
                { wch: 12 }, // Preço
                { wch: 15 }, // Categoria
                { wch: 20 }, // Descrição
            ];
            worksheet['!cols'] = columnWidths;
            
            XLSX.utils.book_append_sheet(workbook, worksheet, 'Produtos');
            
            const fileName = `mercado_envios_${Date.now()}.xlsx`;
            const filePath = path.join(this.outputDir, fileName);
            
            XLSX.writeFile(workbook, filePath);
            
            const fileStats = fs.statSync(filePath);
            
            return {
                id: `spreadsheet_${Date.now()}`,
                tenantId,
                generatedAt: new Date(),
                generatedBy,
                fileName,
                filePath,
                fileSize: fileStats.size,
                format: 'xlsx',
                totalProducts: products.length,
                totalQuantity: products.reduce((sum, p) => sum + p.quantityToSend, 0),
                totalValue: products.reduce((sum, p) => sum + (p.unitPrice * p.quantityToSend), 0),
                warehousesCount: new Set(products.map(p => p.warehouseId)).size,
                products,
                hasErrors: errors.length > 0,
                errors,
                warnings,
                status: 'generated',
                config
            };
        } catch (error) {
            return {
                id: `spreadsheet_${Date.now()}`,
                tenantId,
                generatedAt: new Date(),
                generatedBy,
                fileName: '',
                filePath: '',
                fileSize: 0,
                format: 'xlsx',
                totalProducts: products.length,
                totalQuantity: 0,
                totalValue: 0,
                warehousesCount: 0,
                products: [],
                hasErrors: true,
                errors: [{
                    type: 'system',
                    severity: 'error',
                    code: 'GENERATION_ERROR',
                    message: `Erro ao gerar planilha: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
                }],
                warnings: [],
                status: 'generated',
                config
            };
        }
    }

    /**
     * Gera arquivo CSV para Mercado Envios Full
     */
    async generateCsvSpreadsheet(
        products: ShippingProduct[],
        config: SpreadsheetConfig,
        tenantId: string,
        generatedBy: string
    ): Promise<SpreadsheetGenerationResult> {
        try {
            const { data, errors, warnings } = this.processProducts(products, config);
            
            const fileName = `mercado_envios_${Date.now()}.csv`;
            const filePath = path.join(this.outputDir, fileName);
            
            const csvWriter = createObjectCsvWriter({
                path: filePath,
                header: [
                    { id: 'sku', title: 'SKU' },
                    { id: 'title', title: 'Título' },
                    { id: 'quantity', title: 'Quantidade' },
                    { id: 'unitPrice', title: 'Preço' },
                    { id: 'category', title: 'Categoria' },
                    { id: 'warehouseCode', title: 'Armazém' }
                ]
            });
            
            await csvWriter.writeRecords(data);
            
            const fileStats = fs.statSync(filePath);
            
            return {
                id: `spreadsheet_${Date.now()}`,
                tenantId,
                generatedAt: new Date(),
                generatedBy,
                fileName,
                filePath,
                fileSize: fileStats.size,
                format: 'csv',
                totalProducts: products.length,
                totalQuantity: products.reduce((sum, p) => sum + p.quantityToSend, 0),
                totalValue: products.reduce((sum, p) => sum + (p.unitPrice * p.quantityToSend), 0),
                warehousesCount: new Set(products.map(p => p.warehouseId)).size,
                products,
                hasErrors: errors.length > 0,
                errors,
                warnings,
                status: 'generated',
                config
            };
        } catch (error) {
            return {
                id: `spreadsheet_${Date.now()}`,
                tenantId,
                generatedAt: new Date(),
                generatedBy,
                fileName: '',
                filePath: '',
                fileSize: 0,
                format: 'csv',
                totalProducts: products.length,
                totalQuantity: 0,
                totalValue: 0,
                warehousesCount: 0,
                products: [],
                hasErrors: true,
                errors: [{
                    type: 'system',
                    severity: 'error',
                    code: 'GENERATION_ERROR',
                    message: `Erro ao gerar CSV: ${error instanceof Error ? error.message : 'Erro desconhecido'}`
                }],
                warnings: [],
                status: 'generated',
                config
            };
        }
    }

    /**
     * Processa produtos e valida dados
     */
    private processProducts(
        products: ShippingProduct[],
        config: SpreadsheetConfig
    ): {
        data: ShippingSpreadsheetRow[],
        errors: SpreadsheetError[],
        warnings: SpreadsheetWarning[]
    } {
        const data: ShippingSpreadsheetRow[] = [];
        const errors: SpreadsheetError[] = [];
        const warnings: SpreadsheetWarning[] = [];
        
        products.forEach((product, index) => {
            const row = index + 1;
            
            // Validações obrigatórias
            if (!product.sku) {
                errors.push({
                    type: 'validation',
                    severity: 'error',
                    code: 'MISSING_SKU',
                    message: 'SKU é obrigatório',
                    affectedSku: product.sku,
                    affectedField: 'sku'
                });
                return;
            }
            
            if (!product.title) {
                errors.push({
                    type: 'validation',
                    severity: 'error',
                    code: 'MISSING_TITLE',
                    message: 'Título é obrigatório',
                    affectedSku: product.sku,
                    affectedField: 'title'
                });
                return;
            }
            
            if (product.quantityToSend === undefined || product.quantityToSend < 0) {
                errors.push({
                    type: 'validation',
                    severity: 'error',
                    code: 'INVALID_QUANTITY',
                    message: 'Quantidade deve ser um número não negativo',
                    affectedSku: product.sku,
                    affectedField: 'quantityToSend'
                });
                return;
            }
            
            if (!product.unitPrice || product.unitPrice <= 0) {
                errors.push({
                    type: 'validation',
                    severity: 'error',
                    code: 'INVALID_PRICE',
                    message: 'Preço deve ser maior que zero',
                    affectedSku: product.sku,
                    affectedField: 'unitPrice'
                });
                return;
            }
            
            // Validações de aviso
            if (product.quantityToSend === 0) {
                warnings.push({
                    type: 'missing_data',
                    message: 'Produto com quantidade zerada',
                    affectedSku: product.sku,
                    affectedField: 'quantityToSend'
                });
            }
            
            if (product.title.length > 60) {
                warnings.push({
                    type: 'estimated_value',
                    message: 'Título muito longo (máximo recomendado: 60 caracteres)',
                    affectedSku: product.sku,
                    affectedField: 'title'
                });
            }
            
            if (!product.barcode) {
                warnings.push({
                    type: 'missing_data',
                    message: 'Código de barras não informado',
                    affectedSku: product.sku,
                    affectedField: 'barcode'
                });
            }
            
            // Adicionar produto processado
            data.push({
                sku: product.sku,
                title: product.title.substring(0, 60), // Truncar se necessário
                quantity: product.quantityToSend,
                barcode: product.barcode,
                height: product.height,
                width: product.width,
                depth: product.depth,
                weight: product.weight,
                unitPrice: product.unitPrice,
                category: product.category || '',
                brand: product.brand || '',
                warehouseCode: product.warehouseId
            });
        });
        
        return { data, errors, warnings };
    }

    /**
     * Gera planilha baseada nos parâmetros fornecidos
     */
    async generateSpreadsheet(
        params: GenerateSpreadsheetParams,
        products: ShippingProduct[],
        config: SpreadsheetConfig
    ): Promise<SpreadsheetGenerationResult> {
        const { format = 'xlsx', tenantId, generatedBy } = params;
        
        if (format === 'csv') {
            return this.generateCsvSpreadsheet(products, config, tenantId, generatedBy);
        } else {
            return this.generateExcelSpreadsheet(products, config, tenantId, generatedBy);
        }
    }

    /**
     * Lista arquivos gerados
     */
    listGeneratedFiles(): string[] {
        if (!fs.existsSync(this.outputDir)) {
            return [];
        }
        
        return fs.readdirSync(this.outputDir)
            .filter(file => file.endsWith('.xlsx') || file.endsWith('.csv'))
            .map(file => path.join(this.outputDir, file));
    }

    /**
     * Remove arquivo gerado
     */
    deleteFile(filePath: string): boolean {
        try {
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    /**
     * Limpa arquivos antigos (mais de 24 horas)
     */
    cleanupOldFiles(): number {
        const files = this.listGeneratedFiles();
        const now = Date.now();
        const maxAge = 24 * 60 * 60 * 1000; // 24 horas
        let deletedCount = 0;
        
        files.forEach(filePath => {
            try {
                const stats = fs.statSync(filePath);
                if (now - stats.mtime.getTime() > maxAge) {
                    fs.unlinkSync(filePath);
                    deletedCount++;
                }
            } catch (error) {
                // Ignorar erros de arquivo
            }
        });
        
        return deletedCount;
    }
}

export default SpreadsheetGeneratorService;
export { SpreadsheetGeneratorService };
export const spreadsheetGeneratorService = new SpreadsheetGeneratorService();