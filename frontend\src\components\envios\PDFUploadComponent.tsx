/**
 * PDF Upload Component - Upload de Comprovantes de Envio
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import { 
  DocumentIcon, 
  CloudArrowUpIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import FileUploadService from '../../services/fileUploadService';
import { useNotificationStore } from '../../store/notificationStore';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  url: string;
  uploadedAt: Date;
}

interface PDFUploadComponentProps {
  onFilesUploaded?: (files: UploadedFile[]) => void;
  maxFiles?: number;
  allowMultiple?: boolean;
  className?: string;
}

const PDFUploadComponent: React.FC<PDFUploadComponentProps> = ({
  onFilesUploaded,
  maxFiles = 5,
  allowMultiple = true,
  className = ''
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [isDragActive, setIsDragActive] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { addNotification } = useNotificationStore();

  const handleFiles = useCallback(async (files: FileList | null) => {
    if (!files) return;

    const acceptedFiles = Array.from(files).filter(file => file.type === 'application/pdf');
    if (!allowMultiple && acceptedFiles.length > 1) {
      addNotification({
        title: 'Múltiplos arquivos não permitidos',
        message: 'Selecione apenas um arquivo por vez.',
        type: 'warning',
        severity: 'warning',
        duration: 4000,
      });
      return;
    }

    if (uploadedFiles.length + acceptedFiles.length > maxFiles) {
      addNotification({
        title: 'Limite de arquivos excedido',
        message: `Máximo de ${maxFiles} arquivos permitidos.`,
        type: 'warning',
        severity: 'warning',
        duration: 4000,
      });
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const newFiles: UploadedFile[] = [];

      for (let i = 0; i < acceptedFiles.length; i++) {
        const file = acceptedFiles[i];
        
        // Validar arquivo
        const validation = FileUploadService.validateFile(file, 'document');
        if (!validation.valid) {
          addNotification({
            title: `Erro no arquivo: ${file.name}`,
            message: validation.error || 'Arquivo inválido',
            type: 'error',
            severity: 'error',
            duration: 5000,
          });
          continue;
        }

        // Upload com progress
        const result = await FileUploadService.uploadWithProgress(
          file,
          'document',
          (progress) => {
            const totalProgress = ((i / acceptedFiles.length) * 100) + (progress / acceptedFiles.length);
            setUploadProgress(Math.round(totalProgress));
          },
          {
            isPublic: false,
            expiresIn: 30 * 24 * 60 * 60 // 30 dias
          }
        );

        if (result.success && result.data) {
          newFiles.push({
            id: result.data.file.id,
            name: result.data.file.originalName,
            size: result.data.file.fileSize,
            url: result.data.url,
            uploadedAt: new Date(result.data.file.createdAt)
          });

          addNotification({
            title: 'Upload concluído',
            message: `${file.name} foi enviado com sucesso.`,
            type: 'success',
            severity: 'success',
            duration: 3000,
          });
        } else {
          addNotification({
            title: `Erro no upload: ${file.name}`,
            message: result.error || 'Erro desconhecido',
            type: 'error',
            severity: 'error',
            duration: 5000,
          });
        }
      }

      if (newFiles.length > 0) {
        const updatedFiles = [...uploadedFiles, ...newFiles];
        setUploadedFiles(updatedFiles);
        onFilesUploaded?.(updatedFiles);
      }

    } catch (error) {
      console.error('Erro no upload:', error);
      addNotification({
        title: 'Erro no upload',
        message: 'Ocorreu um erro inesperado durante o upload.',
        type: 'error',
        severity: 'error',
        duration: 5000,
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [uploadedFiles, maxFiles, allowMultiple, onFilesUploaded, addNotification]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragActive(false);
    handleFiles(e.dataTransfer.files);
  }, [handleFiles]);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  }, [handleFiles]);

  const handleClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const removeFile = async (fileId: string) => {
    try {
      const result = await FileUploadService.deleteFile(fileId);
      if (result.success) {
        const updatedFiles = uploadedFiles.filter(f => f.id !== fileId);
        setUploadedFiles(updatedFiles);
        onFilesUploaded?.(updatedFiles);

        addNotification({
          title: 'Arquivo removido',
          message: 'O arquivo foi removido com sucesso.',
          type: 'success',
          severity: 'success',
          duration: 3000,
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Erro ao remover arquivo:', error);
      addNotification({
        title: 'Erro ao remover',
        message: 'Não foi possível remover o arquivo.',
        type: 'error',
        severity: 'error',
        duration: 4000,
      });
    }
  };

  const downloadFile = (file: UploadedFile) => {
    window.open(file.url, '_blank');
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DocumentIcon className="w-5 h-5" />
          Upload de Comprovantes PDF
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Drop Zone */}
        <div
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
            ${isDragActive
              ? 'border-primary bg-primary/5'
              : 'border-gray-300 hover:border-primary hover:bg-gray-50'
            }
            ${uploading ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".pdf"
            multiple={allowMultiple}
            onChange={handleFileInputChange}
            className="hidden"
          />
          
          <CloudArrowUpIcon className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          
          {isDragActive ? (
            <p className="text-primary font-medium">
              Solte os arquivos PDF aqui...
            </p>
          ) : (
            <div>
              <p className="text-gray-600 mb-2">
                Arraste arquivos PDF aqui ou clique para selecionar
              </p>
              <p className="text-sm text-gray-500">
                Máximo {maxFiles} arquivo{maxFiles > 1 ? 's' : ''} • Até 10MB cada
              </p>
            </div>
          )}
        </div>

        {/* Upload Progress */}
        {uploading && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Enviando arquivos...</span>
              <span>{uploadProgress}%</span>
            </div>
            <Progress value={uploadProgress} className="w-full" />
          </div>
        )}

        {/* Uploaded Files List */}
        {uploadedFiles.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium text-sm text-gray-700">
              Arquivos Enviados ({uploadedFiles.length})
            </h4>
            
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <DocumentIcon className="w-8 h-8 text-red-500" />
                  <div>
                    <p className="font-medium text-sm">{file.name}</p>
                    <p className="text-xs text-gray-500">
                      {FileUploadService.formatFileSize(file.size)} • 
                      Enviado em {file.uploadedAt.toLocaleDateString('pt-BR')}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Enviado
                  </Badge>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => downloadFile(file)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    Baixar
                  </Button>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(file.id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Info */}
        <div className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
          <ExclamationTriangleIcon className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Dicas importantes:</p>
            <ul className="text-xs space-y-1">
              <li>• Apenas arquivos PDF são aceitos</li>
              <li>• Tamanho máximo de 10MB por arquivo</li>
              <li>• Os arquivos ficam disponíveis por 30 dias</li>
              <li>• Use nomes descritivos para facilitar a identificação</li>
            </ul>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PDFUploadComponent;
