/**
 * Testes Unitários - Cache Service
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import CacheService, { CacheNamespaces, CacheConfigs } from '../../services/cacheService';

// Mock do Redis
const mockRedisClient = {
  get: jest.fn(),
  set: jest.fn(),
  setEx: jest.fn(),
  del: jest.fn(),
  keys: jest.fn(),
  exists: jest.fn(),
  expire: jest.fn(),
  ttl: jest.fn(),
  flushdb: jest.fn(),
  mget: jest.fn(),
  mset: jest.fn(),
  incr: jest.fn(),
  decr: jest.fn()
};

jest.mock('../../config/redis', () => ({
  getRedisClient: () => mockRedisClient,
  connectRedis: jest.fn().mockResolvedValue(mockRedisClient)
}));

describe('CacheService', () => {
  let cacheService: CacheService;

  beforeEach(() => {
    jest.clearAllMocks();
    cacheService = new CacheService();
    
    // Reset metrics
    (cacheService as any).metrics = {
      hits: 0,
      misses: 0,
      operations: 0,
      errors: 0,
      totalExecutionTime: 0
    };
  });

  describe('Configuração e Inicialização', () => {
    it('deve ter configurações de namespace definidas', () => {
      expect(CacheNamespaces).toBeDefined();
      expect(CacheNamespaces.MERCADO_LIVRE).toBe('ml');
      expect(CacheNamespaces.STOCK).toBe('stock');
      expect(CacheNamespaces.USER).toBe('user');
      expect(CacheNamespaces.SESSION).toBe('session');
    });

    it('deve instanciar corretamente', () => {
      expect(cacheService).toBeDefined();
      expect(cacheService).toBeInstanceOf(CacheService);
    });
  });

  describe('Operações Básicas', () => {
    const namespace = 'test';
    const testKey = 'key';
    const testValue = { id: 1, name: 'Test Item' };
    const tenantId = 'tenant-123';

    it('deve armazenar dados com set()', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');

      const result = await cacheService.set(namespace, testKey, testValue, { ttl: 300 }, tenantId);

      expect(mockRedisClient.setEx).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('deve recuperar dados com get()', async () => {
      const serializedValue = JSON.stringify(testValue);
      mockRedisClient.get.mockResolvedValue(serializedValue);

      const result = await cacheService.get(namespace, testKey, tenantId);

      expect(mockRedisClient.get).toHaveBeenCalled();
      expect(result).toEqual(testValue);
    });

    it('deve retornar null quando chave não existe', async () => {
      mockRedisClient.get.mockResolvedValue(null);

      const result = await cacheService.get(namespace, 'non-existent-key', tenantId);

      expect(result).toBeNull();
    });

    it('deve deletar chave com delete()', async () => {
      mockRedisClient.del.mockResolvedValue(1);

      const result = await cacheService.delete(namespace, testKey, tenantId);

      expect(mockRedisClient.del).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('deve verificar existência com exists()', async () => {
      mockRedisClient.exists.mockResolvedValue(1);

      const result = await cacheService.exists(namespace, testKey, tenantId);

      expect(mockRedisClient.exists).toHaveBeenCalled();
      expect(result).toBe(true);
    });
  });

  describe('Operações com Namespace', () => {
    const tenantId = 'tenant-123';
    const namespace = 'ml';
    const key = 'item:MLB123';
    const value = { id: 'MLB123', title: 'Produto Teste' };

    it('deve armazenar com namespace', async () => {
      mockRedisClient.setEx.mockResolvedValue('OK');

      const result = await cacheService.set(namespace, key, value, { ttl: 600 }, tenantId);

      expect(mockRedisClient.setEx).toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('deve recuperar com namespace', async () => {
      mockRedisClient.get.mockResolvedValue(JSON.stringify(value));

      const result = await cacheService.get(namespace, key, tenantId);

      expect(mockRedisClient.get).toHaveBeenCalled();
      expect(result).toEqual(value);
    });
  });

  describe('Métricas', () => {
    it('deve incrementar hit count em cache hit', async () => {
      mockRedisClient.get.mockResolvedValue(JSON.stringify({ test: 'data' }));

      await cacheService.get('test', 'key', 'tenant-123');

      const metrics = cacheService.getMetrics();
      expect(metrics.hits).toBe(1);
      expect(metrics.misses).toBe(0);
    });

    it('deve incrementar miss count em cache miss', async () => {
      mockRedisClient.get.mockResolvedValue(null);

      await cacheService.get('test', 'non-existent-key', 'tenant-123');

      const metrics = cacheService.getMetrics();
      expect(metrics.hits).toBe(0);
      expect(metrics.misses).toBe(1);
    });
  });

  describe('Tratamento de Erros', () => {
    it('deve lidar com erro no Redis get()', async () => {
      mockRedisClient.get.mockRejectedValue(new Error('Redis connection error'));

      const result = await cacheService.get('test', 'key', 'tenant-123');

      expect(result).toBeNull();
    });

    it('deve lidar com JSON inválido no get()', async () => {
      mockRedisClient.get.mockResolvedValue('invalid-json-{');

      const result = await cacheService.get('test', 'key', 'tenant-123');

      expect(result).toBeNull();
    });
  });
});
