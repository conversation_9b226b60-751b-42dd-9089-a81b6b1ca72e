import { RedisClientType } from 'redis';
import { getRedisClient, getRedisStatus } from '../config/redis';
import { logger } from '../utils/logger';

export interface CacheConfig {
  ttl: number; // Time to live em segundos
  prefix?: string;
  compression?: boolean;
  invalidateOnUpdate?: boolean;
}

export interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  hitRate: number;
  totalRequests: number;
}

export class CacheService {
  private redis: RedisClientType | null;
  private metrics: CacheMetrics;
  private defaultTTL: number;
  private memoryCache: Map<string, { value: any; expiry?: number }>;

  constructor() {
    this.redis = getRedisClient();
    this.memoryCache = new Map();
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      hitRate: 0,
      totalRequests: 0
    };
    this.defaultTTL = parseInt(process.env.CACHE_DEFAULT_TTL || '3600'); // 1 hora
  }

  /**
   * Gera chave de cache com namespace
   */
  private generateKey(namespace: string, key: string, tenantId?: string): string {
    const parts = ['magnow', namespace];
    if (tenantId) parts.push(tenantId);
    parts.push(key);
    return parts.join(':');
  }

  /**
   * Atualiza métricas de performance
   */
  private updateMetrics(type: 'hit' | 'miss' | 'set' | 'delete'): void {
    switch (type) {
      case 'hit':
        this.metrics.hits++;
        break;
      case 'miss':
        this.metrics.misses++;
        break;
      case 'set':
        this.metrics.sets++;
        break;
      case 'delete':
        this.metrics.deletes++;
        break;
    }
    
    if (type === 'hit' || type === 'miss') {
      this.metrics.totalRequests++;
      this.metrics.hitRate = this.metrics.hits / this.metrics.totalRequests;
    }
  }

  /**
   * Busca um valor no cache
   */
  async get<T = any>(
    namespace: string, 
    key: string, 
    tenantId?: string
  ): Promise<T | null> {
    try {
      const cacheKey = this.generateKey(namespace, key, tenantId);
      
      // Tentar Redis primeiro
      if (this.redis && getRedisStatus().available) {
        const value = await this.redis.get(cacheKey);
        
        if (value) {
          this.updateMetrics('hit');
          logger.debug(`Cache HIT (Redis): ${cacheKey}`);
          return JSON.parse(value) as T;
        }
      } else {
        // Fallback para cache em memória
        const cached = this.memoryCache.get(cacheKey);
        if (cached) {
          if (cached.expiry && Date.now() > cached.expiry) {
            this.memoryCache.delete(cacheKey);
          } else {
            this.updateMetrics('hit');
            logger.debug(`Cache HIT (Memory): ${cacheKey}`);
            return cached.value as T;
          }
        }
      }
      
      this.updateMetrics('miss');
      logger.debug(`Cache MISS: ${cacheKey}`);
      return null;
      
    } catch (error) {
      logger.debug(`Erro ao buscar cache ${namespace}:${key}, tentando fallback`, { error, tenantId });
      
      // Fallback para memória em caso de erro
      const cacheKey = this.generateKey(namespace, key, tenantId);
      const cached = this.memoryCache.get(cacheKey);
      if (cached && (!cached.expiry || Date.now() <= cached.expiry)) {
        this.updateMetrics('hit');
        return cached.value as T;
      }
      
      this.updateMetrics('miss');
      return null;
    }
  }

  /**
   * Define um valor no cache
   */
  async set(
    namespace: string,
    key: string,
    value: any,
    options: Partial<CacheConfig> = {},
    tenantId?: string
  ): Promise<boolean> {
    try {
      const cacheKey = this.generateKey(namespace, key, tenantId);
      const ttl = options.ttl || this.defaultTTL;
      const jsonValue = JSON.stringify(value);
      
      await this.redis.setEx(cacheKey, ttl, jsonValue);
      
      this.updateMetrics('set');
      logger.debug(`Cache SET: ${cacheKey} (TTL: ${ttl}s)`);
      
      return true;
    } catch (error) {
      logger.error(`Erro ao definir cache ${namespace}:${key}`, { error, tenantId });
      return false;
    }
  }

  /**
   * Remove um valor do cache
   */
  async delete(namespace: string, key: string, tenantId?: string): Promise<boolean> {
    try {
      const cacheKey = this.generateKey(namespace, key, tenantId);
      const result = await this.redis.del(cacheKey);
      
      this.updateMetrics('delete');
      logger.debug(`Cache DELETE: ${cacheKey}`);
      
      return result > 0;
    } catch (error) {
      logger.error(`Erro ao deletar cache ${namespace}:${key}`, { error, tenantId });
      return false;
    }
  }

  /**
   * Remove múltiplas chaves por padrão
   */
  async deleteByPattern(pattern: string, tenantId?: string): Promise<number> {
    try {
      const searchPattern = tenantId 
        ? `magnow:*:${tenantId}:${pattern}*`
        : `magnow:*:${pattern}*`;
      
      const keys = await this.redis.keys(searchPattern);
      
      if (keys.length === 0) return 0;
      
      const result = await this.redis.del(keys);
      
      logger.info(`Cache PATTERN DELETE: ${searchPattern} (${result} keys removed)`);
      
      return result;
    } catch (error) {
      logger.error(`Erro ao deletar cache por padrão ${pattern}`, { error, tenantId });
      return 0;
    }
  }

  /**
   * Verifica se uma chave existe no cache
   */
  async exists(namespace: string, key: string, tenantId?: string): Promise<boolean> {
    try {
      const cacheKey = this.generateKey(namespace, key, tenantId);
      const result = await this.redis.exists(cacheKey);
      return result > 0;
    } catch (error) {
      logger.error(`Erro ao verificar existência do cache ${namespace}:${key}`, { error });
      return false;
    }
  }

  /**
   * Define TTL para uma chave existente
   */
  async expire(namespace: string, key: string, ttl: number, tenantId?: string): Promise<boolean> {
    try {
      const cacheKey = this.generateKey(namespace, key, tenantId);
      const result = await this.redis.expire(cacheKey, ttl);
      return result;
    } catch (error) {
      logger.error(`Erro ao definir TTL do cache ${namespace}:${key}`, { error });
      return false;
    }
  }

  /**
   * Busca ou executa função se não estiver em cache (cache-aside pattern)
   */
  async getOrSet<T>(
    namespace: string,
    key: string,
    fetcher: () => Promise<T>,
    options: Partial<CacheConfig> = {},
    tenantId?: string
  ): Promise<T> {
    try {
      // Tentar buscar no cache primeiro
      const cached = await this.get<T>(namespace, key, tenantId);
      if (cached !== null) {
        return cached;
      }

      // Se não encontrou, executar a função
      const result = await fetcher();
      
      // Armazenar no cache
      await this.set(namespace, key, result, options, tenantId);
      
      return result;
    } catch (error) {
      logger.error(`Erro em getOrSet ${namespace}:${key}`, { error, tenantId });
      throw error;
    }
  }

  /**
   * Invalidação inteligente por tags
   */
  async invalidateByTags(tags: string[], tenantId?: string): Promise<number> {
    let totalDeleted = 0;
    
    for (const tag of tags) {
      const deleted = await this.deleteByPattern(tag, tenantId);
      totalDeleted += deleted;
    }
    
    logger.info(`Cache invalidado por tags: ${tags.join(', ')} (${totalDeleted} chaves removidas)`);
    
    return totalDeleted;
  }

  /**
   * Limpa todo o cache de um tenant
   */
  async clearTenant(tenantId: string): Promise<number> {
    try {
      const pattern = `magnow:*:${tenantId}:*`;
      const keys = await this.redis.keys(pattern);
      
      if (keys.length === 0) return 0;
      
      const result = await this.redis.del(keys);
      
      logger.info(`Cache do tenant ${tenantId} limpo (${result} chaves removidas)`);
      
      return result;
    } catch (error) {
      logger.error(`Erro ao limpar cache do tenant ${tenantId}`, { error });
      return 0;
    }
  }

  /**
   * Obtém métricas de performance do cache
   */
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  /**
   * Reseta métricas
   */
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      hitRate: 0,
      totalRequests: 0
    };
  }

  /**
   * Obtém estatísticas do Redis
   */
  async getRedisStats() {
    try {
      const info = await this.redis.info();
      const dbSize = await this.redis.dbSize();
      
      return {
        connected: this.redis.isReady,
        dbSize,
        info: this.parseRedisInfo(info)
      };
    } catch (error) {
      logger.error('Erro ao obter estatísticas do Redis', { error });
      return null;
    }
  }

  /**
   * Parse das informações do Redis
   */
  private parseRedisInfo(info: string) {
    const lines = info.split('\r\n');
    const parsed: Record<string, any> = {};
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        parsed[key] = isNaN(Number(value)) ? value : Number(value);
      }
    }
    
    return parsed;
  }
}

// Cache configurations específicas
export const CacheConfigs = {
  // Cache de curta duração para dados dinâmicos
  SHORT: { ttl: 300 }, // 5 minutos
  
  // Cache padrão para consultas normais
  MEDIUM: { ttl: 1800 }, // 30 minutos
  
  // Cache de longa duração para dados estáticos
  LONG: { ttl: 3600 }, // 1 hora
  
  // Cache para dados do Mercado Livre
  MERCADO_LIVRE: { ttl: 600 }, // 10 minutos
  
  // Cache para cálculos de estoque
  STOCK_CALCULATIONS: { ttl: 900 }, // 15 minutos
  
  // Cache de sessões de usuário
  USER_SESSIONS: { ttl: 86400 }, // 24 horas
  
  // Cache de configurações
  CONFIG: { ttl: 7200 }, // 2 horas
  
  // Cache para produtos
  PRODUCTS: { ttl: 1800 }, // 30 minutos
  
  // Cache para queries de banco
  DATABASE_QUERIES: { ttl: 600 }, // 10 minutos
};

// Namespaces para organização
export const CacheNamespaces = {
  MERCADO_LIVRE: 'ml',
  STOCK: 'stock',
  USER: 'user',
  SESSION: 'session',
  CONFIG: 'config',
  ANALYTICS: 'analytics',
  PRODUCTS: 'products',
  ORDERS: 'orders',
  SPREADSHEETS: 'spreadsheets',
  DATABASE: 'db'
};

export default CacheService; 