import { useState, useCallback, useRef, useEffect } from 'react';

export interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface UseAsyncStateOptions {
  initialData?: any;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
  retryDelay?: number;
  maxRetries?: number;
}

export interface UseAsyncStateReturn<T> extends AsyncState<T> {
  execute: (asyncFn: () => Promise<T>) => Promise<T | null>;
  reset: () => void;
  retry: () => Promise<T | null>;
  setData: (data: T | null) => void;
  setError: (error: string | null) => void;
  setLoading: (loading: boolean) => void;
}

export function useAsyncState<T = any>(
  options: UseAsyncStateOptions = {}
): UseAsyncStateReturn<T> {
  const {
    initialData = null,
    onSuccess,
    onError,
    retryDelay = 1000,
    maxRetries = 3
  } = options;

  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: false,
    error: null
  });

  const lastAsyncFnRef = useRef<(() => Promise<T>) | null>(null);
  const retryCountRef = useRef(0);
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  const setData = useCallback((data: T | null) => {
    if (!isMountedRef.current) return;
    setState(prev => ({ ...prev, data }));
  }, []);

  const setError = useCallback((error: string | null) => {
    if (!isMountedRef.current) return;
    setState(prev => ({ ...prev, error }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    if (!isMountedRef.current) return;
    setState(prev => ({ ...prev, loading }));
  }, []);

  const reset = useCallback(() => {
    if (!isMountedRef.current) return;
    setState({
      data: initialData,
      loading: false,
      error: null
    });
    retryCountRef.current = 0;
    lastAsyncFnRef.current = null;
  }, [initialData]);

  const execute = useCallback(async (asyncFn: () => Promise<T>): Promise<T | null> => {
    if (!isMountedRef.current) return null;

    lastAsyncFnRef.current = asyncFn;
    retryCountRef.current = 0;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      const result = await asyncFn();
      
      if (!isMountedRef.current) return null;

      setState({
        data: result,
        loading: false,
        error: null
      });

      onSuccess?.(result);
      return result;
    } catch (error) {
      if (!isMountedRef.current) return null;

      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage
      });

      onError?.(errorMessage);
      return null;
    }
  }, [onSuccess, onError]);

  const retry = useCallback(async (): Promise<T | null> => {
    if (!lastAsyncFnRef.current || !isMountedRef.current) return null;

    if (retryCountRef.current >= maxRetries) {
      const errorMessage = `Máximo de tentativas excedido (${maxRetries})`;
      setState(prev => ({ ...prev, error: errorMessage }));
      onError?.(errorMessage);
      return null;
    }

    retryCountRef.current += 1;

    // Delay before retry
    if (retryDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }

    if (!isMountedRef.current) return null;

    setState(prev => ({
      ...prev,
      loading: true,
      error: null
    }));

    try {
      const result = await lastAsyncFnRef.current();
      
      if (!isMountedRef.current) return null;

      setState({
        data: result,
        loading: false,
        error: null
      });

      onSuccess?.(result);
      return result;
    } catch (error) {
      if (!isMountedRef.current) return null;

      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      
      setState({
        data: null,
        loading: false,
        error: errorMessage
      });

      onError?.(errorMessage);
      return null;
    }
  }, [maxRetries, retryDelay, onSuccess, onError]);

  return {
    ...state,
    execute,
    reset,
    retry,
    setData,
    setError,
    setLoading
  };
}

// Hook especializado para operações CRUD
export function useCrudState<T = any>(options: UseAsyncStateOptions = {}) {
  const asyncState = useAsyncState<T[]>(options);

  const create = useCallback(async (createFn: () => Promise<T>) => {
    const newItem = await asyncState.execute(createFn);
    if (newItem && asyncState.data) {
      asyncState.setData([...asyncState.data, newItem]);
    }
    return newItem;
  }, [asyncState]);

  const update = useCallback(async (id: string, updateFn: () => Promise<T>) => {
    const updatedItem = await asyncState.execute(updateFn);
    if (updatedItem && asyncState.data) {
      const updatedData = asyncState.data.map(item => 
        (item as any).id === id ? updatedItem : item
      );
      asyncState.setData(updatedData);
    }
    return updatedItem;
  }, [asyncState]);

  const remove = useCallback(async (id: string, deleteFn: () => Promise<void>) => {
    await asyncState.execute(deleteFn);
    if (asyncState.data) {
      const filteredData = asyncState.data.filter(item => (item as any).id !== id);
      asyncState.setData(filteredData);
    }
  }, [asyncState]);

  return {
    ...asyncState,
    create,
    update,
    remove
  };
}

export default useAsyncState;
