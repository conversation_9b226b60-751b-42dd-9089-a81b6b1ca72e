/**
 * @deprecated This modal-based ML Full Wizard has been replaced by a full-page implementation.
 * Use the new route '/ml-full-wizard' instead of this modal component.
 * This file is kept for reference but should not be used in new implementations.
 */

import { useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Button } from '../ui/button';
import { Progress } from '../ui/progress';
import { Badge } from '../ui/badge';
import {
  X,
  Package,
  Settings,
  FileText,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useMLFullWizardStore } from '../../store/mlFullWizardStore';
import ProductSelectionStep from './ProductSelectionStep';
import QuantityConfigurationStep from './QuantityConfigurationStep';
import ReviewStep from './ReviewStep';
import CompletionStep from './CompletionStep';

interface MLFullWizardProps {
  isOpen: boolean;
  onClose: () => void;
}

const stepIcons = {
  1: Package,
  2: Settings,
  3: FileText,
  4: CheckCircle,
};

export default function MLFullWizard({ isOpen, onClose }: MLFullWizardProps) {
  const {
    currentStep,
    steps,
    error,
    isLoadingProducts,
    selectedProducts,
    resetWizard,
    loadProducts,
    clearError,
    getStepProgress,
    nextStep,
    previousStep,
  } = useMLFullWizardStore();

  // Load products when wizard opens
  useEffect(() => {
    if (isOpen) {
      loadProducts();
    }
  }, [isOpen, loadProducts]);

  // Reset wizard when closing
  const handleClose = () => {
    resetWizard();
    onClose();
  };



  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <ProductSelectionStep onNext={nextStep} onBack={previousStep} />;
      case 2:
        return <QuantityConfigurationStep onNext={nextStep} onBack={previousStep} />;
      case 3:
        return <ReviewStep onNext={nextStep} onBack={previousStep} />;
      case 4:
        return <CompletionStep onNext={nextStep} onBack={previousStep} />;
      default:
        return null;
    }
  };

  const getStepIcon = (stepNumber: number) => {
    const IconComponent = stepIcons[stepNumber as keyof typeof stepIcons];
    return IconComponent ? <IconComponent className="h-4 w-4" /> : null;
  };

  const getStepStatus = (step: typeof steps[0]) => {
    if (step.isCompleted) return 'completed';
    if (step.isActive) return 'active';
    return 'pending';
  };

  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'active':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl h-[90vh] flex flex-col p-0">
        <DialogHeader className="px-6 py-4 border-b">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl font-semibold">
                Wizard ML Full - Geração de Planilha
              </DialogTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Gere planilhas para envio de produtos ao Mercado Livre Full
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleClose}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="px-6 py-4 border-b bg-muted/30">
          <div className="space-y-3">
            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="font-medium">Progresso</span>
                <span>{Math.round(getStepProgress())}%</span>
              </div>
              <Progress value={getStepProgress()} className="h-2" />
            </div>

            {/* Steps Indicator */}
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const status = getStepStatus(step);
                const isLast = index === steps.length - 1;
                
                return (
                  <div key={step.id} className="flex items-center flex-1">
                    <div className="flex items-center gap-2">
                      <Badge 
                        className={`${getStepStatusColor(status)} flex items-center gap-1 px-2 py-1`}
                      >
                        {getStepIcon(step.id)}
                        <span className="text-xs font-medium">{step.id}</span>
                      </Badge>
                      <div className="hidden md:block">
                        <div className="text-sm font-medium">{step.title}</div>
                        <div className="text-xs text-muted-foreground">{step.description}</div>
                      </div>
                    </div>
                    
                    {!isLast && (
                      <div className="flex-1 mx-4">
                        <div className={`h-0.5 ${
                          step.isCompleted ? 'bg-green-300' : 'bg-gray-200'
                        }`} />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Selected Products Counter */}
            {selectedProducts.length > 0 && (
              <div className="flex items-center gap-2 text-sm">
                <Package className="h-4 w-4 text-blue-500" />
                <span>
                  {selectedProducts.length} {selectedProducts.length === 1 ? 'produto selecionado' : 'produtos selecionados'}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="px-6 py-3 border-b bg-red-50">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="ml-auto h-6 px-2 text-red-600 hover:text-red-800"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 overflow-hidden">
          {isLoadingProducts ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center space-y-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="text-muted-foreground">Carregando produtos...</p>
              </div>
            </div>
          ) : (
            <div className="h-full overflow-y-auto">
              {renderStepContent()}
            </div>
          )}
        </div>

        {/* Navigation Footer */}
        <div className="px-6 py-4 border-t bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Passo {currentStep} de {steps.length}
            </div>
            <div className="text-sm text-muted-foreground">
              {selectedProducts.length} produto{selectedProducts.length !== 1 ? 's' : ''} selecionado{selectedProducts.length !== 1 ? 's' : ''}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
