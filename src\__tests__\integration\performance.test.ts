/**
 * Testes de Performance e Cache
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 * 
 * Versão simulada para testar performance e funcionamento do cache
 */

import request from 'supertest';
import express from 'express';
import { mockPrismaClient } from '../setup';

interface CalculationResult {
  itemId: string;
  stockCalculated: number;
  processingTime: number;
}

interface PerformanceResult {
  load: number;
  responseTime: number;
  averagePerItem: number;
}

describe('Performance and Cache Tests (Mock)', () => {
  let app: express.Application;
  
  // Mock do cache Redis para simular hit/miss
  let mockCache: Record<string, { value: any, expiry: number }> = {};
  let cacheStats = { hits: 0, misses: 0 };
  
  const simulateCache = {
    get: async (key: string) => {
      const item = mockCache[key];
      if (item && item.expiry > Date.now()) {
        cacheStats.hits++;
        return item.value;
      }
      cacheStats.misses++;
      return null;
    },
    set: async (key: string, value: any, ttl: number = 3600) => {
      mockCache[key] = { value, expiry: Date.now() + (ttl * 1000) };
      return true;
    },
    del: async (key: string) => {
      delete mockCache[key];
      return true;
    },
    clear: async () => {
      mockCache = {};
      cacheStats = { hits: 0, misses: 0 };
    }
  };

  beforeAll(() => {
    // Criar uma aplicação Express simples para testar performance
    app = express();
    
    // Middlewares básicos
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));
    
    // Simulação de middleware de cache
    app.use('/api/cached/*', async (req, res, next) => {
      const cacheKey = `route:${req.method}:${req.path}`;
      const cached = await simulateCache.get(cacheKey);
      
      if (cached) {
        res.set('X-Cache', 'HIT');
        return res.json(cached);
      }
      
      res.set('X-Cache', 'MISS');
      // Salvar função original de json para interceptar
      const originalJson = res.json;
      res.json = function(data) {
        simulateCache.set(cacheKey, data, 300); // 5 min TTL
        return originalJson.call(this, data);
      };
      return next();
    });
    
    // Rotas simuladas para testes de performance
    app.get('/api/cached/stock/quick', (req, res) => {
      return res.json({ 
        stock: 100, 
        timestamp: Date.now(),
        processingTime: Math.floor(Math.random() * 50) + 10 // 10-60ms
      });
    });
    
    app.get('/api/cached/stock/slow', async (req, res) => {
      // Simular operação lenta
      await new Promise(resolve => setTimeout(resolve, 200));
      return res.json({ 
        stock: 100, 
        timestamp: Date.now(),
        processingTime: 200
      });
    });
    
    app.get('/api/performance/bulk-calculate', async (req, res) => {
      const { items = 10 } = req.query;
      const itemCount = parseInt(items as string);
      
      const startTime = Date.now();
      
      // Simular cálculo em lote
      const results: CalculationResult[] = [];
      for (let i = 0; i < itemCount; i++) {
        results.push({
          itemId: `MLB${i + 1}`,
          stockCalculated: Math.floor(Math.random() * 100) + 1,
          processingTime: Math.floor(Math.random() * 10) + 1
        });
      }
      
      const totalTime = Date.now() - startTime;
      
      return res.json({
        results,
        totalItems: itemCount,
        totalProcessingTime: totalTime,
        averageTimePerItem: totalTime / itemCount
      });
    });
    
    app.get('/api/cache/stats', (req, res) => {
      return res.json({
        cacheStats,
        totalRequests: cacheStats.hits + cacheStats.misses,
        hitRate: cacheStats.hits + cacheStats.misses > 0 ? 
          (cacheStats.hits / (cacheStats.hits + cacheStats.misses) * 100).toFixed(2) + '%' : '0%'
      });
    });
    
    app.delete('/api/cache/clear', async (req, res) => {
      await simulateCache.clear();
      return res.json({ message: 'Cache cleared successfully' });
    });
    
    app.get('/api/cache/invalidate/*', async (req, res) => {
      // Extrair o caminho após '/api/cache/invalidate/'
      const pathToInvalidate = req.url.replace('/api/cache/invalidate/', '');
      const cacheKey = `route:GET:/api/cached/${pathToInvalidate}`;
      await simulateCache.del(cacheKey);
      return res.json({ message: `Cache invalidated for key: ${pathToInvalidate}` });
    });
  });

  beforeEach(async () => {
    // Reset cache before each test
    await simulateCache.clear();
  });

  describe('Cache Functionality Tests', () => {
    it('deve retornar MISS na primeira requisição', async () => {
      const response = await request(app)
        .get('/api/cached/stock/quick');

      expect(response.status).toBe(200);
      expect(response.headers['x-cache']).toBe('MISS');
      expect(response.body).toHaveProperty('stock');
      expect(response.body).toHaveProperty('timestamp');
    });

    it('deve retornar HIT na segunda requisição', async () => {
      // Primeira requisição - MISS
      await request(app).get('/api/cached/stock/quick');
      
      // Segunda requisição - HIT
      const response = await request(app)
        .get('/api/cached/stock/quick');

      expect(response.status).toBe(200);
      expect(response.headers['x-cache']).toBe('HIT');
      expect(response.body).toHaveProperty('stock');
    });

    it('deve calcular hit rate corretamente', async () => {
      // 1 MISS + 2 HITS = 66.67% hit rate
      await request(app).get('/api/cached/stock/quick');
      await request(app).get('/api/cached/stock/quick');
      await request(app).get('/api/cached/stock/quick');
      
      const statsResponse = await request(app)
        .get('/api/cache/stats');

      expect(statsResponse.status).toBe(200);
      expect(statsResponse.body.cacheStats.hits).toBe(2);
      expect(statsResponse.body.cacheStats.misses).toBe(1);
      expect(statsResponse.body.hitRate).toBe('66.67%');
    });

    it('deve limpar cache com sucesso', async () => {
      // Gerar algumas entradas de cache
      await request(app).get('/api/cached/stock/quick');
      await request(app).get('/api/cached/stock/quick');
      
      // Limpar cache
      const clearResponse = await request(app)
        .delete('/api/cache/clear');

      expect(clearResponse.status).toBe(200);
      expect(clearResponse.body.message).toBe('Cache cleared successfully');
      
      // Verificar se stats foram resetadas
      const statsResponse = await request(app)
        .get('/api/cache/stats');
      
      expect(statsResponse.body.cacheStats.hits).toBe(0);
      expect(statsResponse.body.cacheStats.misses).toBe(0);
    });

    it('deve invalidar cache específico por chave', async () => {
      // Gerar entrada no cache
      await request(app).get('/api/cached/stock/quick');
      
      // Verificar HIT
      const hitResponse = await request(app).get('/api/cached/stock/quick');
      expect(hitResponse.headers['x-cache']).toBe('HIT');
      
      // Invalidar cache
      const invalidateResponse = await request(app)
        .get('/api/cache/invalidate/stock/quick');
      expect(invalidateResponse.status).toBe(200);
      
      // Delay para garantir que a invalidação foi processada
      await new Promise(resolve => setTimeout(resolve, 50));
      
      // Verificar MISS após invalidação
      const missResponse = await request(app).get('/api/cached/stock/quick');
      expect(missResponse.headers['x-cache']).toBe('MISS');
    });
  });

  describe('Performance Tests', () => {
    it('deve processar requisições rápidas em menos de 100ms', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/cached/stock/quick');

      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(responseTime).toBeLessThan(100); // Menos de 100ms
      expect(response.body.processingTime).toBeLessThan(70); // Tempo simulado < 70ms
    });

    it('deve identificar rotas lentas e melhorar com cache', async () => {
      // Primeira requisição - lenta
      const slowStart = Date.now();
      const slowResponse = await request(app)
        .get('/api/cached/stock/slow');
      const slowTime = Date.now() - slowStart;
      
      expect(slowResponse.status).toBe(200);
      expect(slowResponse.headers['x-cache']).toBe('MISS');
      expect(slowTime).toBeGreaterThan(150); // Deve ser lenta (>150ms)
      
      // Segunda requisição - rápida pelo cache
      const fastStart = Date.now();
      const fastResponse = await request(app)
        .get('/api/cached/stock/slow');
      const fastTime = Date.now() - fastStart;
      
      expect(fastResponse.status).toBe(200);
      expect(fastResponse.headers['x-cache']).toBe('HIT');
      expect(fastTime).toBeLessThan(50); // Deve ser rápida pelo cache (<50ms)
    });

    it('deve processar cálculos em lote eficientemente', async () => {
      const startTime = Date.now();
      
      const response = await request(app)
        .get('/api/performance/bulk-calculate?items=50');

      const responseTime = Date.now() - startTime;
      
      expect(response.status).toBe(200);
      expect(response.body.totalItems).toBe(50);
      expect(response.body.results).toHaveLength(50);
      expect(response.body.averageTimePerItem).toBeLessThan(20); // <20ms por item em média
      expect(responseTime).toBeLessThan(1000); // Todo o lote em <1s
    });

    it('deve manter performance consistente com diferentes cargas', async () => {
      const loads = [1, 10, 25, 50];
      const results: PerformanceResult[] = [];
      
      for (const load of loads) {
        const startTime = Date.now();
        const response = await request(app)
          .get(`/api/performance/bulk-calculate?items=${load}`);
        const responseTime = Date.now() - startTime;
        
        expect(response.status).toBe(200);
        
        results.push({
          load,
          responseTime,
          averagePerItem: responseTime / load // Usar o tempo de resposta real dividido pelo número de itens
        });
      }
      
      // Verificar que a performance per-item não degrada muito
      const averageTimes = results.map(r => r.averagePerItem).filter(time => time > 0 && !isNaN(time));
      const maxTime = Math.max(...averageTimes);
      const minTime = Math.min(...averageTimes);
      
      // Verificar se temos valores válidos
      expect(averageTimes.length).toBeGreaterThan(0);
      expect(minTime).toBeGreaterThan(0);
      
      // Degradação máxima aceitável: 10x (mais realista para diferentes cargas)
      expect(maxTime / minTime).toBeLessThan(10);
    });

    it('deve medir hit rate em cenário real de uso', async () => {
      // Simular uso real: algumas rotas acessadas múltiplas vezes
      const routes = [
        '/api/cached/stock/quick',
        '/api/cached/stock/quick',
        '/api/cached/stock/quick',
        '/api/cached/stock/slow',
        '/api/cached/stock/slow'
      ];
      
      for (const route of routes) {
        await request(app).get(route);
      }
      
      const statsResponse = await request(app)
        .get('/api/cache/stats');

      expect(statsResponse.status).toBe(200);
      
      const { cacheStats, hitRate } = statsResponse.body;
      
      // Deve ter pelo menos 50% de hit rate
      const hitRateNum = parseFloat(hitRate.replace('%', ''));
      expect(hitRateNum).toBeGreaterThan(50);
      
      // Total de requisições deve ser 5
      expect(cacheStats.hits + cacheStats.misses).toBe(5);
    });
  });
}); 