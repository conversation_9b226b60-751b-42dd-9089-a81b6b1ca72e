import { useEffect, useRef, useCallback } from 'react';
import { useNotificationStore } from '../store/notificationStore';

interface UseNotificationsOptions {
  enableRealTime?: boolean;
  enableToasts?: boolean;
  maxNotifications?: number;
  pollingInterval?: number;
}

export const useNotifications = (options: UseNotificationsOptions = {}) => {
  const {
    enableRealTime = true,
    enableToasts = true,
    maxNotifications = 50,
    pollingInterval = 60000 // 1 minute (reduced from 30s)
  } = options;

  // Use the centralized notification store
  const {
    notifications,
    unreadCount,
    isConnected,
    loading,
    error,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    loadFromStorage,
    setPollingInterval,
    setEnableToasts,
    setMaxNotifications,
    setConnectionStatus,
    setError
  } = useNotificationStore();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);

  // Configure store based on options
  useEffect(() => {
    setPollingInterval(pollingInterval);
    setEnableToasts(enableToasts);
    setMaxNotifications(maxNotifications);
  }, [pollingInterval, enableToasts, maxNotifications, setPollingInterval, setEnableToasts, setMaxNotifications]);

  // MOCK DATA FOR DEVELOPMENT - Simulate polling with throttling
  const pollNotifications = useCallback(async () => {
    if (!isMountedRef.current) return;

    try {
      setError(null);

      // TODO: Replace with real API call
      // const response = await apiService.getNotifications();

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      if (!isMountedRef.current) return;

      // Generate mock notification with throttling
      const mockNotification = {
        type: 'stock_alert' as const,
        title: 'Estoque baixo detectado',
        message: 'Produto XYZ está com apenas 2 unidades',
        severity: 'warning' as const,
        productId: 'xyz123',
        autoClose: false
      };

      // Add notification (will be throttled automatically by store)
      addNotification(mockNotification);

      setConnectionStatus(true);
    } catch (error) {
      console.error('Erro ao buscar notificações:', error);
      setConnectionStatus(false);
      setError('Erro ao conectar com o servidor de notificações');
    }
  }, [addNotification, setConnectionStatus, setError]);

  // Load notifications from storage on mount
  useEffect(() => {
    loadFromStorage();
    isMountedRef.current = true;

    return () => {
      isMountedRef.current = false;
    };
  }, [loadFromStorage]);

  // Setup polling with proper cleanup
  useEffect(() => {
    if (!enableRealTime) return;

    // Start polling immediately
    pollNotifications();

    // Setup interval with proper cleanup
    intervalRef.current = setInterval(() => {
      if (isMountedRef.current) {
        pollNotifications();
      }
    }, pollingInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [enableRealTime, pollingInterval, pollNotifications]);

  // Return store methods and state
  return {
    notifications,
    unreadCount,
    isConnected,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    addNotification,
    refresh: pollNotifications
  };
};