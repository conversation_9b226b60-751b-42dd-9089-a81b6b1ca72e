import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import stockService from '../services/stockService';
import apiService from '../services/api';
import type { StockItem, CreateStockItemDto, UpdateStockItemDto } from '../services/stockService';
import type { ProductWithStock, StockCalculation, StockAlert } from '../types/api';
// MOCK DATA IMPORT
import { mockMLProducts } from '../mocks/mlProductsMock';

// MOCK DATA FOR DEVELOPMENT
const mockStockItems: StockItem[] = [
  {
    id: 'stock-001',
    productId: 'ml-prod-001',
    productTitle: 'Sacos Organizadores A Vácuo Sun 50x60 3un 60x80 2un 70x110',
    productSku: 'SAC-VAC-001',
    currentQuantity: 45,
    location: 'Estoque Principal',
    lastUpdated: '2024-01-18T14:22:00Z',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-18T14:22:00Z',
  },
  {
    id: 'stock-002',
    productId: 'ml-prod-002',
    productTitle: 'Smartphone Samsung Galaxy A54 128GB 6GB RAM Câmera Tripla',
    productSku: 'SAMS-A54-128',
    currentQuantity: 8,
    location: 'Estoque Principal',
    lastUpdated: '2024-01-18T12:15:00Z',
    createdAt: '2024-01-10T09:20:00Z',
    updatedAt: '2024-01-18T12:15:00Z',
  },
  {
    id: 'stock-003',
    productId: 'ml-prod-003',
    productTitle: 'Tênis Nike Air Max 270 Masculino Original',
    productSku: 'NIKE-AM270-M',
    currentQuantity: 0,
    location: 'Estoque Principal',
    lastUpdated: '2024-01-17T16:45:00Z',
    createdAt: '2024-01-05T11:10:00Z',
    updatedAt: '2024-01-17T16:45:00Z',
  },
  {
    id: 'stock-004',
    productId: 'ml-prod-004',
    productTitle: 'Notebook Dell Inspiron 15 3000 Intel Core i5 8GB 256GB SSD',
    productSku: 'DELL-INS15-I5',
    currentQuantity: 12,
    location: 'Estoque Secundário',
    lastUpdated: '2024-01-18T10:30:00Z',
    createdAt: '2024-01-08T14:25:00Z',
    updatedAt: '2024-01-18T10:30:00Z',
  },
  {
    id: 'stock-005',
    productId: 'ml-prod-005',
    productTitle: 'Cafeteira Elétrica Philco PH31 Preta 30 Xícaras',
    productSku: 'PHIL-CAF-31',
    currentQuantity: 3,
    location: 'Estoque Principal',
    lastUpdated: '2024-01-18T08:20:00Z',
    createdAt: '2024-01-12T13:40:00Z',
    updatedAt: '2024-01-18T08:20:00Z',
  },
  {
    id: 'stock-006',
    productId: 'ml-prod-006',
    productTitle: 'Fone de Ouvido Bluetooth JBL Tune 510BT Sem Fio',
    productSku: 'JBL-T510-BT',
    currentQuantity: 25,
    location: 'Estoque Principal',
    lastUpdated: '2024-01-18T15:10:00Z',
    createdAt: '2024-01-14T16:55:00Z',
    updatedAt: '2024-01-18T15:10:00Z',
  },
  {
    id: 'stock-007',
    productId: 'ml-prod-007',
    productTitle: 'Smart TV LG 43" 4K UHD ThinQ AI 43UP7500PSF',
    productSku: 'LG-43UP7500',
    currentQuantity: 6,
    location: 'Estoque Secundário',
    lastUpdated: '2024-01-17T19:30:00Z',
    createdAt: '2024-01-06T12:15:00Z',
    updatedAt: '2024-01-17T19:30:00Z',
  },
  {
    id: 'stock-008',
    productId: 'ml-prod-008',
    productTitle: 'Relógio Smartwatch Xiaomi Mi Band 7 Preto',
    productSku: 'XIAO-MB7-BK',
    currentQuantity: 18,
    location: 'Estoque Principal',
    lastUpdated: '2024-01-18T11:45:00Z',
    createdAt: '2024-01-09T10:30:00Z',
    updatedAt: '2024-01-18T11:45:00Z',
  },
  {
    id: 'stock-009',
    productId: 'ml-prod-009',
    productTitle: 'Aspirador de Pó Robô Multilaser HO041 Preto',
    productSku: 'MULT-HO041',
    currentQuantity: 2,
    location: 'Estoque Principal',
    lastUpdated: '2024-01-18T13:20:00Z',
    createdAt: '2024-01-11T15:45:00Z',
    updatedAt: '2024-01-18T13:20:00Z',
  },
  {
    id: 'stock-010',
    productId: 'ml-prod-010',
    productTitle: 'Caixa de Som Bluetooth JBL Flip 5 Portátil',
    productSku: 'JBL-FLIP5',
    currentQuantity: 14,
    location: 'Estoque Secundário',
    lastUpdated: '2024-01-18T09:15:00Z',
    createdAt: '2024-01-13T11:20:00Z',
    updatedAt: '2024-01-18T09:15:00Z',
  },
];

const mockStatistics: StockStatistics = {
  totalItems: 10,
  totalQuantity: 133,
  lowStockItems: 4, // Items with quantity <= 5
  outOfStockItems: 1, // Items with quantity = 0
  locations: ['Estoque Principal', 'Estoque Secundário'],
  lastUpdated: '2024-01-18T15:10:00Z',
};

// Interfaces para filtros de estoque
export interface StockFilters {
  search?: string;
  location?: string;
  status?: ('in_stock' | 'low_stock' | 'out_of_stock')[];
  minQuantity?: number;
  maxQuantity?: number;
  sortBy?: 'productTitle' | 'currentQuantity' | 'location' | 'lastUpdated';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Interface para estatísticas de estoque
export interface StockStatistics {
  totalItems: number;
  totalQuantity: number;
  lowStockItems: number;
  outOfStockItems: number;
  locations: string[];
  lastUpdated: string;
}

// Interface para paginação
export interface StockPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// Interface principal do store
interface StockState {
  // Data
  stockItems: StockItem[];
  filteredItems: StockItem[];
  statistics: StockStatistics | null;

  // Integration with Products
  productsWithStock: ProductWithStock[];
  stockCalculations: Record<string, StockCalculation>;
  stockAlerts: StockAlert[];

  // Loading states
  stockItemsLoading: boolean;
  statisticsLoading: boolean;
  productsLoading: boolean;
  calculationsLoading: boolean;
  alertsLoading: boolean;

  // Filters and pagination
  filters: StockFilters;
  pagination: StockPagination;

  // Error handling
  error: string | null;
  
  // Actions - CRUD operations
  loadStockItems: () => Promise<void>;
  createStockItem: (item: CreateStockItemDto) => Promise<StockItem>;
  updateStockItem: (id: string, item: UpdateStockItemDto) => Promise<StockItem>;
  deleteStockItem: (id: string) => Promise<void>;
  refreshStockItem: (id: string) => Promise<void>;
  
  // Actions - Statistics
  loadStatistics: () => Promise<void>;
  refreshStatistics: () => Promise<void>;
  
  // Actions - Filters and search
  setFilters: (filters: Partial<StockFilters>) => void;
  clearFilters: () => void;
  applyFilters: () => void;
  
  // Actions - Pagination
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  
  // Actions - Product Integration
  loadProductsWithStock: (accountId?: string) => Promise<void>;
  loadStockCalculations: () => Promise<void>;
  loadStockAlerts: () => Promise<void>;
  updateProductStock: (productId: string, quantity: number, reason?: string) => Promise<void>;
  syncProductStock: (productId: string) => Promise<void>;
  calculateStockGap: (productId: string) => Promise<StockCalculation | null>;
  markAlertAsRead: (alertId: string) => Promise<void>;

  // Actions - Utility
  clearError: () => void;
  refreshAll: () => Promise<void>;
}

export const useStockStore = create<StockState>()(
  devtools(
    (set, get) => ({
      // Initial state
      stockItems: [],
      filteredItems: [],
      statistics: null,

      // Product integration state
      productsWithStock: [],
      stockCalculations: {},
      stockAlerts: [],

      stockItemsLoading: false,
      statisticsLoading: false,
      productsLoading: false,
      calculationsLoading: false,
      alertsLoading: false,
      
      filters: {
        page: 1,
        limit: 10,
        sortBy: 'lastUpdated',
        sortOrder: 'desc',
      },
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
      
      error: null,
      
      // CRUD Actions
      loadStockItems: async () => {
        set({ stockItemsLoading: true, error: null });
        try {
          // MOCK DATA FOR DEVELOPMENT - Replace with real API call
          // const items = await stockService.getAllStockItems();

          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 800));

          set({
            stockItems: mockStockItems,
            stockItemsLoading: false
          });

          // Apply current filters
          get().applyFilters();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar itens de estoque';
          set({
            error: errorMessage,
            stockItemsLoading: false
          });
        }
      },
      
      createStockItem: async (item: CreateStockItemDto): Promise<StockItem> => {
        set({ error: null });
        try {
          const newItem = await stockService.createStockItem(item);
          
          // Add to local state
          const { stockItems } = get();
          set({ stockItems: [...stockItems, newItem] });
          
          // Refresh filters and statistics
          get().applyFilters();
          get().refreshStatistics();
          
          return newItem;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao criar item de estoque';
          set({ error: errorMessage });
          throw error;
        }
      },
      
      updateStockItem: async (id: string, item: UpdateStockItemDto): Promise<StockItem> => {
        set({ error: null });
        try {
          const updatedItem = await stockService.updateStockItem(id, item);
          
          // Update local state
          const { stockItems } = get();
          const updatedItems = stockItems.map(stockItem => 
            stockItem.id === id ? updatedItem : stockItem
          );
          set({ stockItems: updatedItems });
          
          // Refresh filters and statistics
          get().applyFilters();
          get().refreshStatistics();
          
          return updatedItem;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar item de estoque';
          set({ error: errorMessage });
          throw error;
        }
      },
      
      deleteStockItem: async (id: string): Promise<void> => {
        set({ error: null });
        try {
          await stockService.deleteStockItem(id);
          
          // Remove from local state
          const { stockItems } = get();
          const filteredItems = stockItems.filter(item => item.id !== id);
          set({ stockItems: filteredItems });
          
          // Refresh filters and statistics
          get().applyFilters();
          get().refreshStatistics();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao excluir item de estoque';
          set({ error: errorMessage });
          throw error;
        }
      },
      
      refreshStockItem: async (id: string): Promise<void> => {
        set({ error: null });
        try {
          const item = await stockService.getStockItemById(id);
          if (item) {
            const { stockItems } = get();
            const updatedItems = stockItems.map(stockItem => 
              stockItem.id === id ? item : stockItem
            );
            set({ stockItems: updatedItems });
            get().applyFilters();
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar item';
          set({ error: errorMessage });
        }
      },
      
      // Statistics Actions
      loadStatistics: async () => {
        set({ statisticsLoading: true, error: null });
        try {
          // MOCK DATA FOR DEVELOPMENT - Replace with real API call
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 600));

          const { stockItems } = get();

          // Calculate statistics from current items (or use mock data)
          const statistics: StockStatistics = stockItems.length > 0 ? {
            totalItems: stockItems.length,
            totalQuantity: stockItems.reduce((sum, item) => sum + item.currentQuantity, 0),
            lowStockItems: stockItems.filter(item => item.currentQuantity <= 5 && item.currentQuantity > 0).length,
            outOfStockItems: stockItems.filter(item => item.currentQuantity === 0).length,
            locations: [...new Set(stockItems.map(item => item.location))],
            lastUpdated: new Date().toISOString(),
          } : mockStatistics;

          set({
            statistics,
            statisticsLoading: false
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar estatísticas';
          set({
            error: errorMessage,
            statisticsLoading: false
          });
        }
      },
      
      refreshStatistics: async () => {
        return get().loadStatistics();
      },
      
      // Filter Actions
      setFilters: (newFilters: Partial<StockFilters>) => {
        const { filters } = get();
        const updatedFilters = { ...filters, ...newFilters };
        set({ filters: updatedFilters });
        get().applyFilters();
      },
      
      clearFilters: () => {
        const defaultFilters: StockFilters = {
          page: 1,
          limit: 10,
          sortBy: 'lastUpdated',
          sortOrder: 'desc',
        };
        set({ filters: defaultFilters });
        get().applyFilters();
      },
      
      applyFilters: () => {
        const { stockItems, filters } = get();
        let filtered = [...stockItems];
        
        // Apply search filter
        if (filters.search) {
          const searchLower = filters.search.toLowerCase();
          filtered = filtered.filter(item =>
            item.productTitle.toLowerCase().includes(searchLower) ||
            item.productSku.toLowerCase().includes(searchLower) ||
            item.location.toLowerCase().includes(searchLower)
          );
        }
        
        // Apply location filter
        if (filters.location) {
          filtered = filtered.filter(item => item.location === filters.location);
        }
        
        // Apply quantity filters
        if (filters.minQuantity !== undefined) {
          filtered = filtered.filter(item => item.currentQuantity >= filters.minQuantity!);
        }
        if (filters.maxQuantity !== undefined) {
          filtered = filtered.filter(item => item.currentQuantity <= filters.maxQuantity!);
        }
        
        // Apply status filter
        if (filters.status?.length) {
          filtered = filtered.filter(item => {
            const itemStatus = item.currentQuantity === 0 ? 'out_of_stock' :
                             item.currentQuantity <= 10 ? 'low_stock' : 'in_stock';
            return filters.status!.includes(itemStatus as any);
          });
        }
        
        // Apply sorting
        if (filters.sortBy) {
          filtered.sort((a, b) => {
            const aValue = a[filters.sortBy!];
            const bValue = b[filters.sortBy!];
            
            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            if (aValue > bValue) comparison = 1;
            
            return filters.sortOrder === 'desc' ? -comparison : comparison;
          });
        }
        
        // Calculate pagination
        const page = filters.page || 1;
        const limit = filters.limit || 10;
        const total = filtered.length;
        const totalPages = Math.ceil(total / limit);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        
        const paginatedItems = filtered.slice(startIndex, endIndex);
        
        set({
          filteredItems: paginatedItems,
          pagination: {
            page,
            limit,
            total,
            totalPages,
          },
        });
      },
      
      // Pagination Actions
      setPage: (page: number) => {
        get().setFilters({ page });
      },
      
      setLimit: (limit: number) => {
        get().setFilters({ limit, page: 1 }); // Reset to first page when changing limit
      },
      
      // Utility Actions
      clearError: () => {
        set({ error: null });
      },
      
      // Product Integration Actions
      loadProductsWithStock: async (accountId?: string) => {
        set({ productsLoading: true, error: null });
        try {
          // MOCK DATA FOR DEVELOPMENT - Replace with real API call
          // const products = await apiService.getProductsWithStock(accountId);

          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 700));

          set({
            productsWithStock: mockMLProducts,
            productsLoading: false
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar produtos com estoque';
          set({
            error: errorMessage,
            productsLoading: false
          });
        }
      },

      loadStockCalculations: async () => {
        set({ calculationsLoading: true, error: null });
        try {
          // MOCK DATA FOR DEVELOPMENT - Replace with real API call
          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 500));

          const { productsWithStock } = get();
          const calculations: Record<string, StockCalculation> = {};

          // Calculate stock metrics for each product (using mock data)
          for (const product of productsWithStock) {
            if (product.stockCalculation) {
              calculations[product.id] = product.stockCalculation;
            }
          }

          set({
            stockCalculations: calculations,
            calculationsLoading: false
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao calcular métricas de estoque';
          set({
            error: errorMessage,
            calculationsLoading: false
          });
        }
      },

      loadStockAlerts: async () => {
        set({ alertsLoading: true, error: null });
        try {
          // MOCK DATA FOR DEVELOPMENT - Replace with real API call
          // const alerts = await apiService.getStockAlerts();

          // Simulate API delay
          await new Promise(resolve => setTimeout(resolve, 400));

          // Generate mock alerts from products with stock issues
          const { productsWithStock } = get();
          const mockAlerts: StockAlert[] = productsWithStock
            .filter(product => product.availableQuantity <= 5 || (product.stockCalculation?.gap && product.stockCalculation.gap > 0))
            .map((product, index) => ({
              id: `alert-${product.id}`,
              productId: product.id,
              type: product.availableQuantity === 0 ? 'out_of_stock' :
                    product.availableQuantity <= 5 ? 'low_stock' : 'stock_gap',
              severity: product.availableQuantity === 0 ? 'critical' :
                       product.availableQuantity <= 2 ? 'high' : 'medium',
              message: product.availableQuantity === 0
                ? `Produto ${product.title} está sem estoque`
                : product.availableQuantity <= 5
                ? `Produto ${product.title} com estoque baixo (${product.availableQuantity} unidades)`
                : `Produto ${product.title} com gap de estoque (${product.stockCalculation?.gap} unidades)`,
              isRead: index % 3 === 0, // Some alerts read, some unread
              createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
              updatedAt: new Date().toISOString(),
            }));

          set({
            stockAlerts: mockAlerts,
            alertsLoading: false
          });

          // INTEGRATION: Process alerts through notification system
          try {
            const { useNotificationStore } = await import('./notificationStore');
            const notificationStore = useNotificationStore.getState();
            notificationStore.processStockAlerts(mockAlerts);
          } catch (error) {
            console.warn('Failed to process stock alerts through notification system:', error);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao carregar alertas de estoque';
          set({
            error: errorMessage,
            alertsLoading: false
          });
        }
      },

      updateProductStock: async (productId: string, quantity: number, reason?: string) => {
        set({ error: null });
        try {
          const updateData = {
            quantity,
            reason: reason || 'Ajuste manual de estoque'
          };

          await apiService.updateProductStock(productId, updateData);

          // Update local state
          const { productsWithStock } = get();
          const updatedProducts = productsWithStock.map(product =>
            product.id === productId
              ? { ...product, currentStock: quantity }
              : product
          );

          set({ productsWithStock: updatedProducts });

          // Refresh calculations and alerts
          get().loadStockCalculations();
          get().loadStockAlerts();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao atualizar estoque do produto';
          set({ error: errorMessage });
          throw error;
        }
      },

      syncProductStock: async (productId: string) => {
        set({ error: null });
        try {
          const syncedProduct = await apiService.syncProductStock(productId);

          // Update local state
          const { productsWithStock } = get();
          const updatedProducts = productsWithStock.map(product =>
            product.id === productId ? syncedProduct : product
          );

          set({ productsWithStock: updatedProducts });

          // Refresh calculations
          get().loadStockCalculations();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao sincronizar estoque';
          set({ error: errorMessage });
          throw error;
        }
      },

      calculateStockGap: async (productId: string): Promise<StockCalculation | null> => {
        set({ error: null });
        try {
          const calculation = await apiService.calculateStockGap(productId);

          // Update local calculations
          const { stockCalculations } = get();
          set({
            stockCalculations: {
              ...stockCalculations,
              [productId]: calculation
            }
          });

          return calculation;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao calcular gap de estoque';
          set({ error: errorMessage });
          return null;
        }
      },

      markAlertAsRead: async (alertId: string) => {
        set({ error: null });
        try {
          await apiService.markStockAlertAsRead(alertId);

          // Update local state
          const { stockAlerts } = get();
          const updatedAlerts = stockAlerts.map(alert =>
            alert.id === alertId
              ? { ...alert, isRead: true }
              : alert
          );

          set({ stockAlerts: updatedAlerts });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Erro ao marcar alerta como lido';
          set({ error: errorMessage });
        }
      },

      refreshAll: async () => {
        await Promise.all([
          get().loadStockItems(),
          get().loadStatistics(),
          get().loadProductsWithStock(),
          get().loadStockCalculations(),
          get().loadStockAlerts(),
        ]);
      },
    }),
    {
      name: 'stock-store',
    }
  )
);

export default useStockStore;
