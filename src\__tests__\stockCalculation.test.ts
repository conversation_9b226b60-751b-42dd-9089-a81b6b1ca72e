/**
 * Testes Unitários - Motor de Cálculo de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { StockCalculationService } from '../services/stockCalculationService';
import { SalesAnalysisService } from '../services/salesAnalysisService';
import { 
  StockConfiguration, 
  SalesAnalysis,
  CurrentStock
} from '../types/stock';

// Mock do SalesAnalysisService
jest.mock('../services/salesAnalysisService');

// Mock do logger
jest.mock('../utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}));

describe('Motor de Cálculo de Estoque', () => {
  let stockCalculationService: StockCalculationService;
  let mockSalesAnalysisService: jest.Mocked<SalesAnalysisService>;

  const mockTenantId = 'tenant-123';
  const mockMlItemId = 'MLB123456789';
  const mockSku = 'PRODUTO-001';

  beforeEach(() => {
    jest.clearAllMocks();
    mockSalesAnalysisService = new SalesAnalysisService() as jest.Mocked<SalesAnalysisService>;
    stockCalculationService = new StockCalculationService();
    // Substitui o serviço de análise de vendas pelo mock
    (stockCalculationService as any).salesAnalysisService = mockSalesAnalysisService;
  });

  describe('Cálculo de Estoque Ideal', () => {
    const mockConfiguration: StockConfiguration = {
      id: 'config-1',
      tenantId: mockTenantId,
      mlItemId: mockMlItemId,
      sku: mockSku,
      coverageDays: 30,
      safetyStockDays: 7,
      minStockLevel: 5,
      maxStockLevel: 100,
      analysisWindowDays: 60,
      seasonalityFactor: 1.2,
      trendWeight: 0.3,
      criticalGapThreshold: 10,
      warningGapThreshold: 5,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const mockCurrentStock: CurrentStock = {
      mlItemId: mockMlItemId,
      sku: mockSku,
      currentQuantity: 25,
      availableQuantity: 20,
      reservedQuantity: 5,
      inTransitQuantity: 10,
      lastUpdated: new Date()
    };

    const mockSalesAnalysis: SalesAnalysis = {
      mlItemId: mockMlItemId,
      sku: mockSku,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-02-29'),
      totalDays: 60,
      totalQuantitySold: 120,
      totalRevenue: 12000,
      averageDailySales: 2.0,
      medianDailySales: 1.8,
      salesVariance: 0.5,
      salesStandardDeviation: 0.7,
      maxDailySales: 5,
      minDailySales: 0,
      trendDirection: 'increasing',
      trendStrength: 0.3,
      seasonalityDetected: true,
      seasonalityPattern: {
        type: 'weekly',
        peakPeriods: ['friday', 'saturday'],
        lowPeriods: ['monday', 'tuesday'],
        seasonalityFactor: 1.2
      }
    };

    beforeEach(() => {
      mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockResolvedValue(mockSalesAnalysis);
    });

    test('deve calcular estoque ideal com vendas estáveis', async () => {
      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        mockConfiguration
      );

      expect(result).toBeDefined();
      expect(result.mlItemId).toBe(mockMlItemId);
      expect(result.sku).toBe(mockSku);
      expect(result.currentStock).toBe(25);
      expect(result.inTransitStock).toBe(10);
      expect(result.averageDailySales).toBe(2.0);
      expect(result.idealStock).toBeGreaterThan(0);
      expect(result.status).toBeDefined();
      expect(result.priority).toBeDefined();
    });

    test('deve identificar estoque crítico', async () => {
      const lowStockConfig: StockConfiguration = {
        ...mockConfiguration,
        criticalGapThreshold: 5
      };

      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        lowStockConfig
      );

      if (result.stockGap > 5) {
        expect(result.status).toBe('critical');
        expect(result.priority).toBe('high');
        expect(result.recommendedAction).toBe('urgent_restock');
      }
    });

    test('deve identificar estoque excessivo', async () => {
      const highCurrentStock: CurrentStock = {
        ...mockCurrentStock,
        currentQuantity: 200,
        availableQuantity: 190,
        inTransitQuantity: 50
      };

      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        mockConfiguration
      );

      if (result.stockGap < 0) {
        expect(result.status).toBe('excess');
        expect(result.recommendedAction).toBe('reduce_stock');
      }
    });

    test('deve tratar vendas zero adequadamente', async () => {
      const mockSalesAnalysisZero: SalesAnalysis = {
        ...mockSalesAnalysis,
        averageDailySales: 0,
        totalQuantitySold: 0
      };

      mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockResolvedValue(mockSalesAnalysisZero);

      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        mockConfiguration
      );

      expect(result.averageDailySales).toBe(0);
      expect(result.adjustedDailySales).toBeGreaterThan(0); // Deve ter valor mínimo
    });

    test('deve aplicar estoque mínimo quando necessário', async () => {
      const lowSalesConfig: StockConfiguration = {
        ...mockConfiguration,
        minStockLevel: 50
      };

      const lowSalesAnalysis: SalesAnalysis = {
        ...mockSalesAnalysis,
        averageDailySales: 0.5
      };

      mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockResolvedValue(lowSalesAnalysis);

      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        lowSalesConfig
      );

      expect(result.idealStock).toBeGreaterThanOrEqual(50);
    });

    test('deve calcular tendência crescente corretamente', async () => {
      const increasingAnalysis: SalesAnalysis = {
        ...mockSalesAnalysis,
        trendDirection: 'increasing',
        trendStrength: 0.5
      };

      mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockResolvedValue(increasingAnalysis);

      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        mockConfiguration
      );

      expect(result.adjustedDailySales).toBeGreaterThan(result.averageDailySales);
    });

    test('deve calcular tendência decrescente corretamente', async () => {
      const decreasingAnalysis: SalesAnalysis = {
        ...mockSalesAnalysis,
        trendDirection: 'decreasing',
        trendStrength: 0.4
      };

      mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockResolvedValue(decreasingAnalysis);

      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        mockConfiguration
      );

      expect(result.adjustedDailySales).toBeLessThan(result.averageDailySales);
    });
  });

  describe('Validações e Tratamento de Erros', () => {
    test('deve tratar erro na análise de vendas', async () => {
      mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockRejectedValue(
        new Error('Erro na análise de vendas')
      );

      const mockConfiguration: StockConfiguration = {
        id: 'config-1',
        tenantId: mockTenantId,
        mlItemId: mockMlItemId,
        sku: mockSku,
        coverageDays: 30,
        safetyStockDays: 7,
        minStockLevel: 5,
        analysisWindowDays: 60,
        seasonalityFactor: 1.0,
        trendWeight: 0.3,
        criticalGapThreshold: 10,
        warningGapThreshold: 5,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const mockCurrentStock: CurrentStock = {
        mlItemId: mockMlItemId,
        sku: mockSku,
        currentQuantity: 25,
        availableQuantity: 20,
        reservedQuantity: 5,
        inTransitQuantity: 10,
        lastUpdated: new Date()
      };

      await expect(
        stockCalculationService.calculateIdealStock(
          mockTenantId,
          mockMlItemId,
          mockConfiguration
        )
      ).rejects.toThrow('Erro na análise de vendas');
    });

    test('deve validar parâmetros de entrada', async () => {
      const invalidConfig = null as any;
      const validStock: CurrentStock = {
        mlItemId: mockMlItemId,
        sku: mockSku,
        currentQuantity: 25,
        availableQuantity: 20,
        reservedQuantity: 5,
        inTransitQuantity: 10,
        lastUpdated: new Date()
      };

      await expect(
        stockCalculationService.calculateIdealStock(
          mockTenantId,
          mockMlItemId,
          invalidConfig
        )
      ).rejects.toThrow();
    });
  });

  describe('Cálculos Matemáticos', () => {
    test('deve calcular dias de cobertura corretamente', async () => {
      const mockConfiguration: StockConfiguration = {
        id: 'config-1',
        tenantId: mockTenantId,
        mlItemId: mockMlItemId,
        sku: mockSku,
        coverageDays: 30,
        safetyStockDays: 7,
        minStockLevel: 5,
        analysisWindowDays: 60,
        seasonalityFactor: 1.0,
        trendWeight: 0.0,
        criticalGapThreshold: 10,
        warningGapThreshold: 5,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const mockCurrentStock: CurrentStock = {
        mlItemId: mockMlItemId,
        sku: mockSku,
        currentQuantity: 60,
        availableQuantity: 60,
        reservedQuantity: 0,
        inTransitQuantity: 0,
        lastUpdated: new Date()
      };

      const stableAnalysis: SalesAnalysis = {
        mlItemId: mockMlItemId,
        sku: mockSku,
        startDate: new Date(),
        endDate: new Date(),
        totalDays: 30,
        totalQuantitySold: 60,
        totalRevenue: 6000,
        averageDailySales: 2.0,
        medianDailySales: 2.0,
        salesVariance: 0.1,
        salesStandardDeviation: 0.3,
        maxDailySales: 3,
        minDailySales: 1,
        trendDirection: 'stable',
        trendStrength: 0.0,
        seasonalityDetected: false
      };

      mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockResolvedValue(stableAnalysis);

      const result = await stockCalculationService.calculateIdealStock(
        mockTenantId,
        mockMlItemId,
        mockConfiguration
      );

      // Com vendas de 2/dia e estoque de 60, deve ter 30 dias de cobertura
      expect(result.coverageDaysAvailable).toBeCloseTo(30, 0);
    });

    test('deve determinar status baseado no gap', async () => {
      const testCases = [
        { currentStock: 10, expectedStatus: 'critical' },
        { currentStock: 65, expectedStatus: 'warning' },
        { currentStock: 70, expectedStatus: 'optimal' },
        { currentStock: 150, expectedStatus: 'excess' }
      ];

      for (const testCase of testCases) {
        const customStock: CurrentStock = {
          mlItemId: mockMlItemId,
          sku: mockSku,
          currentQuantity: testCase.currentStock,
          availableQuantity: testCase.currentStock,
          reservedQuantity: 0,
          inTransitQuantity: 0,
          lastUpdated: new Date()
        };

        const customConfig: StockConfiguration = {
          id: 'config-1',
          tenantId: mockTenantId,
          mlItemId: mockMlItemId,
          sku: mockSku,
          coverageDays: 30,
          safetyStockDays: 5,
          minStockLevel: 5,
          analysisWindowDays: 30,
          seasonalityFactor: 1.0,
          trendWeight: 0.0,
          criticalGapThreshold: 10,
          warningGapThreshold: 5,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        };

        const stableAnalysis: SalesAnalysis = {
          mlItemId: mockMlItemId,
          sku: mockSku,
          startDate: new Date(),
          endDate: new Date(),
          totalDays: 30,
          totalQuantitySold: 60,
          totalRevenue: 6000,
          averageDailySales: 2.0,
          medianDailySales: 2.0,
          salesVariance: 0.1,
          salesStandardDeviation: 0.3,
          maxDailySales: 3,
          minDailySales: 1,
          trendDirection: 'stable',
          trendStrength: 0.0,
          seasonalityDetected: false
        };

        mockSalesAnalysisService.analyzeSalesHistory = jest.fn().mockResolvedValue(stableAnalysis);

        const result = await stockCalculationService.calculateIdealStock(
          mockTenantId,
          mockMlItemId,
          customConfig
        );

        expect(result.status).toBe(testCase.expectedStatus);
      }
    });
  });
});