{"name": "magnow-backend", "version": "1.0.0", "description": "Sistema SaaS de controle inteligente de estoque integrado ao Mercado Livre", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:open": "jest --coverage && npx opener coverage/lcov-report/index.html", "test:integration": "jest --testPathPattern=integration", "test:integration:coverage": "jest --config=jest.integration.config.js --coverage", "test:unit": "jest --testPathPattern=services", "test:ci": "jest --config=jest.integration.config.js --coverage --ci --watchAll=false --passWithNoTests", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:seed": "prisma db seed", "db:studio": "prisma studio", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:backend": "docker-compose up -d postgres redis && docker-compose up backend", "docker:dev": "docker-compose --profile development up -d", "docker:prod": "docker-compose --profile frontend --profile nginx up -d", "docker:clean": "docker-compose down -v && docker system prune -f"}, "keywords": ["mercado-livre", "estoque", "saas", "multi-tenant", "o<PERSON>h"], "author": "Magnow Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.22.0", "@types/redis": "^4.0.10", "@types/swagger-ui-express": "^4.1.8", "axios": "^1.6.8", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.2.0", "helmet": "^7.1.0", "joi": "^17.12.2", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "redis": "^4.7.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^9.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xlsx": "^0.18.5", "zod": "^4.0.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/crypto-js": "^4.2.2", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.6", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.11", "@types/node": "^20.11.30", "@types/node-cron": "^3.0.11", "@types/supertest": "^6.0.2", "@types/swagger-jsdoc": "^6.0.4", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.3.1", "@typescript-eslint/parser": "^7.3.1", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.0", "prettier": "^3.2.5", "prisma": "^5.22.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "tsx": "^4.7.1", "typescript": "^5.4.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}