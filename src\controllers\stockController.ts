/**
 * Controller de Estoque
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { Request, Response, NextFunction } from 'express';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import { StockCalculationService } from '../services/stockCalculationService';
import { StockGapService } from '../services/stockGapService';
import { StockAlertService } from '../services/stockAlertService';
import { getCachedPrismaService } from '../services/cachedPrismaService';
import { 
  StockCalculationParams,
  StockGapAnalysisParams,
  StockStatus,
  StockPriority,
  AlertSeverity
} from '../types/stock';
import { ValidationError, NotFoundError } from '../middleware/errorHandler';

export class StockController {
  private prisma: PrismaClient;
  private stockCalculationService: StockCalculationService;
  private stockGapService: StockGapService;
  private stockAlertService: StockAlertService;

  constructor() {
    this.prisma = new PrismaClient();
    this.stockCalculationService = new StockCalculationService(this.prisma);
    this.stockGapService = new StockGapService(this.prisma);
    this.stockAlertService = new StockAlertService(this.prisma);
  }

  /**
   * GET /api/stock/calculations
   * Lista cálculos de estoque com filtros
   */
  public getCalculations = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        mlItemIds,
        status,
        priority,
        limit = 50,
        offset = 0,
        sortBy = 'calculatedAt',
        sortOrder = 'desc'
      } = req.query;

      logger.info('Buscando cálculos de estoque', { 
        tenantId, 
        userId,
        filters: { mlItemIds, status, priority, limit, offset }
      });

      // Valida parâmetros
      const parsedLimit = Math.min(parseInt(limit as string) || 50, 100);
      const parsedOffset = Math.max(parseInt(offset as string) || 0, 0);

      // Busca cálculos do banco
      const where: any = { tenantId };
      
      if (mlItemIds) {
        const itemIds = Array.isArray(mlItemIds) ? mlItemIds : [mlItemIds];
        where.mlItemId = { in: itemIds };
      }

      const calculations = await this.prisma.stockCalculation.findMany({
        where: {
          tenantId,
          ...(mlItemIds && {
            product: {
              mlId: { in: Array.isArray(mlItemIds) ? mlItemIds : [mlItemIds] }
            }
          })
        },
        orderBy: { [sortBy as string]: sortOrder },
        take: parsedLimit,
        skip: parsedOffset,
        include: {
          product: {
            select: {
              mlId: true,
              title: true,
              sku: true,
              price: true,
              status: true
            }
          }
        }
      });

      // Converte para formato de resposta
      const results = calculations.map(calc => ({
        id: calc.id,
        mlItemId: calc.product.mlId,
        sku: calc.product.sku,
        product: calc.product,
        calculation: {
          mlItemId: calc.product.mlId,
          sku: calc.product.sku || '',
          tenantId: calc.tenantId,
          currentStock: calc.currentStock,
          averageDailySales: Number(calc.averageSales),
          idealStock: calc.idealStock,
          stockGap: calc.stockGap,
          daysOfCoverage: calc.daysOfCoverage,
          safetyStock: calc.safetyStock,
          unitsInTransit: calc.unitsInTransit,
          calculatedAt: calc.createdAt,
          status: calc.stockGap < 0 ? 'critical' : calc.stockGap === 0 ? 'optimal' : 'excess',
          priority: calc.stockGap < -10 ? 'high' : calc.stockGap < 0 ? 'medium' : 'low',
          confidence: 0.8,
          metadata: {
            calculationVersion: '1.0',
            dataQuality: 'good'
          }
        },
        calculatedAt: calc.createdAt,
        triggeredBy: 'manual',
        calculationTimeMs: 0
      }));

      // Conta total para paginação
      const total = await this.prisma.stockCalculation.count({ 
        where: {
          tenantId,
          ...(mlItemIds && {
            product: {
              mlId: { in: Array.isArray(mlItemIds) ? mlItemIds : [mlItemIds] }
            }
          })
        }
      });

      res.json({
        success: true,
        data: {
          calculations: results,
          pagination: {
            total,
            limit: parsedLimit,
            offset: parsedOffset,
            hasMore: parsedOffset + parsedLimit < total
          }
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * POST /api/stock/calculate
   * Executa cálculo de estoque para produtos específicos
   */
  public calculateStock = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        mlItemIds,
        forceRecalculation = false,
        includeInactive = false
      } = req.body;

      logger.info('Executando cálculo de estoque', { 
        tenantId, 
        userId,
        mlItemIds: mlItemIds?.length || 'todos',
        forceRecalculation
      });

      const params: StockCalculationParams = {
        mlItemIds,
        forceRecalculation,
        includeInactive
      };

      // Executa cálculo
      const result = await this.stockCalculationService.calculateBatchIdealStock(tenantId, params);

      logger.info('Cálculo de estoque concluído', {
        tenantId,
        totalItems: result.totalItems,
        successful: result.successfulCalculations,
        failed: result.failedCalculations,
        executionTimeMs: result.executionTimeMs
      });

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/stock/calculate/:mlItemId
   * Calcula estoque ideal para produto específico
   */
  public calculateSingleProduct = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { mlItemId } = req.params;
      const { configuration } = req.body;

      if (!mlItemId) {
        throw new ValidationError('mlItemId é obrigatório');
      }

      logger.info('Calculando estoque para produto específico', { 
        tenantId, 
        userId,
        mlItemId
      });

      const result = await this.stockCalculationService.calculateIdealStock(
        tenantId,
        mlItemId,
        configuration
      );

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/stock/gap/report
   * Gera relatório de gap de estoque
   */
  public getGapReport = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        mlItemIds,
        status,
        priority,
        minGap,
        maxGap,
        sortBy = 'gap',
        sortOrder = 'desc',
        limit = 100,
        format = 'json'
      } = req.query;

      logger.info('Gerando relatório de gap', { tenantId, userId });

      const params: StockGapAnalysisParams = {
        mlItemIds: mlItemIds ? (Array.isArray(mlItemIds) ? mlItemIds : [mlItemIds]) : undefined,
        status: status ? (Array.isArray(status) ? status : [status]) : undefined,
        priority: priority ? (Array.isArray(priority) ? priority : [priority]) : undefined,
        minGap: minGap ? parseFloat(minGap as string) : undefined,
        maxGap: maxGap ? parseFloat(maxGap as string) : undefined,
        sortBy: sortBy as any,
        sortOrder: sortOrder as any,
        limit: Math.min(parseInt(limit as string) || 100, 500)
      };

      if (format === 'csv') {
        const csvData = await this.stockGapService.exportGapReport(tenantId, 'csv', params);
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', 'attachment; filename="gap-report.csv"');
        res.send(csvData);
      } else {
        const report = await this.stockGapService.generateGapReport(tenantId, params);
        res.json({
          success: true,
          data: report
        });
      }
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/stock/gap/critical
   * Lista produtos com gap crítico
   */
  public getCriticalGapProducts = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { limit = 50 } = req.query;

      logger.info('Buscando produtos com gap crítico', { tenantId, userId });

      const products = await this.stockGapService.getCriticalGapProducts(
        tenantId,
        Math.min(parseInt(limit as string) || 50, 100)
      );

      res.json({
        success: true,
        data: {
          products,
          count: products.length
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * POST /api/stock/gap/analyze
   * Analisa gap de produtos específicos
   */
  public analyzeProductsGap = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { mlItemIds, forceRecalculation = false } = req.body;

      if (!mlItemIds || !Array.isArray(mlItemIds) || mlItemIds.length === 0) {
        throw new ValidationError('mlItemIds deve ser um array não vazio');
      }

      logger.info('Analisando gap de produtos específicos', { 
        tenantId, 
        userId,
        itemCount: mlItemIds.length
      });

      const products = await this.stockGapService.analyzeProductsGap(
        tenantId,
        mlItemIds,
        forceRecalculation
      );

      res.json({
        success: true,
        data: {
          products,
          count: products.length
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/stock/alerts
   * Lista alertas ativos
   */
  public getAlerts = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        severity,
        mlItemIds,
        limit = 50
      } = req.query;

      logger.info('Buscando alertas ativos', { tenantId, userId });

      const filters = {
        severity: severity ? (Array.isArray(severity) ? severity : [severity]) : undefined,
        mlItemIds: mlItemIds ? (Array.isArray(mlItemIds) ? mlItemIds : [mlItemIds]) : undefined,
        limit: Math.min(parseInt(limit as string) || 50, 100)
      };

      const alerts = await this.stockAlertService.getActiveAlerts(tenantId, filters);

      res.json({
        success: true,
        data: {
          alerts,
          count: alerts.length
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * POST /api/stock/alerts
   * Cria alerta manual
   */
  public createAlert = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { mlItemId, severity, message } = req.body;

      if (!mlItemId || !severity || !message) {
        throw new ValidationError('mlItemId, severity e message são obrigatórios');
      }

      if (!['critical', 'warning', 'info'].includes(severity)) {
        throw new ValidationError('severity deve ser: critical, warning ou info');
      }

      logger.info('Criando alerta manual', { tenantId, userId, mlItemId, severity });

      const alert = await this.stockAlertService.createManualAlert(
        tenantId,
        mlItemId,
        severity as AlertSeverity,
        message,
        userId
      );

      res.status(201).json({
        success: true,
        data: alert
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * PUT /api/stock/alerts/:alertId/resolve
   * Resolve alerta específico
   */
  public resolveAlert = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { alertId } = req.params;
      const { resolution } = req.body;

      if (!alertId) {
        throw new ValidationError('alertId é obrigatório');
      }

      if (!resolution) {
        throw new ValidationError('resolution é obrigatório');
      }

      logger.info('Resolvendo alerta', { tenantId, userId, alertId });

      await this.stockAlertService.resolveAlert(tenantId, alertId, resolution, userId);

      res.json({
        success: true,
        message: 'Alerta resolvido com sucesso'
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * POST /api/stock/alerts/process
   * Processa alertas para o tenant
   */
  public processAlerts = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;

      logger.info('Processando alertas manualmente', { tenantId, userId });

      const result = await this.stockAlertService.processAlerts(tenantId);

      res.json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * GET /api/stock/config
   * Busca configurações de estoque
   */
  public getStockConfig = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;

      logger.info('Buscando configurações de estoque', { tenantId, userId });

      // Busca configuração global do sistema
      const systemConfig = await this.prisma.stockSystemConfiguration.findFirst({
        where: { tenantId }
      });

      // Busca configuração de alertas
      const alertConfig = await this.prisma.stockAlertConfiguration.findFirst({
        where: { tenantId }
      });

      res.json({
        success: true,
        data: {
          systemConfiguration: systemConfig,
          alertConfiguration: alertConfig
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * PUT /api/stock/config
   * Atualiza configurações de estoque
   */
  public updateStockConfig = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const { systemConfiguration, alertConfiguration } = req.body;

      logger.info('Atualizando configurações de estoque', { tenantId, userId });

      const results: any = {};

      // Atualiza configuração do sistema se fornecida
      if (systemConfiguration) {
        results.systemConfiguration = await this.prisma.stockSystemConfiguration.upsert({
          where: { tenantId },
          update: systemConfiguration,
          create: {
            tenantId,
            ...systemConfiguration
          }
        });
      }

      // Atualiza configuração de alertas se fornecida
      if (alertConfiguration) {
        results.alertConfiguration = await this.stockAlertService.configureAlerts(
          tenantId,
          alertConfiguration
        );
      }

      res.json({
        success: true,
        data: results
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * POST /api/stock/jobs/setup
   * Configura job de recálculo automático
   */
  public setupAutomaticJob = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId } = req.user!;
      const { intervalHours = 6, enabled = true } = req.body;

      // TODO: Implementar StockSchedulerService quando necessário
      // const schedulerService = new StockSchedulerService(this.stockCalculationService, this.stockAlertService);
      // await schedulerService.setupTenantJob(tenantId, {
      //   name: 'automatic_recalculation',
      //   tenantId,
      //   intervalMs: intervalHours * 60 * 60 * 1000,
      //   enabled
      // });

      logger.info('Job de recálculo automático configurado', {
        tenantId,
        intervalHours,
        enabled
      });

      res.json({
        success: true,
        message: 'Job de recálculo automático configurado com sucesso',
        data: {
          intervalHours,
          enabled,
          nextExecution: new Date(Date.now() + intervalHours * 60 * 60 * 1000)
        }
      });

    } catch (error: any) {
      logger.error('Erro ao configurar job automático', {
        tenantId: req.user?.tenantId,
        error: error.message
      });
      next(error);
    }
  };

  /**
   * POST /api/stock/jobs/execute
   * Executa job de recálculo manualmente
   */
  public executeManualJob = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;

      logger.info('Iniciando execução manual de job de recálculo', { tenantId, userId });

      // Executa recálculo em lote
      const result = await this.stockCalculationService.calculateBatchIdealStock(tenantId, {});

      logger.info('Job manual concluído', {
        tenantId,
        userId,
        totalItems: result.totalItems,
        successful: result.successfulCalculations,
        failed: result.failedCalculations
      });

      res.json({
        success: true,
        message: 'Job de recálculo executado com sucesso',
        data: {
          executionId: `manual_${Date.now()}`,
          totalItems: result.totalItems,
          successful: result.successfulCalculations,
          failed: result.failedCalculations,
          executionTimeMs: result.executionTimeMs,
          executedAt: new Date()
        }
      });

    } catch (error: any) {
      logger.error('Erro na execução manual do job', {
        tenantId: req.user?.tenantId,
        userId: req.user?.userId,
        error: error.message
      });
      next(error);
    }
  };

  /**
   * GET /api/stock/metrics
   * Busca métricas do sistema de estoque
   */
  public getStockMetrics = async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { tenantId, userId } = req.user!;
      const {
        startDate,
        endDate = new Date().toISOString()
      } = req.query;

      logger.info('Buscando métricas de estoque', { tenantId, userId });

      const start = startDate ? new Date(startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      const end = new Date(endDate as string);

      // Busca estatísticas de alertas
      const alertStats = await this.stockAlertService.getAlertStatistics(tenantId, start, end);

      // Busca impacto financeiro
      const financialImpact = await this.stockGapService.calculateFinancialImpact(tenantId);

      // Busca métricas gerais do sistema
      const systemMetrics = await this.calculateSystemMetrics(tenantId, start, end);

      res.json({
        success: true,
        data: {
          alertStatistics: alertStats,
          financialImpact,
          systemMetrics,
          period: {
            start,
            end
          }
        }
      });
    } catch (error) {
      next(error);
    }
  };

  /**
   * Calcula métricas gerais do sistema
   */
  private async calculateSystemMetrics(tenantId: string, startDate: Date, endDate: Date) {
    try {
      // Total de produtos
      const totalProducts = await this.prisma.product.count({
        where: { tenantId, status: 'active' }
      });

      // Cálculos recentes
      const recentCalculations = await this.prisma.stockCalculation.count({
        where: {
          tenantId,
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      });

      // Tempo médio de cálculo (estimado)
      const calculations = await this.prisma.stockCalculation.findMany({
        where: {
          tenantId,
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        select: { 
          id: true,
          createdAt: true
        }
      });

      const averageCalculationTime = 1000; // Valor estimado em ms

      // Calcula taxa de sucesso (assumindo que todos os registros são sucessos)
      const successfulCalculations = calculations.length;
      const calculationSuccessRate = calculations.length > 0 ? 1.0 : 0;

      return {
        totalProducts,
        recentCalculations,
        averageCalculationTimeMs: averageCalculationTime,
        calculationSuccessRate
      };
    } catch (error) {
      logger.error('Erro ao calcular métricas do sistema', { error, tenantId });
      return {
        totalProducts: 0,
        recentCalculations: 0,
        averageCalculationTimeMs: 0,
        calculationSuccessRate: 0
      };
    }
  }
}