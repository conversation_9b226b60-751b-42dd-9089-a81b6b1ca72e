import React, { useEffect, useState } from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '../../lib/utils';
import { 
  CheckCircleIcon, 
  ExclamationTriangleIcon, 
  InformationCircleIcon,
  XCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';

const toastVariants = cva(
  'max-w-sm w-full pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transition-all duration-300 ease-in-out',
  {
    variants: {
      variant: {
        default: 'bg-surface border-border text-foreground',
        success: 'bg-success-50 border-success-200 text-success-900',
        error: 'bg-danger-50 border-danger-200 text-danger-900',
        warning: 'bg-warning-50 border-warning-200 text-warning-900',
        info: 'bg-info-50 border-info-200 text-info-900',
      },
      size: {
        sm: 'text-sm',
        default: 'text-base',
        lg: 'text-lg',
      },
      position: {
        'top-left': 'rounded-lg shadow-lg border',
        'top-right': 'rounded-lg shadow-lg border',
        'bottom-left': 'rounded-lg shadow-lg border',
        'bottom-right': 'rounded-lg shadow-lg border',
        'top-center': 'rounded-lg shadow-lg border',
        'bottom-center': 'rounded-lg shadow-lg border',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      position: 'top-right',
    },
  }
);

const toastIconVariants = cva(
  'h-5 w-5 flex-shrink-0',
  {
    variants: {
      variant: {
        default: 'text-foreground',
        success: 'text-success-500',
        error: 'text-danger-500',
        warning: 'text-warning-500',
        info: 'text-info-500',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

const toastActionVariants = cva(
  'text-sm font-medium transition-colors duration-200 hover:underline focus:outline-none focus:underline',
  {
    variants: {
      variant: {
        default: 'text-foreground hover:text-foreground/80',
        success: 'text-success-700 hover:text-success-600',
        error: 'text-danger-700 hover:text-danger-600',
        warning: 'text-warning-700 hover:text-warning-600',
        info: 'text-info-700 hover:text-info-600',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

export interface ToastProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof toastVariants> {
  id: string;
  title: string;
  message?: string;
  duration?: number;
  onClose?: (id: string) => void;
  action?: {
    label: string;
    onClick: () => void;
  };
  showIcon?: boolean;
  closable?: boolean;
  autoClose?: boolean;
}

const getToastIcon = (variant: ToastProps['variant']) => {
  switch (variant) {
    case 'success':
      return CheckCircleIcon;
    case 'error':
      return XCircleIcon;
    case 'warning':
      return ExclamationTriangleIcon;
    case 'info':
    default:
      return InformationCircleIcon;
  }
};

export const Toast = React.forwardRef<HTMLDivElement, ToastProps>(
  ({ 
    id,
    variant,
    size,
    position,
    title,
    message,
    duration = 5000,
    onClose,
    action,
    showIcon = true,
    closable = true,
    autoClose = true,
    className,
    ...props 
  }, ref) => {
    const [isVisible, setIsVisible] = useState(true);
    const [isRemoving, setIsRemoving] = useState(false);

    useEffect(() => {
      if (autoClose && duration > 0) {
        const timer = setTimeout(() => {
          handleClose();
        }, duration);

        return () => clearTimeout(timer);
      }
    }, [autoClose, duration]);

    const handleClose = () => {
      setIsRemoving(true);
      setTimeout(() => {
        setIsVisible(false);
        onClose?.(id);
      }, 300);
    };

    if (!isVisible) return null;

    const IconComponent = getToastIcon(variant);

    return (
      <div
        ref={ref}
        className={cn(
          toastVariants({ variant, size, position, className }),
          isRemoving && 'transform translate-x-full opacity-0'
        )}
        role="alert"
        aria-live="polite"
        aria-atomic="true"
        {...props}
      >
        <div className="p-4">
          <div className="flex items-start">
            {showIcon && (
              <div className="flex-shrink-0">
                <IconComponent className={toastIconVariants({ variant })} />
              </div>
            )}
            <div className={cn('w-0 flex-1', showIcon && 'ml-3')}>
              <div className="pt-0.5">
                <p className="text-sm font-medium">
                  {title}
                </p>
                {message && (
                  <p className="mt-1 text-sm opacity-90">
                    {message}
                  </p>
                )}
                {action && (
                  <div className="mt-3">
                    <button
                      type="button"
                      onClick={action.onClick}
                      className={toastActionVariants({ variant })}
                    >
                      {action.label}
                    </button>
                  </div>
                )}
              </div>
            </div>
            {closable && (
              <div className="ml-4 flex-shrink-0 flex">
                <button
                  type="button"
                  onClick={handleClose}
                  className={cn(
                    'inline-flex transition-colors duration-200 hover:opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent',
                    variant === 'success' && 'text-success-700 focus:ring-success-500',
                    variant === 'error' && 'text-danger-700 focus:ring-danger-500',
                    variant === 'warning' && 'text-warning-700 focus:ring-warning-500',
                    variant === 'info' && 'text-info-700 focus:ring-info-500',
                    variant === 'default' && 'text-foreground focus:ring-primary-500'
                  )}
                >
                  <span className="sr-only">Fechar</span>
                  <XMarkIcon className="h-5 w-5" />
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }
);
Toast.displayName = 'Toast';

// Toast Provider Context
export interface ToastData {
  id: string;
  variant?: ToastProps['variant'];
  title: string;
  message?: string;
  duration?: number;
  action?: ToastProps['action'];
  showIcon?: boolean;
  closable?: boolean;
  autoClose?: boolean;
}

export interface ToastContextType {
  toasts: ToastData[];
  addToast: (toast: Omit<ToastData, 'id'>) => void;
  removeToast: (id: string) => void;
  clearAllToasts: () => void;
}

const ToastContext = React.createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = React.useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};

const toastContainerVariants = cva(
  'fixed z-50 flex flex-col gap-2 p-4 pointer-events-none',
  {
    variants: {
      position: {
        'top-left': 'top-0 left-0',
        'top-right': 'top-0 right-0',
        'bottom-left': 'bottom-0 left-0',
        'bottom-right': 'bottom-0 right-0',
        'top-center': 'top-0 left-1/2 transform -translate-x-1/2',
        'bottom-center': 'bottom-0 left-1/2 transform -translate-x-1/2',
      },
    },
    defaultVariants: {
      position: 'top-right',
    },
  }
);

export interface ToastProviderProps {
  children: React.ReactNode;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center';
  maxToasts?: number;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ 
  children, 
  position = 'top-right',
  maxToasts = 5
}) => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  const addToast = (toast: Omit<ToastData, 'id'>) => {
    const id = Math.random().toString(36).substr(2, 9);
    const newToast: ToastData = { ...toast, id };
    
    setToasts(prev => {
      const newToasts = [newToast, ...prev];
      return newToasts.slice(0, maxToasts);
    });
  };

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  const clearAllToasts = () => {
    setToasts([]);
  };

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast, clearAllToasts }}>
      {children}
      <div className={toastContainerVariants({ position })}>
        {toasts.map(toast => (
          <Toast
            key={toast.id}
            {...toast}
            position={position}
            onClose={removeToast}
          />
        ))}
      </div>
    </ToastContext.Provider>
  );
};

// Convenience functions
export const createToast = {
  success: (title: string, message?: string, options?: Partial<ToastData>) => ({
    variant: 'success' as const,
    title,
    message,
    ...options,
  }),
  error: (title: string, message?: string, options?: Partial<ToastData>) => ({
    variant: 'error' as const,
    title,
    message,
    ...options,
  }),
  warning: (title: string, message?: string, options?: Partial<ToastData>) => ({
    variant: 'warning' as const,
    title,
    message,
    ...options,
  }),
  info: (title: string, message?: string, options?: Partial<ToastData>) => ({
    variant: 'info' as const,
    title,
    message,
    ...options,
  }),
  default: (title: string, message?: string, options?: Partial<ToastData>) => ({
    variant: 'default' as const,
    title,
    message,
    ...options,
  }),
};

export {
  toastVariants,
  toastIconVariants,
  toastActionVariants,
  toastContainerVariants,
};

export default Toast; 
