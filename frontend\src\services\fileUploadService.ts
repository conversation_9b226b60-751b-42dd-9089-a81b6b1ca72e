/**
 * File Upload Service - Integração com Backend
 * Sistema Magnow - Controle Inteligente de Estoque para Mercado Livre
 */

import { apiService } from './api';

export interface UploadResponse {
  success: boolean;
  message: string;
  data?: {
    file: {
      id: string;
      originalName: string;
      fileSize: number;
      fileType: string;
      mimeType: string;
      createdAt: string;
    };
    url: string;
  };
  error?: string;
}

export interface FileInfo {
  id: string;
  originalName: string;
  fileSize: number;
  fileType: 'avatar' | 'document' | 'spreadsheet';
  mimeType: string;
  isPublic: boolean;
  downloadCount?: number;
  lastAccessedAt?: string;
  createdAt: string;
}

export interface FileListResponse {
  success: boolean;
  data: {
    files: FileInfo[];
    pagination: {
      limit: number;
      offset: number;
      total: number;
    };
  };
}

export class FileUploadService {
  /**
   * Upload de avatar de usuário
   */
  static async uploadAvatar(file: File): Promise<UploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiService.post<UploadResponse>(
        '/files/upload/avatar',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: 'Erro ao enviar avatar',
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Upload de documento (PDF)
   */
  static async uploadDocument(
    file: File, 
    options?: { 
      isPublic?: boolean; 
      expiresIn?: number; 
    }
  ): Promise<UploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      if (options?.isPublic !== undefined) {
        formData.append('isPublic', String(options.isPublic));
      }
      
      if (options?.expiresIn) {
        formData.append('expiresIn', String(options.expiresIn));
      }

      const response = await apiService.post<UploadResponse>(
        '/files/upload/document',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: 'Erro ao enviar documento',
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Upload de planilha
   */
  static async uploadSpreadsheet(
    file: File, 
    options?: { 
      isPublic?: boolean; 
    }
  ): Promise<UploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      if (options?.isPublic !== undefined) {
        formData.append('isPublic', String(options.isPublic));
      }

      const response = await apiService.post<UploadResponse>(
        '/files/upload/spreadsheet',
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: 'Erro ao enviar planilha',
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Obter URL de download de arquivo
   */
  static getDownloadUrl(fileId: string): string {
    return `${apiService.defaults.baseURL}/files/download/${fileId}`;
  }

  /**
   * Obter URL de preview de imagem
   */
  static getPreviewUrl(fileId: string): string {
    return `${apiService.defaults.baseURL}/files/preview/${fileId}`;
  }

  /**
   * Listar arquivos
   */
  static async listFiles(options?: {
    fileType?: 'avatar' | 'document' | 'spreadsheet';
    uploadedBy?: string;
    limit?: number;
    offset?: number;
  }): Promise<FileListResponse> {
    try {
      const params = new URLSearchParams();
      
      if (options?.fileType) params.append('fileType', options.fileType);
      if (options?.uploadedBy) params.append('uploadedBy', options.uploadedBy);
      if (options?.limit) params.append('limit', String(options.limit));
      if (options?.offset) params.append('offset', String(options.offset));

      const response = await apiService.get<FileListResponse>(
        `/files?${params.toString()}`
      );

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        data: {
          files: [],
          pagination: { limit: 0, offset: 0, total: 0 }
        }
      };
    }
  }

  /**
   * Obter informações de um arquivo
   */
  static async getFileInfo(fileId: string): Promise<{
    success: boolean;
    data?: {
      file: FileInfo;
      downloadUrl: string;
    };
    error?: string;
  }> {
    try {
      const response = await apiService.get(`/files/${fileId}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Deletar arquivo
   */
  static async deleteFile(fileId: string): Promise<{
    success: boolean;
    message: string;
    error?: string;
  }> {
    try {
      const response = await apiService.delete(`/files/${fileId}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: 'Erro ao deletar arquivo',
        error: error.response?.data?.message || error.message
      };
    }
  }

  /**
   * Validar arquivo antes do upload
   */
  static validateFile(
    file: File, 
    type: 'avatar' | 'document' | 'spreadsheet'
  ): { valid: boolean; error?: string } {
    const validations = {
      avatar: {
        maxSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
        allowedExtensions: ['.jpg', '.jpeg', '.png', '.webp']
      },
      document: {
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['application/pdf'],
        allowedExtensions: ['.pdf']
      },
      spreadsheet: {
        maxSize: 15 * 1024 * 1024, // 15MB
        allowedTypes: [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel',
          'text/csv'
        ],
        allowedExtensions: ['.xlsx', '.xls', '.csv']
      }
    };

    const config = validations[type];

    // Verificar tamanho
    if (file.size > config.maxSize) {
      return {
        valid: false,
        error: `Arquivo muito grande. Máximo permitido: ${Math.round(config.maxSize / 1024 / 1024)}MB`
      };
    }

    // Verificar tipo MIME
    if (!config.allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: `Tipo de arquivo não permitido. Tipos aceitos: ${config.allowedTypes.join(', ')}`
      };
    }

    // Verificar extensão
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!config.allowedExtensions.includes(extension)) {
      return {
        valid: false,
        error: `Extensão não permitida. Extensões aceitas: ${config.allowedExtensions.join(', ')}`
      };
    }

    return { valid: true };
  }

  /**
   * Formatar tamanho de arquivo para exibição
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Obter ícone baseado no tipo de arquivo
   */
  static getFileIcon(mimeType: string): string {
    if (mimeType.startsWith('image/')) return '🖼️';
    if (mimeType === 'application/pdf') return '📄';
    if (mimeType.includes('spreadsheet') || mimeType.includes('excel') || mimeType === 'text/csv') return '📊';
    return '📁';
  }

  /**
   * Upload com progress tracking
   */
  static async uploadWithProgress(
    file: File,
    type: 'avatar' | 'document' | 'spreadsheet',
    onProgress?: (progress: number) => void,
    options?: any
  ): Promise<UploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      // Adicionar opções específicas
      if (options) {
        Object.keys(options).forEach(key => {
          formData.append(key, String(options[key]));
        });
      }

      const response = await apiService.post<UploadResponse>(
        `/files/upload/${type}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (onProgress && progressEvent.total) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              onProgress(progress);
            }
          },
        }
      );

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        message: `Erro ao enviar ${type}`,
        error: error.response?.data?.message || error.message
      };
    }
  }
}

export default FileUploadService;
