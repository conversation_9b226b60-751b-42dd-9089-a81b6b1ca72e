import swaggerJSDoc from 'swagger-jsdoc';
import * as swaggerUi from 'swagger-ui-express';

const options: swaggerJSDoc.Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Magnow API',
      version: '1.0.0',
      description: 'API para gestão de estoque integrada com Mercado Livre',
      contact: {
        name: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>'
      },
      license: {
        name: 'MIT',
        url: 'https://opensource.org/licenses/MIT'
      }
    },
    servers: [
      {
        url: 'http://localhost:3000',
        description: 'Servidor de Desenvolvimento'
      },
      {
        url: 'https://api.magnow.com',
        description: 'Servidor de Produção'
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT'
        }
      },
      responses: {
        Unauthorized: {
          description: 'Token de acesso inválido ou não fornecido',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        Forbidden: {
          description: 'Acesso negado - permissões insuficientes',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        NotFound: {
          description: 'Recurso não encontrado',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        BadRequest: {
          description: 'Dados da requisição inválidos',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        },
        TooManyRequests: {
          description: 'Muitas requisições - limite de rate limiting atingido',
          content: {
            'application/json': {
              schema: {
                $ref: '#/components/schemas/Error'
              }
            }
          }
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            error: {
              type: 'string',
              description: 'Mensagem de erro'
            },
            message: {
              type: 'string',
              description: 'Detalhes do erro'
            },
            code: {
              type: 'string',
              description: 'Código do erro'
            }
          }
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'ID único do usuário'
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Email do usuário'
            },
            name: {
              type: 'string',
              description: 'Nome do usuário'
            },
            role: {
              type: 'string',
              enum: ['USER', 'ADMIN'],
              description: 'Papel do usuário no sistema'
            },
            tenantId: {
              type: 'string',
              description: 'ID do tenant (isolamento multi-inquilino)'
            }
          }
        },
        AuthResponse: {
          type: 'object',
          properties: {
            token: {
              type: 'string',
              description: 'Token JWT para autenticação'
            },
            user: {
              $ref: '#/components/schemas/User'
            }
          }
        },
        StockItem: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'ID único do item'
            },
            sku: {
              type: 'string',
              description: 'SKU do produto'
            },
            name: {
              type: 'string',
              description: 'Nome do produto'
            },
            currentStock: {
              type: 'integer',
              description: 'Estoque atual'
            },
            reservedStock: {
              type: 'integer',
              description: 'Estoque reservado'
            },
            availableStock: {
              type: 'integer',
              description: 'Estoque disponível'
            },
            minStock: {
              type: 'integer',
              description: 'Estoque mínimo'
            },
            warehouse: {
              type: 'string',
              description: 'Armazém onde está localizado'
            },
            category: {
              type: 'string',
              description: 'Categoria do produto'
            }
          }
        },
        MercadoLivreIntegration: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'ID da integração'
            },
            userId: {
              type: 'string',
              description: 'ID do usuário'
            },
            accessToken: {
              type: 'string',
              description: 'Token de acesso do ML'
            },
            refreshToken: {
              type: 'string',
              description: 'Token de refresh do ML'
            },
            mlUserId: {
              type: 'string',
              description: 'ID do usuário no Mercado Livre'
            },
            isActive: {
              type: 'boolean',
              description: 'Status da integração'
            }
          }
        },
        Report: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'ID único do relatório'
            },
            type: {
              type: 'string',
              enum: ['STOCK', 'SALES', 'PERFORMANCE'],
              description: 'Tipo de relatório'
            },
            period: {
              type: 'string',
              description: 'Período do relatório'
            },
            data: {
              type: 'object',
              description: 'Dados do relatório'
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Data de criação'
            }
          }
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: [
    './src/routes/*.ts',
    './src/controllers/*.ts'
  ]
};

export const swaggerSpec = swaggerJSDoc(options);
export { swaggerUi };

export default {
  swaggerSpec,
  swaggerUi
}; 